// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_soldier.proto

package com.yorha.proto;

public final class PlayerSoldier {
  private PlayerSoldier() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_SoldierTrain_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierTrain_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierTrain_C2S}
   */
  public static final class Player_SoldierTrain_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierTrain_C2S)
      Player_SoldierTrain_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierTrain_C2S.newBuilder() to construct.
    private Player_SoldierTrain_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierTrain_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierTrain_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierTrain_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S other = (com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierTrain_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierTrain_C2S)
        com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S result = new com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierTrain_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierTrain_C2S)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierTrain_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierTrain_C2S>() {
      @java.lang.Override
      public Player_SoldierTrain_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierTrain_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierTrain_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierTrain_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierTrain_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierTrain_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierTrain_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierTrain_S2C}
   */
  public static final class Player_SoldierTrain_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierTrain_S2C)
      Player_SoldierTrain_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierTrain_S2C.newBuilder() to construct.
    private Player_SoldierTrain_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierTrain_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierTrain_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierTrain_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C other = (com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierTrain_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierTrain_S2C)
        com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierTrain_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C result = new com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierTrain_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierTrain_S2C)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierTrain_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierTrain_S2C>() {
      @java.lang.Override
      public Player_SoldierTrain_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierTrain_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierTrain_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierTrain_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierTrain_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierFastTrain_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierFastTrain_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierFastTrain_C2S}
   */
  public static final class Player_SoldierFastTrain_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierFastTrain_C2S)
      Player_SoldierFastTrain_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierFastTrain_C2S.newBuilder() to construct.
    private Player_SoldierFastTrain_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierFastTrain_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierFastTrain_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierFastTrain_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S other = (com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierFastTrain_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierFastTrain_C2S)
        com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S result = new com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierFastTrain_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierFastTrain_C2S)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierFastTrain_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierFastTrain_C2S>() {
      @java.lang.Override
      public Player_SoldierFastTrain_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierFastTrain_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierFastTrain_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierFastTrain_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierFastTrain_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierFastTrain_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierFastTrain_S2C}
   */
  public static final class Player_SoldierFastTrain_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierFastTrain_S2C)
      Player_SoldierFastTrain_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierFastTrain_S2C.newBuilder() to construct.
    private Player_SoldierFastTrain_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierFastTrain_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierFastTrain_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierFastTrain_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C other = (com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierFastTrain_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierFastTrain_S2C)
        com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C result = new com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierFastTrain_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierFastTrain_S2C)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierFastTrain_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierFastTrain_S2C>() {
      @java.lang.Override
      public Player_SoldierFastTrain_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierFastTrain_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierFastTrain_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierFastTrain_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierFastTrain_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierGather_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierGather_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierGather_C2S}
   */
  public static final class Player_SoldierGather_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierGather_C2S)
      Player_SoldierGather_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierGather_C2S.newBuilder() to construct.
    private Player_SoldierGather_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierGather_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierGather_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierGather_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S other = (com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierGather_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierGather_C2S)
        com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S result = new com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierGather_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierGather_C2S)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierGather_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierGather_C2S>() {
      @java.lang.Override
      public Player_SoldierGather_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierGather_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierGather_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierGather_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierGather_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierGather_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierGather_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierGather_S2C}
   */
  public static final class Player_SoldierGather_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierGather_S2C)
      Player_SoldierGather_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierGather_S2C.newBuilder() to construct.
    private Player_SoldierGather_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierGather_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierGather_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierGather_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C other = (com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierGather_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierGather_S2C)
        com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierGather_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C result = new com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierGather_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierGather_S2C)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierGather_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierGather_S2C>() {
      @java.lang.Override
      public Player_SoldierGather_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierGather_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierGather_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierGather_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierGather_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierRushTrain_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierRushTrain_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();

    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     * @return Whether the itemList field is set.
     */
    boolean hasItemList();
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     * @return The itemList.
     */
    com.yorha.proto.StructPB.ItemListPB getItemList();
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     */
    com.yorha.proto.StructPB.ItemListPBOrBuilder getItemListOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierRushTrain_C2S}
   */
  public static final class Player_SoldierRushTrain_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierRushTrain_C2S)
      Player_SoldierRushTrain_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierRushTrain_C2S.newBuilder() to construct.
    private Player_SoldierRushTrain_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierRushTrain_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierRushTrain_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierRushTrain_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            case 26: {
              com.yorha.proto.StructPB.ItemListPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = itemList_.toBuilder();
              }
              itemList_ = input.readMessage(com.yorha.proto.StructPB.ItemListPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(itemList_);
                itemList_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    public static final int ITEMLIST_FIELD_NUMBER = 3;
    private com.yorha.proto.StructPB.ItemListPB itemList_;
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     * @return Whether the itemList field is set.
     */
    @java.lang.Override
    public boolean hasItemList() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     * @return The itemList.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ItemListPB getItemList() {
      return itemList_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemList_;
    }
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ItemListPBOrBuilder getItemListOrBuilder() {
      return itemList_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemList_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getItemList());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getItemList());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S other = (com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (hasItemList() != other.hasItemList()) return false;
      if (hasItemList()) {
        if (!getItemList()
            .equals(other.getItemList())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      if (hasItemList()) {
        hash = (37 * hash) + ITEMLIST_FIELD_NUMBER;
        hash = (53 * hash) + getItemList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierRushTrain_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierRushTrain_C2S)
        com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getItemListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (itemListBuilder_ == null) {
          itemList_ = null;
        } else {
          itemListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S result = new com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (itemListBuilder_ == null) {
            result.itemList_ = itemList_;
          } else {
            result.itemList_ = itemListBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        if (other.hasItemList()) {
          mergeItemList(other.getItemList());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.ItemListPB itemList_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder> itemListBuilder_;
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       * @return Whether the itemList field is set.
       */
      public boolean hasItemList() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       * @return The itemList.
       */
      public com.yorha.proto.StructPB.ItemListPB getItemList() {
        if (itemListBuilder_ == null) {
          return itemList_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemList_;
        } else {
          return itemListBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public Builder setItemList(com.yorha.proto.StructPB.ItemListPB value) {
        if (itemListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          itemList_ = value;
          onChanged();
        } else {
          itemListBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public Builder setItemList(
          com.yorha.proto.StructPB.ItemListPB.Builder builderForValue) {
        if (itemListBuilder_ == null) {
          itemList_ = builderForValue.build();
          onChanged();
        } else {
          itemListBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public Builder mergeItemList(com.yorha.proto.StructPB.ItemListPB value) {
        if (itemListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              itemList_ != null &&
              itemList_ != com.yorha.proto.StructPB.ItemListPB.getDefaultInstance()) {
            itemList_ =
              com.yorha.proto.StructPB.ItemListPB.newBuilder(itemList_).mergeFrom(value).buildPartial();
          } else {
            itemList_ = value;
          }
          onChanged();
        } else {
          itemListBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public Builder clearItemList() {
        if (itemListBuilder_ == null) {
          itemList_ = null;
          onChanged();
        } else {
          itemListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public com.yorha.proto.StructPB.ItemListPB.Builder getItemListBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getItemListFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public com.yorha.proto.StructPB.ItemListPBOrBuilder getItemListOrBuilder() {
        if (itemListBuilder_ != null) {
          return itemListBuilder_.getMessageOrBuilder();
        } else {
          return itemList_ == null ?
              com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemList_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder> 
          getItemListFieldBuilder() {
        if (itemListBuilder_ == null) {
          itemListBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder>(
                  getItemList(),
                  getParentForChildren(),
                  isClean());
          itemList_ = null;
        }
        return itemListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierRushTrain_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierRushTrain_C2S)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierRushTrain_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierRushTrain_C2S>() {
      @java.lang.Override
      public Player_SoldierRushTrain_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierRushTrain_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierRushTrain_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierRushTrain_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierRushTrain_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierRushTrain_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierRushTrain_S2C}
   */
  public static final class Player_SoldierRushTrain_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierRushTrain_S2C)
      Player_SoldierRushTrain_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierRushTrain_S2C.newBuilder() to construct.
    private Player_SoldierRushTrain_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierRushTrain_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierRushTrain_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierRushTrain_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C other = (com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierRushTrain_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierRushTrain_S2C)
        com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C result = new com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierRushTrain_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierRushTrain_S2C)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierRushTrain_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierRushTrain_S2C>() {
      @java.lang.Override
      public Player_SoldierRushTrain_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierRushTrain_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierRushTrain_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierRushTrain_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierRushTrain_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierDismiss_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierDismiss_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();

    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return Whether the sPassWord field is set.
     */
    boolean hasSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The sPassWord.
     */
    java.lang.String getSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The bytes for sPassWord.
     */
    com.google.protobuf.ByteString
        getSPassWordBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierDismiss_C2S}
   */
  public static final class Player_SoldierDismiss_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierDismiss_C2S)
      Player_SoldierDismiss_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierDismiss_C2S.newBuilder() to construct.
    private Player_SoldierDismiss_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierDismiss_C2S() {
      sPassWord_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierDismiss_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierDismiss_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              sPassWord_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    public static final int SPASSWORD_FIELD_NUMBER = 3;
    private volatile java.lang.Object sPassWord_;
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return Whether the sPassWord field is set.
     */
    @java.lang.Override
    public boolean hasSPassWord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The sPassWord.
     */
    @java.lang.Override
    public java.lang.String getSPassWord() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sPassWord_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The bytes for sPassWord.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSPassWordBytes() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sPassWord_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, sPassWord_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, sPassWord_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S other = (com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (hasSPassWord() != other.hasSPassWord()) return false;
      if (hasSPassWord()) {
        if (!getSPassWord()
            .equals(other.getSPassWord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      if (hasSPassWord()) {
        hash = (37 * hash) + SPASSWORD_FIELD_NUMBER;
        hash = (53 * hash) + getSPassWord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierDismiss_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierDismiss_C2S)
        com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        sPassWord_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S result = new com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.sPassWord_ = sPassWord_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        if (other.hasSPassWord()) {
          bitField0_ |= 0x00000004;
          sPassWord_ = other.sPassWord_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object sPassWord_ = "";
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return Whether the sPassWord field is set.
       */
      public boolean hasSPassWord() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return The sPassWord.
       */
      public java.lang.String getSPassWord() {
        java.lang.Object ref = sPassWord_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sPassWord_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return The bytes for sPassWord.
       */
      public com.google.protobuf.ByteString
          getSPassWordBytes() {
        java.lang.Object ref = sPassWord_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sPassWord_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @param value The sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWord(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSPassWord() {
        bitField0_ = (bitField0_ & ~0x00000004);
        sPassWord_ = getDefaultInstance().getSPassWord();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @param value The bytes for sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWordBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierDismiss_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierDismiss_C2S)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierDismiss_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierDismiss_C2S>() {
      @java.lang.Override
      public Player_SoldierDismiss_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierDismiss_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierDismiss_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierDismiss_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierDismiss_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierDismiss_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierDismiss_S2C}
   */
  public static final class Player_SoldierDismiss_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierDismiss_S2C)
      Player_SoldierDismiss_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierDismiss_S2C.newBuilder() to construct.
    private Player_SoldierDismiss_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierDismiss_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierDismiss_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierDismiss_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C other = (com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierDismiss_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierDismiss_S2C)
        com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C result = new com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierDismiss_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierDismiss_S2C)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierDismiss_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierDismiss_S2C>() {
      @java.lang.Override
      public Player_SoldierDismiss_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierDismiss_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierDismiss_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierDismiss_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierDismiss_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierLevelUp_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierLevelUp_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierLevelUp_C2S}
   */
  public static final class Player_SoldierLevelUp_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierLevelUp_C2S)
      Player_SoldierLevelUp_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierLevelUp_C2S.newBuilder() to construct.
    private Player_SoldierLevelUp_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierLevelUp_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierLevelUp_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierLevelUp_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S other = (com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierLevelUp_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierLevelUp_C2S)
        com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S result = new com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierLevelUp_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierLevelUp_C2S)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierLevelUp_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierLevelUp_C2S>() {
      @java.lang.Override
      public Player_SoldierLevelUp_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierLevelUp_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierLevelUp_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierLevelUp_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierLevelUp_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierLevelUp_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierLevelUp_S2C}
   */
  public static final class Player_SoldierLevelUp_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierLevelUp_S2C)
      Player_SoldierLevelUp_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierLevelUp_S2C.newBuilder() to construct.
    private Player_SoldierLevelUp_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierLevelUp_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierLevelUp_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierLevelUp_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C other = (com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierLevelUp_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierLevelUp_S2C)
        com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C result = new com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierLevelUp_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierLevelUp_S2C)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierLevelUp_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierLevelUp_S2C>() {
      @java.lang.Override
      public Player_SoldierLevelUp_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierLevelUp_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierLevelUp_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierLevelUp_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierLevelUp_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierFastLevelUp_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierFastLevelUp_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierFastLevelUp_C2S}
   */
  public static final class Player_SoldierFastLevelUp_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierFastLevelUp_C2S)
      Player_SoldierFastLevelUp_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierFastLevelUp_C2S.newBuilder() to construct.
    private Player_SoldierFastLevelUp_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierFastLevelUp_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierFastLevelUp_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierFastLevelUp_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S other = (com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierFastLevelUp_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierFastLevelUp_C2S)
        com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S.class, com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S result = new com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierFastLevelUp_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierFastLevelUp_C2S)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierFastLevelUp_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierFastLevelUp_C2S>() {
      @java.lang.Override
      public Player_SoldierFastLevelUp_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierFastLevelUp_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierFastLevelUp_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierFastLevelUp_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SoldierFastLevelUp_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SoldierFastLevelUp_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SoldierFastLevelUp_S2C}
   */
  public static final class Player_SoldierFastLevelUp_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SoldierFastLevelUp_S2C)
      Player_SoldierFastLevelUp_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SoldierFastLevelUp_S2C.newBuilder() to construct.
    private Player_SoldierFastLevelUp_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SoldierFastLevelUp_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SoldierFastLevelUp_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SoldierFastLevelUp_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C other = (com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SoldierFastLevelUp_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SoldierFastLevelUp_S2C)
        com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C.class, com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSoldier.internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C build() {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C buildPartial() {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C result = new com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C other) {
        if (other == com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SoldierFastLevelUp_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SoldierFastLevelUp_S2C)
    private static final com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C();
    }

    public static com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SoldierFastLevelUp_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SoldierFastLevelUp_S2C>() {
      @java.lang.Override
      public Player_SoldierFastLevelUp_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SoldierFastLevelUp_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SoldierFastLevelUp_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SoldierFastLevelUp_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSoldier.Player_SoldierFastLevelUp_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierTrain_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierTrain_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierTrain_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierTrain_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierGather_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierGather_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierGather_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierGather_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/player/cs/player_soldier." +
      "proto\022\017com.yorha.proto\032\"cs_proto/gen/com" +
      "mon/structPB.proto\"9\n\027Player_SoldierTrai" +
      "n_C2S\022\021\n\tsoldierId\030\001 \001(\005\022\013\n\003num\030\002 \001(\005\"\031\n" +
      "\027Player_SoldierTrain_S2C\"=\n\033Player_Soldi" +
      "erFastTrain_C2S\022\021\n\tsoldierId\030\001 \001(\005\022\013\n\003nu" +
      "m\030\002 \001(\005\"\035\n\033Player_SoldierFastTrain_S2C\"-" +
      "\n\030Player_SoldierGather_C2S\022\021\n\tsoldierId\030" +
      "\001 \001(\005\"\032\n\030Player_SoldierGather_S2C\"l\n\033Pla" +
      "yer_SoldierRushTrain_C2S\022\021\n\tsoldierId\030\001 " +
      "\001(\005\022\013\n\003num\030\002 \001(\005\022-\n\010itemList\030\003 \001(\0132\033.com" +
      ".yorha.proto.ItemListPB\"\035\n\033Player_Soldie" +
      "rRushTrain_S2C\"N\n\031Player_SoldierDismiss_" +
      "C2S\022\021\n\tsoldierId\030\001 \001(\005\022\013\n\003num\030\002 \001(\005\022\021\n\ts" +
      "PassWord\030\003 \001(\t\"\033\n\031Player_SoldierDismiss_" +
      "S2C\";\n\031Player_SoldierLevelUp_C2S\022\021\n\tsold" +
      "ierId\030\001 \001(\005\022\013\n\003num\030\002 \001(\005\"\033\n\031Player_Soldi" +
      "erLevelUp_S2C\"?\n\035Player_SoldierFastLevel" +
      "Up_C2S\022\021\n\tsoldierId\030\001 \001(\005\022\013\n\003num\030\002 \001(\005\"\037" +
      "\n\035Player_SoldierFastLevelUp_S2CB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_SoldierTrain_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_SoldierTrain_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierTrain_C2S_descriptor,
        new java.lang.String[] { "SoldierId", "Num", });
    internal_static_com_yorha_proto_Player_SoldierTrain_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_SoldierTrain_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierTrain_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierFastTrain_C2S_descriptor,
        new java.lang.String[] { "SoldierId", "Num", });
    internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierFastTrain_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SoldierGather_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_SoldierGather_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierGather_C2S_descriptor,
        new java.lang.String[] { "SoldierId", });
    internal_static_com_yorha_proto_Player_SoldierGather_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_SoldierGather_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierGather_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierRushTrain_C2S_descriptor,
        new java.lang.String[] { "SoldierId", "Num", "ItemList", });
    internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierRushTrain_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierDismiss_C2S_descriptor,
        new java.lang.String[] { "SoldierId", "Num", "SPassWord", });
    internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierDismiss_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierLevelUp_C2S_descriptor,
        new java.lang.String[] { "SoldierId", "Num", });
    internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierLevelUp_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierFastLevelUp_C2S_descriptor,
        new java.lang.String[] { "SoldierId", "Num", });
    internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SoldierFastLevelUp_S2C_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.StructPB.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

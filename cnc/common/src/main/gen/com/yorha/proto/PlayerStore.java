// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_store.proto

package com.yorha.proto;

public final class PlayerStore {
  private PlayerStore() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_FetchCommissariatStoreInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchCommissariatStoreInfo_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchCommissariatStoreInfo_C2S}
   */
  public static final class Player_FetchCommissariatStoreInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchCommissariatStoreInfo_C2S)
      Player_FetchCommissariatStoreInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchCommissariatStoreInfo_C2S.newBuilder() to construct.
    private Player_FetchCommissariatStoreInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchCommissariatStoreInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchCommissariatStoreInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchCommissariatStoreInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S.class, com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S other = (com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchCommissariatStoreInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchCommissariatStoreInfo_C2S)
        com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S.class, com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S build() {
        com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S buildPartial() {
        com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S result = new com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S other) {
        if (other == com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchCommissariatStoreInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchCommissariatStoreInfo_C2S)
    private static final com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S();
    }

    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchCommissariatStoreInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchCommissariatStoreInfo_C2S>() {
      @java.lang.Override
      public Player_FetchCommissariatStoreInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchCommissariatStoreInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchCommissariatStoreInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchCommissariatStoreInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchCommissariatStoreInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchCommissariatStoreInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.StoreInfo> 
        getInfoList();
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    com.yorha.proto.StructMsg.StoreInfo getInfo(int index);
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    int getInfoCount();
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.StoreInfoOrBuilder> 
        getInfoOrBuilderList();
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    com.yorha.proto.StructMsg.StoreInfoOrBuilder getInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchCommissariatStoreInfo_S2C}
   */
  public static final class Player_FetchCommissariatStoreInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchCommissariatStoreInfo_S2C)
      Player_FetchCommissariatStoreInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchCommissariatStoreInfo_S2C.newBuilder() to construct.
    private Player_FetchCommissariatStoreInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchCommissariatStoreInfo_S2C() {
      info_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchCommissariatStoreInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchCommissariatStoreInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = new java.util.ArrayList<com.yorha.proto.StructMsg.StoreInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              info_.add(
                  input.readMessage(com.yorha.proto.StructMsg.StoreInfo.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          info_ = java.util.Collections.unmodifiableList(info_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C.class, com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C.Builder.class);
    }

    public static final int INFO_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.StructMsg.StoreInfo> info_;
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.StoreInfo> getInfoList() {
      return info_;
    }
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.StoreInfoOrBuilder> 
        getInfoOrBuilderList() {
      return info_;
    }
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public int getInfoCount() {
      return info_.size();
    }
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.StoreInfo getInfo(int index) {
      return info_.get(index);
    }
    /**
     * <pre>
     * 商店信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.StoreInfoOrBuilder getInfoOrBuilder(
        int index) {
      return info_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < info_.size(); i++) {
        output.writeMessage(1, info_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < info_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, info_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C other = (com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C) obj;

      if (!getInfoList()
          .equals(other.getInfoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfoCount() > 0) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchCommissariatStoreInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchCommissariatStoreInfo_S2C)
        com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C.class, com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C build() {
        com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C buildPartial() {
        com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C result = new com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            info_ = java.util.Collections.unmodifiableList(info_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.info_ = info_;
        } else {
          result.info_ = infoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C other) {
        if (other == com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C.getDefaultInstance()) return this;
        if (infoBuilder_ == null) {
          if (!other.info_.isEmpty()) {
            if (info_.isEmpty()) {
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfoIsMutable();
              info_.addAll(other.info_);
            }
            onChanged();
          }
        } else {
          if (!other.info_.isEmpty()) {
            if (infoBuilder_.isEmpty()) {
              infoBuilder_.dispose();
              infoBuilder_ = null;
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfoFieldBuilder() : null;
            } else {
              infoBuilder_.addAllMessages(other.info_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.StructMsg.StoreInfo> info_ =
        java.util.Collections.emptyList();
      private void ensureInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          info_ = new java.util.ArrayList<com.yorha.proto.StructMsg.StoreInfo>(info_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.StoreInfo, com.yorha.proto.StructMsg.StoreInfo.Builder, com.yorha.proto.StructMsg.StoreInfoOrBuilder> infoBuilder_;

      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.StoreInfo> getInfoList() {
        if (infoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(info_);
        } else {
          return infoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public int getInfoCount() {
        if (infoBuilder_ == null) {
          return info_.size();
        } else {
          return infoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfo getInfo(int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);
        } else {
          return infoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.StructMsg.StoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.set(index, value);
          onChanged();
        } else {
          infoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.StructMsg.StoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.set(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addInfo(com.yorha.proto.StructMsg.StoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(value);
          onChanged();
        } else {
          infoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.StructMsg.StoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(index, value);
          onChanged();
        } else {
          infoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addInfo(
          com.yorha.proto.StructMsg.StoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.StructMsg.StoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addAllInfo(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.StoreInfo> values) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, info_);
          onChanged();
        } else {
          infoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder removeInfo(int index) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.remove(index);
          onChanged();
        } else {
          infoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfo.Builder getInfoBuilder(
          int index) {
        return getInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfoOrBuilder getInfoOrBuilder(
          int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);  } else {
          return infoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.StoreInfoOrBuilder> 
           getInfoOrBuilderList() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(info_);
        }
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfo.Builder addInfoBuilder() {
        return getInfoFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.StoreInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfo.Builder addInfoBuilder(
          int index) {
        return getInfoFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.StoreInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 商店信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.StoreInfo.Builder> 
           getInfoBuilderList() {
        return getInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.StoreInfo, com.yorha.proto.StructMsg.StoreInfo.Builder, com.yorha.proto.StructMsg.StoreInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.StoreInfo, com.yorha.proto.StructMsg.StoreInfo.Builder, com.yorha.proto.StructMsg.StoreInfoOrBuilder>(
                  info_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchCommissariatStoreInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchCommissariatStoreInfo_S2C)
    private static final com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C();
    }

    public static com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchCommissariatStoreInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchCommissariatStoreInfo_S2C>() {
      @java.lang.Override
      public Player_FetchCommissariatStoreInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchCommissariatStoreInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchCommissariatStoreInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchCommissariatStoreInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerStore.Player_FetchCommissariatStoreInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_BuyStoreItem_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_BuyStoreItem_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商店类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
     * @return Whether the storeType field is set.
     */
    boolean hasStoreType();
    /**
     * <pre>
     * 商店类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
     * @return The storeType.
     */
    com.yorha.proto.CommonEnum.StoreType getStoreType();

    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 2;</code>
     * @return Whether the configGoodsId field is set.
     */
    boolean hasConfigGoodsId();
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 2;</code>
     * @return The configGoodsId.
     */
    long getConfigGoodsId();

    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 3;</code>
     * @return Whether the buyNum field is set.
     */
    boolean hasBuyNum();
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 3;</code>
     * @return The buyNum.
     */
    int getBuyNum();

    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 4;</code>
     * @return Whether the sPassWord field is set.
     */
    boolean hasSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 4;</code>
     * @return The sPassWord.
     */
    java.lang.String getSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 4;</code>
     * @return The bytes for sPassWord.
     */
    com.google.protobuf.ByteString
        getSPassWordBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_BuyStoreItem_C2S}
   */
  public static final class Player_BuyStoreItem_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_BuyStoreItem_C2S)
      Player_BuyStoreItem_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_BuyStoreItem_C2S.newBuilder() to construct.
    private Player_BuyStoreItem_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_BuyStoreItem_C2S() {
      storeType_ = 0;
      sPassWord_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_BuyStoreItem_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_BuyStoreItem_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.StoreType value = com.yorha.proto.CommonEnum.StoreType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                storeType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              configGoodsId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              buyNum_ = input.readInt32();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              sPassWord_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S.class, com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int STORETYPE_FIELD_NUMBER = 1;
    private int storeType_;
    /**
     * <pre>
     * 商店类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
     * @return Whether the storeType field is set.
     */
    @java.lang.Override public boolean hasStoreType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 商店类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
     * @return The storeType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.StoreType getStoreType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.StoreType result = com.yorha.proto.CommonEnum.StoreType.valueOf(storeType_);
      return result == null ? com.yorha.proto.CommonEnum.StoreType.STORE_TYPE_NONE : result;
    }

    public static final int CONFIGGOODSID_FIELD_NUMBER = 2;
    private long configGoodsId_;
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 2;</code>
     * @return Whether the configGoodsId field is set.
     */
    @java.lang.Override
    public boolean hasConfigGoodsId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 2;</code>
     * @return The configGoodsId.
     */
    @java.lang.Override
    public long getConfigGoodsId() {
      return configGoodsId_;
    }

    public static final int BUYNUM_FIELD_NUMBER = 3;
    private int buyNum_;
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 3;</code>
     * @return Whether the buyNum field is set.
     */
    @java.lang.Override
    public boolean hasBuyNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 3;</code>
     * @return The buyNum.
     */
    @java.lang.Override
    public int getBuyNum() {
      return buyNum_;
    }

    public static final int SPASSWORD_FIELD_NUMBER = 4;
    private volatile java.lang.Object sPassWord_;
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 4;</code>
     * @return Whether the sPassWord field is set.
     */
    @java.lang.Override
    public boolean hasSPassWord() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 4;</code>
     * @return The sPassWord.
     */
    @java.lang.Override
    public java.lang.String getSPassWord() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sPassWord_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 4;</code>
     * @return The bytes for sPassWord.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSPassWordBytes() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sPassWord_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, storeType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, configGoodsId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, buyNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, sPassWord_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, storeType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, configGoodsId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, buyNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, sPassWord_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S other = (com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S) obj;

      if (hasStoreType() != other.hasStoreType()) return false;
      if (hasStoreType()) {
        if (storeType_ != other.storeType_) return false;
      }
      if (hasConfigGoodsId() != other.hasConfigGoodsId()) return false;
      if (hasConfigGoodsId()) {
        if (getConfigGoodsId()
            != other.getConfigGoodsId()) return false;
      }
      if (hasBuyNum() != other.hasBuyNum()) return false;
      if (hasBuyNum()) {
        if (getBuyNum()
            != other.getBuyNum()) return false;
      }
      if (hasSPassWord() != other.hasSPassWord()) return false;
      if (hasSPassWord()) {
        if (!getSPassWord()
            .equals(other.getSPassWord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasStoreType()) {
        hash = (37 * hash) + STORETYPE_FIELD_NUMBER;
        hash = (53 * hash) + storeType_;
      }
      if (hasConfigGoodsId()) {
        hash = (37 * hash) + CONFIGGOODSID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getConfigGoodsId());
      }
      if (hasBuyNum()) {
        hash = (37 * hash) + BUYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getBuyNum();
      }
      if (hasSPassWord()) {
        hash = (37 * hash) + SPASSWORD_FIELD_NUMBER;
        hash = (53 * hash) + getSPassWord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_BuyStoreItem_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_BuyStoreItem_C2S)
        com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S.class, com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        storeType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        configGoodsId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        buyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        sPassWord_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S build() {
        com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S buildPartial() {
        com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S result = new com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.storeType_ = storeType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.configGoodsId_ = configGoodsId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.buyNum_ = buyNum_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.sPassWord_ = sPassWord_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S) {
          return mergeFrom((com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S other) {
        if (other == com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S.getDefaultInstance()) return this;
        if (other.hasStoreType()) {
          setStoreType(other.getStoreType());
        }
        if (other.hasConfigGoodsId()) {
          setConfigGoodsId(other.getConfigGoodsId());
        }
        if (other.hasBuyNum()) {
          setBuyNum(other.getBuyNum());
        }
        if (other.hasSPassWord()) {
          bitField0_ |= 0x00000008;
          sPassWord_ = other.sPassWord_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int storeType_ = 0;
      /**
       * <pre>
       * 商店类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
       * @return Whether the storeType field is set.
       */
      @java.lang.Override public boolean hasStoreType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 商店类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
       * @return The storeType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.StoreType getStoreType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.StoreType result = com.yorha.proto.CommonEnum.StoreType.valueOf(storeType_);
        return result == null ? com.yorha.proto.CommonEnum.StoreType.STORE_TYPE_NONE : result;
      }
      /**
       * <pre>
       * 商店类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
       * @param value The storeType to set.
       * @return This builder for chaining.
       */
      public Builder setStoreType(com.yorha.proto.CommonEnum.StoreType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        storeType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 商店类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStoreType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        storeType_ = 0;
        onChanged();
        return this;
      }

      private long configGoodsId_ ;
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 2;</code>
       * @return Whether the configGoodsId field is set.
       */
      @java.lang.Override
      public boolean hasConfigGoodsId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 2;</code>
       * @return The configGoodsId.
       */
      @java.lang.Override
      public long getConfigGoodsId() {
        return configGoodsId_;
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 2;</code>
       * @param value The configGoodsId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigGoodsId(long value) {
        bitField0_ |= 0x00000002;
        configGoodsId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        configGoodsId_ = 0L;
        onChanged();
        return this;
      }

      private int buyNum_ ;
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 3;</code>
       * @return Whether the buyNum field is set.
       */
      @java.lang.Override
      public boolean hasBuyNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 3;</code>
       * @return The buyNum.
       */
      @java.lang.Override
      public int getBuyNum() {
        return buyNum_;
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 3;</code>
       * @param value The buyNum to set.
       * @return This builder for chaining.
       */
      public Builder setBuyNum(int value) {
        bitField0_ |= 0x00000004;
        buyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuyNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        buyNum_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object sPassWord_ = "";
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 4;</code>
       * @return Whether the sPassWord field is set.
       */
      public boolean hasSPassWord() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 4;</code>
       * @return The sPassWord.
       */
      public java.lang.String getSPassWord() {
        java.lang.Object ref = sPassWord_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sPassWord_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 4;</code>
       * @return The bytes for sPassWord.
       */
      public com.google.protobuf.ByteString
          getSPassWordBytes() {
        java.lang.Object ref = sPassWord_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sPassWord_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 4;</code>
       * @param value The sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWord(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSPassWord() {
        bitField0_ = (bitField0_ & ~0x00000008);
        sPassWord_ = getDefaultInstance().getSPassWord();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 4;</code>
       * @param value The bytes for sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWordBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_BuyStoreItem_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_BuyStoreItem_C2S)
    private static final com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S();
    }

    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_BuyStoreItem_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_BuyStoreItem_C2S>() {
      @java.lang.Override
      public Player_BuyStoreItem_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_BuyStoreItem_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_BuyStoreItem_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_BuyStoreItem_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerStore.Player_BuyStoreItem_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_BuyStoreItem_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_BuyStoreItem_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 成功则返回剩余可购买数目，失败通过错误码返回
     * </pre>
     *
     * <code>optional int32 leftCanBuyNum = 1;</code>
     * @return Whether the leftCanBuyNum field is set.
     */
    boolean hasLeftCanBuyNum();
    /**
     * <pre>
     * 成功则返回剩余可购买数目，失败通过错误码返回
     * </pre>
     *
     * <code>optional int32 leftCanBuyNum = 1;</code>
     * @return The leftCanBuyNum.
     */
    int getLeftCanBuyNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_BuyStoreItem_S2C}
   */
  public static final class Player_BuyStoreItem_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_BuyStoreItem_S2C)
      Player_BuyStoreItem_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_BuyStoreItem_S2C.newBuilder() to construct.
    private Player_BuyStoreItem_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_BuyStoreItem_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_BuyStoreItem_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_BuyStoreItem_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              leftCanBuyNum_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C.class, com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int LEFTCANBUYNUM_FIELD_NUMBER = 1;
    private int leftCanBuyNum_;
    /**
     * <pre>
     * 成功则返回剩余可购买数目，失败通过错误码返回
     * </pre>
     *
     * <code>optional int32 leftCanBuyNum = 1;</code>
     * @return Whether the leftCanBuyNum field is set.
     */
    @java.lang.Override
    public boolean hasLeftCanBuyNum() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 成功则返回剩余可购买数目，失败通过错误码返回
     * </pre>
     *
     * <code>optional int32 leftCanBuyNum = 1;</code>
     * @return The leftCanBuyNum.
     */
    @java.lang.Override
    public int getLeftCanBuyNum() {
      return leftCanBuyNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, leftCanBuyNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, leftCanBuyNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C other = (com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C) obj;

      if (hasLeftCanBuyNum() != other.hasLeftCanBuyNum()) return false;
      if (hasLeftCanBuyNum()) {
        if (getLeftCanBuyNum()
            != other.getLeftCanBuyNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLeftCanBuyNum()) {
        hash = (37 * hash) + LEFTCANBUYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getLeftCanBuyNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_BuyStoreItem_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_BuyStoreItem_S2C)
        com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C.class, com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        leftCanBuyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C build() {
        com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C buildPartial() {
        com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C result = new com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.leftCanBuyNum_ = leftCanBuyNum_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C) {
          return mergeFrom((com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C other) {
        if (other == com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C.getDefaultInstance()) return this;
        if (other.hasLeftCanBuyNum()) {
          setLeftCanBuyNum(other.getLeftCanBuyNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int leftCanBuyNum_ ;
      /**
       * <pre>
       * 成功则返回剩余可购买数目，失败通过错误码返回
       * </pre>
       *
       * <code>optional int32 leftCanBuyNum = 1;</code>
       * @return Whether the leftCanBuyNum field is set.
       */
      @java.lang.Override
      public boolean hasLeftCanBuyNum() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 成功则返回剩余可购买数目，失败通过错误码返回
       * </pre>
       *
       * <code>optional int32 leftCanBuyNum = 1;</code>
       * @return The leftCanBuyNum.
       */
      @java.lang.Override
      public int getLeftCanBuyNum() {
        return leftCanBuyNum_;
      }
      /**
       * <pre>
       * 成功则返回剩余可购买数目，失败通过错误码返回
       * </pre>
       *
       * <code>optional int32 leftCanBuyNum = 1;</code>
       * @param value The leftCanBuyNum to set.
       * @return This builder for chaining.
       */
      public Builder setLeftCanBuyNum(int value) {
        bitField0_ |= 0x00000001;
        leftCanBuyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成功则返回剩余可购买数目，失败通过错误码返回
       * </pre>
       *
       * <code>optional int32 leftCanBuyNum = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLeftCanBuyNum() {
        bitField0_ = (bitField0_ & ~0x00000001);
        leftCanBuyNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_BuyStoreItem_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_BuyStoreItem_S2C)
    private static final com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C();
    }

    public static com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_BuyStoreItem_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_BuyStoreItem_S2C>() {
      @java.lang.Override
      public Player_BuyStoreItem_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_BuyStoreItem_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_BuyStoreItem_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_BuyStoreItem_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerStore.Player_BuyStoreItem_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_BuyAndUseStoreItem_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_BuyAndUseStoreItem_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商店类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
     * @return Whether the storeType field is set.
     */
    boolean hasStoreType();
    /**
     * <pre>
     * 商店类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
     * @return The storeType.
     */
    com.yorha.proto.CommonEnum.StoreType getStoreType();

    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 2;</code>
     * @return Whether the configGoodsId field is set.
     */
    boolean hasConfigGoodsId();
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 2;</code>
     * @return The configGoodsId.
     */
    long getConfigGoodsId();

    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 3;</code>
     * @return Whether the buyNum field is set.
     */
    boolean hasBuyNum();
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 3;</code>
     * @return The buyNum.
     */
    int getBuyNum();

    /**
     * <pre>
     * 使用参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
     * @return Whether the params field is set.
     */
    boolean hasParams();
    /**
     * <pre>
     * 使用参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
     * @return The params.
     */
    com.yorha.proto.PlayerPB.ItemUseParamsPB getParams();
    /**
     * <pre>
     * 使用参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
     */
    com.yorha.proto.PlayerPB.ItemUseParamsPBOrBuilder getParamsOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_BuyAndUseStoreItem_C2S}
   */
  public static final class Player_BuyAndUseStoreItem_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_BuyAndUseStoreItem_C2S)
      Player_BuyAndUseStoreItem_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_BuyAndUseStoreItem_C2S.newBuilder() to construct.
    private Player_BuyAndUseStoreItem_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_BuyAndUseStoreItem_C2S() {
      storeType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_BuyAndUseStoreItem_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_BuyAndUseStoreItem_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.StoreType value = com.yorha.proto.CommonEnum.StoreType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                storeType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              configGoodsId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              buyNum_ = input.readInt32();
              break;
            }
            case 34: {
              com.yorha.proto.PlayerPB.ItemUseParamsPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = params_.toBuilder();
              }
              params_ = input.readMessage(com.yorha.proto.PlayerPB.ItemUseParamsPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(params_);
                params_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S.class, com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int STORETYPE_FIELD_NUMBER = 1;
    private int storeType_;
    /**
     * <pre>
     * 商店类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
     * @return Whether the storeType field is set.
     */
    @java.lang.Override public boolean hasStoreType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 商店类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
     * @return The storeType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.StoreType getStoreType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.StoreType result = com.yorha.proto.CommonEnum.StoreType.valueOf(storeType_);
      return result == null ? com.yorha.proto.CommonEnum.StoreType.STORE_TYPE_NONE : result;
    }

    public static final int CONFIGGOODSID_FIELD_NUMBER = 2;
    private long configGoodsId_;
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 2;</code>
     * @return Whether the configGoodsId field is set.
     */
    @java.lang.Override
    public boolean hasConfigGoodsId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 2;</code>
     * @return The configGoodsId.
     */
    @java.lang.Override
    public long getConfigGoodsId() {
      return configGoodsId_;
    }

    public static final int BUYNUM_FIELD_NUMBER = 3;
    private int buyNum_;
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 3;</code>
     * @return Whether the buyNum field is set.
     */
    @java.lang.Override
    public boolean hasBuyNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 3;</code>
     * @return The buyNum.
     */
    @java.lang.Override
    public int getBuyNum() {
      return buyNum_;
    }

    public static final int PARAMS_FIELD_NUMBER = 4;
    private com.yorha.proto.PlayerPB.ItemUseParamsPB params_;
    /**
     * <pre>
     * 使用参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
     * @return Whether the params field is set.
     */
    @java.lang.Override
    public boolean hasParams() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 使用参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
     * @return The params.
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPB.ItemUseParamsPB getParams() {
      return params_ == null ? com.yorha.proto.PlayerPB.ItemUseParamsPB.getDefaultInstance() : params_;
    }
    /**
     * <pre>
     * 使用参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPB.ItemUseParamsPBOrBuilder getParamsOrBuilder() {
      return params_ == null ? com.yorha.proto.PlayerPB.ItemUseParamsPB.getDefaultInstance() : params_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, storeType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, configGoodsId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, buyNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getParams());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, storeType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, configGoodsId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, buyNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getParams());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S other = (com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S) obj;

      if (hasStoreType() != other.hasStoreType()) return false;
      if (hasStoreType()) {
        if (storeType_ != other.storeType_) return false;
      }
      if (hasConfigGoodsId() != other.hasConfigGoodsId()) return false;
      if (hasConfigGoodsId()) {
        if (getConfigGoodsId()
            != other.getConfigGoodsId()) return false;
      }
      if (hasBuyNum() != other.hasBuyNum()) return false;
      if (hasBuyNum()) {
        if (getBuyNum()
            != other.getBuyNum()) return false;
      }
      if (hasParams() != other.hasParams()) return false;
      if (hasParams()) {
        if (!getParams()
            .equals(other.getParams())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasStoreType()) {
        hash = (37 * hash) + STORETYPE_FIELD_NUMBER;
        hash = (53 * hash) + storeType_;
      }
      if (hasConfigGoodsId()) {
        hash = (37 * hash) + CONFIGGOODSID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getConfigGoodsId());
      }
      if (hasBuyNum()) {
        hash = (37 * hash) + BUYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getBuyNum();
      }
      if (hasParams()) {
        hash = (37 * hash) + PARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getParams().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_BuyAndUseStoreItem_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_BuyAndUseStoreItem_C2S)
        com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S.class, com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        storeType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        configGoodsId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        buyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (paramsBuilder_ == null) {
          params_ = null;
        } else {
          paramsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S build() {
        com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S buildPartial() {
        com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S result = new com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.storeType_ = storeType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.configGoodsId_ = configGoodsId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.buyNum_ = buyNum_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (paramsBuilder_ == null) {
            result.params_ = params_;
          } else {
            result.params_ = paramsBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S) {
          return mergeFrom((com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S other) {
        if (other == com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S.getDefaultInstance()) return this;
        if (other.hasStoreType()) {
          setStoreType(other.getStoreType());
        }
        if (other.hasConfigGoodsId()) {
          setConfigGoodsId(other.getConfigGoodsId());
        }
        if (other.hasBuyNum()) {
          setBuyNum(other.getBuyNum());
        }
        if (other.hasParams()) {
          mergeParams(other.getParams());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int storeType_ = 0;
      /**
       * <pre>
       * 商店类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
       * @return Whether the storeType field is set.
       */
      @java.lang.Override public boolean hasStoreType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 商店类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
       * @return The storeType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.StoreType getStoreType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.StoreType result = com.yorha.proto.CommonEnum.StoreType.valueOf(storeType_);
        return result == null ? com.yorha.proto.CommonEnum.StoreType.STORE_TYPE_NONE : result;
      }
      /**
       * <pre>
       * 商店类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
       * @param value The storeType to set.
       * @return This builder for chaining.
       */
      public Builder setStoreType(com.yorha.proto.CommonEnum.StoreType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        storeType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 商店类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.StoreType storeType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStoreType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        storeType_ = 0;
        onChanged();
        return this;
      }

      private long configGoodsId_ ;
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 2;</code>
       * @return Whether the configGoodsId field is set.
       */
      @java.lang.Override
      public boolean hasConfigGoodsId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 2;</code>
       * @return The configGoodsId.
       */
      @java.lang.Override
      public long getConfigGoodsId() {
        return configGoodsId_;
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 2;</code>
       * @param value The configGoodsId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigGoodsId(long value) {
        bitField0_ |= 0x00000002;
        configGoodsId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        configGoodsId_ = 0L;
        onChanged();
        return this;
      }

      private int buyNum_ ;
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 3;</code>
       * @return Whether the buyNum field is set.
       */
      @java.lang.Override
      public boolean hasBuyNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 3;</code>
       * @return The buyNum.
       */
      @java.lang.Override
      public int getBuyNum() {
        return buyNum_;
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 3;</code>
       * @param value The buyNum to set.
       * @return This builder for chaining.
       */
      public Builder setBuyNum(int value) {
        bitField0_ |= 0x00000004;
        buyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuyNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        buyNum_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.PlayerPB.ItemUseParamsPB params_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPB.ItemUseParamsPB, com.yorha.proto.PlayerPB.ItemUseParamsPB.Builder, com.yorha.proto.PlayerPB.ItemUseParamsPBOrBuilder> paramsBuilder_;
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       * @return Whether the params field is set.
       */
      public boolean hasParams() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       * @return The params.
       */
      public com.yorha.proto.PlayerPB.ItemUseParamsPB getParams() {
        if (paramsBuilder_ == null) {
          return params_ == null ? com.yorha.proto.PlayerPB.ItemUseParamsPB.getDefaultInstance() : params_;
        } else {
          return paramsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       */
      public Builder setParams(com.yorha.proto.PlayerPB.ItemUseParamsPB value) {
        if (paramsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          params_ = value;
          onChanged();
        } else {
          paramsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       */
      public Builder setParams(
          com.yorha.proto.PlayerPB.ItemUseParamsPB.Builder builderForValue) {
        if (paramsBuilder_ == null) {
          params_ = builderForValue.build();
          onChanged();
        } else {
          paramsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       */
      public Builder mergeParams(com.yorha.proto.PlayerPB.ItemUseParamsPB value) {
        if (paramsBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              params_ != null &&
              params_ != com.yorha.proto.PlayerPB.ItemUseParamsPB.getDefaultInstance()) {
            params_ =
              com.yorha.proto.PlayerPB.ItemUseParamsPB.newBuilder(params_).mergeFrom(value).buildPartial();
          } else {
            params_ = value;
          }
          onChanged();
        } else {
          paramsBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       */
      public Builder clearParams() {
        if (paramsBuilder_ == null) {
          params_ = null;
          onChanged();
        } else {
          paramsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       */
      public com.yorha.proto.PlayerPB.ItemUseParamsPB.Builder getParamsBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getParamsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       */
      public com.yorha.proto.PlayerPB.ItemUseParamsPBOrBuilder getParamsOrBuilder() {
        if (paramsBuilder_ != null) {
          return paramsBuilder_.getMessageOrBuilder();
        } else {
          return params_ == null ?
              com.yorha.proto.PlayerPB.ItemUseParamsPB.getDefaultInstance() : params_;
        }
      }
      /**
       * <pre>
       * 使用参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseParamsPB params = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPB.ItemUseParamsPB, com.yorha.proto.PlayerPB.ItemUseParamsPB.Builder, com.yorha.proto.PlayerPB.ItemUseParamsPBOrBuilder> 
          getParamsFieldBuilder() {
        if (paramsBuilder_ == null) {
          paramsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.PlayerPB.ItemUseParamsPB, com.yorha.proto.PlayerPB.ItemUseParamsPB.Builder, com.yorha.proto.PlayerPB.ItemUseParamsPBOrBuilder>(
                  getParams(),
                  getParentForChildren(),
                  isClean());
          params_ = null;
        }
        return paramsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_BuyAndUseStoreItem_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_BuyAndUseStoreItem_C2S)
    private static final com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S();
    }

    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_BuyAndUseStoreItem_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_BuyAndUseStoreItem_C2S>() {
      @java.lang.Override
      public Player_BuyAndUseStoreItem_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_BuyAndUseStoreItem_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_BuyAndUseStoreItem_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_BuyAndUseStoreItem_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_BuyAndUseStoreItem_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_BuyAndUseStoreItem_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 成功则返回剩余可购买数目
     * </pre>
     *
     * <code>optional int32 leftCanBuyNum = 1;</code>
     * @return Whether the leftCanBuyNum field is set.
     */
    boolean hasLeftCanBuyNum();
    /**
     * <pre>
     * 成功则返回剩余可购买数目
     * </pre>
     *
     * <code>optional int32 leftCanBuyNum = 1;</code>
     * @return The leftCanBuyNum.
     */
    int getLeftCanBuyNum();

    /**
     * <pre>
     * 使用结果参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
     * @return Whether the result field is set.
     */
    boolean hasResult();
    /**
     * <pre>
     * 使用结果参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
     * @return The result.
     */
    com.yorha.proto.PlayerPB.ItemUseResultPB getResult();
    /**
     * <pre>
     * 使用结果参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
     */
    com.yorha.proto.PlayerPB.ItemUseResultPBOrBuilder getResultOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_BuyAndUseStoreItem_S2C}
   */
  public static final class Player_BuyAndUseStoreItem_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_BuyAndUseStoreItem_S2C)
      Player_BuyAndUseStoreItem_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_BuyAndUseStoreItem_S2C.newBuilder() to construct.
    private Player_BuyAndUseStoreItem_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_BuyAndUseStoreItem_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_BuyAndUseStoreItem_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_BuyAndUseStoreItem_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              leftCanBuyNum_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.PlayerPB.ItemUseResultPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = result_.toBuilder();
              }
              result_ = input.readMessage(com.yorha.proto.PlayerPB.ItemUseResultPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(result_);
                result_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C.class, com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int LEFTCANBUYNUM_FIELD_NUMBER = 1;
    private int leftCanBuyNum_;
    /**
     * <pre>
     * 成功则返回剩余可购买数目
     * </pre>
     *
     * <code>optional int32 leftCanBuyNum = 1;</code>
     * @return Whether the leftCanBuyNum field is set.
     */
    @java.lang.Override
    public boolean hasLeftCanBuyNum() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 成功则返回剩余可购买数目
     * </pre>
     *
     * <code>optional int32 leftCanBuyNum = 1;</code>
     * @return The leftCanBuyNum.
     */
    @java.lang.Override
    public int getLeftCanBuyNum() {
      return leftCanBuyNum_;
    }

    public static final int RESULT_FIELD_NUMBER = 2;
    private com.yorha.proto.PlayerPB.ItemUseResultPB result_;
    /**
     * <pre>
     * 使用结果参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 使用结果参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
     * @return The result.
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPB.ItemUseResultPB getResult() {
      return result_ == null ? com.yorha.proto.PlayerPB.ItemUseResultPB.getDefaultInstance() : result_;
    }
    /**
     * <pre>
     * 使用结果参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPB.ItemUseResultPBOrBuilder getResultOrBuilder() {
      return result_ == null ? com.yorha.proto.PlayerPB.ItemUseResultPB.getDefaultInstance() : result_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, leftCanBuyNum_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getResult());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, leftCanBuyNum_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getResult());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C other = (com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C) obj;

      if (hasLeftCanBuyNum() != other.hasLeftCanBuyNum()) return false;
      if (hasLeftCanBuyNum()) {
        if (getLeftCanBuyNum()
            != other.getLeftCanBuyNum()) return false;
      }
      if (hasResult() != other.hasResult()) return false;
      if (hasResult()) {
        if (!getResult()
            .equals(other.getResult())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLeftCanBuyNum()) {
        hash = (37 * hash) + LEFTCANBUYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getLeftCanBuyNum();
      }
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_BuyAndUseStoreItem_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_BuyAndUseStoreItem_S2C)
        com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C.class, com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getResultFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        leftCanBuyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (resultBuilder_ == null) {
          result_ = null;
        } else {
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerStore.internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C build() {
        com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C buildPartial() {
        com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C result = new com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.leftCanBuyNum_ = leftCanBuyNum_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (resultBuilder_ == null) {
            result.result_ = result_;
          } else {
            result.result_ = resultBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C) {
          return mergeFrom((com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C other) {
        if (other == com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C.getDefaultInstance()) return this;
        if (other.hasLeftCanBuyNum()) {
          setLeftCanBuyNum(other.getLeftCanBuyNum());
        }
        if (other.hasResult()) {
          mergeResult(other.getResult());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int leftCanBuyNum_ ;
      /**
       * <pre>
       * 成功则返回剩余可购买数目
       * </pre>
       *
       * <code>optional int32 leftCanBuyNum = 1;</code>
       * @return Whether the leftCanBuyNum field is set.
       */
      @java.lang.Override
      public boolean hasLeftCanBuyNum() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 成功则返回剩余可购买数目
       * </pre>
       *
       * <code>optional int32 leftCanBuyNum = 1;</code>
       * @return The leftCanBuyNum.
       */
      @java.lang.Override
      public int getLeftCanBuyNum() {
        return leftCanBuyNum_;
      }
      /**
       * <pre>
       * 成功则返回剩余可购买数目
       * </pre>
       *
       * <code>optional int32 leftCanBuyNum = 1;</code>
       * @param value The leftCanBuyNum to set.
       * @return This builder for chaining.
       */
      public Builder setLeftCanBuyNum(int value) {
        bitField0_ |= 0x00000001;
        leftCanBuyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成功则返回剩余可购买数目
       * </pre>
       *
       * <code>optional int32 leftCanBuyNum = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLeftCanBuyNum() {
        bitField0_ = (bitField0_ & ~0x00000001);
        leftCanBuyNum_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.PlayerPB.ItemUseResultPB result_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPB.ItemUseResultPB, com.yorha.proto.PlayerPB.ItemUseResultPB.Builder, com.yorha.proto.PlayerPB.ItemUseResultPBOrBuilder> resultBuilder_;
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       * @return Whether the result field is set.
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       * @return The result.
       */
      public com.yorha.proto.PlayerPB.ItemUseResultPB getResult() {
        if (resultBuilder_ == null) {
          return result_ == null ? com.yorha.proto.PlayerPB.ItemUseResultPB.getDefaultInstance() : result_;
        } else {
          return resultBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       */
      public Builder setResult(com.yorha.proto.PlayerPB.ItemUseResultPB value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          result_ = value;
          onChanged();
        } else {
          resultBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       */
      public Builder setResult(
          com.yorha.proto.PlayerPB.ItemUseResultPB.Builder builderForValue) {
        if (resultBuilder_ == null) {
          result_ = builderForValue.build();
          onChanged();
        } else {
          resultBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       */
      public Builder mergeResult(com.yorha.proto.PlayerPB.ItemUseResultPB value) {
        if (resultBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              result_ != null &&
              result_ != com.yorha.proto.PlayerPB.ItemUseResultPB.getDefaultInstance()) {
            result_ =
              com.yorha.proto.PlayerPB.ItemUseResultPB.newBuilder(result_).mergeFrom(value).buildPartial();
          } else {
            result_ = value;
          }
          onChanged();
        } else {
          resultBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = null;
          onChanged();
        } else {
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       */
      public com.yorha.proto.PlayerPB.ItemUseResultPB.Builder getResultBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getResultFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       */
      public com.yorha.proto.PlayerPB.ItemUseResultPBOrBuilder getResultOrBuilder() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilder();
        } else {
          return result_ == null ?
              com.yorha.proto.PlayerPB.ItemUseResultPB.getDefaultInstance() : result_;
        }
      }
      /**
       * <pre>
       * 使用结果参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemUseResultPB result = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPB.ItemUseResultPB, com.yorha.proto.PlayerPB.ItemUseResultPB.Builder, com.yorha.proto.PlayerPB.ItemUseResultPBOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.PlayerPB.ItemUseResultPB, com.yorha.proto.PlayerPB.ItemUseResultPB.Builder, com.yorha.proto.PlayerPB.ItemUseResultPBOrBuilder>(
                  getResult(),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_BuyAndUseStoreItem_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_BuyAndUseStoreItem_S2C)
    private static final com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C();
    }

    public static com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_BuyAndUseStoreItem_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_BuyAndUseStoreItem_S2C>() {
      @java.lang.Override
      public Player_BuyAndUseStoreItem_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_BuyAndUseStoreItem_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_BuyAndUseStoreItem_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_BuyAndUseStoreItem_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerStore.Player_BuyAndUseStoreItem_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)ss_proto/gen/player/cs/player_store.pr" +
      "oto\022\017com.yorha.proto\032\"cs_proto/gen/playe" +
      "r/playerPB.proto\032%ss_proto/gen/common/co" +
      "mmon_enum.proto\032$ss_proto/gen/common/str" +
      "uct_msg.proto\"\'\n%Player_FetchCommissaria" +
      "tStoreInfo_C2S\"Q\n%Player_FetchCommissari" +
      "atStoreInfo_S2C\022(\n\004info\030\001 \003(\0132\032.com.yorh" +
      "a.proto.StoreInfo\"\202\001\n\027Player_BuyStoreIte" +
      "m_C2S\022-\n\tstoreType\030\001 \001(\0162\032.com.yorha.pro" +
      "to.StoreType\022\025\n\rconfigGoodsId\030\002 \001(\003\022\016\n\006b" +
      "uyNum\030\003 \001(\005\022\021\n\tsPassWord\030\004 \001(\t\"0\n\027Player" +
      "_BuyStoreItem_S2C\022\025\n\rleftCanBuyNum\030\001 \001(\005" +
      "\"\247\001\n\035Player_BuyAndUseStoreItem_C2S\022-\n\tst" +
      "oreType\030\001 \001(\0162\032.com.yorha.proto.StoreTyp" +
      "e\022\025\n\rconfigGoodsId\030\002 \001(\003\022\016\n\006buyNum\030\003 \001(\005" +
      "\0220\n\006params\030\004 \001(\0132 .com.yorha.proto.ItemU" +
      "seParamsPB\"h\n\035Player_BuyAndUseStoreItem_" +
      "S2C\022\025\n\rleftCanBuyNum\030\001 \001(\005\0220\n\006result\030\002 \001" +
      "(\0132 .com.yorha.proto.ItemUseResultPBB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.PlayerPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchCommissariatStoreInfo_S2C_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_BuyStoreItem_C2S_descriptor,
        new java.lang.String[] { "StoreType", "ConfigGoodsId", "BuyNum", "SPassWord", });
    internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_BuyStoreItem_S2C_descriptor,
        new java.lang.String[] { "LeftCanBuyNum", });
    internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_C2S_descriptor,
        new java.lang.String[] { "StoreType", "ConfigGoodsId", "BuyNum", "Params", });
    internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_BuyAndUseStoreItem_S2C_descriptor,
        new java.lang.String[] { "LeftCanBuyNum", "Result", });
    com.yorha.proto.PlayerPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

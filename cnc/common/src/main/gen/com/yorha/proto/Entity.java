// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/entity/entity.proto

package com.yorha.proto;

public final class Entity {
  private Entity() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface EntityNtfMsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.EntityNtfMsg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> 
        getNewEntitiesList();
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    com.yorha.proto.EntityAttrOuterClass.EntityAttr getNewEntities(int index);
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    int getNewEntitiesCount();
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> 
        getNewEntitiesOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder getNewEntitiesOrBuilder(
        int index);

    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @return A list containing the delEntities.
     */
    java.util.List<java.lang.Long> getDelEntitiesList();
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @return The count of delEntities.
     */
    int getDelEntitiesCount();
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @param index The index of the element to return.
     * @return The delEntities at the given index.
     */
    long getDelEntities(int index);

    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> 
        getModEntitiesList();
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    com.yorha.proto.EntityAttrOuterClass.EntityAttr getModEntities(int index);
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    int getModEntitiesCount();
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    java.util.List<? extends com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> 
        getModEntitiesOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder getModEntitiesOrBuilder(
        int index);

    /**
     * <code>optional .com.yorha.proto.SceneObjectNtfReason reason = 4;</code>
     * @return Whether the reason field is set.
     */
    boolean hasReason();
    /**
     * <code>optional .com.yorha.proto.SceneObjectNtfReason reason = 4;</code>
     * @return The reason.
     */
    com.yorha.proto.CommonEnum.SceneObjectNtfReason getReason();

    /**
     * <code>optional int32 zoneId = 5;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <code>optional int32 zoneId = 5;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.EntityNtfMsg}
   */
  public static final class EntityNtfMsg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.EntityNtfMsg)
      EntityNtfMsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EntityNtfMsg.newBuilder() to construct.
    private EntityNtfMsg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EntityNtfMsg() {
      newEntities_ = java.util.Collections.emptyList();
      delEntities_ = emptyLongList();
      modEntities_ = java.util.Collections.emptyList();
      reason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EntityNtfMsg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private EntityNtfMsg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                newEntities_ = new java.util.ArrayList<com.yorha.proto.EntityAttrOuterClass.EntityAttr>();
                mutable_bitField0_ |= 0x00000001;
              }
              newEntities_.add(
                  input.readMessage(com.yorha.proto.EntityAttrOuterClass.EntityAttr.PARSER, extensionRegistry));
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                delEntities_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              delEntities_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                delEntities_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                delEntities_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                modEntities_ = new java.util.ArrayList<com.yorha.proto.EntityAttrOuterClass.EntityAttr>();
                mutable_bitField0_ |= 0x00000004;
              }
              modEntities_.add(
                  input.readMessage(com.yorha.proto.EntityAttrOuterClass.EntityAttr.PARSER, extensionRegistry));
              break;
            }
            case 32: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SceneObjectNtfReason value = com.yorha.proto.CommonEnum.SceneObjectNtfReason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(4, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                reason_ = rawValue;
              }
              break;
            }
            case 40: {
              bitField0_ |= 0x00000002;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          newEntities_ = java.util.Collections.unmodifiableList(newEntities_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          delEntities_.makeImmutable(); // C
        }
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          modEntities_ = java.util.Collections.unmodifiableList(modEntities_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Entity.internal_static_com_yorha_proto_EntityNtfMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Entity.internal_static_com_yorha_proto_EntityNtfMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Entity.EntityNtfMsg.class, com.yorha.proto.Entity.EntityNtfMsg.Builder.class);
    }

    private int bitField0_;
    public static final int NEWENTITIES_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> newEntities_;
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> getNewEntitiesList() {
      return newEntities_;
    }
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> 
        getNewEntitiesOrBuilderList() {
      return newEntities_;
    }
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public int getNewEntitiesCount() {
      return newEntities_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.EntityAttrOuterClass.EntityAttr getNewEntities(int index) {
      return newEntities_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder getNewEntitiesOrBuilder(
        int index) {
      return newEntities_.get(index);
    }

    public static final int DELENTITIES_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList delEntities_;
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @return A list containing the delEntities.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDelEntitiesList() {
      return delEntities_;
    }
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @return The count of delEntities.
     */
    public int getDelEntitiesCount() {
      return delEntities_.size();
    }
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @param index The index of the element to return.
     * @return The delEntities at the given index.
     */
    public long getDelEntities(int index) {
      return delEntities_.getLong(index);
    }

    public static final int MODENTITIES_FIELD_NUMBER = 3;
    private java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> modEntities_;
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> getModEntitiesList() {
      return modEntities_;
    }
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> 
        getModEntitiesOrBuilderList() {
      return modEntities_;
    }
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    @java.lang.Override
    public int getModEntitiesCount() {
      return modEntities_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.EntityAttrOuterClass.EntityAttr getModEntities(int index) {
      return modEntities_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder getModEntitiesOrBuilder(
        int index) {
      return modEntities_.get(index);
    }

    public static final int REASON_FIELD_NUMBER = 4;
    private int reason_;
    /**
     * <code>optional .com.yorha.proto.SceneObjectNtfReason reason = 4;</code>
     * @return Whether the reason field is set.
     */
    @java.lang.Override public boolean hasReason() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SceneObjectNtfReason reason = 4;</code>
     * @return The reason.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SceneObjectNtfReason getReason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SceneObjectNtfReason result = com.yorha.proto.CommonEnum.SceneObjectNtfReason.valueOf(reason_);
      return result == null ? com.yorha.proto.CommonEnum.SceneObjectNtfReason.SONR_NONE : result;
    }

    public static final int ZONEID_FIELD_NUMBER = 5;
    private int zoneId_;
    /**
     * <code>optional int32 zoneId = 5;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 zoneId = 5;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < newEntities_.size(); i++) {
        output.writeMessage(1, newEntities_.get(i));
      }
      for (int i = 0; i < delEntities_.size(); i++) {
        output.writeInt64(2, delEntities_.getLong(i));
      }
      for (int i = 0; i < modEntities_.size(); i++) {
        output.writeMessage(3, modEntities_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(4, reason_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(5, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < newEntities_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, newEntities_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < delEntities_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(delEntities_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDelEntitiesList().size();
      }
      for (int i = 0; i < modEntities_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, modEntities_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, reason_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Entity.EntityNtfMsg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Entity.EntityNtfMsg other = (com.yorha.proto.Entity.EntityNtfMsg) obj;

      if (!getNewEntitiesList()
          .equals(other.getNewEntitiesList())) return false;
      if (!getDelEntitiesList()
          .equals(other.getDelEntitiesList())) return false;
      if (!getModEntitiesList()
          .equals(other.getModEntitiesList())) return false;
      if (hasReason() != other.hasReason()) return false;
      if (hasReason()) {
        if (reason_ != other.reason_) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getNewEntitiesCount() > 0) {
        hash = (37 * hash) + NEWENTITIES_FIELD_NUMBER;
        hash = (53 * hash) + getNewEntitiesList().hashCode();
      }
      if (getDelEntitiesCount() > 0) {
        hash = (37 * hash) + DELENTITIES_FIELD_NUMBER;
        hash = (53 * hash) + getDelEntitiesList().hashCode();
      }
      if (getModEntitiesCount() > 0) {
        hash = (37 * hash) + MODENTITIES_FIELD_NUMBER;
        hash = (53 * hash) + getModEntitiesList().hashCode();
      }
      if (hasReason()) {
        hash = (37 * hash) + REASON_FIELD_NUMBER;
        hash = (53 * hash) + reason_;
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.EntityNtfMsg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Entity.EntityNtfMsg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.EntityNtfMsg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.EntityNtfMsg)
        com.yorha.proto.Entity.EntityNtfMsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_EntityNtfMsg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_EntityNtfMsg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Entity.EntityNtfMsg.class, com.yorha.proto.Entity.EntityNtfMsg.Builder.class);
      }

      // Construct using com.yorha.proto.Entity.EntityNtfMsg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getNewEntitiesFieldBuilder();
          getModEntitiesFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (newEntitiesBuilder_ == null) {
          newEntities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          newEntitiesBuilder_.clear();
        }
        delEntities_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        if (modEntitiesBuilder_ == null) {
          modEntities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          modEntitiesBuilder_.clear();
        }
        reason_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_EntityNtfMsg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Entity.EntityNtfMsg getDefaultInstanceForType() {
        return com.yorha.proto.Entity.EntityNtfMsg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Entity.EntityNtfMsg build() {
        com.yorha.proto.Entity.EntityNtfMsg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Entity.EntityNtfMsg buildPartial() {
        com.yorha.proto.Entity.EntityNtfMsg result = new com.yorha.proto.Entity.EntityNtfMsg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (newEntitiesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            newEntities_ = java.util.Collections.unmodifiableList(newEntities_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.newEntities_ = newEntities_;
        } else {
          result.newEntities_ = newEntitiesBuilder_.build();
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          delEntities_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.delEntities_ = delEntities_;
        if (modEntitiesBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            modEntities_ = java.util.Collections.unmodifiableList(modEntities_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.modEntities_ = modEntities_;
        } else {
          result.modEntities_ = modEntitiesBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.reason_ = reason_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Entity.EntityNtfMsg) {
          return mergeFrom((com.yorha.proto.Entity.EntityNtfMsg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Entity.EntityNtfMsg other) {
        if (other == com.yorha.proto.Entity.EntityNtfMsg.getDefaultInstance()) return this;
        if (newEntitiesBuilder_ == null) {
          if (!other.newEntities_.isEmpty()) {
            if (newEntities_.isEmpty()) {
              newEntities_ = other.newEntities_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureNewEntitiesIsMutable();
              newEntities_.addAll(other.newEntities_);
            }
            onChanged();
          }
        } else {
          if (!other.newEntities_.isEmpty()) {
            if (newEntitiesBuilder_.isEmpty()) {
              newEntitiesBuilder_.dispose();
              newEntitiesBuilder_ = null;
              newEntities_ = other.newEntities_;
              bitField0_ = (bitField0_ & ~0x00000001);
              newEntitiesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getNewEntitiesFieldBuilder() : null;
            } else {
              newEntitiesBuilder_.addAllMessages(other.newEntities_);
            }
          }
        }
        if (!other.delEntities_.isEmpty()) {
          if (delEntities_.isEmpty()) {
            delEntities_ = other.delEntities_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDelEntitiesIsMutable();
            delEntities_.addAll(other.delEntities_);
          }
          onChanged();
        }
        if (modEntitiesBuilder_ == null) {
          if (!other.modEntities_.isEmpty()) {
            if (modEntities_.isEmpty()) {
              modEntities_ = other.modEntities_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureModEntitiesIsMutable();
              modEntities_.addAll(other.modEntities_);
            }
            onChanged();
          }
        } else {
          if (!other.modEntities_.isEmpty()) {
            if (modEntitiesBuilder_.isEmpty()) {
              modEntitiesBuilder_.dispose();
              modEntitiesBuilder_ = null;
              modEntities_ = other.modEntities_;
              bitField0_ = (bitField0_ & ~0x00000004);
              modEntitiesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getModEntitiesFieldBuilder() : null;
            } else {
              modEntitiesBuilder_.addAllMessages(other.modEntities_);
            }
          }
        }
        if (other.hasReason()) {
          setReason(other.getReason());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Entity.EntityNtfMsg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Entity.EntityNtfMsg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> newEntities_ =
        java.util.Collections.emptyList();
      private void ensureNewEntitiesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          newEntities_ = new java.util.ArrayList<com.yorha.proto.EntityAttrOuterClass.EntityAttr>(newEntities_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.EntityAttrOuterClass.EntityAttr, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder, com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> newEntitiesBuilder_;

      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> getNewEntitiesList() {
        if (newEntitiesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(newEntities_);
        } else {
          return newEntitiesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public int getNewEntitiesCount() {
        if (newEntitiesBuilder_ == null) {
          return newEntities_.size();
        } else {
          return newEntitiesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr getNewEntities(int index) {
        if (newEntitiesBuilder_ == null) {
          return newEntities_.get(index);
        } else {
          return newEntitiesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder setNewEntities(
          int index, com.yorha.proto.EntityAttrOuterClass.EntityAttr value) {
        if (newEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewEntitiesIsMutable();
          newEntities_.set(index, value);
          onChanged();
        } else {
          newEntitiesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder setNewEntities(
          int index, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder builderForValue) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          newEntities_.set(index, builderForValue.build());
          onChanged();
        } else {
          newEntitiesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder addNewEntities(com.yorha.proto.EntityAttrOuterClass.EntityAttr value) {
        if (newEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewEntitiesIsMutable();
          newEntities_.add(value);
          onChanged();
        } else {
          newEntitiesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder addNewEntities(
          int index, com.yorha.proto.EntityAttrOuterClass.EntityAttr value) {
        if (newEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewEntitiesIsMutable();
          newEntities_.add(index, value);
          onChanged();
        } else {
          newEntitiesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder addNewEntities(
          com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder builderForValue) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          newEntities_.add(builderForValue.build());
          onChanged();
        } else {
          newEntitiesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder addNewEntities(
          int index, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder builderForValue) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          newEntities_.add(index, builderForValue.build());
          onChanged();
        } else {
          newEntitiesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder addAllNewEntities(
          java.lang.Iterable<? extends com.yorha.proto.EntityAttrOuterClass.EntityAttr> values) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, newEntities_);
          onChanged();
        } else {
          newEntitiesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder clearNewEntities() {
        if (newEntitiesBuilder_ == null) {
          newEntities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          newEntitiesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public Builder removeNewEntities(int index) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          newEntities_.remove(index);
          onChanged();
        } else {
          newEntitiesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder getNewEntitiesBuilder(
          int index) {
        return getNewEntitiesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder getNewEntitiesOrBuilder(
          int index) {
        if (newEntitiesBuilder_ == null) {
          return newEntities_.get(index);  } else {
          return newEntitiesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> 
           getNewEntitiesOrBuilderList() {
        if (newEntitiesBuilder_ != null) {
          return newEntitiesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(newEntities_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder addNewEntitiesBuilder() {
        return getNewEntitiesFieldBuilder().addBuilder(
            com.yorha.proto.EntityAttrOuterClass.EntityAttr.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder addNewEntitiesBuilder(
          int index) {
        return getNewEntitiesFieldBuilder().addBuilder(
            index, com.yorha.proto.EntityAttrOuterClass.EntityAttr.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr newEntities = 1;</code>
       */
      public java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder> 
           getNewEntitiesBuilderList() {
        return getNewEntitiesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.EntityAttrOuterClass.EntityAttr, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder, com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> 
          getNewEntitiesFieldBuilder() {
        if (newEntitiesBuilder_ == null) {
          newEntitiesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.EntityAttrOuterClass.EntityAttr, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder, com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder>(
                  newEntities_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          newEntities_ = null;
        }
        return newEntitiesBuilder_;
      }

      private com.google.protobuf.Internal.LongList delEntities_ = emptyLongList();
      private void ensureDelEntitiesIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          delEntities_ = mutableCopy(delEntities_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @return A list containing the delEntities.
       */
      public java.util.List<java.lang.Long>
          getDelEntitiesList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(delEntities_) : delEntities_;
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @return The count of delEntities.
       */
      public int getDelEntitiesCount() {
        return delEntities_.size();
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @param index The index of the element to return.
       * @return The delEntities at the given index.
       */
      public long getDelEntities(int index) {
        return delEntities_.getLong(index);
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @param index The index to set the value at.
       * @param value The delEntities to set.
       * @return This builder for chaining.
       */
      public Builder setDelEntities(
          int index, long value) {
        ensureDelEntitiesIsMutable();
        delEntities_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @param value The delEntities to add.
       * @return This builder for chaining.
       */
      public Builder addDelEntities(long value) {
        ensureDelEntitiesIsMutable();
        delEntities_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @param values The delEntities to add.
       * @return This builder for chaining.
       */
      public Builder addAllDelEntities(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDelEntitiesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, delEntities_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDelEntities() {
        delEntities_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> modEntities_ =
        java.util.Collections.emptyList();
      private void ensureModEntitiesIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          modEntities_ = new java.util.ArrayList<com.yorha.proto.EntityAttrOuterClass.EntityAttr>(modEntities_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.EntityAttrOuterClass.EntityAttr, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder, com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> modEntitiesBuilder_;

      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr> getModEntitiesList() {
        if (modEntitiesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(modEntities_);
        } else {
          return modEntitiesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public int getModEntitiesCount() {
        if (modEntitiesBuilder_ == null) {
          return modEntities_.size();
        } else {
          return modEntitiesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr getModEntities(int index) {
        if (modEntitiesBuilder_ == null) {
          return modEntities_.get(index);
        } else {
          return modEntitiesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder setModEntities(
          int index, com.yorha.proto.EntityAttrOuterClass.EntityAttr value) {
        if (modEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModEntitiesIsMutable();
          modEntities_.set(index, value);
          onChanged();
        } else {
          modEntitiesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder setModEntities(
          int index, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder builderForValue) {
        if (modEntitiesBuilder_ == null) {
          ensureModEntitiesIsMutable();
          modEntities_.set(index, builderForValue.build());
          onChanged();
        } else {
          modEntitiesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder addModEntities(com.yorha.proto.EntityAttrOuterClass.EntityAttr value) {
        if (modEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModEntitiesIsMutable();
          modEntities_.add(value);
          onChanged();
        } else {
          modEntitiesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder addModEntities(
          int index, com.yorha.proto.EntityAttrOuterClass.EntityAttr value) {
        if (modEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureModEntitiesIsMutable();
          modEntities_.add(index, value);
          onChanged();
        } else {
          modEntitiesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder addModEntities(
          com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder builderForValue) {
        if (modEntitiesBuilder_ == null) {
          ensureModEntitiesIsMutable();
          modEntities_.add(builderForValue.build());
          onChanged();
        } else {
          modEntitiesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder addModEntities(
          int index, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder builderForValue) {
        if (modEntitiesBuilder_ == null) {
          ensureModEntitiesIsMutable();
          modEntities_.add(index, builderForValue.build());
          onChanged();
        } else {
          modEntitiesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder addAllModEntities(
          java.lang.Iterable<? extends com.yorha.proto.EntityAttrOuterClass.EntityAttr> values) {
        if (modEntitiesBuilder_ == null) {
          ensureModEntitiesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, modEntities_);
          onChanged();
        } else {
          modEntitiesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder clearModEntities() {
        if (modEntitiesBuilder_ == null) {
          modEntities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          modEntitiesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public Builder removeModEntities(int index) {
        if (modEntitiesBuilder_ == null) {
          ensureModEntitiesIsMutable();
          modEntities_.remove(index);
          onChanged();
        } else {
          modEntitiesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder getModEntitiesBuilder(
          int index) {
        return getModEntitiesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder getModEntitiesOrBuilder(
          int index) {
        if (modEntitiesBuilder_ == null) {
          return modEntities_.get(index);  } else {
          return modEntitiesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public java.util.List<? extends com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> 
           getModEntitiesOrBuilderList() {
        if (modEntitiesBuilder_ != null) {
          return modEntitiesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(modEntities_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder addModEntitiesBuilder() {
        return getModEntitiesFieldBuilder().addBuilder(
            com.yorha.proto.EntityAttrOuterClass.EntityAttr.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder addModEntitiesBuilder(
          int index) {
        return getModEntitiesFieldBuilder().addBuilder(
            index, com.yorha.proto.EntityAttrOuterClass.EntityAttr.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.EntityAttr modEntities = 3;</code>
       */
      public java.util.List<com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder> 
           getModEntitiesBuilderList() {
        return getModEntitiesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.EntityAttrOuterClass.EntityAttr, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder, com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder> 
          getModEntitiesFieldBuilder() {
        if (modEntitiesBuilder_ == null) {
          modEntitiesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.EntityAttrOuterClass.EntityAttr, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder, com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder>(
                  modEntities_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          modEntities_ = null;
        }
        return modEntitiesBuilder_;
      }

      private int reason_ = 0;
      /**
       * <code>optional .com.yorha.proto.SceneObjectNtfReason reason = 4;</code>
       * @return Whether the reason field is set.
       */
      @java.lang.Override public boolean hasReason() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SceneObjectNtfReason reason = 4;</code>
       * @return The reason.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SceneObjectNtfReason getReason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SceneObjectNtfReason result = com.yorha.proto.CommonEnum.SceneObjectNtfReason.valueOf(reason_);
        return result == null ? com.yorha.proto.CommonEnum.SceneObjectNtfReason.SONR_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.SceneObjectNtfReason reason = 4;</code>
       * @param value The reason to set.
       * @return This builder for chaining.
       */
      public Builder setReason(com.yorha.proto.CommonEnum.SceneObjectNtfReason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        reason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SceneObjectNtfReason reason = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearReason() {
        bitField0_ = (bitField0_ & ~0x00000008);
        reason_ = 0;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <code>optional int32 zoneId = 5;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 zoneId = 5;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <code>optional int32 zoneId = 5;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000010;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.EntityNtfMsg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.EntityNtfMsg)
    private static final com.yorha.proto.Entity.EntityNtfMsg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Entity.EntityNtfMsg();
    }

    public static com.yorha.proto.Entity.EntityNtfMsg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<EntityNtfMsg>
        PARSER = new com.google.protobuf.AbstractParser<EntityNtfMsg>() {
      @java.lang.Override
      public EntityNtfMsg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EntityNtfMsg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<EntityNtfMsg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EntityNtfMsg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Entity.EntityNtfMsg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SceneObjBriefAttrOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SceneObjBriefAttr)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return Whether the entityType field is set.
     */
    boolean hasEntityType();
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return The entityType.
     */
    com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType();

    /**
     * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
     * @return Whether the mapBuildingAttr field is set.
     */
    boolean hasMapBuildingAttr();
    /**
     * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
     * @return The mapBuildingAttr.
     */
    com.yorha.proto.StructPB.BriefMapBuildingPB getMapBuildingAttr();
    /**
     * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
     */
    com.yorha.proto.StructPB.BriefMapBuildingPBOrBuilder getMapBuildingAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
     * @return Whether the dropObjAttr field is set.
     */
    boolean hasDropObjAttr();
    /**
     * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
     * @return The dropObjAttr.
     */
    com.yorha.proto.StructPB.BriefDropObjPB getDropObjAttr();
    /**
     * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
     */
    com.yorha.proto.StructPB.BriefDropObjPBOrBuilder getDropObjAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
     * @return Whether the monsterAttr field is set.
     */
    boolean hasMonsterAttr();
    /**
     * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
     * @return The monsterAttr.
     */
    com.yorha.proto.StructPB.BriefMonsterPB getMonsterAttr();
    /**
     * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
     */
    com.yorha.proto.StructPB.BriefMonsterPBOrBuilder getMonsterAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
     * @return Whether the caveAttr field is set.
     */
    boolean hasCaveAttr();
    /**
     * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
     * @return The caveAttr.
     */
    com.yorha.proto.StructMsg.BriefCaveMsg getCaveAttr();
    /**
     * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
     */
    com.yorha.proto.StructMsg.BriefCaveMsgOrBuilder getCaveAttrOrBuilder();

    /**
     * <pre>
     * 简简简要数据  只有一个point的  可以的话尽量复用
     * </pre>
     *
     * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
     * @return Whether the pointAttr field is set.
     */
    boolean hasPointAttr();
    /**
     * <pre>
     * 简简简要数据  只有一个point的  可以的话尽量复用
     * </pre>
     *
     * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
     * @return The pointAttr.
     */
    com.yorha.proto.StructMsg.BriefPoint getPointAttr();
    /**
     * <pre>
     * 简简简要数据  只有一个point的  可以的话尽量复用
     * </pre>
     *
     * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
     */
    com.yorha.proto.StructMsg.BriefPointOrBuilder getPointAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
     * @return Whether the clanResAttr field is set.
     */
    boolean hasClanResAttr();
    /**
     * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
     * @return The clanResAttr.
     */
    com.yorha.proto.StructMsg.BriefClanRes getClanResAttr();
    /**
     * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
     */
    com.yorha.proto.StructMsg.BriefClanResOrBuilder getClanResAttrOrBuilder();

    public com.yorha.proto.Entity.SceneObjBriefAttr.EntityAttrCase getEntityAttrCase();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SceneObjBriefAttr}
   */
  public static final class SceneObjBriefAttr extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SceneObjBriefAttr)
      SceneObjBriefAttrOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SceneObjBriefAttr.newBuilder() to construct.
    private SceneObjBriefAttr(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SceneObjBriefAttr() {
      entityType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SceneObjBriefAttr();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SceneObjBriefAttr(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              entityId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.EntityAttrOuterClass.EntityType value = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                entityType_ = rawValue;
              }
              break;
            }
            case 90: {
              com.yorha.proto.StructPB.BriefMapBuildingPB.Builder subBuilder = null;
              if (entityAttrCase_ == 11) {
                subBuilder = ((com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.StructPB.BriefMapBuildingPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 11;
              break;
            }
            case 98: {
              com.yorha.proto.StructPB.BriefDropObjPB.Builder subBuilder = null;
              if (entityAttrCase_ == 12) {
                subBuilder = ((com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.StructPB.BriefDropObjPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 12;
              break;
            }
            case 106: {
              com.yorha.proto.StructPB.BriefMonsterPB.Builder subBuilder = null;
              if (entityAttrCase_ == 13) {
                subBuilder = ((com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.StructPB.BriefMonsterPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 13;
              break;
            }
            case 122: {
              com.yorha.proto.StructMsg.BriefCaveMsg.Builder subBuilder = null;
              if (entityAttrCase_ == 15) {
                subBuilder = ((com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.StructMsg.BriefCaveMsg.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 15;
              break;
            }
            case 130: {
              com.yorha.proto.StructMsg.BriefPoint.Builder subBuilder = null;
              if (entityAttrCase_ == 16) {
                subBuilder = ((com.yorha.proto.StructMsg.BriefPoint) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.StructMsg.BriefPoint.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.StructMsg.BriefPoint) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 16;
              break;
            }
            case 138: {
              com.yorha.proto.StructMsg.BriefClanRes.Builder subBuilder = null;
              if (entityAttrCase_ == 17) {
                subBuilder = ((com.yorha.proto.StructMsg.BriefClanRes) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.StructMsg.BriefClanRes.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.StructMsg.BriefClanRes) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 17;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefAttr_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefAttr_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Entity.SceneObjBriefAttr.class, com.yorha.proto.Entity.SceneObjBriefAttr.Builder.class);
    }

    private int bitField0_;
    private int entityAttrCase_ = 0;
    private java.lang.Object entityAttr_;
    public enum EntityAttrCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      MAPBUILDINGATTR(11),
      DROPOBJATTR(12),
      MONSTERATTR(13),
      CAVEATTR(15),
      POINTATTR(16),
      CLANRESATTR(17),
      ENTITYATTR_NOT_SET(0);
      private final int value;
      private EntityAttrCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static EntityAttrCase valueOf(int value) {
        return forNumber(value);
      }

      public static EntityAttrCase forNumber(int value) {
        switch (value) {
          case 11: return MAPBUILDINGATTR;
          case 12: return DROPOBJATTR;
          case 13: return MONSTERATTR;
          case 15: return CAVEATTR;
          case 16: return POINTATTR;
          case 17: return CLANRESATTR;
          case 0: return ENTITYATTR_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public EntityAttrCase
    getEntityAttrCase() {
      return EntityAttrCase.forNumber(
          entityAttrCase_);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int ENTITYTYPE_FIELD_NUMBER = 2;
    private int entityType_;
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return Whether the entityType field is set.
     */
    @java.lang.Override public boolean hasEntityType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return The entityType.
     */
    @java.lang.Override public com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.EntityAttrOuterClass.EntityType result = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(entityType_);
      return result == null ? com.yorha.proto.EntityAttrOuterClass.EntityType.ET_Unknown : result;
    }

    public static final int MAPBUILDINGATTR_FIELD_NUMBER = 11;
    /**
     * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
     * @return Whether the mapBuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasMapBuildingAttr() {
      return entityAttrCase_ == 11;
    }
    /**
     * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
     * @return The mapBuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.BriefMapBuildingPB getMapBuildingAttr() {
      if (entityAttrCase_ == 11) {
         return (com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_;
      }
      return com.yorha.proto.StructPB.BriefMapBuildingPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.BriefMapBuildingPBOrBuilder getMapBuildingAttrOrBuilder() {
      if (entityAttrCase_ == 11) {
         return (com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_;
      }
      return com.yorha.proto.StructPB.BriefMapBuildingPB.getDefaultInstance();
    }

    public static final int DROPOBJATTR_FIELD_NUMBER = 12;
    /**
     * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
     * @return Whether the dropObjAttr field is set.
     */
    @java.lang.Override
    public boolean hasDropObjAttr() {
      return entityAttrCase_ == 12;
    }
    /**
     * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
     * @return The dropObjAttr.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.BriefDropObjPB getDropObjAttr() {
      if (entityAttrCase_ == 12) {
         return (com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_;
      }
      return com.yorha.proto.StructPB.BriefDropObjPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.BriefDropObjPBOrBuilder getDropObjAttrOrBuilder() {
      if (entityAttrCase_ == 12) {
         return (com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_;
      }
      return com.yorha.proto.StructPB.BriefDropObjPB.getDefaultInstance();
    }

    public static final int MONSTERATTR_FIELD_NUMBER = 13;
    /**
     * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
     * @return Whether the monsterAttr field is set.
     */
    @java.lang.Override
    public boolean hasMonsterAttr() {
      return entityAttrCase_ == 13;
    }
    /**
     * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
     * @return The monsterAttr.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.BriefMonsterPB getMonsterAttr() {
      if (entityAttrCase_ == 13) {
         return (com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_;
      }
      return com.yorha.proto.StructPB.BriefMonsterPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.BriefMonsterPBOrBuilder getMonsterAttrOrBuilder() {
      if (entityAttrCase_ == 13) {
         return (com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_;
      }
      return com.yorha.proto.StructPB.BriefMonsterPB.getDefaultInstance();
    }

    public static final int CAVEATTR_FIELD_NUMBER = 15;
    /**
     * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
     * @return Whether the caveAttr field is set.
     */
    @java.lang.Override
    public boolean hasCaveAttr() {
      return entityAttrCase_ == 15;
    }
    /**
     * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
     * @return The caveAttr.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.BriefCaveMsg getCaveAttr() {
      if (entityAttrCase_ == 15) {
         return (com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_;
      }
      return com.yorha.proto.StructMsg.BriefCaveMsg.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.BriefCaveMsgOrBuilder getCaveAttrOrBuilder() {
      if (entityAttrCase_ == 15) {
         return (com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_;
      }
      return com.yorha.proto.StructMsg.BriefCaveMsg.getDefaultInstance();
    }

    public static final int POINTATTR_FIELD_NUMBER = 16;
    /**
     * <pre>
     * 简简简要数据  只有一个point的  可以的话尽量复用
     * </pre>
     *
     * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
     * @return Whether the pointAttr field is set.
     */
    @java.lang.Override
    public boolean hasPointAttr() {
      return entityAttrCase_ == 16;
    }
    /**
     * <pre>
     * 简简简要数据  只有一个point的  可以的话尽量复用
     * </pre>
     *
     * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
     * @return The pointAttr.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.BriefPoint getPointAttr() {
      if (entityAttrCase_ == 16) {
         return (com.yorha.proto.StructMsg.BriefPoint) entityAttr_;
      }
      return com.yorha.proto.StructMsg.BriefPoint.getDefaultInstance();
    }
    /**
     * <pre>
     * 简简简要数据  只有一个point的  可以的话尽量复用
     * </pre>
     *
     * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.BriefPointOrBuilder getPointAttrOrBuilder() {
      if (entityAttrCase_ == 16) {
         return (com.yorha.proto.StructMsg.BriefPoint) entityAttr_;
      }
      return com.yorha.proto.StructMsg.BriefPoint.getDefaultInstance();
    }

    public static final int CLANRESATTR_FIELD_NUMBER = 17;
    /**
     * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
     * @return Whether the clanResAttr field is set.
     */
    @java.lang.Override
    public boolean hasClanResAttr() {
      return entityAttrCase_ == 17;
    }
    /**
     * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
     * @return The clanResAttr.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.BriefClanRes getClanResAttr() {
      if (entityAttrCase_ == 17) {
         return (com.yorha.proto.StructMsg.BriefClanRes) entityAttr_;
      }
      return com.yorha.proto.StructMsg.BriefClanRes.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.BriefClanResOrBuilder getClanResAttrOrBuilder() {
      if (entityAttrCase_ == 17) {
         return (com.yorha.proto.StructMsg.BriefClanRes) entityAttr_;
      }
      return com.yorha.proto.StructMsg.BriefClanRes.getDefaultInstance();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, entityType_);
      }
      if (entityAttrCase_ == 11) {
        output.writeMessage(11, (com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_);
      }
      if (entityAttrCase_ == 12) {
        output.writeMessage(12, (com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_);
      }
      if (entityAttrCase_ == 13) {
        output.writeMessage(13, (com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_);
      }
      if (entityAttrCase_ == 15) {
        output.writeMessage(15, (com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_);
      }
      if (entityAttrCase_ == 16) {
        output.writeMessage(16, (com.yorha.proto.StructMsg.BriefPoint) entityAttr_);
      }
      if (entityAttrCase_ == 17) {
        output.writeMessage(17, (com.yorha.proto.StructMsg.BriefClanRes) entityAttr_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, entityType_);
      }
      if (entityAttrCase_ == 11) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, (com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_);
      }
      if (entityAttrCase_ == 12) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, (com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_);
      }
      if (entityAttrCase_ == 13) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, (com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_);
      }
      if (entityAttrCase_ == 15) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, (com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_);
      }
      if (entityAttrCase_ == 16) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, (com.yorha.proto.StructMsg.BriefPoint) entityAttr_);
      }
      if (entityAttrCase_ == 17) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(17, (com.yorha.proto.StructMsg.BriefClanRes) entityAttr_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Entity.SceneObjBriefAttr)) {
        return super.equals(obj);
      }
      com.yorha.proto.Entity.SceneObjBriefAttr other = (com.yorha.proto.Entity.SceneObjBriefAttr) obj;

      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (hasEntityType() != other.hasEntityType()) return false;
      if (hasEntityType()) {
        if (entityType_ != other.entityType_) return false;
      }
      if (!getEntityAttrCase().equals(other.getEntityAttrCase())) return false;
      switch (entityAttrCase_) {
        case 11:
          if (!getMapBuildingAttr()
              .equals(other.getMapBuildingAttr())) return false;
          break;
        case 12:
          if (!getDropObjAttr()
              .equals(other.getDropObjAttr())) return false;
          break;
        case 13:
          if (!getMonsterAttr()
              .equals(other.getMonsterAttr())) return false;
          break;
        case 15:
          if (!getCaveAttr()
              .equals(other.getCaveAttr())) return false;
          break;
        case 16:
          if (!getPointAttr()
              .equals(other.getPointAttr())) return false;
          break;
        case 17:
          if (!getClanResAttr()
              .equals(other.getClanResAttr())) return false;
          break;
        case 0:
        default:
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      if (hasEntityType()) {
        hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
        hash = (53 * hash) + entityType_;
      }
      switch (entityAttrCase_) {
        case 11:
          hash = (37 * hash) + MAPBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getMapBuildingAttr().hashCode();
          break;
        case 12:
          hash = (37 * hash) + DROPOBJATTR_FIELD_NUMBER;
          hash = (53 * hash) + getDropObjAttr().hashCode();
          break;
        case 13:
          hash = (37 * hash) + MONSTERATTR_FIELD_NUMBER;
          hash = (53 * hash) + getMonsterAttr().hashCode();
          break;
        case 15:
          hash = (37 * hash) + CAVEATTR_FIELD_NUMBER;
          hash = (53 * hash) + getCaveAttr().hashCode();
          break;
        case 16:
          hash = (37 * hash) + POINTATTR_FIELD_NUMBER;
          hash = (53 * hash) + getPointAttr().hashCode();
          break;
        case 17:
          hash = (37 * hash) + CLANRESATTR_FIELD_NUMBER;
          hash = (53 * hash) + getClanResAttr().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.SceneObjBriefAttr parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Entity.SceneObjBriefAttr prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SceneObjBriefAttr}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SceneObjBriefAttr)
        com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefAttr_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefAttr_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Entity.SceneObjBriefAttr.class, com.yorha.proto.Entity.SceneObjBriefAttr.Builder.class);
      }

      // Construct using com.yorha.proto.Entity.SceneObjBriefAttr.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        entityType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        entityAttrCase_ = 0;
        entityAttr_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefAttr_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Entity.SceneObjBriefAttr getDefaultInstanceForType() {
        return com.yorha.proto.Entity.SceneObjBriefAttr.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Entity.SceneObjBriefAttr build() {
        com.yorha.proto.Entity.SceneObjBriefAttr result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Entity.SceneObjBriefAttr buildPartial() {
        com.yorha.proto.Entity.SceneObjBriefAttr result = new com.yorha.proto.Entity.SceneObjBriefAttr(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.entityType_ = entityType_;
        if (entityAttrCase_ == 11) {
          if (mapBuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = mapBuildingAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 12) {
          if (dropObjAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = dropObjAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 13) {
          if (monsterAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = monsterAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 15) {
          if (caveAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = caveAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 16) {
          if (pointAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = pointAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 17) {
          if (clanResAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = clanResAttrBuilder_.build();
          }
        }
        result.bitField0_ = to_bitField0_;
        result.entityAttrCase_ = entityAttrCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Entity.SceneObjBriefAttr) {
          return mergeFrom((com.yorha.proto.Entity.SceneObjBriefAttr)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Entity.SceneObjBriefAttr other) {
        if (other == com.yorha.proto.Entity.SceneObjBriefAttr.getDefaultInstance()) return this;
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        if (other.hasEntityType()) {
          setEntityType(other.getEntityType());
        }
        switch (other.getEntityAttrCase()) {
          case MAPBUILDINGATTR: {
            mergeMapBuildingAttr(other.getMapBuildingAttr());
            break;
          }
          case DROPOBJATTR: {
            mergeDropObjAttr(other.getDropObjAttr());
            break;
          }
          case MONSTERATTR: {
            mergeMonsterAttr(other.getMonsterAttr());
            break;
          }
          case CAVEATTR: {
            mergeCaveAttr(other.getCaveAttr());
            break;
          }
          case POINTATTR: {
            mergePointAttr(other.getPointAttr());
            break;
          }
          case CLANRESATTR: {
            mergeClanResAttr(other.getClanResAttr());
            break;
          }
          case ENTITYATTR_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Entity.SceneObjBriefAttr parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Entity.SceneObjBriefAttr) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int entityAttrCase_ = 0;
      private java.lang.Object entityAttr_;
      public EntityAttrCase
          getEntityAttrCase() {
        return EntityAttrCase.forNumber(
            entityAttrCase_);
      }

      public Builder clearEntityAttr() {
        entityAttrCase_ = 0;
        entityAttr_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private long entityId_ ;
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000001;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int entityType_ = 0;
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return Whether the entityType field is set.
       */
      @java.lang.Override public boolean hasEntityType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return The entityType.
       */
      @java.lang.Override
      public com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.EntityAttrOuterClass.EntityType result = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(entityType_);
        return result == null ? com.yorha.proto.EntityAttrOuterClass.EntityType.ET_Unknown : result;
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @param value The entityType to set.
       * @return This builder for chaining.
       */
      public Builder setEntityType(com.yorha.proto.EntityAttrOuterClass.EntityType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        entityType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        entityType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.BriefMapBuildingPB, com.yorha.proto.StructPB.BriefMapBuildingPB.Builder, com.yorha.proto.StructPB.BriefMapBuildingPBOrBuilder> mapBuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       * @return Whether the mapBuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasMapBuildingAttr() {
        return entityAttrCase_ == 11;
      }
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       * @return The mapBuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.StructPB.BriefMapBuildingPB getMapBuildingAttr() {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 11) {
            return (com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_;
          }
          return com.yorha.proto.StructPB.BriefMapBuildingPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 11) {
            return mapBuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.StructPB.BriefMapBuildingPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       */
      public Builder setMapBuildingAttr(com.yorha.proto.StructPB.BriefMapBuildingPB value) {
        if (mapBuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          mapBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       */
      public Builder setMapBuildingAttr(
          com.yorha.proto.StructPB.BriefMapBuildingPB.Builder builderForValue) {
        if (mapBuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          mapBuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       */
      public Builder mergeMapBuildingAttr(com.yorha.proto.StructPB.BriefMapBuildingPB value) {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 11 &&
              entityAttr_ != com.yorha.proto.StructPB.BriefMapBuildingPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.StructPB.BriefMapBuildingPB.newBuilder((com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 11) {
            mapBuildingAttrBuilder_.mergeFrom(value);
          }
          mapBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       */
      public Builder clearMapBuildingAttr() {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 11) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 11) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          mapBuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       */
      public com.yorha.proto.StructPB.BriefMapBuildingPB.Builder getMapBuildingAttrBuilder() {
        return getMapBuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       */
      @java.lang.Override
      public com.yorha.proto.StructPB.BriefMapBuildingPBOrBuilder getMapBuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 11) && (mapBuildingAttrBuilder_ != null)) {
          return mapBuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 11) {
            return (com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_;
          }
          return com.yorha.proto.StructPB.BriefMapBuildingPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefMapBuildingPB mapBuildingAttr = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.BriefMapBuildingPB, com.yorha.proto.StructPB.BriefMapBuildingPB.Builder, com.yorha.proto.StructPB.BriefMapBuildingPBOrBuilder> 
          getMapBuildingAttrFieldBuilder() {
        if (mapBuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 11)) {
            entityAttr_ = com.yorha.proto.StructPB.BriefMapBuildingPB.getDefaultInstance();
          }
          mapBuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.BriefMapBuildingPB, com.yorha.proto.StructPB.BriefMapBuildingPB.Builder, com.yorha.proto.StructPB.BriefMapBuildingPBOrBuilder>(
                  (com.yorha.proto.StructPB.BriefMapBuildingPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 11;
        onChanged();;
        return mapBuildingAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.BriefDropObjPB, com.yorha.proto.StructPB.BriefDropObjPB.Builder, com.yorha.proto.StructPB.BriefDropObjPBOrBuilder> dropObjAttrBuilder_;
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       * @return Whether the dropObjAttr field is set.
       */
      @java.lang.Override
      public boolean hasDropObjAttr() {
        return entityAttrCase_ == 12;
      }
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       * @return The dropObjAttr.
       */
      @java.lang.Override
      public com.yorha.proto.StructPB.BriefDropObjPB getDropObjAttr() {
        if (dropObjAttrBuilder_ == null) {
          if (entityAttrCase_ == 12) {
            return (com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_;
          }
          return com.yorha.proto.StructPB.BriefDropObjPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 12) {
            return dropObjAttrBuilder_.getMessage();
          }
          return com.yorha.proto.StructPB.BriefDropObjPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       */
      public Builder setDropObjAttr(com.yorha.proto.StructPB.BriefDropObjPB value) {
        if (dropObjAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          dropObjAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       */
      public Builder setDropObjAttr(
          com.yorha.proto.StructPB.BriefDropObjPB.Builder builderForValue) {
        if (dropObjAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          dropObjAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       */
      public Builder mergeDropObjAttr(com.yorha.proto.StructPB.BriefDropObjPB value) {
        if (dropObjAttrBuilder_ == null) {
          if (entityAttrCase_ == 12 &&
              entityAttr_ != com.yorha.proto.StructPB.BriefDropObjPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.StructPB.BriefDropObjPB.newBuilder((com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 12) {
            dropObjAttrBuilder_.mergeFrom(value);
          }
          dropObjAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       */
      public Builder clearDropObjAttr() {
        if (dropObjAttrBuilder_ == null) {
          if (entityAttrCase_ == 12) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 12) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          dropObjAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       */
      public com.yorha.proto.StructPB.BriefDropObjPB.Builder getDropObjAttrBuilder() {
        return getDropObjAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       */
      @java.lang.Override
      public com.yorha.proto.StructPB.BriefDropObjPBOrBuilder getDropObjAttrOrBuilder() {
        if ((entityAttrCase_ == 12) && (dropObjAttrBuilder_ != null)) {
          return dropObjAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 12) {
            return (com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_;
          }
          return com.yorha.proto.StructPB.BriefDropObjPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefDropObjPB dropObjAttr = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.BriefDropObjPB, com.yorha.proto.StructPB.BriefDropObjPB.Builder, com.yorha.proto.StructPB.BriefDropObjPBOrBuilder> 
          getDropObjAttrFieldBuilder() {
        if (dropObjAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 12)) {
            entityAttr_ = com.yorha.proto.StructPB.BriefDropObjPB.getDefaultInstance();
          }
          dropObjAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.BriefDropObjPB, com.yorha.proto.StructPB.BriefDropObjPB.Builder, com.yorha.proto.StructPB.BriefDropObjPBOrBuilder>(
                  (com.yorha.proto.StructPB.BriefDropObjPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 12;
        onChanged();;
        return dropObjAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.BriefMonsterPB, com.yorha.proto.StructPB.BriefMonsterPB.Builder, com.yorha.proto.StructPB.BriefMonsterPBOrBuilder> monsterAttrBuilder_;
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       * @return Whether the monsterAttr field is set.
       */
      @java.lang.Override
      public boolean hasMonsterAttr() {
        return entityAttrCase_ == 13;
      }
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       * @return The monsterAttr.
       */
      @java.lang.Override
      public com.yorha.proto.StructPB.BriefMonsterPB getMonsterAttr() {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 13) {
            return (com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_;
          }
          return com.yorha.proto.StructPB.BriefMonsterPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 13) {
            return monsterAttrBuilder_.getMessage();
          }
          return com.yorha.proto.StructPB.BriefMonsterPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       */
      public Builder setMonsterAttr(com.yorha.proto.StructPB.BriefMonsterPB value) {
        if (monsterAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          monsterAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       */
      public Builder setMonsterAttr(
          com.yorha.proto.StructPB.BriefMonsterPB.Builder builderForValue) {
        if (monsterAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          monsterAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       */
      public Builder mergeMonsterAttr(com.yorha.proto.StructPB.BriefMonsterPB value) {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 13 &&
              entityAttr_ != com.yorha.proto.StructPB.BriefMonsterPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.StructPB.BriefMonsterPB.newBuilder((com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 13) {
            monsterAttrBuilder_.mergeFrom(value);
          }
          monsterAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       */
      public Builder clearMonsterAttr() {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 13) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 13) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          monsterAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       */
      public com.yorha.proto.StructPB.BriefMonsterPB.Builder getMonsterAttrBuilder() {
        return getMonsterAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       */
      @java.lang.Override
      public com.yorha.proto.StructPB.BriefMonsterPBOrBuilder getMonsterAttrOrBuilder() {
        if ((entityAttrCase_ == 13) && (monsterAttrBuilder_ != null)) {
          return monsterAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 13) {
            return (com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_;
          }
          return com.yorha.proto.StructPB.BriefMonsterPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefMonsterPB monsterAttr = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.BriefMonsterPB, com.yorha.proto.StructPB.BriefMonsterPB.Builder, com.yorha.proto.StructPB.BriefMonsterPBOrBuilder> 
          getMonsterAttrFieldBuilder() {
        if (monsterAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 13)) {
            entityAttr_ = com.yorha.proto.StructPB.BriefMonsterPB.getDefaultInstance();
          }
          monsterAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.BriefMonsterPB, com.yorha.proto.StructPB.BriefMonsterPB.Builder, com.yorha.proto.StructPB.BriefMonsterPBOrBuilder>(
                  (com.yorha.proto.StructPB.BriefMonsterPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 13;
        onChanged();;
        return monsterAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.BriefCaveMsg, com.yorha.proto.StructMsg.BriefCaveMsg.Builder, com.yorha.proto.StructMsg.BriefCaveMsgOrBuilder> caveAttrBuilder_;
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       * @return Whether the caveAttr field is set.
       */
      @java.lang.Override
      public boolean hasCaveAttr() {
        return entityAttrCase_ == 15;
      }
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       * @return The caveAttr.
       */
      @java.lang.Override
      public com.yorha.proto.StructMsg.BriefCaveMsg getCaveAttr() {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 15) {
            return (com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_;
          }
          return com.yorha.proto.StructMsg.BriefCaveMsg.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 15) {
            return caveAttrBuilder_.getMessage();
          }
          return com.yorha.proto.StructMsg.BriefCaveMsg.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       */
      public Builder setCaveAttr(com.yorha.proto.StructMsg.BriefCaveMsg value) {
        if (caveAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          caveAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       */
      public Builder setCaveAttr(
          com.yorha.proto.StructMsg.BriefCaveMsg.Builder builderForValue) {
        if (caveAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          caveAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       */
      public Builder mergeCaveAttr(com.yorha.proto.StructMsg.BriefCaveMsg value) {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 15 &&
              entityAttr_ != com.yorha.proto.StructMsg.BriefCaveMsg.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.StructMsg.BriefCaveMsg.newBuilder((com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 15) {
            caveAttrBuilder_.mergeFrom(value);
          }
          caveAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       */
      public Builder clearCaveAttr() {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 15) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 15) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          caveAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       */
      public com.yorha.proto.StructMsg.BriefCaveMsg.Builder getCaveAttrBuilder() {
        return getCaveAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       */
      @java.lang.Override
      public com.yorha.proto.StructMsg.BriefCaveMsgOrBuilder getCaveAttrOrBuilder() {
        if ((entityAttrCase_ == 15) && (caveAttrBuilder_ != null)) {
          return caveAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 15) {
            return (com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_;
          }
          return com.yorha.proto.StructMsg.BriefCaveMsg.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefCaveMsg caveAttr = 15;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.BriefCaveMsg, com.yorha.proto.StructMsg.BriefCaveMsg.Builder, com.yorha.proto.StructMsg.BriefCaveMsgOrBuilder> 
          getCaveAttrFieldBuilder() {
        if (caveAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 15)) {
            entityAttr_ = com.yorha.proto.StructMsg.BriefCaveMsg.getDefaultInstance();
          }
          caveAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.BriefCaveMsg, com.yorha.proto.StructMsg.BriefCaveMsg.Builder, com.yorha.proto.StructMsg.BriefCaveMsgOrBuilder>(
                  (com.yorha.proto.StructMsg.BriefCaveMsg) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 15;
        onChanged();;
        return caveAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.BriefPoint, com.yorha.proto.StructMsg.BriefPoint.Builder, com.yorha.proto.StructMsg.BriefPointOrBuilder> pointAttrBuilder_;
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       * @return Whether the pointAttr field is set.
       */
      @java.lang.Override
      public boolean hasPointAttr() {
        return entityAttrCase_ == 16;
      }
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       * @return The pointAttr.
       */
      @java.lang.Override
      public com.yorha.proto.StructMsg.BriefPoint getPointAttr() {
        if (pointAttrBuilder_ == null) {
          if (entityAttrCase_ == 16) {
            return (com.yorha.proto.StructMsg.BriefPoint) entityAttr_;
          }
          return com.yorha.proto.StructMsg.BriefPoint.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 16) {
            return pointAttrBuilder_.getMessage();
          }
          return com.yorha.proto.StructMsg.BriefPoint.getDefaultInstance();
        }
      }
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       */
      public Builder setPointAttr(com.yorha.proto.StructMsg.BriefPoint value) {
        if (pointAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          pointAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       */
      public Builder setPointAttr(
          com.yorha.proto.StructMsg.BriefPoint.Builder builderForValue) {
        if (pointAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          pointAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       */
      public Builder mergePointAttr(com.yorha.proto.StructMsg.BriefPoint value) {
        if (pointAttrBuilder_ == null) {
          if (entityAttrCase_ == 16 &&
              entityAttr_ != com.yorha.proto.StructMsg.BriefPoint.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.StructMsg.BriefPoint.newBuilder((com.yorha.proto.StructMsg.BriefPoint) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 16) {
            pointAttrBuilder_.mergeFrom(value);
          }
          pointAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       */
      public Builder clearPointAttr() {
        if (pointAttrBuilder_ == null) {
          if (entityAttrCase_ == 16) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 16) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          pointAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       */
      public com.yorha.proto.StructMsg.BriefPoint.Builder getPointAttrBuilder() {
        return getPointAttrFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       */
      @java.lang.Override
      public com.yorha.proto.StructMsg.BriefPointOrBuilder getPointAttrOrBuilder() {
        if ((entityAttrCase_ == 16) && (pointAttrBuilder_ != null)) {
          return pointAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 16) {
            return (com.yorha.proto.StructMsg.BriefPoint) entityAttr_;
          }
          return com.yorha.proto.StructMsg.BriefPoint.getDefaultInstance();
        }
      }
      /**
       * <pre>
       * 简简简要数据  只有一个point的  可以的话尽量复用
       * </pre>
       *
       * <code>.com.yorha.proto.BriefPoint pointAttr = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.BriefPoint, com.yorha.proto.StructMsg.BriefPoint.Builder, com.yorha.proto.StructMsg.BriefPointOrBuilder> 
          getPointAttrFieldBuilder() {
        if (pointAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 16)) {
            entityAttr_ = com.yorha.proto.StructMsg.BriefPoint.getDefaultInstance();
          }
          pointAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.BriefPoint, com.yorha.proto.StructMsg.BriefPoint.Builder, com.yorha.proto.StructMsg.BriefPointOrBuilder>(
                  (com.yorha.proto.StructMsg.BriefPoint) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 16;
        onChanged();;
        return pointAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.BriefClanRes, com.yorha.proto.StructMsg.BriefClanRes.Builder, com.yorha.proto.StructMsg.BriefClanResOrBuilder> clanResAttrBuilder_;
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       * @return Whether the clanResAttr field is set.
       */
      @java.lang.Override
      public boolean hasClanResAttr() {
        return entityAttrCase_ == 17;
      }
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       * @return The clanResAttr.
       */
      @java.lang.Override
      public com.yorha.proto.StructMsg.BriefClanRes getClanResAttr() {
        if (clanResAttrBuilder_ == null) {
          if (entityAttrCase_ == 17) {
            return (com.yorha.proto.StructMsg.BriefClanRes) entityAttr_;
          }
          return com.yorha.proto.StructMsg.BriefClanRes.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 17) {
            return clanResAttrBuilder_.getMessage();
          }
          return com.yorha.proto.StructMsg.BriefClanRes.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       */
      public Builder setClanResAttr(com.yorha.proto.StructMsg.BriefClanRes value) {
        if (clanResAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          clanResAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       */
      public Builder setClanResAttr(
          com.yorha.proto.StructMsg.BriefClanRes.Builder builderForValue) {
        if (clanResAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          clanResAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       */
      public Builder mergeClanResAttr(com.yorha.proto.StructMsg.BriefClanRes value) {
        if (clanResAttrBuilder_ == null) {
          if (entityAttrCase_ == 17 &&
              entityAttr_ != com.yorha.proto.StructMsg.BriefClanRes.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.StructMsg.BriefClanRes.newBuilder((com.yorha.proto.StructMsg.BriefClanRes) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 17) {
            clanResAttrBuilder_.mergeFrom(value);
          }
          clanResAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       */
      public Builder clearClanResAttr() {
        if (clanResAttrBuilder_ == null) {
          if (entityAttrCase_ == 17) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 17) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          clanResAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       */
      public com.yorha.proto.StructMsg.BriefClanRes.Builder getClanResAttrBuilder() {
        return getClanResAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       */
      @java.lang.Override
      public com.yorha.proto.StructMsg.BriefClanResOrBuilder getClanResAttrOrBuilder() {
        if ((entityAttrCase_ == 17) && (clanResAttrBuilder_ != null)) {
          return clanResAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 17) {
            return (com.yorha.proto.StructMsg.BriefClanRes) entityAttr_;
          }
          return com.yorha.proto.StructMsg.BriefClanRes.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.BriefClanRes clanResAttr = 17;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.BriefClanRes, com.yorha.proto.StructMsg.BriefClanRes.Builder, com.yorha.proto.StructMsg.BriefClanResOrBuilder> 
          getClanResAttrFieldBuilder() {
        if (clanResAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 17)) {
            entityAttr_ = com.yorha.proto.StructMsg.BriefClanRes.getDefaultInstance();
          }
          clanResAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.BriefClanRes, com.yorha.proto.StructMsg.BriefClanRes.Builder, com.yorha.proto.StructMsg.BriefClanResOrBuilder>(
                  (com.yorha.proto.StructMsg.BriefClanRes) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 17;
        onChanged();;
        return clanResAttrBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SceneObjBriefAttr)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SceneObjBriefAttr)
    private static final com.yorha.proto.Entity.SceneObjBriefAttr DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Entity.SceneObjBriefAttr();
    }

    public static com.yorha.proto.Entity.SceneObjBriefAttr getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SceneObjBriefAttr>
        PARSER = new com.google.protobuf.AbstractParser<SceneObjBriefAttr>() {
      @java.lang.Override
      public SceneObjBriefAttr parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SceneObjBriefAttr(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SceneObjBriefAttr> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SceneObjBriefAttr> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Entity.SceneObjBriefAttr getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SceneObjBriefNtfMsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SceneObjBriefNtfMsg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    java.util.List<com.yorha.proto.Entity.SceneObjBriefAttr> 
        getNewEntitiesList();
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    com.yorha.proto.Entity.SceneObjBriefAttr getNewEntities(int index);
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    int getNewEntitiesCount();
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder> 
        getNewEntitiesOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder getNewEntitiesOrBuilder(
        int index);

    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @return A list containing the delEntities.
     */
    java.util.List<java.lang.Long> getDelEntitiesList();
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @return The count of delEntities.
     */
    int getDelEntitiesCount();
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @param index The index of the element to return.
     * @return The delEntities at the given index.
     */
    long getDelEntities(int index);

    /**
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SceneObjBriefNtfMsg}
   */
  public static final class SceneObjBriefNtfMsg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SceneObjBriefNtfMsg)
      SceneObjBriefNtfMsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SceneObjBriefNtfMsg.newBuilder() to construct.
    private SceneObjBriefNtfMsg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SceneObjBriefNtfMsg() {
      newEntities_ = java.util.Collections.emptyList();
      delEntities_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SceneObjBriefNtfMsg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SceneObjBriefNtfMsg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                newEntities_ = new java.util.ArrayList<com.yorha.proto.Entity.SceneObjBriefAttr>();
                mutable_bitField0_ |= 0x00000001;
              }
              newEntities_.add(
                  input.readMessage(com.yorha.proto.Entity.SceneObjBriefAttr.PARSER, extensionRegistry));
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                delEntities_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              delEntities_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                delEntities_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                delEntities_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          newEntities_ = java.util.Collections.unmodifiableList(newEntities_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          delEntities_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefNtfMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefNtfMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Entity.SceneObjBriefNtfMsg.class, com.yorha.proto.Entity.SceneObjBriefNtfMsg.Builder.class);
    }

    private int bitField0_;
    public static final int NEWENTITIES_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.Entity.SceneObjBriefAttr> newEntities_;
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.Entity.SceneObjBriefAttr> getNewEntitiesList() {
      return newEntities_;
    }
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder> 
        getNewEntitiesOrBuilderList() {
      return newEntities_;
    }
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public int getNewEntitiesCount() {
      return newEntities_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Entity.SceneObjBriefAttr getNewEntities(int index) {
      return newEntities_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder getNewEntitiesOrBuilder(
        int index) {
      return newEntities_.get(index);
    }

    public static final int DELENTITIES_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList delEntities_;
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @return A list containing the delEntities.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDelEntitiesList() {
      return delEntities_;
    }
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @return The count of delEntities.
     */
    public int getDelEntitiesCount() {
      return delEntities_.size();
    }
    /**
     * <code>repeated int64 delEntities = 2;</code>
     * @param index The index of the element to return.
     * @return The delEntities at the given index.
     */
    public long getDelEntities(int index) {
      return delEntities_.getLong(index);
    }

    public static final int ZONEID_FIELD_NUMBER = 3;
    private int zoneId_;
    /**
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < newEntities_.size(); i++) {
        output.writeMessage(1, newEntities_.get(i));
      }
      for (int i = 0; i < delEntities_.size(); i++) {
        output.writeInt64(2, delEntities_.getLong(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(3, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < newEntities_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, newEntities_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < delEntities_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(delEntities_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDelEntitiesList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Entity.SceneObjBriefNtfMsg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Entity.SceneObjBriefNtfMsg other = (com.yorha.proto.Entity.SceneObjBriefNtfMsg) obj;

      if (!getNewEntitiesList()
          .equals(other.getNewEntitiesList())) return false;
      if (!getDelEntitiesList()
          .equals(other.getDelEntitiesList())) return false;
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getNewEntitiesCount() > 0) {
        hash = (37 * hash) + NEWENTITIES_FIELD_NUMBER;
        hash = (53 * hash) + getNewEntitiesList().hashCode();
      }
      if (getDelEntitiesCount() > 0) {
        hash = (37 * hash) + DELENTITIES_FIELD_NUMBER;
        hash = (53 * hash) + getDelEntitiesList().hashCode();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Entity.SceneObjBriefNtfMsg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SceneObjBriefNtfMsg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SceneObjBriefNtfMsg)
        com.yorha.proto.Entity.SceneObjBriefNtfMsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefNtfMsg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefNtfMsg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Entity.SceneObjBriefNtfMsg.class, com.yorha.proto.Entity.SceneObjBriefNtfMsg.Builder.class);
      }

      // Construct using com.yorha.proto.Entity.SceneObjBriefNtfMsg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getNewEntitiesFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (newEntitiesBuilder_ == null) {
          newEntities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          newEntitiesBuilder_.clear();
        }
        delEntities_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Entity.internal_static_com_yorha_proto_SceneObjBriefNtfMsg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Entity.SceneObjBriefNtfMsg getDefaultInstanceForType() {
        return com.yorha.proto.Entity.SceneObjBriefNtfMsg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Entity.SceneObjBriefNtfMsg build() {
        com.yorha.proto.Entity.SceneObjBriefNtfMsg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Entity.SceneObjBriefNtfMsg buildPartial() {
        com.yorha.proto.Entity.SceneObjBriefNtfMsg result = new com.yorha.proto.Entity.SceneObjBriefNtfMsg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (newEntitiesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            newEntities_ = java.util.Collections.unmodifiableList(newEntities_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.newEntities_ = newEntities_;
        } else {
          result.newEntities_ = newEntitiesBuilder_.build();
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          delEntities_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.delEntities_ = delEntities_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Entity.SceneObjBriefNtfMsg) {
          return mergeFrom((com.yorha.proto.Entity.SceneObjBriefNtfMsg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Entity.SceneObjBriefNtfMsg other) {
        if (other == com.yorha.proto.Entity.SceneObjBriefNtfMsg.getDefaultInstance()) return this;
        if (newEntitiesBuilder_ == null) {
          if (!other.newEntities_.isEmpty()) {
            if (newEntities_.isEmpty()) {
              newEntities_ = other.newEntities_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureNewEntitiesIsMutable();
              newEntities_.addAll(other.newEntities_);
            }
            onChanged();
          }
        } else {
          if (!other.newEntities_.isEmpty()) {
            if (newEntitiesBuilder_.isEmpty()) {
              newEntitiesBuilder_.dispose();
              newEntitiesBuilder_ = null;
              newEntities_ = other.newEntities_;
              bitField0_ = (bitField0_ & ~0x00000001);
              newEntitiesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getNewEntitiesFieldBuilder() : null;
            } else {
              newEntitiesBuilder_.addAllMessages(other.newEntities_);
            }
          }
        }
        if (!other.delEntities_.isEmpty()) {
          if (delEntities_.isEmpty()) {
            delEntities_ = other.delEntities_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDelEntitiesIsMutable();
            delEntities_.addAll(other.delEntities_);
          }
          onChanged();
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Entity.SceneObjBriefNtfMsg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Entity.SceneObjBriefNtfMsg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.Entity.SceneObjBriefAttr> newEntities_ =
        java.util.Collections.emptyList();
      private void ensureNewEntitiesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          newEntities_ = new java.util.ArrayList<com.yorha.proto.Entity.SceneObjBriefAttr>(newEntities_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Entity.SceneObjBriefAttr, com.yorha.proto.Entity.SceneObjBriefAttr.Builder, com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder> newEntitiesBuilder_;

      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public java.util.List<com.yorha.proto.Entity.SceneObjBriefAttr> getNewEntitiesList() {
        if (newEntitiesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(newEntities_);
        } else {
          return newEntitiesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public int getNewEntitiesCount() {
        if (newEntitiesBuilder_ == null) {
          return newEntities_.size();
        } else {
          return newEntitiesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public com.yorha.proto.Entity.SceneObjBriefAttr getNewEntities(int index) {
        if (newEntitiesBuilder_ == null) {
          return newEntities_.get(index);
        } else {
          return newEntitiesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder setNewEntities(
          int index, com.yorha.proto.Entity.SceneObjBriefAttr value) {
        if (newEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewEntitiesIsMutable();
          newEntities_.set(index, value);
          onChanged();
        } else {
          newEntitiesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder setNewEntities(
          int index, com.yorha.proto.Entity.SceneObjBriefAttr.Builder builderForValue) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          newEntities_.set(index, builderForValue.build());
          onChanged();
        } else {
          newEntitiesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder addNewEntities(com.yorha.proto.Entity.SceneObjBriefAttr value) {
        if (newEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewEntitiesIsMutable();
          newEntities_.add(value);
          onChanged();
        } else {
          newEntitiesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder addNewEntities(
          int index, com.yorha.proto.Entity.SceneObjBriefAttr value) {
        if (newEntitiesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNewEntitiesIsMutable();
          newEntities_.add(index, value);
          onChanged();
        } else {
          newEntitiesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder addNewEntities(
          com.yorha.proto.Entity.SceneObjBriefAttr.Builder builderForValue) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          newEntities_.add(builderForValue.build());
          onChanged();
        } else {
          newEntitiesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder addNewEntities(
          int index, com.yorha.proto.Entity.SceneObjBriefAttr.Builder builderForValue) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          newEntities_.add(index, builderForValue.build());
          onChanged();
        } else {
          newEntitiesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder addAllNewEntities(
          java.lang.Iterable<? extends com.yorha.proto.Entity.SceneObjBriefAttr> values) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, newEntities_);
          onChanged();
        } else {
          newEntitiesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder clearNewEntities() {
        if (newEntitiesBuilder_ == null) {
          newEntities_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          newEntitiesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public Builder removeNewEntities(int index) {
        if (newEntitiesBuilder_ == null) {
          ensureNewEntitiesIsMutable();
          newEntities_.remove(index);
          onChanged();
        } else {
          newEntitiesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public com.yorha.proto.Entity.SceneObjBriefAttr.Builder getNewEntitiesBuilder(
          int index) {
        return getNewEntitiesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder getNewEntitiesOrBuilder(
          int index) {
        if (newEntitiesBuilder_ == null) {
          return newEntities_.get(index);  } else {
          return newEntitiesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder> 
           getNewEntitiesOrBuilderList() {
        if (newEntitiesBuilder_ != null) {
          return newEntitiesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(newEntities_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public com.yorha.proto.Entity.SceneObjBriefAttr.Builder addNewEntitiesBuilder() {
        return getNewEntitiesFieldBuilder().addBuilder(
            com.yorha.proto.Entity.SceneObjBriefAttr.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public com.yorha.proto.Entity.SceneObjBriefAttr.Builder addNewEntitiesBuilder(
          int index) {
        return getNewEntitiesFieldBuilder().addBuilder(
            index, com.yorha.proto.Entity.SceneObjBriefAttr.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.SceneObjBriefAttr newEntities = 1;</code>
       */
      public java.util.List<com.yorha.proto.Entity.SceneObjBriefAttr.Builder> 
           getNewEntitiesBuilderList() {
        return getNewEntitiesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Entity.SceneObjBriefAttr, com.yorha.proto.Entity.SceneObjBriefAttr.Builder, com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder> 
          getNewEntitiesFieldBuilder() {
        if (newEntitiesBuilder_ == null) {
          newEntitiesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.Entity.SceneObjBriefAttr, com.yorha.proto.Entity.SceneObjBriefAttr.Builder, com.yorha.proto.Entity.SceneObjBriefAttrOrBuilder>(
                  newEntities_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          newEntities_ = null;
        }
        return newEntitiesBuilder_;
      }

      private com.google.protobuf.Internal.LongList delEntities_ = emptyLongList();
      private void ensureDelEntitiesIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          delEntities_ = mutableCopy(delEntities_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @return A list containing the delEntities.
       */
      public java.util.List<java.lang.Long>
          getDelEntitiesList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(delEntities_) : delEntities_;
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @return The count of delEntities.
       */
      public int getDelEntitiesCount() {
        return delEntities_.size();
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @param index The index of the element to return.
       * @return The delEntities at the given index.
       */
      public long getDelEntities(int index) {
        return delEntities_.getLong(index);
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @param index The index to set the value at.
       * @param value The delEntities to set.
       * @return This builder for chaining.
       */
      public Builder setDelEntities(
          int index, long value) {
        ensureDelEntitiesIsMutable();
        delEntities_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @param value The delEntities to add.
       * @return This builder for chaining.
       */
      public Builder addDelEntities(long value) {
        ensureDelEntitiesIsMutable();
        delEntities_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @param values The delEntities to add.
       * @return This builder for chaining.
       */
      public Builder addAllDelEntities(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDelEntitiesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, delEntities_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delEntities = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDelEntities() {
        delEntities_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <code>optional int32 zoneId = 3;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 zoneId = 3;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <code>optional int32 zoneId = 3;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000004;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SceneObjBriefNtfMsg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SceneObjBriefNtfMsg)
    private static final com.yorha.proto.Entity.SceneObjBriefNtfMsg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Entity.SceneObjBriefNtfMsg();
    }

    public static com.yorha.proto.Entity.SceneObjBriefNtfMsg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SceneObjBriefNtfMsg>
        PARSER = new com.google.protobuf.AbstractParser<SceneObjBriefNtfMsg>() {
      @java.lang.Override
      public SceneObjBriefNtfMsg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SceneObjBriefNtfMsg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SceneObjBriefNtfMsg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SceneObjBriefNtfMsg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Entity.SceneObjBriefNtfMsg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_EntityNtfMsg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_EntityNtfMsg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SceneObjBriefAttr_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SceneObjBriefAttr_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SceneObjBriefNtfMsg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SceneObjBriefNtfMsg_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n ss_proto/gen/entity/entity.proto\022\017com." +
      "yorha.proto\032\"cs_proto/gen/common/structP" +
      "B.proto\032%ss_proto/gen/common/common_enum" +
      ".proto\032$ss_proto/gen/common/struct_msg.p" +
      "roto\032%ss_proto/gen/entity/entity_attr.pr" +
      "oto\"\316\001\n\014EntityNtfMsg\0220\n\013newEntities\030\001 \003(" +
      "\0132\033.com.yorha.proto.EntityAttr\022\023\n\013delEnt" +
      "ities\030\002 \003(\003\0220\n\013modEntities\030\003 \003(\0132\033.com.y" +
      "orha.proto.EntityAttr\0225\n\006reason\030\004 \001(\0162%." +
      "com.yorha.proto.SceneObjectNtfReason\022\016\n\006" +
      "zoneId\030\005 \001(\005\"\257\003\n\021SceneObjBriefAttr\022\020\n\010en" +
      "tityId\030\001 \001(\003\022/\n\nentityType\030\002 \001(\0162\033.com.y" +
      "orha.proto.EntityType\022>\n\017mapBuildingAttr" +
      "\030\013 \001(\0132#.com.yorha.proto.BriefMapBuildin" +
      "gPBH\000\0226\n\013dropObjAttr\030\014 \001(\0132\037.com.yorha.p" +
      "roto.BriefDropObjPBH\000\0226\n\013monsterAttr\030\r \001" +
      "(\0132\037.com.yorha.proto.BriefMonsterPBH\000\0221\n" +
      "\010caveAttr\030\017 \001(\0132\035.com.yorha.proto.BriefC" +
      "aveMsgH\000\0220\n\tpointAttr\030\020 \001(\0132\033.com.yorha." +
      "proto.BriefPointH\000\0224\n\013clanResAttr\030\021 \001(\0132" +
      "\035.com.yorha.proto.BriefClanResH\000B\014\n\nenti" +
      "tyAttr\"s\n\023SceneObjBriefNtfMsg\0227\n\013newEnti" +
      "ties\030\001 \003(\0132\".com.yorha.proto.SceneObjBri" +
      "efAttr\022\023\n\013delEntities\030\002 \003(\003\022\016\n\006zoneId\030\003 " +
      "\001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
          com.yorha.proto.EntityAttrOuterClass.getDescriptor(),
        });
    internal_static_com_yorha_proto_EntityNtfMsg_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_EntityNtfMsg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_EntityNtfMsg_descriptor,
        new java.lang.String[] { "NewEntities", "DelEntities", "ModEntities", "Reason", "ZoneId", });
    internal_static_com_yorha_proto_SceneObjBriefAttr_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_SceneObjBriefAttr_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SceneObjBriefAttr_descriptor,
        new java.lang.String[] { "EntityId", "EntityType", "MapBuildingAttr", "DropObjAttr", "MonsterAttr", "CaveAttr", "PointAttr", "ClanResAttr", "EntityAttr", });
    internal_static_com_yorha_proto_SceneObjBriefNtfMsg_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SceneObjBriefNtfMsg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SceneObjBriefNtfMsg_descriptor,
        new java.lang.String[] { "NewEntities", "DelEntities", "ZoneId", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
    com.yorha.proto.EntityAttrOuterClass.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_clan_tech.proto

package com.yorha.proto;

public final class PlayerClanTech {
  private PlayerClanTech() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_FetchClanTechInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanTechInfo_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanTechInfo_C2S}
   */
  public static final class Player_FetchClanTechInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanTechInfo_C2S)
      Player_FetchClanTechInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanTechInfo_C2S.newBuilder() to construct.
    private Player_FetchClanTechInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanTechInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanTechInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanTechInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S.class, com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S other = (com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanTechInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanTechInfo_C2S)
        com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S.class, com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S build() {
        com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S result = new com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanTechInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanTechInfo_C2S)
    private static final com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S();
    }

    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanTechInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanTechInfo_C2S>() {
      @java.lang.Override
      public Player_FetchClanTechInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanTechInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanTechInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanTechInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchClanTechInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanTechInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
     * @return Whether the techModel field is set.
     */
    boolean hasTechModel();
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
     * @return The techModel.
     */
    com.yorha.proto.ClanPB.ClanTechModelPB getTechModel();
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
     */
    com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder getTechModelOrBuilder();

    /**
     * <pre>
     * 我今日的捐献
     * </pre>
     *
     * <code>optional int32 myContributionDaily = 2;</code>
     * @return Whether the myContributionDaily field is set.
     */
    boolean hasMyContributionDaily();
    /**
     * <pre>
     * 我今日的捐献
     * </pre>
     *
     * <code>optional int32 myContributionDaily = 2;</code>
     * @return The myContributionDaily.
     */
    int getMyContributionDaily();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanTechInfo_S2C}
   */
  public static final class Player_FetchClanTechInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanTechInfo_S2C)
      Player_FetchClanTechInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanTechInfo_S2C.newBuilder() to construct.
    private Player_FetchClanTechInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanTechInfo_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanTechInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanTechInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.ClanPB.ClanTechModelPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = techModel_.toBuilder();
              }
              techModel_ = input.readMessage(com.yorha.proto.ClanPB.ClanTechModelPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(techModel_);
                techModel_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              myContributionDaily_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C.class, com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int TECHMODEL_FIELD_NUMBER = 1;
    private com.yorha.proto.ClanPB.ClanTechModelPB techModel_;
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
     * @return Whether the techModel field is set.
     */
    @java.lang.Override
    public boolean hasTechModel() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
     * @return The techModel.
     */
    @java.lang.Override
    public com.yorha.proto.ClanPB.ClanTechModelPB getTechModel() {
      return techModel_ == null ? com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance() : techModel_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder getTechModelOrBuilder() {
      return techModel_ == null ? com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance() : techModel_;
    }

    public static final int MYCONTRIBUTIONDAILY_FIELD_NUMBER = 2;
    private int myContributionDaily_;
    /**
     * <pre>
     * 我今日的捐献
     * </pre>
     *
     * <code>optional int32 myContributionDaily = 2;</code>
     * @return Whether the myContributionDaily field is set.
     */
    @java.lang.Override
    public boolean hasMyContributionDaily() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 我今日的捐献
     * </pre>
     *
     * <code>optional int32 myContributionDaily = 2;</code>
     * @return The myContributionDaily.
     */
    @java.lang.Override
    public int getMyContributionDaily() {
      return myContributionDaily_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getTechModel());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, myContributionDaily_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getTechModel());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, myContributionDaily_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C other = (com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C) obj;

      if (hasTechModel() != other.hasTechModel()) return false;
      if (hasTechModel()) {
        if (!getTechModel()
            .equals(other.getTechModel())) return false;
      }
      if (hasMyContributionDaily() != other.hasMyContributionDaily()) return false;
      if (hasMyContributionDaily()) {
        if (getMyContributionDaily()
            != other.getMyContributionDaily()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTechModel()) {
        hash = (37 * hash) + TECHMODEL_FIELD_NUMBER;
        hash = (53 * hash) + getTechModel().hashCode();
      }
      if (hasMyContributionDaily()) {
        hash = (37 * hash) + MYCONTRIBUTIONDAILY_FIELD_NUMBER;
        hash = (53 * hash) + getMyContributionDaily();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanTechInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanTechInfo_S2C)
        com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C.class, com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTechModelFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (techModelBuilder_ == null) {
          techModel_ = null;
        } else {
          techModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        myContributionDaily_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C build() {
        com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C result = new com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (techModelBuilder_ == null) {
            result.techModel_ = techModel_;
          } else {
            result.techModel_ = techModelBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.myContributionDaily_ = myContributionDaily_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C.getDefaultInstance()) return this;
        if (other.hasTechModel()) {
          mergeTechModel(other.getTechModel());
        }
        if (other.hasMyContributionDaily()) {
          setMyContributionDaily(other.getMyContributionDaily());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.ClanPB.ClanTechModelPB techModel_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanPB.ClanTechModelPB, com.yorha.proto.ClanPB.ClanTechModelPB.Builder, com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder> techModelBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       * @return Whether the techModel field is set.
       */
      public boolean hasTechModel() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       * @return The techModel.
       */
      public com.yorha.proto.ClanPB.ClanTechModelPB getTechModel() {
        if (techModelBuilder_ == null) {
          return techModel_ == null ? com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance() : techModel_;
        } else {
          return techModelBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       */
      public Builder setTechModel(com.yorha.proto.ClanPB.ClanTechModelPB value) {
        if (techModelBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          techModel_ = value;
          onChanged();
        } else {
          techModelBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       */
      public Builder setTechModel(
          com.yorha.proto.ClanPB.ClanTechModelPB.Builder builderForValue) {
        if (techModelBuilder_ == null) {
          techModel_ = builderForValue.build();
          onChanged();
        } else {
          techModelBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       */
      public Builder mergeTechModel(com.yorha.proto.ClanPB.ClanTechModelPB value) {
        if (techModelBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              techModel_ != null &&
              techModel_ != com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance()) {
            techModel_ =
              com.yorha.proto.ClanPB.ClanTechModelPB.newBuilder(techModel_).mergeFrom(value).buildPartial();
          } else {
            techModel_ = value;
          }
          onChanged();
        } else {
          techModelBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       */
      public Builder clearTechModel() {
        if (techModelBuilder_ == null) {
          techModel_ = null;
          onChanged();
        } else {
          techModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       */
      public com.yorha.proto.ClanPB.ClanTechModelPB.Builder getTechModelBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTechModelFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       */
      public com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder getTechModelOrBuilder() {
        if (techModelBuilder_ != null) {
          return techModelBuilder_.getMessageOrBuilder();
        } else {
          return techModel_ == null ?
              com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance() : techModel_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB techModel = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanPB.ClanTechModelPB, com.yorha.proto.ClanPB.ClanTechModelPB.Builder, com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder> 
          getTechModelFieldBuilder() {
        if (techModelBuilder_ == null) {
          techModelBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ClanPB.ClanTechModelPB, com.yorha.proto.ClanPB.ClanTechModelPB.Builder, com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder>(
                  getTechModel(),
                  getParentForChildren(),
                  isClean());
          techModel_ = null;
        }
        return techModelBuilder_;
      }

      private int myContributionDaily_ ;
      /**
       * <pre>
       * 我今日的捐献
       * </pre>
       *
       * <code>optional int32 myContributionDaily = 2;</code>
       * @return Whether the myContributionDaily field is set.
       */
      @java.lang.Override
      public boolean hasMyContributionDaily() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 我今日的捐献
       * </pre>
       *
       * <code>optional int32 myContributionDaily = 2;</code>
       * @return The myContributionDaily.
       */
      @java.lang.Override
      public int getMyContributionDaily() {
        return myContributionDaily_;
      }
      /**
       * <pre>
       * 我今日的捐献
       * </pre>
       *
       * <code>optional int32 myContributionDaily = 2;</code>
       * @param value The myContributionDaily to set.
       * @return This builder for chaining.
       */
      public Builder setMyContributionDaily(int value) {
        bitField0_ |= 0x00000002;
        myContributionDaily_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 我今日的捐献
       * </pre>
       *
       * <code>optional int32 myContributionDaily = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMyContributionDaily() {
        bitField0_ = (bitField0_ & ~0x00000002);
        myContributionDaily_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanTechInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanTechInfo_S2C)
    private static final com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C();
    }

    public static com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanTechInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanTechInfo_S2C>() {
      @java.lang.Override
      public Player_FetchClanTechInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanTechInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanTechInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanTechInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_FetchClanTechInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ClanTechDonate_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ClanTechDonate_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 科技id
     * </pre>
     *
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    boolean hasClanTechId();
    /**
     * <pre>
     * 科技id
     * </pre>
     *
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    int getClanTechId();

    /**
     * <pre>
     * 捐献类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanTechDonateType donateType = 2;</code>
     * @return Whether the donateType field is set.
     */
    boolean hasDonateType();
    /**
     * <pre>
     * 捐献类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanTechDonateType donateType = 2;</code>
     * @return The donateType.
     */
    com.yorha.proto.CommonEnum.ClanTechDonateType getDonateType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ClanTechDonate_C2S}
   */
  public static final class Player_ClanTechDonate_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ClanTechDonate_C2S)
      Player_ClanTechDonate_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ClanTechDonate_C2S.newBuilder() to construct.
    private Player_ClanTechDonate_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ClanTechDonate_C2S() {
      donateType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ClanTechDonate_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ClanTechDonate_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanTechId_ = input.readInt32();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanTechDonateType value = com.yorha.proto.CommonEnum.ClanTechDonateType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                donateType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S.class, com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CLANTECHID_FIELD_NUMBER = 1;
    private int clanTechId_;
    /**
     * <pre>
     * 科技id
     * </pre>
     *
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    @java.lang.Override
    public boolean hasClanTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 科技id
     * </pre>
     *
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    @java.lang.Override
    public int getClanTechId() {
      return clanTechId_;
    }

    public static final int DONATETYPE_FIELD_NUMBER = 2;
    private int donateType_;
    /**
     * <pre>
     * 捐献类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanTechDonateType donateType = 2;</code>
     * @return Whether the donateType field is set.
     */
    @java.lang.Override public boolean hasDonateType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 捐献类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanTechDonateType donateType = 2;</code>
     * @return The donateType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanTechDonateType getDonateType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanTechDonateType result = com.yorha.proto.CommonEnum.ClanTechDonateType.valueOf(donateType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanTechDonateType.CTDT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, clanTechId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, donateType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, clanTechId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, donateType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S other = (com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S) obj;

      if (hasClanTechId() != other.hasClanTechId()) return false;
      if (hasClanTechId()) {
        if (getClanTechId()
            != other.getClanTechId()) return false;
      }
      if (hasDonateType() != other.hasDonateType()) return false;
      if (hasDonateType()) {
        if (donateType_ != other.donateType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanTechId()) {
        hash = (37 * hash) + CLANTECHID_FIELD_NUMBER;
        hash = (53 * hash) + getClanTechId();
      }
      if (hasDonateType()) {
        hash = (37 * hash) + DONATETYPE_FIELD_NUMBER;
        hash = (53 * hash) + donateType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ClanTechDonate_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ClanTechDonate_C2S)
        com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S.class, com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanTechId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        donateType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S build() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S result = new com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanTechId_ = clanTechId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.donateType_ = donateType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S.getDefaultInstance()) return this;
        if (other.hasClanTechId()) {
          setClanTechId(other.getClanTechId());
        }
        if (other.hasDonateType()) {
          setDonateType(other.getDonateType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int clanTechId_ ;
      /**
       * <pre>
       * 科技id
       * </pre>
       *
       * <code>optional int32 clanTechId = 1;</code>
       * @return Whether the clanTechId field is set.
       */
      @java.lang.Override
      public boolean hasClanTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 科技id
       * </pre>
       *
       * <code>optional int32 clanTechId = 1;</code>
       * @return The clanTechId.
       */
      @java.lang.Override
      public int getClanTechId() {
        return clanTechId_;
      }
      /**
       * <pre>
       * 科技id
       * </pre>
       *
       * <code>optional int32 clanTechId = 1;</code>
       * @param value The clanTechId to set.
       * @return This builder for chaining.
       */
      public Builder setClanTechId(int value) {
        bitField0_ |= 0x00000001;
        clanTechId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 科技id
       * </pre>
       *
       * <code>optional int32 clanTechId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanTechId_ = 0;
        onChanged();
        return this;
      }

      private int donateType_ = 0;
      /**
       * <pre>
       * 捐献类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanTechDonateType donateType = 2;</code>
       * @return Whether the donateType field is set.
       */
      @java.lang.Override public boolean hasDonateType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 捐献类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanTechDonateType donateType = 2;</code>
       * @return The donateType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanTechDonateType getDonateType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanTechDonateType result = com.yorha.proto.CommonEnum.ClanTechDonateType.valueOf(donateType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanTechDonateType.CTDT_NONE : result;
      }
      /**
       * <pre>
       * 捐献类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanTechDonateType donateType = 2;</code>
       * @param value The donateType to set.
       * @return This builder for chaining.
       */
      public Builder setDonateType(com.yorha.proto.CommonEnum.ClanTechDonateType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        donateType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 捐献类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanTechDonateType donateType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDonateType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        donateType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ClanTechDonate_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ClanTechDonate_C2S)
    private static final com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S();
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ClanTechDonate_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ClanTechDonate_C2S>() {
      @java.lang.Override
      public Player_ClanTechDonate_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ClanTechDonate_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ClanTechDonate_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ClanTechDonate_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ClanTechDonate_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ClanTechDonate_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 返还指定科技当前点数，这样不用重新请求面板数据
     * </pre>
     *
     * <code>optional int32 afterPoint = 1;</code>
     * @return Whether the afterPoint field is set.
     */
    boolean hasAfterPoint();
    /**
     * <pre>
     * 返还指定科技当前点数，这样不用重新请求面板数据
     * </pre>
     *
     * <code>optional int32 afterPoint = 1;</code>
     * @return The afterPoint.
     */
    int getAfterPoint();

    /**
     * <pre>
     * 暴击配置id
     * </pre>
     *
     * <code>optional int32 magnificationTemplateId = 2;</code>
     * @return Whether the magnificationTemplateId field is set.
     */
    boolean hasMagnificationTemplateId();
    /**
     * <pre>
     * 暴击配置id
     * </pre>
     *
     * <code>optional int32 magnificationTemplateId = 2;</code>
     * @return The magnificationTemplateId.
     */
    int getMagnificationTemplateId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ClanTechDonate_S2C}
   */
  public static final class Player_ClanTechDonate_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ClanTechDonate_S2C)
      Player_ClanTechDonate_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ClanTechDonate_S2C.newBuilder() to construct.
    private Player_ClanTechDonate_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ClanTechDonate_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ClanTechDonate_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ClanTechDonate_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              afterPoint_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              magnificationTemplateId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C.class, com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int AFTERPOINT_FIELD_NUMBER = 1;
    private int afterPoint_;
    /**
     * <pre>
     * 返还指定科技当前点数，这样不用重新请求面板数据
     * </pre>
     *
     * <code>optional int32 afterPoint = 1;</code>
     * @return Whether the afterPoint field is set.
     */
    @java.lang.Override
    public boolean hasAfterPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 返还指定科技当前点数，这样不用重新请求面板数据
     * </pre>
     *
     * <code>optional int32 afterPoint = 1;</code>
     * @return The afterPoint.
     */
    @java.lang.Override
    public int getAfterPoint() {
      return afterPoint_;
    }

    public static final int MAGNIFICATIONTEMPLATEID_FIELD_NUMBER = 2;
    private int magnificationTemplateId_;
    /**
     * <pre>
     * 暴击配置id
     * </pre>
     *
     * <code>optional int32 magnificationTemplateId = 2;</code>
     * @return Whether the magnificationTemplateId field is set.
     */
    @java.lang.Override
    public boolean hasMagnificationTemplateId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 暴击配置id
     * </pre>
     *
     * <code>optional int32 magnificationTemplateId = 2;</code>
     * @return The magnificationTemplateId.
     */
    @java.lang.Override
    public int getMagnificationTemplateId() {
      return magnificationTemplateId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, afterPoint_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, magnificationTemplateId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, afterPoint_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, magnificationTemplateId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C other = (com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C) obj;

      if (hasAfterPoint() != other.hasAfterPoint()) return false;
      if (hasAfterPoint()) {
        if (getAfterPoint()
            != other.getAfterPoint()) return false;
      }
      if (hasMagnificationTemplateId() != other.hasMagnificationTemplateId()) return false;
      if (hasMagnificationTemplateId()) {
        if (getMagnificationTemplateId()
            != other.getMagnificationTemplateId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAfterPoint()) {
        hash = (37 * hash) + AFTERPOINT_FIELD_NUMBER;
        hash = (53 * hash) + getAfterPoint();
      }
      if (hasMagnificationTemplateId()) {
        hash = (37 * hash) + MAGNIFICATIONTEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getMagnificationTemplateId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ClanTechDonate_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ClanTechDonate_S2C)
        com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C.class, com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        afterPoint_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        magnificationTemplateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C build() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C result = new com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.afterPoint_ = afterPoint_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.magnificationTemplateId_ = magnificationTemplateId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C.getDefaultInstance()) return this;
        if (other.hasAfterPoint()) {
          setAfterPoint(other.getAfterPoint());
        }
        if (other.hasMagnificationTemplateId()) {
          setMagnificationTemplateId(other.getMagnificationTemplateId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int afterPoint_ ;
      /**
       * <pre>
       * 返还指定科技当前点数，这样不用重新请求面板数据
       * </pre>
       *
       * <code>optional int32 afterPoint = 1;</code>
       * @return Whether the afterPoint field is set.
       */
      @java.lang.Override
      public boolean hasAfterPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 返还指定科技当前点数，这样不用重新请求面板数据
       * </pre>
       *
       * <code>optional int32 afterPoint = 1;</code>
       * @return The afterPoint.
       */
      @java.lang.Override
      public int getAfterPoint() {
        return afterPoint_;
      }
      /**
       * <pre>
       * 返还指定科技当前点数，这样不用重新请求面板数据
       * </pre>
       *
       * <code>optional int32 afterPoint = 1;</code>
       * @param value The afterPoint to set.
       * @return This builder for chaining.
       */
      public Builder setAfterPoint(int value) {
        bitField0_ |= 0x00000001;
        afterPoint_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 返还指定科技当前点数，这样不用重新请求面板数据
       * </pre>
       *
       * <code>optional int32 afterPoint = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterPoint() {
        bitField0_ = (bitField0_ & ~0x00000001);
        afterPoint_ = 0;
        onChanged();
        return this;
      }

      private int magnificationTemplateId_ ;
      /**
       * <pre>
       * 暴击配置id
       * </pre>
       *
       * <code>optional int32 magnificationTemplateId = 2;</code>
       * @return Whether the magnificationTemplateId field is set.
       */
      @java.lang.Override
      public boolean hasMagnificationTemplateId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 暴击配置id
       * </pre>
       *
       * <code>optional int32 magnificationTemplateId = 2;</code>
       * @return The magnificationTemplateId.
       */
      @java.lang.Override
      public int getMagnificationTemplateId() {
        return magnificationTemplateId_;
      }
      /**
       * <pre>
       * 暴击配置id
       * </pre>
       *
       * <code>optional int32 magnificationTemplateId = 2;</code>
       * @param value The magnificationTemplateId to set.
       * @return This builder for chaining.
       */
      public Builder setMagnificationTemplateId(int value) {
        bitField0_ |= 0x00000002;
        magnificationTemplateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 暴击配置id
       * </pre>
       *
       * <code>optional int32 magnificationTemplateId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMagnificationTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        magnificationTemplateId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ClanTechDonate_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ClanTechDonate_S2C)
    private static final com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C();
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ClanTechDonate_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ClanTechDonate_S2C>() {
      @java.lang.Override
      public Player_ClanTechDonate_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ClanTechDonate_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ClanTechDonate_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ClanTechDonate_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_ClanTechDonate_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ClanTechResearch_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ClanTechResearch_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 研发的科技id
     * </pre>
     *
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    boolean hasClanTechId();
    /**
     * <pre>
     * 研发的科技id
     * </pre>
     *
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    int getClanTechId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ClanTechResearch_C2S}
   */
  public static final class Player_ClanTechResearch_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ClanTechResearch_C2S)
      Player_ClanTechResearch_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ClanTechResearch_C2S.newBuilder() to construct.
    private Player_ClanTechResearch_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ClanTechResearch_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ClanTechResearch_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ClanTechResearch_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanTechId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S.class, com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CLANTECHID_FIELD_NUMBER = 1;
    private int clanTechId_;
    /**
     * <pre>
     * 研发的科技id
     * </pre>
     *
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    @java.lang.Override
    public boolean hasClanTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 研发的科技id
     * </pre>
     *
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    @java.lang.Override
    public int getClanTechId() {
      return clanTechId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, clanTechId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, clanTechId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S other = (com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S) obj;

      if (hasClanTechId() != other.hasClanTechId()) return false;
      if (hasClanTechId()) {
        if (getClanTechId()
            != other.getClanTechId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanTechId()) {
        hash = (37 * hash) + CLANTECHID_FIELD_NUMBER;
        hash = (53 * hash) + getClanTechId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ClanTechResearch_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ClanTechResearch_C2S)
        com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S.class, com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanTechId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S build() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S result = new com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanTechId_ = clanTechId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S.getDefaultInstance()) return this;
        if (other.hasClanTechId()) {
          setClanTechId(other.getClanTechId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int clanTechId_ ;
      /**
       * <pre>
       * 研发的科技id
       * </pre>
       *
       * <code>optional int32 clanTechId = 1;</code>
       * @return Whether the clanTechId field is set.
       */
      @java.lang.Override
      public boolean hasClanTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 研发的科技id
       * </pre>
       *
       * <code>optional int32 clanTechId = 1;</code>
       * @return The clanTechId.
       */
      @java.lang.Override
      public int getClanTechId() {
        return clanTechId_;
      }
      /**
       * <pre>
       * 研发的科技id
       * </pre>
       *
       * <code>optional int32 clanTechId = 1;</code>
       * @param value The clanTechId to set.
       * @return This builder for chaining.
       */
      public Builder setClanTechId(int value) {
        bitField0_ |= 0x00000001;
        clanTechId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 研发的科技id
       * </pre>
       *
       * <code>optional int32 clanTechId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanTechId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ClanTechResearch_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ClanTechResearch_C2S)
    private static final com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S();
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ClanTechResearch_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ClanTechResearch_C2S>() {
      @java.lang.Override
      public Player_ClanTechResearch_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ClanTechResearch_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ClanTechResearch_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ClanTechResearch_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ClanTechResearch_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ClanTechResearch_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ClanTechResearch_S2C}
   */
  public static final class Player_ClanTechResearch_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ClanTechResearch_S2C)
      Player_ClanTechResearch_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ClanTechResearch_S2C.newBuilder() to construct.
    private Player_ClanTechResearch_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ClanTechResearch_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ClanTechResearch_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ClanTechResearch_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C.class, com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C other = (com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ClanTechResearch_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ClanTechResearch_S2C)
        com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C.class, com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C build() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C result = new com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ClanTechResearch_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ClanTechResearch_S2C)
    private static final com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C();
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ClanTechResearch_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ClanTechResearch_S2C>() {
      @java.lang.Override
      public Player_ClanTechResearch_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ClanTechResearch_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ClanTechResearch_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ClanTechResearch_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_ClanTechResearch_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ClanTechRecommend_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ClanTechRecommend_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 推荐的科技id,为0表示取消推荐
     * </pre>
     *
     * <code>optional int32 techId = 1;</code>
     * @return Whether the techId field is set.
     */
    boolean hasTechId();
    /**
     * <pre>
     * 推荐的科技id,为0表示取消推荐
     * </pre>
     *
     * <code>optional int32 techId = 1;</code>
     * @return The techId.
     */
    int getTechId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ClanTechRecommend_C2S}
   */
  public static final class Player_ClanTechRecommend_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ClanTechRecommend_C2S)
      Player_ClanTechRecommend_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ClanTechRecommend_C2S.newBuilder() to construct.
    private Player_ClanTechRecommend_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ClanTechRecommend_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ClanTechRecommend_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ClanTechRecommend_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              techId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S.class, com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int TECHID_FIELD_NUMBER = 1;
    private int techId_;
    /**
     * <pre>
     * 推荐的科技id,为0表示取消推荐
     * </pre>
     *
     * <code>optional int32 techId = 1;</code>
     * @return Whether the techId field is set.
     */
    @java.lang.Override
    public boolean hasTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 推荐的科技id,为0表示取消推荐
     * </pre>
     *
     * <code>optional int32 techId = 1;</code>
     * @return The techId.
     */
    @java.lang.Override
    public int getTechId() {
      return techId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, techId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, techId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S other = (com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S) obj;

      if (hasTechId() != other.hasTechId()) return false;
      if (hasTechId()) {
        if (getTechId()
            != other.getTechId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTechId()) {
        hash = (37 * hash) + TECHID_FIELD_NUMBER;
        hash = (53 * hash) + getTechId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ClanTechRecommend_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ClanTechRecommend_C2S)
        com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S.class, com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        techId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S build() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S result = new com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.techId_ = techId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S.getDefaultInstance()) return this;
        if (other.hasTechId()) {
          setTechId(other.getTechId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int techId_ ;
      /**
       * <pre>
       * 推荐的科技id,为0表示取消推荐
       * </pre>
       *
       * <code>optional int32 techId = 1;</code>
       * @return Whether the techId field is set.
       */
      @java.lang.Override
      public boolean hasTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 推荐的科技id,为0表示取消推荐
       * </pre>
       *
       * <code>optional int32 techId = 1;</code>
       * @return The techId.
       */
      @java.lang.Override
      public int getTechId() {
        return techId_;
      }
      /**
       * <pre>
       * 推荐的科技id,为0表示取消推荐
       * </pre>
       *
       * <code>optional int32 techId = 1;</code>
       * @param value The techId to set.
       * @return This builder for chaining.
       */
      public Builder setTechId(int value) {
        bitField0_ |= 0x00000001;
        techId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推荐的科技id,为0表示取消推荐
       * </pre>
       *
       * <code>optional int32 techId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        techId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ClanTechRecommend_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ClanTechRecommend_C2S)
    private static final com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S();
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ClanTechRecommend_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ClanTechRecommend_C2S>() {
      @java.lang.Override
      public Player_ClanTechRecommend_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ClanTechRecommend_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ClanTechRecommend_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ClanTechRecommend_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ClanTechRecommend_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ClanTechRecommend_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ClanTechRecommend_S2C}
   */
  public static final class Player_ClanTechRecommend_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ClanTechRecommend_S2C)
      Player_ClanTechRecommend_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ClanTechRecommend_S2C.newBuilder() to construct.
    private Player_ClanTechRecommend_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ClanTechRecommend_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ClanTechRecommend_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ClanTechRecommend_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C.class, com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C other = (com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ClanTechRecommend_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ClanTechRecommend_S2C)
        com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C.class, com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C build() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C result = new com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ClanTechRecommend_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ClanTechRecommend_S2C)
    private static final com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C();
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ClanTechRecommend_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ClanTechRecommend_S2C>() {
      @java.lang.Override
      public Player_ClanTechRecommend_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ClanTechRecommend_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ClanTechRecommend_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ClanTechRecommend_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_ClanTechRecommend_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ClanTechDetail_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ClanTechDetail_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    boolean hasClanTechId();
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    int getClanTechId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ClanTechDetail_C2S}
   */
  public static final class Player_ClanTechDetail_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ClanTechDetail_C2S)
      Player_ClanTechDetail_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ClanTechDetail_C2S.newBuilder() to construct.
    private Player_ClanTechDetail_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ClanTechDetail_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ClanTechDetail_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ClanTechDetail_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanTechId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S.class, com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CLANTECHID_FIELD_NUMBER = 1;
    private int clanTechId_;
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    @java.lang.Override
    public boolean hasClanTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    @java.lang.Override
    public int getClanTechId() {
      return clanTechId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, clanTechId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, clanTechId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S other = (com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S) obj;

      if (hasClanTechId() != other.hasClanTechId()) return false;
      if (hasClanTechId()) {
        if (getClanTechId()
            != other.getClanTechId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanTechId()) {
        hash = (37 * hash) + CLANTECHID_FIELD_NUMBER;
        hash = (53 * hash) + getClanTechId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ClanTechDetail_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ClanTechDetail_C2S)
        com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S.class, com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanTechId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S build() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S result = new com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanTechId_ = clanTechId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S.getDefaultInstance()) return this;
        if (other.hasClanTechId()) {
          setClanTechId(other.getClanTechId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int clanTechId_ ;
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return Whether the clanTechId field is set.
       */
      @java.lang.Override
      public boolean hasClanTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return The clanTechId.
       */
      @java.lang.Override
      public int getClanTechId() {
        return clanTechId_;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @param value The clanTechId to set.
       * @return This builder for chaining.
       */
      public Builder setClanTechId(int value) {
        bitField0_ |= 0x00000001;
        clanTechId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanTechId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ClanTechDetail_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ClanTechDetail_C2S)
    private static final com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S();
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ClanTechDetail_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ClanTechDetail_C2S>() {
      @java.lang.Override
      public Player_ClanTechDetail_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ClanTechDetail_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ClanTechDetail_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ClanTechDetail_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ClanTechDetail_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ClanTechDetail_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
     * @return Whether the techInfo field is set.
     */
    boolean hasTechInfo();
    /**
     * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
     * @return The techInfo.
     */
    com.yorha.proto.StructPB.ClanTechInfoPB getTechInfo();
    /**
     * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
     */
    com.yorha.proto.StructPB.ClanTechInfoPBOrBuilder getTechInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ClanTechDetail_S2C}
   */
  public static final class Player_ClanTechDetail_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ClanTechDetail_S2C)
      Player_ClanTechDetail_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ClanTechDetail_S2C.newBuilder() to construct.
    private Player_ClanTechDetail_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ClanTechDetail_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ClanTechDetail_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ClanTechDetail_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.ClanTechInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = techInfo_.toBuilder();
              }
              techInfo_ = input.readMessage(com.yorha.proto.StructPB.ClanTechInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(techInfo_);
                techInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C.class, com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int TECHINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.ClanTechInfoPB techInfo_;
    /**
     * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
     * @return Whether the techInfo field is set.
     */
    @java.lang.Override
    public boolean hasTechInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
     * @return The techInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ClanTechInfoPB getTechInfo() {
      return techInfo_ == null ? com.yorha.proto.StructPB.ClanTechInfoPB.getDefaultInstance() : techInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ClanTechInfoPBOrBuilder getTechInfoOrBuilder() {
      return techInfo_ == null ? com.yorha.proto.StructPB.ClanTechInfoPB.getDefaultInstance() : techInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getTechInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getTechInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C other = (com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C) obj;

      if (hasTechInfo() != other.hasTechInfo()) return false;
      if (hasTechInfo()) {
        if (!getTechInfo()
            .equals(other.getTechInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTechInfo()) {
        hash = (37 * hash) + TECHINFO_FIELD_NUMBER;
        hash = (53 * hash) + getTechInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ClanTechDetail_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ClanTechDetail_S2C)
        com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C.class, com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTechInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (techInfoBuilder_ == null) {
          techInfo_ = null;
        } else {
          techInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C build() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C buildPartial() {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C result = new com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (techInfoBuilder_ == null) {
            result.techInfo_ = techInfo_;
          } else {
            result.techInfo_ = techInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C other) {
        if (other == com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C.getDefaultInstance()) return this;
        if (other.hasTechInfo()) {
          mergeTechInfo(other.getTechInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.ClanTechInfoPB techInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ClanTechInfoPB, com.yorha.proto.StructPB.ClanTechInfoPB.Builder, com.yorha.proto.StructPB.ClanTechInfoPBOrBuilder> techInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       * @return Whether the techInfo field is set.
       */
      public boolean hasTechInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       * @return The techInfo.
       */
      public com.yorha.proto.StructPB.ClanTechInfoPB getTechInfo() {
        if (techInfoBuilder_ == null) {
          return techInfo_ == null ? com.yorha.proto.StructPB.ClanTechInfoPB.getDefaultInstance() : techInfo_;
        } else {
          return techInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       */
      public Builder setTechInfo(com.yorha.proto.StructPB.ClanTechInfoPB value) {
        if (techInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          techInfo_ = value;
          onChanged();
        } else {
          techInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       */
      public Builder setTechInfo(
          com.yorha.proto.StructPB.ClanTechInfoPB.Builder builderForValue) {
        if (techInfoBuilder_ == null) {
          techInfo_ = builderForValue.build();
          onChanged();
        } else {
          techInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       */
      public Builder mergeTechInfo(com.yorha.proto.StructPB.ClanTechInfoPB value) {
        if (techInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              techInfo_ != null &&
              techInfo_ != com.yorha.proto.StructPB.ClanTechInfoPB.getDefaultInstance()) {
            techInfo_ =
              com.yorha.proto.StructPB.ClanTechInfoPB.newBuilder(techInfo_).mergeFrom(value).buildPartial();
          } else {
            techInfo_ = value;
          }
          onChanged();
        } else {
          techInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       */
      public Builder clearTechInfo() {
        if (techInfoBuilder_ == null) {
          techInfo_ = null;
          onChanged();
        } else {
          techInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.ClanTechInfoPB.Builder getTechInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTechInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.ClanTechInfoPBOrBuilder getTechInfoOrBuilder() {
        if (techInfoBuilder_ != null) {
          return techInfoBuilder_.getMessageOrBuilder();
        } else {
          return techInfo_ == null ?
              com.yorha.proto.StructPB.ClanTechInfoPB.getDefaultInstance() : techInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfoPB techInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ClanTechInfoPB, com.yorha.proto.StructPB.ClanTechInfoPB.Builder, com.yorha.proto.StructPB.ClanTechInfoPBOrBuilder> 
          getTechInfoFieldBuilder() {
        if (techInfoBuilder_ == null) {
          techInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ClanTechInfoPB, com.yorha.proto.StructPB.ClanTechInfoPB.Builder, com.yorha.proto.StructPB.ClanTechInfoPBOrBuilder>(
                  getTechInfo(),
                  getParentForChildren(),
                  isClean());
          techInfo_ = null;
        }
        return techInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ClanTechDetail_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ClanTechDetail_S2C)
    private static final com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C();
    }

    public static com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ClanTechDetail_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ClanTechDetail_S2C>() {
      @java.lang.Override
      public Player_ClanTechDetail_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ClanTechDetail_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ClanTechDetail_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ClanTechDetail_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.Player_ClanTechDetail_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechInfoUpdateMsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechInfoUpdateMsg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
     * @return Whether the modData field is set.
     */
    boolean hasModData();
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
     * @return The modData.
     */
    com.yorha.proto.ClanPB.ClanTechModelPB getModData();
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
     */
    com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder getModDataOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechInfoUpdateMsg}
   */
  public static final class ClanTechInfoUpdateMsg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechInfoUpdateMsg)
      ClanTechInfoUpdateMsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechInfoUpdateMsg.newBuilder() to construct.
    private ClanTechInfoUpdateMsg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechInfoUpdateMsg() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechInfoUpdateMsg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechInfoUpdateMsg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.ClanPB.ClanTechModelPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = modData_.toBuilder();
              }
              modData_ = input.readMessage(com.yorha.proto.ClanPB.ClanTechModelPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(modData_);
                modData_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg.class, com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg.Builder.class);
    }

    private int bitField0_;
    public static final int MODDATA_FIELD_NUMBER = 1;
    private com.yorha.proto.ClanPB.ClanTechModelPB modData_;
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
     * @return Whether the modData field is set.
     */
    @java.lang.Override
    public boolean hasModData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
     * @return The modData.
     */
    @java.lang.Override
    public com.yorha.proto.ClanPB.ClanTechModelPB getModData() {
      return modData_ == null ? com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance() : modData_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder getModDataOrBuilder() {
      return modData_ == null ? com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance() : modData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getModData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getModData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg other = (com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg) obj;

      if (hasModData() != other.hasModData()) return false;
      if (hasModData()) {
        if (!getModData()
            .equals(other.getModData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasModData()) {
        hash = (37 * hash) + MODDATA_FIELD_NUMBER;
        hash = (53 * hash) + getModData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechInfoUpdateMsg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechInfoUpdateMsg)
        com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg.class, com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getModDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (modDataBuilder_ == null) {
          modData_ = null;
        } else {
          modDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanTech.internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg build() {
        com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg buildPartial() {
        com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg result = new com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (modDataBuilder_ == null) {
            result.modData_ = modData_;
          } else {
            result.modData_ = modDataBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg) {
          return mergeFrom((com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg other) {
        if (other == com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg.getDefaultInstance()) return this;
        if (other.hasModData()) {
          mergeModData(other.getModData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.ClanPB.ClanTechModelPB modData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanPB.ClanTechModelPB, com.yorha.proto.ClanPB.ClanTechModelPB.Builder, com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder> modDataBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       * @return Whether the modData field is set.
       */
      public boolean hasModData() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       * @return The modData.
       */
      public com.yorha.proto.ClanPB.ClanTechModelPB getModData() {
        if (modDataBuilder_ == null) {
          return modData_ == null ? com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance() : modData_;
        } else {
          return modDataBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       */
      public Builder setModData(com.yorha.proto.ClanPB.ClanTechModelPB value) {
        if (modDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          modData_ = value;
          onChanged();
        } else {
          modDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       */
      public Builder setModData(
          com.yorha.proto.ClanPB.ClanTechModelPB.Builder builderForValue) {
        if (modDataBuilder_ == null) {
          modData_ = builderForValue.build();
          onChanged();
        } else {
          modDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       */
      public Builder mergeModData(com.yorha.proto.ClanPB.ClanTechModelPB value) {
        if (modDataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              modData_ != null &&
              modData_ != com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance()) {
            modData_ =
              com.yorha.proto.ClanPB.ClanTechModelPB.newBuilder(modData_).mergeFrom(value).buildPartial();
          } else {
            modData_ = value;
          }
          onChanged();
        } else {
          modDataBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       */
      public Builder clearModData() {
        if (modDataBuilder_ == null) {
          modData_ = null;
          onChanged();
        } else {
          modDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       */
      public com.yorha.proto.ClanPB.ClanTechModelPB.Builder getModDataBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getModDataFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       */
      public com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder getModDataOrBuilder() {
        if (modDataBuilder_ != null) {
          return modDataBuilder_.getMessageOrBuilder();
        } else {
          return modData_ == null ?
              com.yorha.proto.ClanPB.ClanTechModelPB.getDefaultInstance() : modData_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModelPB modData = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanPB.ClanTechModelPB, com.yorha.proto.ClanPB.ClanTechModelPB.Builder, com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder> 
          getModDataFieldBuilder() {
        if (modDataBuilder_ == null) {
          modDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ClanPB.ClanTechModelPB, com.yorha.proto.ClanPB.ClanTechModelPB.Builder, com.yorha.proto.ClanPB.ClanTechModelPBOrBuilder>(
                  getModData(),
                  getParentForChildren(),
                  isClean());
          modData_ = null;
        }
        return modDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechInfoUpdateMsg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechInfoUpdateMsg)
    private static final com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg();
    }

    public static com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechInfoUpdateMsg>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechInfoUpdateMsg>() {
      @java.lang.Override
      public ClanTechInfoUpdateMsg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechInfoUpdateMsg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechInfoUpdateMsg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechInfoUpdateMsg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanTech.ClanTechInfoUpdateMsg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-ss_proto/gen/player/cs/player_clan_tec" +
      "h.proto\022\017com.yorha.proto\032\036cs_proto/gen/c" +
      "lan/clanPB.proto\032\"cs_proto/gen/common/st" +
      "ructPB.proto\032%ss_proto/gen/common/common" +
      "_enum.proto\"\036\n\034Player_FetchClanTechInfo_" +
      "C2S\"p\n\034Player_FetchClanTechInfo_S2C\0223\n\tt" +
      "echModel\030\001 \001(\0132 .com.yorha.proto.ClanTec" +
      "hModelPB\022\033\n\023myContributionDaily\030\002 \001(\005\"h\n" +
      "\031Player_ClanTechDonate_C2S\022\022\n\nclanTechId" +
      "\030\001 \001(\005\0227\n\ndonateType\030\002 \001(\0162#.com.yorha.p" +
      "roto.ClanTechDonateType\"P\n\031Player_ClanTe" +
      "chDonate_S2C\022\022\n\nafterPoint\030\001 \001(\005\022\037\n\027magn" +
      "ificationTemplateId\030\002 \001(\005\"1\n\033Player_Clan" +
      "TechResearch_C2S\022\022\n\nclanTechId\030\001 \001(\005\"\035\n\033" +
      "Player_ClanTechResearch_S2C\".\n\034Player_Cl" +
      "anTechRecommend_C2S\022\016\n\006techId\030\001 \001(\005\"\036\n\034P" +
      "layer_ClanTechRecommend_S2C\"/\n\031Player_Cl" +
      "anTechDetail_C2S\022\022\n\nclanTechId\030\001 \001(\005\"N\n\031" +
      "Player_ClanTechDetail_S2C\0221\n\010techInfo\030\001 " +
      "\001(\0132\037.com.yorha.proto.ClanTechInfoPB\"J\n\025" +
      "ClanTechInfoUpdateMsg\0221\n\007modData\030\001 \001(\0132 " +
      ".com.yorha.proto.ClanTechModelPBB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.ClanPB.getDescriptor(),
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanTechInfo_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanTechInfo_S2C_descriptor,
        new java.lang.String[] { "TechModel", "MyContributionDaily", });
    internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ClanTechDonate_C2S_descriptor,
        new java.lang.String[] { "ClanTechId", "DonateType", });
    internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ClanTechDonate_S2C_descriptor,
        new java.lang.String[] { "AfterPoint", "MagnificationTemplateId", });
    internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ClanTechResearch_C2S_descriptor,
        new java.lang.String[] { "ClanTechId", });
    internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ClanTechResearch_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ClanTechRecommend_C2S_descriptor,
        new java.lang.String[] { "TechId", });
    internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ClanTechRecommend_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ClanTechDetail_C2S_descriptor,
        new java.lang.String[] { "ClanTechId", });
    internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ClanTechDetail_S2C_descriptor,
        new java.lang.String[] { "TechInfo", });
    internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechInfoUpdateMsg_descriptor,
        new java.lang.String[] { "ModData", });
    com.yorha.proto.ClanPB.getDescriptor();
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

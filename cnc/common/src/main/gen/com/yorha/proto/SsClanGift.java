// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clan/ss_clan_gift.proto

package com.yorha.proto;

public final class SsClanGift {
  private SsClanGift() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface AddGiftPointAndKeyAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AddGiftPointAndKeyAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 addPoint = 1;</code>
     * @return Whether the addPoint field is set.
     */
    boolean hasAddPoint();
    /**
     * <code>optional int32 addPoint = 1;</code>
     * @return The addPoint.
     */
    int getAddPoint();

    /**
     * <code>optional int32 addKey = 2;</code>
     * @return Whether the addKey field is set.
     */
    boolean hasAddKey();
    /**
     * <code>optional int32 addKey = 2;</code>
     * @return The addKey.
     */
    int getAddKey();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AddGiftPointAndKeyAsk}
   */
  public static final class AddGiftPointAndKeyAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AddGiftPointAndKeyAsk)
      AddGiftPointAndKeyAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AddGiftPointAndKeyAsk.newBuilder() to construct.
    private AddGiftPointAndKeyAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AddGiftPointAndKeyAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AddGiftPointAndKeyAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AddGiftPointAndKeyAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              addPoint_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              addKey_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk.class, com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ADDPOINT_FIELD_NUMBER = 1;
    private int addPoint_;
    /**
     * <code>optional int32 addPoint = 1;</code>
     * @return Whether the addPoint field is set.
     */
    @java.lang.Override
    public boolean hasAddPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 addPoint = 1;</code>
     * @return The addPoint.
     */
    @java.lang.Override
    public int getAddPoint() {
      return addPoint_;
    }

    public static final int ADDKEY_FIELD_NUMBER = 2;
    private int addKey_;
    /**
     * <code>optional int32 addKey = 2;</code>
     * @return Whether the addKey field is set.
     */
    @java.lang.Override
    public boolean hasAddKey() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 addKey = 2;</code>
     * @return The addKey.
     */
    @java.lang.Override
    public int getAddKey() {
      return addKey_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, addPoint_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, addKey_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, addPoint_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, addKey_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk other = (com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk) obj;

      if (hasAddPoint() != other.hasAddPoint()) return false;
      if (hasAddPoint()) {
        if (getAddPoint()
            != other.getAddPoint()) return false;
      }
      if (hasAddKey() != other.hasAddKey()) return false;
      if (hasAddKey()) {
        if (getAddKey()
            != other.getAddKey()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAddPoint()) {
        hash = (37 * hash) + ADDPOINT_FIELD_NUMBER;
        hash = (53 * hash) + getAddPoint();
      }
      if (hasAddKey()) {
        hash = (37 * hash) + ADDKEY_FIELD_NUMBER;
        hash = (53 * hash) + getAddKey();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AddGiftPointAndKeyAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AddGiftPointAndKeyAsk)
        com.yorha.proto.SsClanGift.AddGiftPointAndKeyAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk.class, com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        addPoint_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        addKey_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk build() {
        com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk buildPartial() {
        com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk result = new com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.addPoint_ = addPoint_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.addKey_ = addKey_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk) {
          return mergeFrom((com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk other) {
        if (other == com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk.getDefaultInstance()) return this;
        if (other.hasAddPoint()) {
          setAddPoint(other.getAddPoint());
        }
        if (other.hasAddKey()) {
          setAddKey(other.getAddKey());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int addPoint_ ;
      /**
       * <code>optional int32 addPoint = 1;</code>
       * @return Whether the addPoint field is set.
       */
      @java.lang.Override
      public boolean hasAddPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 addPoint = 1;</code>
       * @return The addPoint.
       */
      @java.lang.Override
      public int getAddPoint() {
        return addPoint_;
      }
      /**
       * <code>optional int32 addPoint = 1;</code>
       * @param value The addPoint to set.
       * @return This builder for chaining.
       */
      public Builder setAddPoint(int value) {
        bitField0_ |= 0x00000001;
        addPoint_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 addPoint = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddPoint() {
        bitField0_ = (bitField0_ & ~0x00000001);
        addPoint_ = 0;
        onChanged();
        return this;
      }

      private int addKey_ ;
      /**
       * <code>optional int32 addKey = 2;</code>
       * @return Whether the addKey field is set.
       */
      @java.lang.Override
      public boolean hasAddKey() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 addKey = 2;</code>
       * @return The addKey.
       */
      @java.lang.Override
      public int getAddKey() {
        return addKey_;
      }
      /**
       * <code>optional int32 addKey = 2;</code>
       * @param value The addKey to set.
       * @return This builder for chaining.
       */
      public Builder setAddKey(int value) {
        bitField0_ |= 0x00000002;
        addKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 addKey = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddKey() {
        bitField0_ = (bitField0_ & ~0x00000002);
        addKey_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AddGiftPointAndKeyAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AddGiftPointAndKeyAsk)
    private static final com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk();
    }

    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AddGiftPointAndKeyAsk>
        PARSER = new com.google.protobuf.AbstractParser<AddGiftPointAndKeyAsk>() {
      @java.lang.Override
      public AddGiftPointAndKeyAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AddGiftPointAndKeyAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AddGiftPointAndKeyAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AddGiftPointAndKeyAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanGift.AddGiftPointAndKeyAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AddGiftPointAndKeyAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AddGiftPointAndKeyAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 全联盟共享军团礼物信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
     * @return Whether the clanGiftInfo field is set.
     */
    boolean hasClanGiftInfo();
    /**
     * <pre>
     * 全联盟共享军团礼物信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
     * @return The clanGiftInfo.
     */
    com.yorha.proto.StructMsg.ClanGiftSharedInfo getClanGiftInfo();
    /**
     * <pre>
     * 全联盟共享军团礼物信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
     */
    com.yorha.proto.StructMsg.ClanGiftSharedInfoOrBuilder getClanGiftInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AddGiftPointAndKeyAns}
   */
  public static final class AddGiftPointAndKeyAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AddGiftPointAndKeyAns)
      AddGiftPointAndKeyAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AddGiftPointAndKeyAns.newBuilder() to construct.
    private AddGiftPointAndKeyAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AddGiftPointAndKeyAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AddGiftPointAndKeyAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AddGiftPointAndKeyAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructMsg.ClanGiftSharedInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = clanGiftInfo_.toBuilder();
              }
              clanGiftInfo_ = input.readMessage(com.yorha.proto.StructMsg.ClanGiftSharedInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(clanGiftInfo_);
                clanGiftInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns.class, com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns.Builder.class);
    }

    private int bitField0_;
    public static final int CLANGIFTINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.StructMsg.ClanGiftSharedInfo clanGiftInfo_;
    /**
     * <pre>
     * 全联盟共享军团礼物信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
     * @return Whether the clanGiftInfo field is set.
     */
    @java.lang.Override
    public boolean hasClanGiftInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 全联盟共享军团礼物信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
     * @return The clanGiftInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.ClanGiftSharedInfo getClanGiftInfo() {
      return clanGiftInfo_ == null ? com.yorha.proto.StructMsg.ClanGiftSharedInfo.getDefaultInstance() : clanGiftInfo_;
    }
    /**
     * <pre>
     * 全联盟共享军团礼物信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.ClanGiftSharedInfoOrBuilder getClanGiftInfoOrBuilder() {
      return clanGiftInfo_ == null ? com.yorha.proto.StructMsg.ClanGiftSharedInfo.getDefaultInstance() : clanGiftInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getClanGiftInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getClanGiftInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns other = (com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns) obj;

      if (hasClanGiftInfo() != other.hasClanGiftInfo()) return false;
      if (hasClanGiftInfo()) {
        if (!getClanGiftInfo()
            .equals(other.getClanGiftInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanGiftInfo()) {
        hash = (37 * hash) + CLANGIFTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getClanGiftInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AddGiftPointAndKeyAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AddGiftPointAndKeyAns)
        com.yorha.proto.SsClanGift.AddGiftPointAndKeyAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns.class, com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getClanGiftInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (clanGiftInfoBuilder_ == null) {
          clanGiftInfo_ = null;
        } else {
          clanGiftInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddGiftPointAndKeyAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns build() {
        com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns buildPartial() {
        com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns result = new com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (clanGiftInfoBuilder_ == null) {
            result.clanGiftInfo_ = clanGiftInfo_;
          } else {
            result.clanGiftInfo_ = clanGiftInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns) {
          return mergeFrom((com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns other) {
        if (other == com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns.getDefaultInstance()) return this;
        if (other.hasClanGiftInfo()) {
          mergeClanGiftInfo(other.getClanGiftInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructMsg.ClanGiftSharedInfo clanGiftInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.ClanGiftSharedInfo, com.yorha.proto.StructMsg.ClanGiftSharedInfo.Builder, com.yorha.proto.StructMsg.ClanGiftSharedInfoOrBuilder> clanGiftInfoBuilder_;
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       * @return Whether the clanGiftInfo field is set.
       */
      public boolean hasClanGiftInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       * @return The clanGiftInfo.
       */
      public com.yorha.proto.StructMsg.ClanGiftSharedInfo getClanGiftInfo() {
        if (clanGiftInfoBuilder_ == null) {
          return clanGiftInfo_ == null ? com.yorha.proto.StructMsg.ClanGiftSharedInfo.getDefaultInstance() : clanGiftInfo_;
        } else {
          return clanGiftInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       */
      public Builder setClanGiftInfo(com.yorha.proto.StructMsg.ClanGiftSharedInfo value) {
        if (clanGiftInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          clanGiftInfo_ = value;
          onChanged();
        } else {
          clanGiftInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       */
      public Builder setClanGiftInfo(
          com.yorha.proto.StructMsg.ClanGiftSharedInfo.Builder builderForValue) {
        if (clanGiftInfoBuilder_ == null) {
          clanGiftInfo_ = builderForValue.build();
          onChanged();
        } else {
          clanGiftInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       */
      public Builder mergeClanGiftInfo(com.yorha.proto.StructMsg.ClanGiftSharedInfo value) {
        if (clanGiftInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              clanGiftInfo_ != null &&
              clanGiftInfo_ != com.yorha.proto.StructMsg.ClanGiftSharedInfo.getDefaultInstance()) {
            clanGiftInfo_ =
              com.yorha.proto.StructMsg.ClanGiftSharedInfo.newBuilder(clanGiftInfo_).mergeFrom(value).buildPartial();
          } else {
            clanGiftInfo_ = value;
          }
          onChanged();
        } else {
          clanGiftInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       */
      public Builder clearClanGiftInfo() {
        if (clanGiftInfoBuilder_ == null) {
          clanGiftInfo_ = null;
          onChanged();
        } else {
          clanGiftInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       */
      public com.yorha.proto.StructMsg.ClanGiftSharedInfo.Builder getClanGiftInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getClanGiftInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       */
      public com.yorha.proto.StructMsg.ClanGiftSharedInfoOrBuilder getClanGiftInfoOrBuilder() {
        if (clanGiftInfoBuilder_ != null) {
          return clanGiftInfoBuilder_.getMessageOrBuilder();
        } else {
          return clanGiftInfo_ == null ?
              com.yorha.proto.StructMsg.ClanGiftSharedInfo.getDefaultInstance() : clanGiftInfo_;
        }
      }
      /**
       * <pre>
       * 全联盟共享军团礼物信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanGiftSharedInfo clanGiftInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.ClanGiftSharedInfo, com.yorha.proto.StructMsg.ClanGiftSharedInfo.Builder, com.yorha.proto.StructMsg.ClanGiftSharedInfoOrBuilder> 
          getClanGiftInfoFieldBuilder() {
        if (clanGiftInfoBuilder_ == null) {
          clanGiftInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.ClanGiftSharedInfo, com.yorha.proto.StructMsg.ClanGiftSharedInfo.Builder, com.yorha.proto.StructMsg.ClanGiftSharedInfoOrBuilder>(
                  getClanGiftInfo(),
                  getParentForChildren(),
                  isClean());
          clanGiftInfo_ = null;
        }
        return clanGiftInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AddGiftPointAndKeyAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AddGiftPointAndKeyAns)
    private static final com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns();
    }

    public static com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AddGiftPointAndKeyAns>
        PARSER = new com.google.protobuf.AbstractParser<AddGiftPointAndKeyAns>() {
      @java.lang.Override
      public AddGiftPointAndKeyAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AddGiftPointAndKeyAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AddGiftPointAndKeyAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AddGiftPointAndKeyAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanGift.AddGiftPointAndKeyAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AddClanGiftCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AddClanGiftCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 giftItemId = 1;</code>
     * @return Whether the giftItemId field is set.
     */
    boolean hasGiftItemId();
    /**
     * <code>optional int32 giftItemId = 1;</code>
     * @return The giftItemId.
     */
    int getGiftItemId();

    /**
     * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
     * @return Whether the giftRecord field is set.
     */
    boolean hasGiftRecord();
    /**
     * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
     * @return The giftRecord.
     */
    com.yorha.proto.Struct.ClanGiftRecord getGiftRecord();
    /**
     * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
     */
    com.yorha.proto.Struct.ClanGiftRecordOrBuilder getGiftRecordOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AddClanGiftCmd}
   */
  public static final class AddClanGiftCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AddClanGiftCmd)
      AddClanGiftCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AddClanGiftCmd.newBuilder() to construct.
    private AddClanGiftCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AddClanGiftCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AddClanGiftCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AddClanGiftCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              giftItemId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.Struct.ClanGiftRecord.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = giftRecord_.toBuilder();
              }
              giftRecord_ = input.readMessage(com.yorha.proto.Struct.ClanGiftRecord.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(giftRecord_);
                giftRecord_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddClanGiftCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddClanGiftCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanGift.AddClanGiftCmd.class, com.yorha.proto.SsClanGift.AddClanGiftCmd.Builder.class);
    }

    private int bitField0_;
    public static final int GIFTITEMID_FIELD_NUMBER = 1;
    private int giftItemId_;
    /**
     * <code>optional int32 giftItemId = 1;</code>
     * @return Whether the giftItemId field is set.
     */
    @java.lang.Override
    public boolean hasGiftItemId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 giftItemId = 1;</code>
     * @return The giftItemId.
     */
    @java.lang.Override
    public int getGiftItemId() {
      return giftItemId_;
    }

    public static final int GIFTRECORD_FIELD_NUMBER = 2;
    private com.yorha.proto.Struct.ClanGiftRecord giftRecord_;
    /**
     * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
     * @return Whether the giftRecord field is set.
     */
    @java.lang.Override
    public boolean hasGiftRecord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
     * @return The giftRecord.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ClanGiftRecord getGiftRecord() {
      return giftRecord_ == null ? com.yorha.proto.Struct.ClanGiftRecord.getDefaultInstance() : giftRecord_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ClanGiftRecordOrBuilder getGiftRecordOrBuilder() {
      return giftRecord_ == null ? com.yorha.proto.Struct.ClanGiftRecord.getDefaultInstance() : giftRecord_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, giftItemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getGiftRecord());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, giftItemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getGiftRecord());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanGift.AddClanGiftCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanGift.AddClanGiftCmd other = (com.yorha.proto.SsClanGift.AddClanGiftCmd) obj;

      if (hasGiftItemId() != other.hasGiftItemId()) return false;
      if (hasGiftItemId()) {
        if (getGiftItemId()
            != other.getGiftItemId()) return false;
      }
      if (hasGiftRecord() != other.hasGiftRecord()) return false;
      if (hasGiftRecord()) {
        if (!getGiftRecord()
            .equals(other.getGiftRecord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGiftItemId()) {
        hash = (37 * hash) + GIFTITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getGiftItemId();
      }
      if (hasGiftRecord()) {
        hash = (37 * hash) + GIFTRECORD_FIELD_NUMBER;
        hash = (53 * hash) + getGiftRecord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanGift.AddClanGiftCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanGift.AddClanGiftCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AddClanGiftCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AddClanGiftCmd)
        com.yorha.proto.SsClanGift.AddClanGiftCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddClanGiftCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddClanGiftCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanGift.AddClanGiftCmd.class, com.yorha.proto.SsClanGift.AddClanGiftCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanGift.AddClanGiftCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getGiftRecordFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        giftItemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (giftRecordBuilder_ == null) {
          giftRecord_ = null;
        } else {
          giftRecordBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanGift.internal_static_com_yorha_proto_AddClanGiftCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddClanGiftCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanGift.AddClanGiftCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddClanGiftCmd build() {
        com.yorha.proto.SsClanGift.AddClanGiftCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanGift.AddClanGiftCmd buildPartial() {
        com.yorha.proto.SsClanGift.AddClanGiftCmd result = new com.yorha.proto.SsClanGift.AddClanGiftCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.giftItemId_ = giftItemId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (giftRecordBuilder_ == null) {
            result.giftRecord_ = giftRecord_;
          } else {
            result.giftRecord_ = giftRecordBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanGift.AddClanGiftCmd) {
          return mergeFrom((com.yorha.proto.SsClanGift.AddClanGiftCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanGift.AddClanGiftCmd other) {
        if (other == com.yorha.proto.SsClanGift.AddClanGiftCmd.getDefaultInstance()) return this;
        if (other.hasGiftItemId()) {
          setGiftItemId(other.getGiftItemId());
        }
        if (other.hasGiftRecord()) {
          mergeGiftRecord(other.getGiftRecord());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanGift.AddClanGiftCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanGift.AddClanGiftCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int giftItemId_ ;
      /**
       * <code>optional int32 giftItemId = 1;</code>
       * @return Whether the giftItemId field is set.
       */
      @java.lang.Override
      public boolean hasGiftItemId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 giftItemId = 1;</code>
       * @return The giftItemId.
       */
      @java.lang.Override
      public int getGiftItemId() {
        return giftItemId_;
      }
      /**
       * <code>optional int32 giftItemId = 1;</code>
       * @param value The giftItemId to set.
       * @return This builder for chaining.
       */
      public Builder setGiftItemId(int value) {
        bitField0_ |= 0x00000001;
        giftItemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 giftItemId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGiftItemId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        giftItemId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.ClanGiftRecord giftRecord_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.ClanGiftRecord, com.yorha.proto.Struct.ClanGiftRecord.Builder, com.yorha.proto.Struct.ClanGiftRecordOrBuilder> giftRecordBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       * @return Whether the giftRecord field is set.
       */
      public boolean hasGiftRecord() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       * @return The giftRecord.
       */
      public com.yorha.proto.Struct.ClanGiftRecord getGiftRecord() {
        if (giftRecordBuilder_ == null) {
          return giftRecord_ == null ? com.yorha.proto.Struct.ClanGiftRecord.getDefaultInstance() : giftRecord_;
        } else {
          return giftRecordBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       */
      public Builder setGiftRecord(com.yorha.proto.Struct.ClanGiftRecord value) {
        if (giftRecordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          giftRecord_ = value;
          onChanged();
        } else {
          giftRecordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       */
      public Builder setGiftRecord(
          com.yorha.proto.Struct.ClanGiftRecord.Builder builderForValue) {
        if (giftRecordBuilder_ == null) {
          giftRecord_ = builderForValue.build();
          onChanged();
        } else {
          giftRecordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       */
      public Builder mergeGiftRecord(com.yorha.proto.Struct.ClanGiftRecord value) {
        if (giftRecordBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              giftRecord_ != null &&
              giftRecord_ != com.yorha.proto.Struct.ClanGiftRecord.getDefaultInstance()) {
            giftRecord_ =
              com.yorha.proto.Struct.ClanGiftRecord.newBuilder(giftRecord_).mergeFrom(value).buildPartial();
          } else {
            giftRecord_ = value;
          }
          onChanged();
        } else {
          giftRecordBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       */
      public Builder clearGiftRecord() {
        if (giftRecordBuilder_ == null) {
          giftRecord_ = null;
          onChanged();
        } else {
          giftRecordBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       */
      public com.yorha.proto.Struct.ClanGiftRecord.Builder getGiftRecordBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getGiftRecordFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       */
      public com.yorha.proto.Struct.ClanGiftRecordOrBuilder getGiftRecordOrBuilder() {
        if (giftRecordBuilder_ != null) {
          return giftRecordBuilder_.getMessageOrBuilder();
        } else {
          return giftRecord_ == null ?
              com.yorha.proto.Struct.ClanGiftRecord.getDefaultInstance() : giftRecord_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanGiftRecord giftRecord = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.ClanGiftRecord, com.yorha.proto.Struct.ClanGiftRecord.Builder, com.yorha.proto.Struct.ClanGiftRecordOrBuilder> 
          getGiftRecordFieldBuilder() {
        if (giftRecordBuilder_ == null) {
          giftRecordBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.ClanGiftRecord, com.yorha.proto.Struct.ClanGiftRecord.Builder, com.yorha.proto.Struct.ClanGiftRecordOrBuilder>(
                  getGiftRecord(),
                  getParentForChildren(),
                  isClean());
          giftRecord_ = null;
        }
        return giftRecordBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AddClanGiftCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AddClanGiftCmd)
    private static final com.yorha.proto.SsClanGift.AddClanGiftCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanGift.AddClanGiftCmd();
    }

    public static com.yorha.proto.SsClanGift.AddClanGiftCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AddClanGiftCmd>
        PARSER = new com.google.protobuf.AbstractParser<AddClanGiftCmd>() {
      @java.lang.Override
      public AddClanGiftCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AddClanGiftCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AddClanGiftCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AddClanGiftCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanGift.AddClanGiftCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AddGiftPointAndKeyAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AddGiftPointAndKeyAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AddClanGiftCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AddClanGiftCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$ss_proto/gen/clan/ss_clan_gift.proto\022\017" +
      "com.yorha.proto\032 ss_proto/gen/common/str" +
      "uct.proto\032$ss_proto/gen/common/struct_ms" +
      "g.proto\"9\n\025AddGiftPointAndKeyAsk\022\020\n\010addP" +
      "oint\030\001 \001(\005\022\016\n\006addKey\030\002 \001(\005\"R\n\025AddGiftPoi" +
      "ntAndKeyAns\0229\n\014clanGiftInfo\030\001 \001(\0132#.com." +
      "yorha.proto.ClanGiftSharedInfo\"Y\n\016AddCla" +
      "nGiftCmd\022\022\n\ngiftItemId\030\001 \001(\005\0223\n\ngiftReco" +
      "rd\030\002 \001(\0132\037.com.yorha.proto.ClanGiftRecor" +
      "dB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AddGiftPointAndKeyAsk_descriptor,
        new java.lang.String[] { "AddPoint", "AddKey", });
    internal_static_com_yorha_proto_AddGiftPointAndKeyAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_AddGiftPointAndKeyAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AddGiftPointAndKeyAns_descriptor,
        new java.lang.String[] { "ClanGiftInfo", });
    internal_static_com_yorha_proto_AddClanGiftCmd_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_AddClanGiftCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AddClanGiftCmd_descriptor,
        new java.lang.String[] { "GiftItemId", "GiftRecord", });
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_city_army.proto

package com.yorha.proto;

public final class SsSceneCityArmy {
  private SsSceneCityArmy() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RepairCityWallAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RepairCityWallAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RepairCityWallAsk}
   */
  public static final class RepairCityWallAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RepairCityWallAsk)
      RepairCityWallAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RepairCityWallAsk.newBuilder() to construct.
    private RepairCityWallAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RepairCityWallAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RepairCityWallAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RepairCityWallAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk.class, com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk other = (com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RepairCityWallAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RepairCityWallAsk)
        com.yorha.proto.SsSceneCityArmy.RepairCityWallAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk.class, com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk build() {
        com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk result = new com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RepairCityWallAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RepairCityWallAsk)
    private static final com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RepairCityWallAsk>
        PARSER = new com.google.protobuf.AbstractParser<RepairCityWallAsk>() {
      @java.lang.Override
      public RepairCityWallAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RepairCityWallAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RepairCityWallAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RepairCityWallAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.RepairCityWallAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RepairCityWallAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RepairCityWallAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.RepairCityWallAns}
   */
  public static final class RepairCityWallAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RepairCityWallAns)
      RepairCityWallAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RepairCityWallAns.newBuilder() to construct.
    private RepairCityWallAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RepairCityWallAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RepairCityWallAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RepairCityWallAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.RepairCityWallAns.class, com.yorha.proto.SsSceneCityArmy.RepairCityWallAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.RepairCityWallAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.RepairCityWallAns other = (com.yorha.proto.SsSceneCityArmy.RepairCityWallAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.RepairCityWallAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RepairCityWallAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RepairCityWallAns)
        com.yorha.proto.SsSceneCityArmy.RepairCityWallAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.RepairCityWallAns.class, com.yorha.proto.SsSceneCityArmy.RepairCityWallAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.RepairCityWallAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_RepairCityWallAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.RepairCityWallAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.RepairCityWallAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.RepairCityWallAns build() {
        com.yorha.proto.SsSceneCityArmy.RepairCityWallAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.RepairCityWallAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.RepairCityWallAns result = new com.yorha.proto.SsSceneCityArmy.RepairCityWallAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.RepairCityWallAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.RepairCityWallAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.RepairCityWallAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.RepairCityWallAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.RepairCityWallAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.RepairCityWallAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RepairCityWallAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RepairCityWallAns)
    private static final com.yorha.proto.SsSceneCityArmy.RepairCityWallAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.RepairCityWallAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.RepairCityWallAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RepairCityWallAns>
        PARSER = new com.google.protobuf.AbstractParser<RepairCityWallAns>() {
      @java.lang.Override
      public RepairCityWallAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RepairCityWallAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RepairCityWallAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RepairCityWallAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.RepairCityWallAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OutFireCityWallAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OutFireCityWallAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OutFireCityWallAsk}
   */
  public static final class OutFireCityWallAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OutFireCityWallAsk)
      OutFireCityWallAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OutFireCityWallAsk.newBuilder() to construct.
    private OutFireCityWallAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OutFireCityWallAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OutFireCityWallAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OutFireCityWallAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk.class, com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk other = (com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OutFireCityWallAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OutFireCityWallAsk)
        com.yorha.proto.SsSceneCityArmy.OutFireCityWallAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk.class, com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk build() {
        com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk result = new com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OutFireCityWallAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OutFireCityWallAsk)
    private static final com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OutFireCityWallAsk>
        PARSER = new com.google.protobuf.AbstractParser<OutFireCityWallAsk>() {
      @java.lang.Override
      public OutFireCityWallAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OutFireCityWallAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OutFireCityWallAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OutFireCityWallAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.OutFireCityWallAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OutFireCityWallAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OutFireCityWallAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.OutFireCityWallAns}
   */
  public static final class OutFireCityWallAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OutFireCityWallAns)
      OutFireCityWallAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OutFireCityWallAns.newBuilder() to construct.
    private OutFireCityWallAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OutFireCityWallAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OutFireCityWallAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OutFireCityWallAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns.class, com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns other = (com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OutFireCityWallAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OutFireCityWallAns)
        com.yorha.proto.SsSceneCityArmy.OutFireCityWallAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns.class, com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_OutFireCityWallAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns build() {
        com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns result = new com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OutFireCityWallAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OutFireCityWallAns)
    private static final com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OutFireCityWallAns>
        PARSER = new com.google.protobuf.AbstractParser<OutFireCityWallAns>() {
      @java.lang.Override
      public OutFireCityWallAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OutFireCityWallAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OutFireCityWallAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OutFireCityWallAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.OutFireCityWallAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetCityFallAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SetCityFallAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SetCityFallAsk}
   */
  public static final class SetCityFallAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SetCityFallAsk)
      SetCityFallAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetCityFallAsk.newBuilder() to construct.
    private SetCityFallAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetCityFallAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetCityFallAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SetCityFallAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.SetCityFallAsk.class, com.yorha.proto.SsSceneCityArmy.SetCityFallAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.SetCityFallAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.SetCityFallAsk other = (com.yorha.proto.SsSceneCityArmy.SetCityFallAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.SetCityFallAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SetCityFallAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SetCityFallAsk)
        com.yorha.proto.SsSceneCityArmy.SetCityFallAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.SetCityFallAsk.class, com.yorha.proto.SsSceneCityArmy.SetCityFallAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.SetCityFallAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.SetCityFallAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.SetCityFallAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.SetCityFallAsk build() {
        com.yorha.proto.SsSceneCityArmy.SetCityFallAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.SetCityFallAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.SetCityFallAsk result = new com.yorha.proto.SsSceneCityArmy.SetCityFallAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.SetCityFallAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.SetCityFallAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.SetCityFallAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.SetCityFallAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.SetCityFallAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.SetCityFallAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SetCityFallAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SetCityFallAsk)
    private static final com.yorha.proto.SsSceneCityArmy.SetCityFallAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.SetCityFallAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SetCityFallAsk>
        PARSER = new com.google.protobuf.AbstractParser<SetCityFallAsk>() {
      @java.lang.Override
      public SetCityFallAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SetCityFallAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SetCityFallAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetCityFallAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.SetCityFallAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetCityFallAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SetCityFallAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 落堡的新位置，为空表示落堡失败或还未升天等
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
     * @return Whether the newPoint field is set.
     */
    boolean hasNewPoint();
    /**
     * <pre>
     * 落堡的新位置，为空表示落堡失败或还未升天等
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
     * @return The newPoint.
     */
    com.yorha.proto.Struct.Point getNewPoint();
    /**
     * <pre>
     * 落堡的新位置，为空表示落堡失败或还未升天等
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getNewPointOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SetCityFallAns}
   */
  public static final class SetCityFallAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SetCityFallAns)
      SetCityFallAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetCityFallAns.newBuilder() to construct.
    private SetCityFallAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetCityFallAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetCityFallAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SetCityFallAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = newPoint_.toBuilder();
              }
              newPoint_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(newPoint_);
                newPoint_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.SetCityFallAns.class, com.yorha.proto.SsSceneCityArmy.SetCityFallAns.Builder.class);
    }

    private int bitField0_;
    public static final int NEWPOINT_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.Point newPoint_;
    /**
     * <pre>
     * 落堡的新位置，为空表示落堡失败或还未升天等
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
     * @return Whether the newPoint field is set.
     */
    @java.lang.Override
    public boolean hasNewPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 落堡的新位置，为空表示落堡失败或还未升天等
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
     * @return The newPoint.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getNewPoint() {
      return newPoint_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : newPoint_;
    }
    /**
     * <pre>
     * 落堡的新位置，为空表示落堡失败或还未升天等
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getNewPointOrBuilder() {
      return newPoint_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : newPoint_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getNewPoint());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getNewPoint());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.SetCityFallAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.SetCityFallAns other = (com.yorha.proto.SsSceneCityArmy.SetCityFallAns) obj;

      if (hasNewPoint() != other.hasNewPoint()) return false;
      if (hasNewPoint()) {
        if (!getNewPoint()
            .equals(other.getNewPoint())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNewPoint()) {
        hash = (37 * hash) + NEWPOINT_FIELD_NUMBER;
        hash = (53 * hash) + getNewPoint().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.SetCityFallAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SetCityFallAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SetCityFallAns)
        com.yorha.proto.SsSceneCityArmy.SetCityFallAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.SetCityFallAns.class, com.yorha.proto.SsSceneCityArmy.SetCityFallAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.SetCityFallAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getNewPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (newPointBuilder_ == null) {
          newPoint_ = null;
        } else {
          newPointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_SetCityFallAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.SetCityFallAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.SetCityFallAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.SetCityFallAns build() {
        com.yorha.proto.SsSceneCityArmy.SetCityFallAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.SetCityFallAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.SetCityFallAns result = new com.yorha.proto.SsSceneCityArmy.SetCityFallAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (newPointBuilder_ == null) {
            result.newPoint_ = newPoint_;
          } else {
            result.newPoint_ = newPointBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.SetCityFallAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.SetCityFallAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.SetCityFallAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.SetCityFallAns.getDefaultInstance()) return this;
        if (other.hasNewPoint()) {
          mergeNewPoint(other.getNewPoint());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.SetCityFallAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.SetCityFallAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.Point newPoint_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> newPointBuilder_;
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       * @return Whether the newPoint field is set.
       */
      public boolean hasNewPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       * @return The newPoint.
       */
      public com.yorha.proto.Struct.Point getNewPoint() {
        if (newPointBuilder_ == null) {
          return newPoint_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : newPoint_;
        } else {
          return newPointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       */
      public Builder setNewPoint(com.yorha.proto.Struct.Point value) {
        if (newPointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          newPoint_ = value;
          onChanged();
        } else {
          newPointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       */
      public Builder setNewPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (newPointBuilder_ == null) {
          newPoint_ = builderForValue.build();
          onChanged();
        } else {
          newPointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       */
      public Builder mergeNewPoint(com.yorha.proto.Struct.Point value) {
        if (newPointBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              newPoint_ != null &&
              newPoint_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            newPoint_ =
              com.yorha.proto.Struct.Point.newBuilder(newPoint_).mergeFrom(value).buildPartial();
          } else {
            newPoint_ = value;
          }
          onChanged();
        } else {
          newPointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       */
      public Builder clearNewPoint() {
        if (newPointBuilder_ == null) {
          newPoint_ = null;
          onChanged();
        } else {
          newPointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getNewPointBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getNewPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getNewPointOrBuilder() {
        if (newPointBuilder_ != null) {
          return newPointBuilder_.getMessageOrBuilder();
        } else {
          return newPoint_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : newPoint_;
        }
      }
      /**
       * <pre>
       * 落堡的新位置，为空表示落堡失败或还未升天等
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point newPoint = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getNewPointFieldBuilder() {
        if (newPointBuilder_ == null) {
          newPointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getNewPoint(),
                  getParentForChildren(),
                  isClean());
          newPoint_ = null;
        }
        return newPointBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SetCityFallAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SetCityFallAns)
    private static final com.yorha.proto.SsSceneCityArmy.SetCityFallAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.SetCityFallAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.SetCityFallAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SetCityFallAns>
        PARSER = new com.google.protobuf.AbstractParser<SetCityFallAns>() {
      @java.lang.Override
      public SetCityFallAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SetCityFallAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SetCityFallAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetCityFallAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.SetCityFallAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MoveCityFixedAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MoveCityFixedAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int32 x = 2;</code>
     * @return Whether the x field is set.
     */
    boolean hasX();
    /**
     * <code>optional int32 x = 2;</code>
     * @return The x.
     */
    int getX();

    /**
     * <code>optional int32 y = 3;</code>
     * @return Whether the y field is set.
     */
    boolean hasY();
    /**
     * <code>optional int32 y = 3;</code>
     * @return The y.
     */
    int getY();

    /**
     * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
     * @return Whether the moveType field is set.
     */
    boolean hasMoveType();
    /**
     * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
     * @return The moveType.
     */
    com.yorha.proto.CommonEnum.MoveCityType getMoveType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MoveCityFixedAsk}
   */
  public static final class MoveCityFixedAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MoveCityFixedAsk)
      MoveCityFixedAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MoveCityFixedAsk.newBuilder() to construct.
    private MoveCityFixedAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MoveCityFixedAsk() {
      moveType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MoveCityFixedAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MoveCityFixedAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              x_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              y_ = input.readInt32();
              break;
            }
            case 32: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MoveCityType value = com.yorha.proto.CommonEnum.MoveCityType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(4, rawValue);
              } else {
                bitField0_ |= 0x00000008;
                moveType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk.class, com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int X_FIELD_NUMBER = 2;
    private int x_;
    /**
     * <code>optional int32 x = 2;</code>
     * @return Whether the x field is set.
     */
    @java.lang.Override
    public boolean hasX() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 x = 2;</code>
     * @return The x.
     */
    @java.lang.Override
    public int getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 3;
    private int y_;
    /**
     * <code>optional int32 y = 3;</code>
     * @return Whether the y field is set.
     */
    @java.lang.Override
    public boolean hasY() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 y = 3;</code>
     * @return The y.
     */
    @java.lang.Override
    public int getY() {
      return y_;
    }

    public static final int MOVETYPE_FIELD_NUMBER = 4;
    private int moveType_;
    /**
     * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
     * @return Whether the moveType field is set.
     */
    @java.lang.Override public boolean hasMoveType() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
     * @return The moveType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MoveCityType getMoveType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MoveCityType result = com.yorha.proto.CommonEnum.MoveCityType.valueOf(moveType_);
      return result == null ? com.yorha.proto.CommonEnum.MoveCityType.MCT_NORMAL : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, x_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, y_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeEnum(4, moveType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, x_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, y_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, moveType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk other = (com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasX() != other.hasX()) return false;
      if (hasX()) {
        if (getX()
            != other.getX()) return false;
      }
      if (hasY() != other.hasY()) return false;
      if (hasY()) {
        if (getY()
            != other.getY()) return false;
      }
      if (hasMoveType() != other.hasMoveType()) return false;
      if (hasMoveType()) {
        if (moveType_ != other.moveType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasX()) {
        hash = (37 * hash) + X_FIELD_NUMBER;
        hash = (53 * hash) + getX();
      }
      if (hasY()) {
        hash = (37 * hash) + Y_FIELD_NUMBER;
        hash = (53 * hash) + getY();
      }
      if (hasMoveType()) {
        hash = (37 * hash) + MOVETYPE_FIELD_NUMBER;
        hash = (53 * hash) + moveType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MoveCityFixedAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MoveCityFixedAsk)
        com.yorha.proto.SsSceneCityArmy.MoveCityFixedAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk.class, com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        x_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        y_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        moveType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk build() {
        com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk result = new com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.x_ = x_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.y_ = y_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.moveType_ = moveType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasX()) {
          setX(other.getX());
        }
        if (other.hasY()) {
          setY(other.getY());
        }
        if (other.hasMoveType()) {
          setMoveType(other.getMoveType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int x_ ;
      /**
       * <code>optional int32 x = 2;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 x = 2;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }
      /**
       * <code>optional int32 x = 2;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(int value) {
        bitField0_ |= 0x00000002;
        x_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 x = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        bitField0_ = (bitField0_ & ~0x00000002);
        x_ = 0;
        onChanged();
        return this;
      }

      private int y_ ;
      /**
       * <code>optional int32 y = 3;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 y = 3;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }
      /**
       * <code>optional int32 y = 3;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(int value) {
        bitField0_ |= 0x00000004;
        y_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 y = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        bitField0_ = (bitField0_ & ~0x00000004);
        y_ = 0;
        onChanged();
        return this;
      }

      private int moveType_ = 0;
      /**
       * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
       * @return Whether the moveType field is set.
       */
      @java.lang.Override public boolean hasMoveType() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
       * @return The moveType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MoveCityType getMoveType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MoveCityType result = com.yorha.proto.CommonEnum.MoveCityType.valueOf(moveType_);
        return result == null ? com.yorha.proto.CommonEnum.MoveCityType.MCT_NORMAL : result;
      }
      /**
       * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
       * @param value The moveType to set.
       * @return This builder for chaining.
       */
      public Builder setMoveType(com.yorha.proto.CommonEnum.MoveCityType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        moveType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearMoveType() {
        bitField0_ = (bitField0_ & ~0x00000008);
        moveType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MoveCityFixedAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MoveCityFixedAsk)
    private static final com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MoveCityFixedAsk>
        PARSER = new com.google.protobuf.AbstractParser<MoveCityFixedAsk>() {
      @java.lang.Override
      public MoveCityFixedAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MoveCityFixedAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MoveCityFixedAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MoveCityFixedAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.MoveCityFixedAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MoveCityFixedAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MoveCityFixedAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.MoveCityFixedAns}
   */
  public static final class MoveCityFixedAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MoveCityFixedAns)
      MoveCityFixedAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MoveCityFixedAns.newBuilder() to construct.
    private MoveCityFixedAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MoveCityFixedAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MoveCityFixedAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MoveCityFixedAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns.class, com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns other = (com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MoveCityFixedAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MoveCityFixedAns)
        com.yorha.proto.SsSceneCityArmy.MoveCityFixedAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns.class, com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityFixedAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns build() {
        com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns result = new com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MoveCityFixedAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MoveCityFixedAns)
    private static final com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MoveCityFixedAns>
        PARSER = new com.google.protobuf.AbstractParser<MoveCityFixedAns>() {
      @java.lang.Override
      public MoveCityFixedAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MoveCityFixedAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MoveCityFixedAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MoveCityFixedAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.MoveCityFixedAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MoveCityRandomAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MoveCityRandomAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MoveCityRandomAsk}
   */
  public static final class MoveCityRandomAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MoveCityRandomAsk)
      MoveCityRandomAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MoveCityRandomAsk.newBuilder() to construct.
    private MoveCityRandomAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MoveCityRandomAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MoveCityRandomAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MoveCityRandomAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk.class, com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk other = (com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MoveCityRandomAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MoveCityRandomAsk)
        com.yorha.proto.SsSceneCityArmy.MoveCityRandomAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk.class, com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk build() {
        com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk result = new com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MoveCityRandomAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MoveCityRandomAsk)
    private static final com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MoveCityRandomAsk>
        PARSER = new com.google.protobuf.AbstractParser<MoveCityRandomAsk>() {
      @java.lang.Override
      public MoveCityRandomAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MoveCityRandomAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MoveCityRandomAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MoveCityRandomAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.MoveCityRandomAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MoveCityRandomAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MoveCityRandomAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 x = 1;</code>
     * @return Whether the x field is set.
     */
    boolean hasX();
    /**
     * <code>optional int32 x = 1;</code>
     * @return The x.
     */
    int getX();

    /**
     * <code>optional int32 y = 2;</code>
     * @return Whether the y field is set.
     */
    boolean hasY();
    /**
     * <code>optional int32 y = 2;</code>
     * @return The y.
     */
    int getY();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MoveCityRandomAns}
   */
  public static final class MoveCityRandomAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MoveCityRandomAns)
      MoveCityRandomAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MoveCityRandomAns.newBuilder() to construct.
    private MoveCityRandomAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MoveCityRandomAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MoveCityRandomAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MoveCityRandomAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              x_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              y_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns.class, com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns.Builder.class);
    }

    private int bitField0_;
    public static final int X_FIELD_NUMBER = 1;
    private int x_;
    /**
     * <code>optional int32 x = 1;</code>
     * @return Whether the x field is set.
     */
    @java.lang.Override
    public boolean hasX() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 x = 1;</code>
     * @return The x.
     */
    @java.lang.Override
    public int getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 2;
    private int y_;
    /**
     * <code>optional int32 y = 2;</code>
     * @return Whether the y field is set.
     */
    @java.lang.Override
    public boolean hasY() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 y = 2;</code>
     * @return The y.
     */
    @java.lang.Override
    public int getY() {
      return y_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, x_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, y_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, x_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, y_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns other = (com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns) obj;

      if (hasX() != other.hasX()) return false;
      if (hasX()) {
        if (getX()
            != other.getX()) return false;
      }
      if (hasY() != other.hasY()) return false;
      if (hasY()) {
        if (getY()
            != other.getY()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasX()) {
        hash = (37 * hash) + X_FIELD_NUMBER;
        hash = (53 * hash) + getX();
      }
      if (hasY()) {
        hash = (37 * hash) + Y_FIELD_NUMBER;
        hash = (53 * hash) + getY();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MoveCityRandomAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MoveCityRandomAns)
        com.yorha.proto.SsSceneCityArmy.MoveCityRandomAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns.class, com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        x_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        y_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityRandomAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns build() {
        com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns result = new com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.x_ = x_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.y_ = y_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns.getDefaultInstance()) return this;
        if (other.hasX()) {
          setX(other.getX());
        }
        if (other.hasY()) {
          setY(other.getY());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int x_ ;
      /**
       * <code>optional int32 x = 1;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 x = 1;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }
      /**
       * <code>optional int32 x = 1;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(int value) {
        bitField0_ |= 0x00000001;
        x_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 x = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        bitField0_ = (bitField0_ & ~0x00000001);
        x_ = 0;
        onChanged();
        return this;
      }

      private int y_ ;
      /**
       * <code>optional int32 y = 2;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 y = 2;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }
      /**
       * <code>optional int32 y = 2;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(int value) {
        bitField0_ |= 0x00000002;
        y_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 y = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        bitField0_ = (bitField0_ & ~0x00000002);
        y_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MoveCityRandomAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MoveCityRandomAns)
    private static final com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MoveCityRandomAns>
        PARSER = new com.google.protobuf.AbstractParser<MoveCityRandomAns>() {
      @java.lang.Override
      public MoveCityRandomAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MoveCityRandomAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MoveCityRandomAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MoveCityRandomAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.MoveCityRandomAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MoveCityVerifyAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MoveCityVerifyAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int32 x = 2;</code>
     * @return Whether the x field is set.
     */
    boolean hasX();
    /**
     * <code>optional int32 x = 2;</code>
     * @return The x.
     */
    int getX();

    /**
     * <code>optional int32 y = 3;</code>
     * @return Whether the y field is set.
     */
    boolean hasY();
    /**
     * <code>optional int32 y = 3;</code>
     * @return The y.
     */
    int getY();

    /**
     * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
     * @return Whether the moveType field is set.
     */
    boolean hasMoveType();
    /**
     * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
     * @return The moveType.
     */
    com.yorha.proto.CommonEnum.MoveCityType getMoveType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MoveCityVerifyAsk}
   */
  public static final class MoveCityVerifyAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MoveCityVerifyAsk)
      MoveCityVerifyAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MoveCityVerifyAsk.newBuilder() to construct.
    private MoveCityVerifyAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MoveCityVerifyAsk() {
      moveType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MoveCityVerifyAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MoveCityVerifyAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              x_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              y_ = input.readInt32();
              break;
            }
            case 32: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MoveCityType value = com.yorha.proto.CommonEnum.MoveCityType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(4, rawValue);
              } else {
                bitField0_ |= 0x00000008;
                moveType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk.class, com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int X_FIELD_NUMBER = 2;
    private int x_;
    /**
     * <code>optional int32 x = 2;</code>
     * @return Whether the x field is set.
     */
    @java.lang.Override
    public boolean hasX() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 x = 2;</code>
     * @return The x.
     */
    @java.lang.Override
    public int getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 3;
    private int y_;
    /**
     * <code>optional int32 y = 3;</code>
     * @return Whether the y field is set.
     */
    @java.lang.Override
    public boolean hasY() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 y = 3;</code>
     * @return The y.
     */
    @java.lang.Override
    public int getY() {
      return y_;
    }

    public static final int MOVETYPE_FIELD_NUMBER = 4;
    private int moveType_;
    /**
     * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
     * @return Whether the moveType field is set.
     */
    @java.lang.Override public boolean hasMoveType() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
     * @return The moveType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MoveCityType getMoveType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MoveCityType result = com.yorha.proto.CommonEnum.MoveCityType.valueOf(moveType_);
      return result == null ? com.yorha.proto.CommonEnum.MoveCityType.MCT_NORMAL : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, x_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, y_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeEnum(4, moveType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, x_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, y_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, moveType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk other = (com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasX() != other.hasX()) return false;
      if (hasX()) {
        if (getX()
            != other.getX()) return false;
      }
      if (hasY() != other.hasY()) return false;
      if (hasY()) {
        if (getY()
            != other.getY()) return false;
      }
      if (hasMoveType() != other.hasMoveType()) return false;
      if (hasMoveType()) {
        if (moveType_ != other.moveType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasX()) {
        hash = (37 * hash) + X_FIELD_NUMBER;
        hash = (53 * hash) + getX();
      }
      if (hasY()) {
        hash = (37 * hash) + Y_FIELD_NUMBER;
        hash = (53 * hash) + getY();
      }
      if (hasMoveType()) {
        hash = (37 * hash) + MOVETYPE_FIELD_NUMBER;
        hash = (53 * hash) + moveType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MoveCityVerifyAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MoveCityVerifyAsk)
        com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk.class, com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        x_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        y_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        moveType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk build() {
        com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk result = new com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.x_ = x_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.y_ = y_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.moveType_ = moveType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasX()) {
          setX(other.getX());
        }
        if (other.hasY()) {
          setY(other.getY());
        }
        if (other.hasMoveType()) {
          setMoveType(other.getMoveType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int x_ ;
      /**
       * <code>optional int32 x = 2;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 x = 2;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }
      /**
       * <code>optional int32 x = 2;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(int value) {
        bitField0_ |= 0x00000002;
        x_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 x = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        bitField0_ = (bitField0_ & ~0x00000002);
        x_ = 0;
        onChanged();
        return this;
      }

      private int y_ ;
      /**
       * <code>optional int32 y = 3;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 y = 3;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }
      /**
       * <code>optional int32 y = 3;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(int value) {
        bitField0_ |= 0x00000004;
        y_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 y = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        bitField0_ = (bitField0_ & ~0x00000004);
        y_ = 0;
        onChanged();
        return this;
      }

      private int moveType_ = 0;
      /**
       * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
       * @return Whether the moveType field is set.
       */
      @java.lang.Override public boolean hasMoveType() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
       * @return The moveType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MoveCityType getMoveType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MoveCityType result = com.yorha.proto.CommonEnum.MoveCityType.valueOf(moveType_);
        return result == null ? com.yorha.proto.CommonEnum.MoveCityType.MCT_NORMAL : result;
      }
      /**
       * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
       * @param value The moveType to set.
       * @return This builder for chaining.
       */
      public Builder setMoveType(com.yorha.proto.CommonEnum.MoveCityType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        moveType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MoveCityType moveType = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearMoveType() {
        bitField0_ = (bitField0_ & ~0x00000008);
        moveType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MoveCityVerifyAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MoveCityVerifyAsk)
    private static final com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MoveCityVerifyAsk>
        PARSER = new com.google.protobuf.AbstractParser<MoveCityVerifyAsk>() {
      @java.lang.Override
      public MoveCityVerifyAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MoveCityVerifyAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MoveCityVerifyAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MoveCityVerifyAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MoveCityVerifyAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MoveCityVerifyAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return Whether the errorCode field is set.
     */
    boolean hasErrorCode();
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return The errorCode.
     */
    int getErrorCode();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MoveCityVerifyAns}
   */
  public static final class MoveCityVerifyAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MoveCityVerifyAns)
      MoveCityVerifyAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MoveCityVerifyAns.newBuilder() to construct.
    private MoveCityVerifyAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MoveCityVerifyAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MoveCityVerifyAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MoveCityVerifyAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              errorCode_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns.class, com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns.Builder.class);
    }

    private int bitField0_;
    public static final int ERRORCODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return Whether the errorCode field is set.
     */
    @java.lang.Override
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return The errorCode.
     */
    @java.lang.Override
    public int getErrorCode() {
      return errorCode_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, errorCode_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorCode_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns other = (com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns) obj;

      if (hasErrorCode() != other.hasErrorCode()) return false;
      if (hasErrorCode()) {
        if (getErrorCode()
            != other.getErrorCode()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasErrorCode()) {
        hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
        hash = (53 * hash) + getErrorCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MoveCityVerifyAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MoveCityVerifyAns)
        com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns.class, com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_MoveCityVerifyAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns build() {
        com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns result = new com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.errorCode_ = errorCode_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns.getDefaultInstance()) return this;
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return Whether the errorCode field is set.
       */
      @java.lang.Override
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000001;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorCode_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MoveCityVerifyAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MoveCityVerifyAns)
    private static final com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MoveCityVerifyAns>
        PARSER = new com.google.protobuf.AbstractParser<MoveCityVerifyAns>() {
      @java.lang.Override
      public MoveCityVerifyAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MoveCityVerifyAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MoveCityVerifyAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MoveCityVerifyAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.MoveCityVerifyAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreateArmyCheckAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateArmyCheckAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     * @return Whether the param field is set.
     */
    boolean hasParam();
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     * @return The param.
     */
    com.yorha.proto.StructPlayer.CreateArmy_C2S_Param getParam();
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     */
    com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateArmyCheckAsk}
   */
  public static final class CreateArmyCheckAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateArmyCheckAsk)
      CreateArmyCheckAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateArmyCheckAsk.newBuilder() to construct.
    private CreateArmyCheckAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateArmyCheckAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateArmyCheckAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateArmyCheckAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk.class, com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int PARAM_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPlayer.CreateArmy_C2S_Param param_;
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     * @return Whether the param field is set.
     */
    @java.lang.Override
    public boolean hasParam() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     * @return The param.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.CreateArmy_C2S_Param getParam() {
      return param_ == null ? com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance() : param_;
    }
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder getParamOrBuilder() {
      return param_ == null ? com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance() : param_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getParam());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getParam());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk other = (com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasParam() != other.hasParam()) return false;
      if (hasParam()) {
        if (!getParam()
            .equals(other.getParam())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasParam()) {
        hash = (37 * hash) + PARAM_FIELD_NUMBER;
        hash = (53 * hash) + getParam().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateArmyCheckAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateArmyCheckAsk)
        com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk.class, com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (paramBuilder_ == null) {
          param_ = null;
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk build() {
        com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk result = new com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (paramBuilder_ == null) {
            result.param_ = param_;
          } else {
            result.param_ = paramBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPlayer.CreateArmy_C2S_Param param_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.CreateArmy_C2S_Param, com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder, com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       * @return Whether the param field is set.
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       * @return The param.
       */
      public com.yorha.proto.StructPlayer.CreateArmy_C2S_Param getParam() {
        if (paramBuilder_ == null) {
          return param_ == null ? com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance() : param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public Builder setParam(com.yorha.proto.StructPlayer.CreateArmy_C2S_Param value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public Builder setParam(
          com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public Builder mergeParam(com.yorha.proto.StructPlayer.CreateArmy_C2S_Param value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              param_ != null &&
              param_ != com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance()) {
            param_ =
              com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = null;
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder getParamBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_ == null ?
              com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance() : param_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.CreateArmy_C2S_Param, com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder, com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayer.CreateArmy_C2S_Param, com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder, com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder>(
                  getParam(),
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateArmyCheckAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateArmyCheckAsk)
    private static final com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateArmyCheckAsk>
        PARSER = new com.google.protobuf.AbstractParser<CreateArmyCheckAsk>() {
      @java.lang.Override
      public CreateArmyCheckAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateArmyCheckAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateArmyCheckAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateArmyCheckAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreateArmyCheckAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateArmyCheckAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 如果是攻打野怪，需要消耗的体力值（不代表最终需要消耗的，因为英雄或许有天赋可以减免消耗）
     * </pre>
     *
     * <code>optional int32 monsterNeedEnergy = 1;</code>
     * @return Whether the monsterNeedEnergy field is set.
     */
    boolean hasMonsterNeedEnergy();
    /**
     * <pre>
     * 如果是攻打野怪，需要消耗的体力值（不代表最终需要消耗的，因为英雄或许有天赋可以减免消耗）
     * </pre>
     *
     * <code>optional int32 monsterNeedEnergy = 1;</code>
     * @return The monsterNeedEnergy.
     */
    int getMonsterNeedEnergy();

    /**
     * <pre>
     * 野怪的配置id
     * </pre>
     *
     * <code>optional int32 monsterId = 2;</code>
     * @return Whether the monsterId field is set.
     */
    boolean hasMonsterId();
    /**
     * <pre>
     * 野怪的配置id
     * </pre>
     *
     * <code>optional int32 monsterId = 2;</code>
     * @return The monsterId.
     */
    int getMonsterId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateArmyCheckAns}
   */
  public static final class CreateArmyCheckAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateArmyCheckAns)
      CreateArmyCheckAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateArmyCheckAns.newBuilder() to construct.
    private CreateArmyCheckAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateArmyCheckAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateArmyCheckAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateArmyCheckAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              monsterNeedEnergy_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              monsterId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns.class, com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns.Builder.class);
    }

    private int bitField0_;
    public static final int MONSTERNEEDENERGY_FIELD_NUMBER = 1;
    private int monsterNeedEnergy_;
    /**
     * <pre>
     * 如果是攻打野怪，需要消耗的体力值（不代表最终需要消耗的，因为英雄或许有天赋可以减免消耗）
     * </pre>
     *
     * <code>optional int32 monsterNeedEnergy = 1;</code>
     * @return Whether the monsterNeedEnergy field is set.
     */
    @java.lang.Override
    public boolean hasMonsterNeedEnergy() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 如果是攻打野怪，需要消耗的体力值（不代表最终需要消耗的，因为英雄或许有天赋可以减免消耗）
     * </pre>
     *
     * <code>optional int32 monsterNeedEnergy = 1;</code>
     * @return The monsterNeedEnergy.
     */
    @java.lang.Override
    public int getMonsterNeedEnergy() {
      return monsterNeedEnergy_;
    }

    public static final int MONSTERID_FIELD_NUMBER = 2;
    private int monsterId_;
    /**
     * <pre>
     * 野怪的配置id
     * </pre>
     *
     * <code>optional int32 monsterId = 2;</code>
     * @return Whether the monsterId field is set.
     */
    @java.lang.Override
    public boolean hasMonsterId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 野怪的配置id
     * </pre>
     *
     * <code>optional int32 monsterId = 2;</code>
     * @return The monsterId.
     */
    @java.lang.Override
    public int getMonsterId() {
      return monsterId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, monsterNeedEnergy_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, monsterId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, monsterNeedEnergy_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, monsterId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns other = (com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns) obj;

      if (hasMonsterNeedEnergy() != other.hasMonsterNeedEnergy()) return false;
      if (hasMonsterNeedEnergy()) {
        if (getMonsterNeedEnergy()
            != other.getMonsterNeedEnergy()) return false;
      }
      if (hasMonsterId() != other.hasMonsterId()) return false;
      if (hasMonsterId()) {
        if (getMonsterId()
            != other.getMonsterId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMonsterNeedEnergy()) {
        hash = (37 * hash) + MONSTERNEEDENERGY_FIELD_NUMBER;
        hash = (53 * hash) + getMonsterNeedEnergy();
      }
      if (hasMonsterId()) {
        hash = (37 * hash) + MONSTERID_FIELD_NUMBER;
        hash = (53 * hash) + getMonsterId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateArmyCheckAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateArmyCheckAns)
        com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns.class, com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        monsterNeedEnergy_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        monsterId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreateArmyCheckAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns build() {
        com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns result = new com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.monsterNeedEnergy_ = monsterNeedEnergy_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.monsterId_ = monsterId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns.getDefaultInstance()) return this;
        if (other.hasMonsterNeedEnergy()) {
          setMonsterNeedEnergy(other.getMonsterNeedEnergy());
        }
        if (other.hasMonsterId()) {
          setMonsterId(other.getMonsterId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int monsterNeedEnergy_ ;
      /**
       * <pre>
       * 如果是攻打野怪，需要消耗的体力值（不代表最终需要消耗的，因为英雄或许有天赋可以减免消耗）
       * </pre>
       *
       * <code>optional int32 monsterNeedEnergy = 1;</code>
       * @return Whether the monsterNeedEnergy field is set.
       */
      @java.lang.Override
      public boolean hasMonsterNeedEnergy() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 如果是攻打野怪，需要消耗的体力值（不代表最终需要消耗的，因为英雄或许有天赋可以减免消耗）
       * </pre>
       *
       * <code>optional int32 monsterNeedEnergy = 1;</code>
       * @return The monsterNeedEnergy.
       */
      @java.lang.Override
      public int getMonsterNeedEnergy() {
        return monsterNeedEnergy_;
      }
      /**
       * <pre>
       * 如果是攻打野怪，需要消耗的体力值（不代表最终需要消耗的，因为英雄或许有天赋可以减免消耗）
       * </pre>
       *
       * <code>optional int32 monsterNeedEnergy = 1;</code>
       * @param value The monsterNeedEnergy to set.
       * @return This builder for chaining.
       */
      public Builder setMonsterNeedEnergy(int value) {
        bitField0_ |= 0x00000001;
        monsterNeedEnergy_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 如果是攻打野怪，需要消耗的体力值（不代表最终需要消耗的，因为英雄或许有天赋可以减免消耗）
       * </pre>
       *
       * <code>optional int32 monsterNeedEnergy = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonsterNeedEnergy() {
        bitField0_ = (bitField0_ & ~0x00000001);
        monsterNeedEnergy_ = 0;
        onChanged();
        return this;
      }

      private int monsterId_ ;
      /**
       * <pre>
       * 野怪的配置id
       * </pre>
       *
       * <code>optional int32 monsterId = 2;</code>
       * @return Whether the monsterId field is set.
       */
      @java.lang.Override
      public boolean hasMonsterId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 野怪的配置id
       * </pre>
       *
       * <code>optional int32 monsterId = 2;</code>
       * @return The monsterId.
       */
      @java.lang.Override
      public int getMonsterId() {
        return monsterId_;
      }
      /**
       * <pre>
       * 野怪的配置id
       * </pre>
       *
       * <code>optional int32 monsterId = 2;</code>
       * @param value The monsterId to set.
       * @return This builder for chaining.
       */
      public Builder setMonsterId(int value) {
        bitField0_ |= 0x00000002;
        monsterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 野怪的配置id
       * </pre>
       *
       * <code>optional int32 monsterId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonsterId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        monsterId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateArmyCheckAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateArmyCheckAns)
    private static final com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateArmyCheckAns>
        PARSER = new com.google.protobuf.AbstractParser<CreateArmyCheckAns>() {
      @java.lang.Override
      public CreateArmyCheckAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateArmyCheckAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateArmyCheckAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateArmyCheckAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.CreateArmyCheckAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreatePlayerArmyAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreatePlayerArmyAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     * @return Whether the param field is set.
     */
    boolean hasParam();
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     * @return The param.
     */
    com.yorha.proto.StructPlayer.CreateArmy_C2S_Param getParam();
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     */
    com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder getParamOrBuilder();

    /**
     * <pre>
     * 打野真实扣掉的体力
     * </pre>
     *
     * <code>optional int32 costEnergy = 3;</code>
     * @return Whether the costEnergy field is set.
     */
    boolean hasCostEnergy();
    /**
     * <pre>
     * 打野真实扣掉的体力
     * </pre>
     *
     * <code>optional int32 costEnergy = 3;</code>
     * @return The costEnergy.
     */
    int getCostEnergy();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreatePlayerArmyAsk}
   */
  public static final class CreatePlayerArmyAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreatePlayerArmyAsk)
      CreatePlayerArmyAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreatePlayerArmyAsk.newBuilder() to construct.
    private CreatePlayerArmyAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreatePlayerArmyAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreatePlayerArmyAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreatePlayerArmyAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              costEnergy_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk.class, com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int PARAM_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPlayer.CreateArmy_C2S_Param param_;
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     * @return Whether the param field is set.
     */
    @java.lang.Override
    public boolean hasParam() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     * @return The param.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.CreateArmy_C2S_Param getParam() {
      return param_ == null ? com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance() : param_;
    }
    /**
     * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder getParamOrBuilder() {
      return param_ == null ? com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance() : param_;
    }

    public static final int COSTENERGY_FIELD_NUMBER = 3;
    private int costEnergy_;
    /**
     * <pre>
     * 打野真实扣掉的体力
     * </pre>
     *
     * <code>optional int32 costEnergy = 3;</code>
     * @return Whether the costEnergy field is set.
     */
    @java.lang.Override
    public boolean hasCostEnergy() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 打野真实扣掉的体力
     * </pre>
     *
     * <code>optional int32 costEnergy = 3;</code>
     * @return The costEnergy.
     */
    @java.lang.Override
    public int getCostEnergy() {
      return costEnergy_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getParam());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, costEnergy_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getParam());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, costEnergy_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk other = (com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasParam() != other.hasParam()) return false;
      if (hasParam()) {
        if (!getParam()
            .equals(other.getParam())) return false;
      }
      if (hasCostEnergy() != other.hasCostEnergy()) return false;
      if (hasCostEnergy()) {
        if (getCostEnergy()
            != other.getCostEnergy()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasParam()) {
        hash = (37 * hash) + PARAM_FIELD_NUMBER;
        hash = (53 * hash) + getParam().hashCode();
      }
      if (hasCostEnergy()) {
        hash = (37 * hash) + COSTENERGY_FIELD_NUMBER;
        hash = (53 * hash) + getCostEnergy();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreatePlayerArmyAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreatePlayerArmyAsk)
        com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk.class, com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (paramBuilder_ == null) {
          param_ = null;
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        costEnergy_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk build() {
        com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk result = new com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (paramBuilder_ == null) {
            result.param_ = param_;
          } else {
            result.param_ = paramBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.costEnergy_ = costEnergy_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        if (other.hasCostEnergy()) {
          setCostEnergy(other.getCostEnergy());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPlayer.CreateArmy_C2S_Param param_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.CreateArmy_C2S_Param, com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder, com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder> paramBuilder_;
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       * @return Whether the param field is set.
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       * @return The param.
       */
      public com.yorha.proto.StructPlayer.CreateArmy_C2S_Param getParam() {
        if (paramBuilder_ == null) {
          return param_ == null ? com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance() : param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public Builder setParam(com.yorha.proto.StructPlayer.CreateArmy_C2S_Param value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public Builder setParam(
          com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public Builder mergeParam(com.yorha.proto.StructPlayer.CreateArmy_C2S_Param value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              param_ != null &&
              param_ != com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance()) {
            param_ =
              com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = null;
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder getParamBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      public com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_ == null ?
              com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.getDefaultInstance() : param_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.CreateArmy_C2S_Param param = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.CreateArmy_C2S_Param, com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder, com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayer.CreateArmy_C2S_Param, com.yorha.proto.StructPlayer.CreateArmy_C2S_Param.Builder, com.yorha.proto.StructPlayer.CreateArmy_C2S_ParamOrBuilder>(
                  getParam(),
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      private int costEnergy_ ;
      /**
       * <pre>
       * 打野真实扣掉的体力
       * </pre>
       *
       * <code>optional int32 costEnergy = 3;</code>
       * @return Whether the costEnergy field is set.
       */
      @java.lang.Override
      public boolean hasCostEnergy() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 打野真实扣掉的体力
       * </pre>
       *
       * <code>optional int32 costEnergy = 3;</code>
       * @return The costEnergy.
       */
      @java.lang.Override
      public int getCostEnergy() {
        return costEnergy_;
      }
      /**
       * <pre>
       * 打野真实扣掉的体力
       * </pre>
       *
       * <code>optional int32 costEnergy = 3;</code>
       * @param value The costEnergy to set.
       * @return This builder for chaining.
       */
      public Builder setCostEnergy(int value) {
        bitField0_ |= 0x00000004;
        costEnergy_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 打野真实扣掉的体力
       * </pre>
       *
       * <code>optional int32 costEnergy = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCostEnergy() {
        bitField0_ = (bitField0_ & ~0x00000004);
        costEnergy_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreatePlayerArmyAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreatePlayerArmyAsk)
    private static final com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreatePlayerArmyAsk>
        PARSER = new com.google.protobuf.AbstractParser<CreatePlayerArmyAsk>() {
      @java.lang.Override
      public CreatePlayerArmyAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreatePlayerArmyAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreatePlayerArmyAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreatePlayerArmyAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreatePlayerArmyAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreatePlayerArmyAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 armyId = 1;</code>
     * @return Whether the armyId field is set.
     */
    boolean hasArmyId();
    /**
     * <code>optional int64 armyId = 1;</code>
     * @return The armyId.
     */
    long getArmyId();

    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return The point.
     */
    com.yorha.proto.Struct.Point getPoint();
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreatePlayerArmyAns}
   */
  public static final class CreatePlayerArmyAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreatePlayerArmyAns)
      CreatePlayerArmyAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreatePlayerArmyAns.newBuilder() to construct.
    private CreatePlayerArmyAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreatePlayerArmyAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreatePlayerArmyAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreatePlayerArmyAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              armyId_ = input.readInt64();
              break;
            }
            case 26: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns.class, com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns.Builder.class);
    }

    private int bitField0_;
    public static final int ARMYID_FIELD_NUMBER = 1;
    private long armyId_;
    /**
     * <code>optional int64 armyId = 1;</code>
     * @return Whether the armyId field is set.
     */
    @java.lang.Override
    public boolean hasArmyId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 armyId = 1;</code>
     * @return The armyId.
     */
    @java.lang.Override
    public long getArmyId() {
      return armyId_;
    }

    public static final int POINT_FIELD_NUMBER = 3;
    private com.yorha.proto.Struct.Point point_;
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, armyId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(3, getPoint());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, armyId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getPoint());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns other = (com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns) obj;

      if (hasArmyId() != other.hasArmyId()) return false;
      if (hasArmyId()) {
        if (getArmyId()
            != other.getArmyId()) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasArmyId()) {
        hash = (37 * hash) + ARMYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getArmyId());
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreatePlayerArmyAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreatePlayerArmyAns)
        com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns.class, com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        armyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_CreatePlayerArmyAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns build() {
        com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns result = new com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.armyId_ = armyId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns.getDefaultInstance()) return this;
        if (other.hasArmyId()) {
          setArmyId(other.getArmyId());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long armyId_ ;
      /**
       * <code>optional int64 armyId = 1;</code>
       * @return Whether the armyId field is set.
       */
      @java.lang.Override
      public boolean hasArmyId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 armyId = 1;</code>
       * @return The armyId.
       */
      @java.lang.Override
      public long getArmyId() {
        return armyId_;
      }
      /**
       * <code>optional int64 armyId = 1;</code>
       * @param value The armyId to set.
       * @return This builder for chaining.
       */
      public Builder setArmyId(long value) {
        bitField0_ |= 0x00000001;
        armyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 armyId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearArmyId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        armyId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Point point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       * @return The point.
       */
      public com.yorha.proto.Struct.Point getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder setPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder setPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder mergePoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            point_ =
              com.yorha.proto.Struct.Point.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreatePlayerArmyAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreatePlayerArmyAns)
    private static final com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreatePlayerArmyAns>
        PARSER = new com.google.protobuf.AbstractParser<CreatePlayerArmyAns>() {
      @java.lang.Override
      public CreatePlayerArmyAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreatePlayerArmyAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreatePlayerArmyAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreatePlayerArmyAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.CreatePlayerArmyAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeArmyActionCheckAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeArmyActionCheckAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeArmyActionCheckAsk}
   */
  public static final class ChangeArmyActionCheckAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeArmyActionCheckAsk)
      ChangeArmyActionCheckAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeArmyActionCheckAsk.newBuilder() to construct.
    private ChangeArmyActionCheckAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeArmyActionCheckAsk() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeArmyActionCheckAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeArmyActionCheckAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              msgBytes_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk.class, com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int MSGBYTES_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, msgBytes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, msgBytes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk other = (com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeArmyActionCheckAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeArmyActionCheckAsk)
        com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk.class, com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk build() {
        com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk result = new com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msgBytes_ = msgBytes_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeArmyActionCheckAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeArmyActionCheckAsk)
    private static final com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeArmyActionCheckAsk>
        PARSER = new com.google.protobuf.AbstractParser<ChangeArmyActionCheckAsk>() {
      @java.lang.Override
      public ChangeArmyActionCheckAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeArmyActionCheckAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeArmyActionCheckAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeArmyActionCheckAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeArmyActionCheckAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeArmyActionCheckAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 如果是攻打野怪，需要消耗的体力值
     * </pre>
     *
     * <code>optional int32 needEnergy = 1;</code>
     * @return Whether the needEnergy field is set.
     */
    boolean hasNeedEnergy();
    /**
     * <pre>
     * 如果是攻打野怪，需要消耗的体力值
     * </pre>
     *
     * <code>optional int32 needEnergy = 1;</code>
     * @return The needEnergy.
     */
    int getNeedEnergy();

    /**
     * <pre>
     * 野怪配置id
     * </pre>
     *
     * <code>optional int32 monsterId = 2;</code>
     * @return Whether the monsterId field is set.
     */
    boolean hasMonsterId();
    /**
     * <pre>
     * 野怪配置id
     * </pre>
     *
     * <code>optional int32 monsterId = 2;</code>
     * @return The monsterId.
     */
    int getMonsterId();

    /**
     * <pre>
     * 连杀数
     * </pre>
     *
     * <code>optional int32 killStreak = 3;</code>
     * @return Whether the killStreak field is set.
     */
    boolean hasKillStreak();
    /**
     * <pre>
     * 连杀数
     * </pre>
     *
     * <code>optional int32 killStreak = 3;</code>
     * @return The killStreak.
     */
    int getKillStreak();

    /**
     * <code>optional int32 mainHeroId = 4;</code>
     * @return Whether the mainHeroId field is set.
     */
    boolean hasMainHeroId();
    /**
     * <code>optional int32 mainHeroId = 4;</code>
     * @return The mainHeroId.
     */
    int getMainHeroId();

    /**
     * <code>optional int32 deputyHeroId = 5;</code>
     * @return Whether the deputyHeroId field is set.
     */
    boolean hasDeputyHeroId();
    /**
     * <code>optional int32 deputyHeroId = 5;</code>
     * @return The deputyHeroId.
     */
    int getDeputyHeroId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeArmyActionCheckAns}
   */
  public static final class ChangeArmyActionCheckAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeArmyActionCheckAns)
      ChangeArmyActionCheckAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeArmyActionCheckAns.newBuilder() to construct.
    private ChangeArmyActionCheckAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeArmyActionCheckAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeArmyActionCheckAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeArmyActionCheckAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              needEnergy_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              monsterId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              killStreak_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              mainHeroId_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              deputyHeroId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns.class, com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns.Builder.class);
    }

    private int bitField0_;
    public static final int NEEDENERGY_FIELD_NUMBER = 1;
    private int needEnergy_;
    /**
     * <pre>
     * 如果是攻打野怪，需要消耗的体力值
     * </pre>
     *
     * <code>optional int32 needEnergy = 1;</code>
     * @return Whether the needEnergy field is set.
     */
    @java.lang.Override
    public boolean hasNeedEnergy() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 如果是攻打野怪，需要消耗的体力值
     * </pre>
     *
     * <code>optional int32 needEnergy = 1;</code>
     * @return The needEnergy.
     */
    @java.lang.Override
    public int getNeedEnergy() {
      return needEnergy_;
    }

    public static final int MONSTERID_FIELD_NUMBER = 2;
    private int monsterId_;
    /**
     * <pre>
     * 野怪配置id
     * </pre>
     *
     * <code>optional int32 monsterId = 2;</code>
     * @return Whether the monsterId field is set.
     */
    @java.lang.Override
    public boolean hasMonsterId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 野怪配置id
     * </pre>
     *
     * <code>optional int32 monsterId = 2;</code>
     * @return The monsterId.
     */
    @java.lang.Override
    public int getMonsterId() {
      return monsterId_;
    }

    public static final int KILLSTREAK_FIELD_NUMBER = 3;
    private int killStreak_;
    /**
     * <pre>
     * 连杀数
     * </pre>
     *
     * <code>optional int32 killStreak = 3;</code>
     * @return Whether the killStreak field is set.
     */
    @java.lang.Override
    public boolean hasKillStreak() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 连杀数
     * </pre>
     *
     * <code>optional int32 killStreak = 3;</code>
     * @return The killStreak.
     */
    @java.lang.Override
    public int getKillStreak() {
      return killStreak_;
    }

    public static final int MAINHEROID_FIELD_NUMBER = 4;
    private int mainHeroId_;
    /**
     * <code>optional int32 mainHeroId = 4;</code>
     * @return Whether the mainHeroId field is set.
     */
    @java.lang.Override
    public boolean hasMainHeroId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 mainHeroId = 4;</code>
     * @return The mainHeroId.
     */
    @java.lang.Override
    public int getMainHeroId() {
      return mainHeroId_;
    }

    public static final int DEPUTYHEROID_FIELD_NUMBER = 5;
    private int deputyHeroId_;
    /**
     * <code>optional int32 deputyHeroId = 5;</code>
     * @return Whether the deputyHeroId field is set.
     */
    @java.lang.Override
    public boolean hasDeputyHeroId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 deputyHeroId = 5;</code>
     * @return The deputyHeroId.
     */
    @java.lang.Override
    public int getDeputyHeroId() {
      return deputyHeroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, needEnergy_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, monsterId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, killStreak_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, mainHeroId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, deputyHeroId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, needEnergy_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, monsterId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, killStreak_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, mainHeroId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, deputyHeroId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns other = (com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns) obj;

      if (hasNeedEnergy() != other.hasNeedEnergy()) return false;
      if (hasNeedEnergy()) {
        if (getNeedEnergy()
            != other.getNeedEnergy()) return false;
      }
      if (hasMonsterId() != other.hasMonsterId()) return false;
      if (hasMonsterId()) {
        if (getMonsterId()
            != other.getMonsterId()) return false;
      }
      if (hasKillStreak() != other.hasKillStreak()) return false;
      if (hasKillStreak()) {
        if (getKillStreak()
            != other.getKillStreak()) return false;
      }
      if (hasMainHeroId() != other.hasMainHeroId()) return false;
      if (hasMainHeroId()) {
        if (getMainHeroId()
            != other.getMainHeroId()) return false;
      }
      if (hasDeputyHeroId() != other.hasDeputyHeroId()) return false;
      if (hasDeputyHeroId()) {
        if (getDeputyHeroId()
            != other.getDeputyHeroId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNeedEnergy()) {
        hash = (37 * hash) + NEEDENERGY_FIELD_NUMBER;
        hash = (53 * hash) + getNeedEnergy();
      }
      if (hasMonsterId()) {
        hash = (37 * hash) + MONSTERID_FIELD_NUMBER;
        hash = (53 * hash) + getMonsterId();
      }
      if (hasKillStreak()) {
        hash = (37 * hash) + KILLSTREAK_FIELD_NUMBER;
        hash = (53 * hash) + getKillStreak();
      }
      if (hasMainHeroId()) {
        hash = (37 * hash) + MAINHEROID_FIELD_NUMBER;
        hash = (53 * hash) + getMainHeroId();
      }
      if (hasDeputyHeroId()) {
        hash = (37 * hash) + DEPUTYHEROID_FIELD_NUMBER;
        hash = (53 * hash) + getDeputyHeroId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeArmyActionCheckAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeArmyActionCheckAns)
        com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns.class, com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        needEnergy_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        monsterId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        killStreak_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        mainHeroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        deputyHeroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangeArmyActionCheckAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns build() {
        com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns result = new com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.needEnergy_ = needEnergy_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.monsterId_ = monsterId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.killStreak_ = killStreak_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.mainHeroId_ = mainHeroId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.deputyHeroId_ = deputyHeroId_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns.getDefaultInstance()) return this;
        if (other.hasNeedEnergy()) {
          setNeedEnergy(other.getNeedEnergy());
        }
        if (other.hasMonsterId()) {
          setMonsterId(other.getMonsterId());
        }
        if (other.hasKillStreak()) {
          setKillStreak(other.getKillStreak());
        }
        if (other.hasMainHeroId()) {
          setMainHeroId(other.getMainHeroId());
        }
        if (other.hasDeputyHeroId()) {
          setDeputyHeroId(other.getDeputyHeroId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int needEnergy_ ;
      /**
       * <pre>
       * 如果是攻打野怪，需要消耗的体力值
       * </pre>
       *
       * <code>optional int32 needEnergy = 1;</code>
       * @return Whether the needEnergy field is set.
       */
      @java.lang.Override
      public boolean hasNeedEnergy() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 如果是攻打野怪，需要消耗的体力值
       * </pre>
       *
       * <code>optional int32 needEnergy = 1;</code>
       * @return The needEnergy.
       */
      @java.lang.Override
      public int getNeedEnergy() {
        return needEnergy_;
      }
      /**
       * <pre>
       * 如果是攻打野怪，需要消耗的体力值
       * </pre>
       *
       * <code>optional int32 needEnergy = 1;</code>
       * @param value The needEnergy to set.
       * @return This builder for chaining.
       */
      public Builder setNeedEnergy(int value) {
        bitField0_ |= 0x00000001;
        needEnergy_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 如果是攻打野怪，需要消耗的体力值
       * </pre>
       *
       * <code>optional int32 needEnergy = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNeedEnergy() {
        bitField0_ = (bitField0_ & ~0x00000001);
        needEnergy_ = 0;
        onChanged();
        return this;
      }

      private int monsterId_ ;
      /**
       * <pre>
       * 野怪配置id
       * </pre>
       *
       * <code>optional int32 monsterId = 2;</code>
       * @return Whether the monsterId field is set.
       */
      @java.lang.Override
      public boolean hasMonsterId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 野怪配置id
       * </pre>
       *
       * <code>optional int32 monsterId = 2;</code>
       * @return The monsterId.
       */
      @java.lang.Override
      public int getMonsterId() {
        return monsterId_;
      }
      /**
       * <pre>
       * 野怪配置id
       * </pre>
       *
       * <code>optional int32 monsterId = 2;</code>
       * @param value The monsterId to set.
       * @return This builder for chaining.
       */
      public Builder setMonsterId(int value) {
        bitField0_ |= 0x00000002;
        monsterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 野怪配置id
       * </pre>
       *
       * <code>optional int32 monsterId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonsterId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        monsterId_ = 0;
        onChanged();
        return this;
      }

      private int killStreak_ ;
      /**
       * <pre>
       * 连杀数
       * </pre>
       *
       * <code>optional int32 killStreak = 3;</code>
       * @return Whether the killStreak field is set.
       */
      @java.lang.Override
      public boolean hasKillStreak() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 连杀数
       * </pre>
       *
       * <code>optional int32 killStreak = 3;</code>
       * @return The killStreak.
       */
      @java.lang.Override
      public int getKillStreak() {
        return killStreak_;
      }
      /**
       * <pre>
       * 连杀数
       * </pre>
       *
       * <code>optional int32 killStreak = 3;</code>
       * @param value The killStreak to set.
       * @return This builder for chaining.
       */
      public Builder setKillStreak(int value) {
        bitField0_ |= 0x00000004;
        killStreak_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 连杀数
       * </pre>
       *
       * <code>optional int32 killStreak = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearKillStreak() {
        bitField0_ = (bitField0_ & ~0x00000004);
        killStreak_ = 0;
        onChanged();
        return this;
      }

      private int mainHeroId_ ;
      /**
       * <code>optional int32 mainHeroId = 4;</code>
       * @return Whether the mainHeroId field is set.
       */
      @java.lang.Override
      public boolean hasMainHeroId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 mainHeroId = 4;</code>
       * @return The mainHeroId.
       */
      @java.lang.Override
      public int getMainHeroId() {
        return mainHeroId_;
      }
      /**
       * <code>optional int32 mainHeroId = 4;</code>
       * @param value The mainHeroId to set.
       * @return This builder for chaining.
       */
      public Builder setMainHeroId(int value) {
        bitField0_ |= 0x00000008;
        mainHeroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 mainHeroId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearMainHeroId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        mainHeroId_ = 0;
        onChanged();
        return this;
      }

      private int deputyHeroId_ ;
      /**
       * <code>optional int32 deputyHeroId = 5;</code>
       * @return Whether the deputyHeroId field is set.
       */
      @java.lang.Override
      public boolean hasDeputyHeroId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 deputyHeroId = 5;</code>
       * @return The deputyHeroId.
       */
      @java.lang.Override
      public int getDeputyHeroId() {
        return deputyHeroId_;
      }
      /**
       * <code>optional int32 deputyHeroId = 5;</code>
       * @param value The deputyHeroId to set.
       * @return This builder for chaining.
       */
      public Builder setDeputyHeroId(int value) {
        bitField0_ |= 0x00000010;
        deputyHeroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 deputyHeroId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeputyHeroId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        deputyHeroId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeArmyActionCheckAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeArmyActionCheckAns)
    private static final com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeArmyActionCheckAns>
        PARSER = new com.google.protobuf.AbstractParser<ChangeArmyActionCheckAns>() {
      @java.lang.Override
      public ChangeArmyActionCheckAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeArmyActionCheckAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeArmyActionCheckAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeArmyActionCheckAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.ChangeArmyActionCheckAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangePlayerArmyActionAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangePlayerArmyActionAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();

    /**
     * <pre>
     * 打野真实扣掉的体力
     * </pre>
     *
     * <code>optional int32 costEnergy = 3;</code>
     * @return Whether the costEnergy field is set.
     */
    boolean hasCostEnergy();
    /**
     * <pre>
     * 打野真实扣掉的体力
     * </pre>
     *
     * <code>optional int32 costEnergy = 3;</code>
     * @return The costEnergy.
     */
    int getCostEnergy();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangePlayerArmyActionAsk}
   */
  public static final class ChangePlayerArmyActionAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangePlayerArmyActionAsk)
      ChangePlayerArmyActionAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangePlayerArmyActionAsk.newBuilder() to construct.
    private ChangePlayerArmyActionAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangePlayerArmyActionAsk() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangePlayerArmyActionAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangePlayerArmyActionAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              msgBytes_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              costEnergy_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk.class, com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int MSGBYTES_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    public static final int COSTENERGY_FIELD_NUMBER = 3;
    private int costEnergy_;
    /**
     * <pre>
     * 打野真实扣掉的体力
     * </pre>
     *
     * <code>optional int32 costEnergy = 3;</code>
     * @return Whether the costEnergy field is set.
     */
    @java.lang.Override
    public boolean hasCostEnergy() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 打野真实扣掉的体力
     * </pre>
     *
     * <code>optional int32 costEnergy = 3;</code>
     * @return The costEnergy.
     */
    @java.lang.Override
    public int getCostEnergy() {
      return costEnergy_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, msgBytes_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, costEnergy_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, msgBytes_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, costEnergy_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk other = (com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (hasCostEnergy() != other.hasCostEnergy()) return false;
      if (hasCostEnergy()) {
        if (getCostEnergy()
            != other.getCostEnergy()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      if (hasCostEnergy()) {
        hash = (37 * hash) + COSTENERGY_FIELD_NUMBER;
        hash = (53 * hash) + getCostEnergy();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangePlayerArmyActionAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangePlayerArmyActionAsk)
        com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk.class, com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        costEnergy_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk build() {
        com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk result = new com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msgBytes_ = msgBytes_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.costEnergy_ = costEnergy_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        if (other.hasCostEnergy()) {
          setCostEnergy(other.getCostEnergy());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }

      private int costEnergy_ ;
      /**
       * <pre>
       * 打野真实扣掉的体力
       * </pre>
       *
       * <code>optional int32 costEnergy = 3;</code>
       * @return Whether the costEnergy field is set.
       */
      @java.lang.Override
      public boolean hasCostEnergy() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 打野真实扣掉的体力
       * </pre>
       *
       * <code>optional int32 costEnergy = 3;</code>
       * @return The costEnergy.
       */
      @java.lang.Override
      public int getCostEnergy() {
        return costEnergy_;
      }
      /**
       * <pre>
       * 打野真实扣掉的体力
       * </pre>
       *
       * <code>optional int32 costEnergy = 3;</code>
       * @param value The costEnergy to set.
       * @return This builder for chaining.
       */
      public Builder setCostEnergy(int value) {
        bitField0_ |= 0x00000004;
        costEnergy_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 打野真实扣掉的体力
       * </pre>
       *
       * <code>optional int32 costEnergy = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCostEnergy() {
        bitField0_ = (bitField0_ & ~0x00000004);
        costEnergy_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangePlayerArmyActionAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangePlayerArmyActionAsk)
    private static final com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangePlayerArmyActionAsk>
        PARSER = new com.google.protobuf.AbstractParser<ChangePlayerArmyActionAsk>() {
      @java.lang.Override
      public ChangePlayerArmyActionAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangePlayerArmyActionAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangePlayerArmyActionAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangePlayerArmyActionAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangePlayerArmyActionAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangePlayerArmyActionAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return The point.
     */
    com.yorha.proto.Struct.Point getPoint();
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangePlayerArmyActionAns}
   */
  public static final class ChangePlayerArmyActionAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangePlayerArmyActionAns)
      ChangePlayerArmyActionAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangePlayerArmyActionAns.newBuilder() to construct.
    private ChangePlayerArmyActionAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangePlayerArmyActionAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangePlayerArmyActionAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangePlayerArmyActionAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns.class, com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns.Builder.class);
    }

    private int bitField0_;
    public static final int POINT_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.Point point_;
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 用来做特效的
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPoint());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPoint());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns other = (com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns) obj;

      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangePlayerArmyActionAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangePlayerArmyActionAns)
        com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns.class, com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ChangePlayerArmyActionAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns build() {
        com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns result = new com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns.getDefaultInstance()) return this;
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.Point point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       * @return The point.
       */
      public com.yorha.proto.Struct.Point getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder setPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder setPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder mergePoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            point_ =
              com.yorha.proto.Struct.Point.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 用来做特效的
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangePlayerArmyActionAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangePlayerArmyActionAns)
    private static final com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangePlayerArmyActionAns>
        PARSER = new com.google.protobuf.AbstractParser<ChangePlayerArmyActionAns>() {
      @java.lang.Override
      public ChangePlayerArmyActionAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangePlayerArmyActionAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangePlayerArmyActionAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangePlayerArmyActionAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.ChangePlayerArmyActionAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ForcedDefeatArmyAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ForcedDefeatArmyAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 armyId = 2;</code>
     * @return Whether the armyId field is set.
     */
    boolean hasArmyId();
    /**
     * <code>optional int64 armyId = 2;</code>
     * @return The armyId.
     */
    long getArmyId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ForcedDefeatArmyAsk}
   */
  public static final class ForcedDefeatArmyAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ForcedDefeatArmyAsk)
      ForcedDefeatArmyAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ForcedDefeatArmyAsk.newBuilder() to construct.
    private ForcedDefeatArmyAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ForcedDefeatArmyAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ForcedDefeatArmyAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ForcedDefeatArmyAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              armyId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk.class, com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ARMYID_FIELD_NUMBER = 2;
    private long armyId_;
    /**
     * <code>optional int64 armyId = 2;</code>
     * @return Whether the armyId field is set.
     */
    @java.lang.Override
    public boolean hasArmyId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 armyId = 2;</code>
     * @return The armyId.
     */
    @java.lang.Override
    public long getArmyId() {
      return armyId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, armyId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, armyId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk other = (com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasArmyId() != other.hasArmyId()) return false;
      if (hasArmyId()) {
        if (getArmyId()
            != other.getArmyId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasArmyId()) {
        hash = (37 * hash) + ARMYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getArmyId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ForcedDefeatArmyAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ForcedDefeatArmyAsk)
        com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk.class, com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        armyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk build() {
        com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk buildPartial() {
        com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk result = new com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.armyId_ = armyId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk other) {
        if (other == com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasArmyId()) {
          setArmyId(other.getArmyId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long armyId_ ;
      /**
       * <code>optional int64 armyId = 2;</code>
       * @return Whether the armyId field is set.
       */
      @java.lang.Override
      public boolean hasArmyId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 armyId = 2;</code>
       * @return The armyId.
       */
      @java.lang.Override
      public long getArmyId() {
        return armyId_;
      }
      /**
       * <code>optional int64 armyId = 2;</code>
       * @param value The armyId to set.
       * @return This builder for chaining.
       */
      public Builder setArmyId(long value) {
        bitField0_ |= 0x00000002;
        armyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 armyId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearArmyId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        armyId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ForcedDefeatArmyAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ForcedDefeatArmyAsk)
    private static final com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk();
    }

    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ForcedDefeatArmyAsk>
        PARSER = new com.google.protobuf.AbstractParser<ForcedDefeatArmyAsk>() {
      @java.lang.Override
      public ForcedDefeatArmyAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ForcedDefeatArmyAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ForcedDefeatArmyAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ForcedDefeatArmyAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ForcedDefeatArmyAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ForcedDefeatArmyAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.ForcedDefeatArmyAns}
   */
  public static final class ForcedDefeatArmyAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ForcedDefeatArmyAns)
      ForcedDefeatArmyAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ForcedDefeatArmyAns.newBuilder() to construct.
    private ForcedDefeatArmyAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ForcedDefeatArmyAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ForcedDefeatArmyAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ForcedDefeatArmyAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns.class, com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns other = (com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ForcedDefeatArmyAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ForcedDefeatArmyAns)
        com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns.class, com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCityArmy.internal_static_com_yorha_proto_ForcedDefeatArmyAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns build() {
        com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns buildPartial() {
        com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns result = new com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns) {
          return mergeFrom((com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns other) {
        if (other == com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ForcedDefeatArmyAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ForcedDefeatArmyAns)
    private static final com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns();
    }

    public static com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ForcedDefeatArmyAns>
        PARSER = new com.google.protobuf.AbstractParser<ForcedDefeatArmyAns>() {
      @java.lang.Override
      public ForcedDefeatArmyAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ForcedDefeatArmyAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ForcedDefeatArmyAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ForcedDefeatArmyAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCityArmy.ForcedDefeatArmyAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RepairCityWallAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RepairCityWallAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RepairCityWallAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RepairCityWallAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OutFireCityWallAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OutFireCityWallAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OutFireCityWallAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OutFireCityWallAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SetCityFallAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SetCityFallAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SetCityFallAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SetCityFallAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MoveCityFixedAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MoveCityFixedAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MoveCityFixedAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MoveCityFixedAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MoveCityRandomAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MoveCityRandomAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MoveCityRandomAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MoveCityRandomAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MoveCityVerifyAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MoveCityVerifyAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MoveCityVerifyAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MoveCityVerifyAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateArmyCheckAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateArmyCheckAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateArmyCheckAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateArmyCheckAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreatePlayerArmyAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreatePlayerArmyAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreatePlayerArmyAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreatePlayerArmyAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeArmyActionCheckAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeArmyActionCheckAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangePlayerArmyActionAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangePlayerArmyActionAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ForcedDefeatArmyAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ForcedDefeatArmyAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ForcedDefeatArmyAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ForcedDefeatArmyAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/scene/ss_scene_city_army." +
      "proto\022\017com.yorha.proto\032%ss_proto/gen/com" +
      "mon/common_enum.proto\032 ss_proto/gen/comm" +
      "on/struct.proto\032\'ss_proto/gen/common/str" +
      "uct_player.proto\"%\n\021RepairCityWallAsk\022\020\n" +
      "\010playerId\030\001 \001(\003\"\023\n\021RepairCityWallAns\"&\n\022" +
      "OutFireCityWallAsk\022\020\n\010playerId\030\001 \001(\003\"\024\n\022" +
      "OutFireCityWallAns\"\"\n\016SetCityFallAsk\022\020\n\010" +
      "playerId\030\001 \001(\003\":\n\016SetCityFallAns\022(\n\010newP" +
      "oint\030\001 \001(\0132\026.com.yorha.proto.Point\"k\n\020Mo" +
      "veCityFixedAsk\022\020\n\010playerId\030\001 \001(\003\022\t\n\001x\030\002 " +
      "\001(\005\022\t\n\001y\030\003 \001(\005\022/\n\010moveType\030\004 \001(\0162\035.com.y" +
      "orha.proto.MoveCityType\"\022\n\020MoveCityFixed" +
      "Ans\"%\n\021MoveCityRandomAsk\022\020\n\010playerId\030\001 \001" +
      "(\003\")\n\021MoveCityRandomAns\022\t\n\001x\030\001 \001(\005\022\t\n\001y\030" +
      "\002 \001(\005\"l\n\021MoveCityVerifyAsk\022\020\n\010playerId\030\001" +
      " \001(\003\022\t\n\001x\030\002 \001(\005\022\t\n\001y\030\003 \001(\005\022/\n\010moveType\030\004" +
      " \001(\0162\035.com.yorha.proto.MoveCityType\"&\n\021M" +
      "oveCityVerifyAns\022\021\n\terrorCode\030\001 \001(\005\"\\\n\022C" +
      "reateArmyCheckAsk\022\020\n\010playerId\030\001 \001(\003\0224\n\005p" +
      "aram\030\002 \001(\0132%.com.yorha.proto.CreateArmy_" +
      "C2S_Param\"B\n\022CreateArmyCheckAns\022\031\n\021monst" +
      "erNeedEnergy\030\001 \001(\005\022\021\n\tmonsterId\030\002 \001(\005\"q\n" +
      "\023CreatePlayerArmyAsk\022\020\n\010playerId\030\001 \001(\003\0224" +
      "\n\005param\030\002 \001(\0132%.com.yorha.proto.CreateAr" +
      "my_C2S_Param\022\022\n\ncostEnergy\030\003 \001(\005\"L\n\023Crea" +
      "tePlayerArmyAns\022\016\n\006armyId\030\001 \001(\003\022%\n\005point" +
      "\030\003 \001(\0132\026.com.yorha.proto.Point\">\n\030Change" +
      "ArmyActionCheckAsk\022\020\n\010playerId\030\001 \001(\003\022\020\n\010" +
      "msgBytes\030\002 \001(\014\"\177\n\030ChangeArmyActionCheckA" +
      "ns\022\022\n\nneedEnergy\030\001 \001(\005\022\021\n\tmonsterId\030\002 \001(" +
      "\005\022\022\n\nkillStreak\030\003 \001(\005\022\022\n\nmainHeroId\030\004 \001(" +
      "\005\022\024\n\014deputyHeroId\030\005 \001(\005\"S\n\031ChangePlayerA" +
      "rmyActionAsk\022\020\n\010playerId\030\001 \001(\003\022\020\n\010msgByt" +
      "es\030\002 \001(\014\022\022\n\ncostEnergy\030\003 \001(\005\"B\n\031ChangePl" +
      "ayerArmyActionAns\022%\n\005point\030\001 \001(\0132\026.com.y" +
      "orha.proto.Point\"7\n\023ForcedDefeatArmyAsk\022" +
      "\020\n\010playerId\030\001 \001(\003\022\016\n\006armyId\030\002 \001(\003\"\025\n\023For" +
      "cedDefeatArmyAnsB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructPlayer.getDescriptor(),
        });
    internal_static_com_yorha_proto_RepairCityWallAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_RepairCityWallAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RepairCityWallAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_RepairCityWallAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_RepairCityWallAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RepairCityWallAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_OutFireCityWallAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_OutFireCityWallAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OutFireCityWallAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_OutFireCityWallAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_OutFireCityWallAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OutFireCityWallAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_SetCityFallAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_SetCityFallAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SetCityFallAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_SetCityFallAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_SetCityFallAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SetCityFallAns_descriptor,
        new java.lang.String[] { "NewPoint", });
    internal_static_com_yorha_proto_MoveCityFixedAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_MoveCityFixedAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MoveCityFixedAsk_descriptor,
        new java.lang.String[] { "PlayerId", "X", "Y", "MoveType", });
    internal_static_com_yorha_proto_MoveCityFixedAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_MoveCityFixedAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MoveCityFixedAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_MoveCityRandomAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_MoveCityRandomAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MoveCityRandomAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_MoveCityRandomAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_MoveCityRandomAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MoveCityRandomAns_descriptor,
        new java.lang.String[] { "X", "Y", });
    internal_static_com_yorha_proto_MoveCityVerifyAsk_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_MoveCityVerifyAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MoveCityVerifyAsk_descriptor,
        new java.lang.String[] { "PlayerId", "X", "Y", "MoveType", });
    internal_static_com_yorha_proto_MoveCityVerifyAns_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_MoveCityVerifyAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MoveCityVerifyAns_descriptor,
        new java.lang.String[] { "ErrorCode", });
    internal_static_com_yorha_proto_CreateArmyCheckAsk_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_CreateArmyCheckAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateArmyCheckAsk_descriptor,
        new java.lang.String[] { "PlayerId", "Param", });
    internal_static_com_yorha_proto_CreateArmyCheckAns_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_CreateArmyCheckAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateArmyCheckAns_descriptor,
        new java.lang.String[] { "MonsterNeedEnergy", "MonsterId", });
    internal_static_com_yorha_proto_CreatePlayerArmyAsk_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_CreatePlayerArmyAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreatePlayerArmyAsk_descriptor,
        new java.lang.String[] { "PlayerId", "Param", "CostEnergy", });
    internal_static_com_yorha_proto_CreatePlayerArmyAns_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_yorha_proto_CreatePlayerArmyAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreatePlayerArmyAns_descriptor,
        new java.lang.String[] { "ArmyId", "Point", });
    internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeArmyActionCheckAsk_descriptor,
        new java.lang.String[] { "PlayerId", "MsgBytes", });
    internal_static_com_yorha_proto_ChangeArmyActionCheckAns_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_com_yorha_proto_ChangeArmyActionCheckAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeArmyActionCheckAns_descriptor,
        new java.lang.String[] { "NeedEnergy", "MonsterId", "KillStreak", "MainHeroId", "DeputyHeroId", });
    internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangePlayerArmyActionAsk_descriptor,
        new java.lang.String[] { "PlayerId", "MsgBytes", "CostEnergy", });
    internal_static_com_yorha_proto_ChangePlayerArmyActionAns_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_com_yorha_proto_ChangePlayerArmyActionAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangePlayerArmyActionAns_descriptor,
        new java.lang.String[] { "Point", });
    internal_static_com_yorha_proto_ForcedDefeatArmyAsk_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_com_yorha_proto_ForcedDefeatArmyAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ForcedDefeatArmyAsk_descriptor,
        new java.lang.String[] { "PlayerId", "ArmyId", });
    internal_static_com_yorha_proto_ForcedDefeatArmyAns_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_com_yorha_proto_ForcedDefeatArmyAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ForcedDefeatArmyAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructPlayer.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

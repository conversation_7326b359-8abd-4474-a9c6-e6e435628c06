// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/city/cityPB.proto

package com.yorha.proto;

public final class CityPB {
  private CityPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CityEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CityEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     * @return The point.
     */
    com.yorha.proto.StructPB.PointPB getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 2;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 2;</code>
     * @return The ownerId.
     */
    long getOwnerId();

    /**
     * <pre>
     * 城堡等级
     * </pre>
     *
     * <code>optional int32 level = 3;</code>
     * @return Whether the level field is set.
     */
    boolean hasLevel();
    /**
     * <pre>
     * 城堡等级
     * </pre>
     *
     * <code>optional int32 level = 3;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 4;</code>
     * @return Whether the camp field is set.
     */
    boolean hasCamp();
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 4;</code>
     * @return The camp.
     */
    com.yorha.proto.CommonEnum.Camp getCamp();

    /**
     * <pre>
     * 城墙状态  正常/冒烟/燃烧
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityWallState wallState = 5;</code>
     * @return Whether the wallState field is set.
     */
    boolean hasWallState();
    /**
     * <pre>
     * 城墙状态  正常/冒烟/燃烧
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityWallState wallState = 5;</code>
     * @return The wallState.
     */
    com.yorha.proto.CommonEnum.CityWallState getWallState();

    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     * @return Whether the troop field is set.
     */
    boolean hasTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     * @return The troop.
     */
    com.yorha.proto.StructPlayerPB.TroopPB getTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     */
    com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder();

    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     * @return Whether the battle field is set.
     */
    boolean hasBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     * @return The battle.
     */
    com.yorha.proto.StructBattlePB.BattlePB getBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     */
    com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder();

    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 8;</code>
     * @return Whether the clanSname field is set.
     */
    boolean hasClanSname();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 8;</code>
     * @return The clanSname.
     */
    java.lang.String getClanSname();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 8;</code>
     * @return The bytes for clanSname.
     */
    com.google.protobuf.ByteString
        getClanSnameBytes();

    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 9;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 9;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <pre>
     * 城池owner的守军数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
     * @return Whether the garrison field is set.
     */
    boolean hasGarrison();
    /**
     * <pre>
     * 城池owner的守军数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
     * @return The garrison.
     */
    com.yorha.proto.StructPB.CityGarrisonPB getGarrison();
    /**
     * <pre>
     * 城池owner的守军数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
     */
    com.yorha.proto.StructPB.CityGarrisonPBOrBuilder getGarrisonOrBuilder();

    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
     * @return Whether the buffSys field is set.
     */
    boolean hasBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
     * @return The buffSys.
     */
    com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
     */
    com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder();

    /**
     * <pre>
     * 和平护盾
     * </pre>
     *
     * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
     * @return Whether the peaceShield field is set.
     */
    boolean hasPeaceShield();
    /**
     * <pre>
     * 和平护盾
     * </pre>
     *
     * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
     * @return The peaceShield.
     */
    com.yorha.proto.StructBattlePB.PeaceShieldPB getPeaceShield();
    /**
     * <pre>
     * 和平护盾
     * </pre>
     *
     * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
     */
    com.yorha.proto.StructBattlePB.PeaceShieldPBOrBuilder getPeaceShieldOrBuilder();

    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
     * @return Whether the devBuffSys field is set.
     */
    boolean hasDevBuffSys();
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
     * @return The devBuffSys.
     */
    com.yorha.proto.StructBattlePB.SceneDevBuffSysPB getDevBuffSys();
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
     */
    com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder getDevBuffSysOrBuilder();

    /**
     * <pre>
     * 升天理由 None即没有升天
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityAscendReason ascendReason = 15;</code>
     * @return Whether the ascendReason field is set.
     */
    boolean hasAscendReason();
    /**
     * <pre>
     * 升天理由 None即没有升天
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityAscendReason ascendReason = 15;</code>
     * @return The ascendReason.
     */
    com.yorha.proto.CommonEnum.CityAscendReason getAscendReason();

    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
     * @return Whether the safeGuard field is set.
     */
    boolean hasSafeGuard();
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
     * @return The safeGuard.
     */
    com.yorha.proto.StructPB.SpecialSafeGuardPB getSafeGuard();
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
     */
    com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder getSafeGuardOrBuilder();

    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
     * @return Whether the cardHead field is set.
     */
    boolean hasCardHead();
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
     * @return The cardHead.
     */
    com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead();
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
     */
    com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder();

    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
     * @return Whether the arrow field is set.
     */
    boolean hasArrow();
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
     * @return The arrow.
     */
    com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow();
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
     */
    com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder();

    /**
     * <pre>
     * 时代等级
     * </pre>
     *
     * <code>optional int32 eraLevel = 19;</code>
     * @return Whether the eraLevel field is set.
     */
    boolean hasEraLevel();
    /**
     * <pre>
     * 时代等级
     * </pre>
     *
     * <code>optional int32 eraLevel = 19;</code>
     * @return The eraLevel.
     */
    int getEraLevel();

    /**
     * <pre>
     * 配置id
     * </pre>
     *
     * <code>optional int32 templateId = 20;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 配置id
     * </pre>
     *
     * <code>optional int32 templateId = 20;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 警戒塔血量
     * </pre>
     *
     * <code>optional int32 guardTowerHp = 21;</code>
     * @return Whether the guardTowerHp field is set.
     */
    boolean hasGuardTowerHp();
    /**
     * <pre>
     * 警戒塔血量
     * </pre>
     *
     * <code>optional int32 guardTowerHp = 21;</code>
     * @return The guardTowerHp.
     */
    int getGuardTowerHp();

    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     * @return Whether the expression field is set.
     */
    boolean hasExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     * @return The expression.
     */
    com.yorha.proto.StructPB.ExpressionPB getExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     */
    com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder();

    /**
     * <pre>
     * 个人旗帜id
     * </pre>
     *
     * <code>optional int32 pFlagId = 23;</code>
     * @return Whether the pFlagId field is set.
     */
    boolean hasPFlagId();
    /**
     * <pre>
     * 个人旗帜id
     * </pre>
     *
     * <code>optional int32 pFlagId = 23;</code>
     * @return The pFlagId.
     */
    int getPFlagId();

    /**
     * <pre>
     * 皮肤配表id
     * </pre>
     *
     * <code>optional int32 dressTemplateId = 24;</code>
     * @return Whether the dressTemplateId field is set.
     */
    boolean hasDressTemplateId();
    /**
     * <pre>
     * 皮肤配表id
     * </pre>
     *
     * <code>optional int32 dressTemplateId = 24;</code>
     * @return The dressTemplateId.
     */
    int getDressTemplateId();

    /**
     * <pre>
     * 玩家主堡身上的王国信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
     * @return Whether the cityKingdomModel field is set.
     */
    boolean hasCityKingdomModel();
    /**
     * <pre>
     * 玩家主堡身上的王国信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
     * @return The cityKingdomModel.
     */
    com.yorha.proto.CityPB.CityKingdomModelPB getCityKingdomModel();
    /**
     * <pre>
     * 玩家主堡身上的王国信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
     */
    com.yorha.proto.CityPB.CityKingdomModelPBOrBuilder getCityKingdomModelOrBuilder();

    /**
     * <code>optional int32 nameplateId = 27;</code>
     * @return Whether the nameplateId field is set.
     */
    boolean hasNameplateId();
    /**
     * <code>optional int32 nameplateId = 27;</code>
     * @return The nameplateId.
     */
    int getNameplateId();

    /**
     * <pre>
     * 所属玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 29;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 所属玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 29;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CityEntityPB}
   */
  public static final class CityEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CityEntityPB)
      CityEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CityEntityPB.newBuilder() to construct.
    private CityEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CityEntityPB() {
      camp_ = 0;
      wallState_ = 0;
      clanSname_ = "";
      ascendReason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CityEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CityEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ownerId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              level_ = input.readInt32();
              break;
            }
            case 32: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Camp value = com.yorha.proto.CommonEnum.Camp.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(4, rawValue);
              } else {
                bitField0_ |= 0x00000008;
                camp_ = rawValue;
              }
              break;
            }
            case 40: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.CityWallState value = com.yorha.proto.CommonEnum.CityWallState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(5, rawValue);
              } else {
                bitField0_ |= 0x00000010;
                wallState_ = rawValue;
              }
              break;
            }
            case 50: {
              com.yorha.proto.StructPlayerPB.TroopPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000020) != 0)) {
                subBuilder = troop_.toBuilder();
              }
              troop_ = input.readMessage(com.yorha.proto.StructPlayerPB.TroopPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(troop_);
                troop_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000020;
              break;
            }
            case 58: {
              com.yorha.proto.StructBattlePB.BattlePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = battle_.toBuilder();
              }
              battle_ = input.readMessage(com.yorha.proto.StructBattlePB.BattlePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battle_);
                battle_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              clanSname_ = bs;
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              clanId_ = input.readInt64();
              break;
            }
            case 90: {
              com.yorha.proto.StructPB.CityGarrisonPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000200) != 0)) {
                subBuilder = garrison_.toBuilder();
              }
              garrison_ = input.readMessage(com.yorha.proto.StructPB.CityGarrisonPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(garrison_);
                garrison_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000200;
              break;
            }
            case 98: {
              com.yorha.proto.StructBattlePB.BuffSysPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000400) != 0)) {
                subBuilder = buffSys_.toBuilder();
              }
              buffSys_ = input.readMessage(com.yorha.proto.StructBattlePB.BuffSysPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buffSys_);
                buffSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000400;
              break;
            }
            case 106: {
              com.yorha.proto.StructBattlePB.PeaceShieldPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000800) != 0)) {
                subBuilder = peaceShield_.toBuilder();
              }
              peaceShield_ = input.readMessage(com.yorha.proto.StructBattlePB.PeaceShieldPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(peaceShield_);
                peaceShield_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000800;
              break;
            }
            case 114: {
              com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00001000) != 0)) {
                subBuilder = devBuffSys_.toBuilder();
              }
              devBuffSys_ = input.readMessage(com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(devBuffSys_);
                devBuffSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00001000;
              break;
            }
            case 120: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.CityAscendReason value = com.yorha.proto.CommonEnum.CityAscendReason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(15, rawValue);
              } else {
                bitField0_ |= 0x00002000;
                ascendReason_ = rawValue;
              }
              break;
            }
            case 130: {
              com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00004000) != 0)) {
                subBuilder = safeGuard_.toBuilder();
              }
              safeGuard_ = input.readMessage(com.yorha.proto.StructPB.SpecialSafeGuardPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(safeGuard_);
                safeGuard_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00004000;
              break;
            }
            case 138: {
              com.yorha.proto.StructPB.PlayerCardHeadPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00008000) != 0)) {
                subBuilder = cardHead_.toBuilder();
              }
              cardHead_ = input.readMessage(com.yorha.proto.StructPB.PlayerCardHeadPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardHead_);
                cardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00008000;
              break;
            }
            case 146: {
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00010000) != 0)) {
                subBuilder = arrow_.toBuilder();
              }
              arrow_ = input.readMessage(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(arrow_);
                arrow_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00010000;
              break;
            }
            case 152: {
              bitField0_ |= 0x00020000;
              eraLevel_ = input.readInt32();
              break;
            }
            case 160: {
              bitField0_ |= 0x00040000;
              templateId_ = input.readInt32();
              break;
            }
            case 168: {
              bitField0_ |= 0x00080000;
              guardTowerHp_ = input.readInt32();
              break;
            }
            case 178: {
              com.yorha.proto.StructPB.ExpressionPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00100000) != 0)) {
                subBuilder = expression_.toBuilder();
              }
              expression_ = input.readMessage(com.yorha.proto.StructPB.ExpressionPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(expression_);
                expression_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00100000;
              break;
            }
            case 184: {
              bitField0_ |= 0x00200000;
              pFlagId_ = input.readInt32();
              break;
            }
            case 192: {
              bitField0_ |= 0x00400000;
              dressTemplateId_ = input.readInt32();
              break;
            }
            case 210: {
              com.yorha.proto.CityPB.CityKingdomModelPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00800000) != 0)) {
                subBuilder = cityKingdomModel_.toBuilder();
              }
              cityKingdomModel_ = input.readMessage(com.yorha.proto.CityPB.CityKingdomModelPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cityKingdomModel_);
                cityKingdomModel_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00800000;
              break;
            }
            case 216: {
              bitField0_ |= 0x01000000;
              nameplateId_ = input.readInt32();
              break;
            }
            case 232: {
              bitField0_ |= 0x02000000;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.CityPB.CityEntityPB.class, com.yorha.proto.CityPB.CityEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int POINT_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.PointPB point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getPoint() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }

    public static final int OWNERID_FIELD_NUMBER = 2;
    private long ownerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 2;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 2;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    public static final int LEVEL_FIELD_NUMBER = 3;
    private int level_;
    /**
     * <pre>
     * 城堡等级
     * </pre>
     *
     * <code>optional int32 level = 3;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 城堡等级
     * </pre>
     *
     * <code>optional int32 level = 3;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int CAMP_FIELD_NUMBER = 4;
    private int camp_;
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 4;</code>
     * @return Whether the camp field is set.
     */
    @java.lang.Override public boolean hasCamp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 4;</code>
     * @return The camp.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Camp getCamp() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
      return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
    }

    public static final int WALLSTATE_FIELD_NUMBER = 5;
    private int wallState_;
    /**
     * <pre>
     * 城墙状态  正常/冒烟/燃烧
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityWallState wallState = 5;</code>
     * @return Whether the wallState field is set.
     */
    @java.lang.Override public boolean hasWallState() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 城墙状态  正常/冒烟/燃烧
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityWallState wallState = 5;</code>
     * @return The wallState.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.CityWallState getWallState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.CityWallState result = com.yorha.proto.CommonEnum.CityWallState.valueOf(wallState_);
      return result == null ? com.yorha.proto.CommonEnum.CityWallState.CS_NORMAL : result;
    }

    public static final int TROOP_FIELD_NUMBER = 6;
    private com.yorha.proto.StructPlayerPB.TroopPB troop_;
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     * @return Whether the troop field is set.
     */
    @java.lang.Override
    public boolean hasTroop() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     * @return The troop.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }

    public static final int BATTLE_FIELD_NUMBER = 7;
    private com.yorha.proto.StructBattlePB.BattlePB battle_;
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     * @return Whether the battle field is set.
     */
    @java.lang.Override
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     * @return The battle.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }

    public static final int CLANSNAME_FIELD_NUMBER = 8;
    private volatile java.lang.Object clanSname_;
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 8;</code>
     * @return Whether the clanSname field is set.
     */
    @java.lang.Override
    public boolean hasClanSname() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 8;</code>
     * @return The clanSname.
     */
    @java.lang.Override
    public java.lang.String getClanSname() {
      java.lang.Object ref = clanSname_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSname_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 8;</code>
     * @return The bytes for clanSname.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSnameBytes() {
      java.lang.Object ref = clanSname_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSname_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLANID_FIELD_NUMBER = 9;
    private long clanId_;
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 9;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 9;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int GARRISON_FIELD_NUMBER = 11;
    private com.yorha.proto.StructPB.CityGarrisonPB garrison_;
    /**
     * <pre>
     * 城池owner的守军数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
     * @return Whether the garrison field is set.
     */
    @java.lang.Override
    public boolean hasGarrison() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 城池owner的守军数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
     * @return The garrison.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.CityGarrisonPB getGarrison() {
      return garrison_ == null ? com.yorha.proto.StructPB.CityGarrisonPB.getDefaultInstance() : garrison_;
    }
    /**
     * <pre>
     * 城池owner的守军数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.CityGarrisonPBOrBuilder getGarrisonOrBuilder() {
      return garrison_ == null ? com.yorha.proto.StructPB.CityGarrisonPB.getDefaultInstance() : garrison_;
    }

    public static final int BUFFSYS_FIELD_NUMBER = 12;
    private com.yorha.proto.StructBattlePB.BuffSysPB buffSys_;
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
     * @return Whether the buffSys field is set.
     */
    @java.lang.Override
    public boolean hasBuffSys() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
     * @return The buffSys.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys() {
      return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder() {
      return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
    }

    public static final int PEACESHIELD_FIELD_NUMBER = 13;
    private com.yorha.proto.StructBattlePB.PeaceShieldPB peaceShield_;
    /**
     * <pre>
     * 和平护盾
     * </pre>
     *
     * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
     * @return Whether the peaceShield field is set.
     */
    @java.lang.Override
    public boolean hasPeaceShield() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 和平护盾
     * </pre>
     *
     * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
     * @return The peaceShield.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.PeaceShieldPB getPeaceShield() {
      return peaceShield_ == null ? com.yorha.proto.StructBattlePB.PeaceShieldPB.getDefaultInstance() : peaceShield_;
    }
    /**
     * <pre>
     * 和平护盾
     * </pre>
     *
     * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.PeaceShieldPBOrBuilder getPeaceShieldOrBuilder() {
      return peaceShield_ == null ? com.yorha.proto.StructBattlePB.PeaceShieldPB.getDefaultInstance() : peaceShield_;
    }

    public static final int DEVBUFFSYS_FIELD_NUMBER = 14;
    private com.yorha.proto.StructBattlePB.SceneDevBuffSysPB devBuffSys_;
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
     * @return Whether the devBuffSys field is set.
     */
    @java.lang.Override
    public boolean hasDevBuffSys() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
     * @return The devBuffSys.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.SceneDevBuffSysPB getDevBuffSys() {
      return devBuffSys_ == null ? com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance() : devBuffSys_;
    }
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder getDevBuffSysOrBuilder() {
      return devBuffSys_ == null ? com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance() : devBuffSys_;
    }

    public static final int ASCENDREASON_FIELD_NUMBER = 15;
    private int ascendReason_;
    /**
     * <pre>
     * 升天理由 None即没有升天
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityAscendReason ascendReason = 15;</code>
     * @return Whether the ascendReason field is set.
     */
    @java.lang.Override public boolean hasAscendReason() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 升天理由 None即没有升天
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityAscendReason ascendReason = 15;</code>
     * @return The ascendReason.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.CityAscendReason getAscendReason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.CityAscendReason result = com.yorha.proto.CommonEnum.CityAscendReason.valueOf(ascendReason_);
      return result == null ? com.yorha.proto.CommonEnum.CityAscendReason.CAR_NONE : result;
    }

    public static final int SAFEGUARD_FIELD_NUMBER = 16;
    private com.yorha.proto.StructPB.SpecialSafeGuardPB safeGuard_;
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
     * @return Whether the safeGuard field is set.
     */
    @java.lang.Override
    public boolean hasSafeGuard() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
     * @return The safeGuard.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.SpecialSafeGuardPB getSafeGuard() {
      return safeGuard_ == null ? com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance() : safeGuard_;
    }
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder getSafeGuardOrBuilder() {
      return safeGuard_ == null ? com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance() : safeGuard_;
    }

    public static final int CARDHEAD_FIELD_NUMBER = 17;
    private com.yorha.proto.StructPB.PlayerCardHeadPB cardHead_;
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
     * @return Whether the cardHead field is set.
     */
    @java.lang.Override
    public boolean hasCardHead() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
     * @return The cardHead.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead() {
      return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
    }
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder() {
      return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
    }

    public static final int ARROW_FIELD_NUMBER = 18;
    private com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB arrow_;
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
     * @return Whether the arrow field is set.
     */
    @java.lang.Override
    public boolean hasArrow() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
     * @return The arrow.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow() {
      return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
    }
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder() {
      return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
    }

    public static final int ERALEVEL_FIELD_NUMBER = 19;
    private int eraLevel_;
    /**
     * <pre>
     * 时代等级
     * </pre>
     *
     * <code>optional int32 eraLevel = 19;</code>
     * @return Whether the eraLevel field is set.
     */
    @java.lang.Override
    public boolean hasEraLevel() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * 时代等级
     * </pre>
     *
     * <code>optional int32 eraLevel = 19;</code>
     * @return The eraLevel.
     */
    @java.lang.Override
    public int getEraLevel() {
      return eraLevel_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 20;
    private int templateId_;
    /**
     * <pre>
     * 配置id
     * </pre>
     *
     * <code>optional int32 templateId = 20;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * 配置id
     * </pre>
     *
     * <code>optional int32 templateId = 20;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int GUARDTOWERHP_FIELD_NUMBER = 21;
    private int guardTowerHp_;
    /**
     * <pre>
     * 警戒塔血量
     * </pre>
     *
     * <code>optional int32 guardTowerHp = 21;</code>
     * @return Whether the guardTowerHp field is set.
     */
    @java.lang.Override
    public boolean hasGuardTowerHp() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 警戒塔血量
     * </pre>
     *
     * <code>optional int32 guardTowerHp = 21;</code>
     * @return The guardTowerHp.
     */
    @java.lang.Override
    public int getGuardTowerHp() {
      return guardTowerHp_;
    }

    public static final int EXPRESSION_FIELD_NUMBER = 22;
    private com.yorha.proto.StructPB.ExpressionPB expression_;
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     * @return Whether the expression field is set.
     */
    @java.lang.Override
    public boolean hasExpression() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     * @return The expression.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ExpressionPB getExpression() {
      return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder() {
      return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
    }

    public static final int PFLAGID_FIELD_NUMBER = 23;
    private int pFlagId_;
    /**
     * <pre>
     * 个人旗帜id
     * </pre>
     *
     * <code>optional int32 pFlagId = 23;</code>
     * @return Whether the pFlagId field is set.
     */
    @java.lang.Override
    public boolean hasPFlagId() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 个人旗帜id
     * </pre>
     *
     * <code>optional int32 pFlagId = 23;</code>
     * @return The pFlagId.
     */
    @java.lang.Override
    public int getPFlagId() {
      return pFlagId_;
    }

    public static final int DRESSTEMPLATEID_FIELD_NUMBER = 24;
    private int dressTemplateId_;
    /**
     * <pre>
     * 皮肤配表id
     * </pre>
     *
     * <code>optional int32 dressTemplateId = 24;</code>
     * @return Whether the dressTemplateId field is set.
     */
    @java.lang.Override
    public boolean hasDressTemplateId() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <pre>
     * 皮肤配表id
     * </pre>
     *
     * <code>optional int32 dressTemplateId = 24;</code>
     * @return The dressTemplateId.
     */
    @java.lang.Override
    public int getDressTemplateId() {
      return dressTemplateId_;
    }

    public static final int CITYKINGDOMMODEL_FIELD_NUMBER = 26;
    private com.yorha.proto.CityPB.CityKingdomModelPB cityKingdomModel_;
    /**
     * <pre>
     * 玩家主堡身上的王国信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
     * @return Whether the cityKingdomModel field is set.
     */
    @java.lang.Override
    public boolean hasCityKingdomModel() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <pre>
     * 玩家主堡身上的王国信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
     * @return The cityKingdomModel.
     */
    @java.lang.Override
    public com.yorha.proto.CityPB.CityKingdomModelPB getCityKingdomModel() {
      return cityKingdomModel_ == null ? com.yorha.proto.CityPB.CityKingdomModelPB.getDefaultInstance() : cityKingdomModel_;
    }
    /**
     * <pre>
     * 玩家主堡身上的王国信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CityPB.CityKingdomModelPBOrBuilder getCityKingdomModelOrBuilder() {
      return cityKingdomModel_ == null ? com.yorha.proto.CityPB.CityKingdomModelPB.getDefaultInstance() : cityKingdomModel_;
    }

    public static final int NAMEPLATEID_FIELD_NUMBER = 27;
    private int nameplateId_;
    /**
     * <code>optional int32 nameplateId = 27;</code>
     * @return Whether the nameplateId field is set.
     */
    @java.lang.Override
    public boolean hasNameplateId() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional int32 nameplateId = 27;</code>
     * @return The nameplateId.
     */
    @java.lang.Override
    public int getNameplateId() {
      return nameplateId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 29;
    private int zoneId_;
    /**
     * <pre>
     * 所属玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 29;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <pre>
     * 所属玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 29;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, ownerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, level_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeEnum(4, camp_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeEnum(5, wallState_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(6, getTroop());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(7, getBattle());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, clanSname_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt64(9, clanId_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeMessage(11, getGarrison());
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeMessage(12, getBuffSys());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeMessage(13, getPeaceShield());
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeMessage(14, getDevBuffSys());
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeEnum(15, ascendReason_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeMessage(16, getSafeGuard());
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeMessage(17, getCardHead());
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeMessage(18, getArrow());
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeInt32(19, eraLevel_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeInt32(20, templateId_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeInt32(21, guardTowerHp_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeMessage(22, getExpression());
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeInt32(23, pFlagId_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeInt32(24, dressTemplateId_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeMessage(26, getCityKingdomModel());
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeInt32(27, nameplateId_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeInt32(29, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, ownerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, level_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, camp_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(5, wallState_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getTroop());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getBattle());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, clanSname_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, clanId_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, getGarrison());
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, getBuffSys());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, getPeaceShield());
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(14, getDevBuffSys());
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(15, ascendReason_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, getSafeGuard());
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(17, getCardHead());
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(18, getArrow());
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(19, eraLevel_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(20, templateId_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(21, guardTowerHp_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(22, getExpression());
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(23, pFlagId_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(24, dressTemplateId_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(26, getCityKingdomModel());
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(27, nameplateId_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(29, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.CityPB.CityEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.CityPB.CityEntityPB other = (com.yorha.proto.CityPB.CityEntityPB) obj;

      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (hasLevel() != other.hasLevel()) return false;
      if (hasLevel()) {
        if (getLevel()
            != other.getLevel()) return false;
      }
      if (hasCamp() != other.hasCamp()) return false;
      if (hasCamp()) {
        if (camp_ != other.camp_) return false;
      }
      if (hasWallState() != other.hasWallState()) return false;
      if (hasWallState()) {
        if (wallState_ != other.wallState_) return false;
      }
      if (hasTroop() != other.hasTroop()) return false;
      if (hasTroop()) {
        if (!getTroop()
            .equals(other.getTroop())) return false;
      }
      if (hasBattle() != other.hasBattle()) return false;
      if (hasBattle()) {
        if (!getBattle()
            .equals(other.getBattle())) return false;
      }
      if (hasClanSname() != other.hasClanSname()) return false;
      if (hasClanSname()) {
        if (!getClanSname()
            .equals(other.getClanSname())) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasGarrison() != other.hasGarrison()) return false;
      if (hasGarrison()) {
        if (!getGarrison()
            .equals(other.getGarrison())) return false;
      }
      if (hasBuffSys() != other.hasBuffSys()) return false;
      if (hasBuffSys()) {
        if (!getBuffSys()
            .equals(other.getBuffSys())) return false;
      }
      if (hasPeaceShield() != other.hasPeaceShield()) return false;
      if (hasPeaceShield()) {
        if (!getPeaceShield()
            .equals(other.getPeaceShield())) return false;
      }
      if (hasDevBuffSys() != other.hasDevBuffSys()) return false;
      if (hasDevBuffSys()) {
        if (!getDevBuffSys()
            .equals(other.getDevBuffSys())) return false;
      }
      if (hasAscendReason() != other.hasAscendReason()) return false;
      if (hasAscendReason()) {
        if (ascendReason_ != other.ascendReason_) return false;
      }
      if (hasSafeGuard() != other.hasSafeGuard()) return false;
      if (hasSafeGuard()) {
        if (!getSafeGuard()
            .equals(other.getSafeGuard())) return false;
      }
      if (hasCardHead() != other.hasCardHead()) return false;
      if (hasCardHead()) {
        if (!getCardHead()
            .equals(other.getCardHead())) return false;
      }
      if (hasArrow() != other.hasArrow()) return false;
      if (hasArrow()) {
        if (!getArrow()
            .equals(other.getArrow())) return false;
      }
      if (hasEraLevel() != other.hasEraLevel()) return false;
      if (hasEraLevel()) {
        if (getEraLevel()
            != other.getEraLevel()) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasGuardTowerHp() != other.hasGuardTowerHp()) return false;
      if (hasGuardTowerHp()) {
        if (getGuardTowerHp()
            != other.getGuardTowerHp()) return false;
      }
      if (hasExpression() != other.hasExpression()) return false;
      if (hasExpression()) {
        if (!getExpression()
            .equals(other.getExpression())) return false;
      }
      if (hasPFlagId() != other.hasPFlagId()) return false;
      if (hasPFlagId()) {
        if (getPFlagId()
            != other.getPFlagId()) return false;
      }
      if (hasDressTemplateId() != other.hasDressTemplateId()) return false;
      if (hasDressTemplateId()) {
        if (getDressTemplateId()
            != other.getDressTemplateId()) return false;
      }
      if (hasCityKingdomModel() != other.hasCityKingdomModel()) return false;
      if (hasCityKingdomModel()) {
        if (!getCityKingdomModel()
            .equals(other.getCityKingdomModel())) return false;
      }
      if (hasNameplateId() != other.hasNameplateId()) return false;
      if (hasNameplateId()) {
        if (getNameplateId()
            != other.getNameplateId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      if (hasLevel()) {
        hash = (37 * hash) + LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getLevel();
      }
      if (hasCamp()) {
        hash = (37 * hash) + CAMP_FIELD_NUMBER;
        hash = (53 * hash) + camp_;
      }
      if (hasWallState()) {
        hash = (37 * hash) + WALLSTATE_FIELD_NUMBER;
        hash = (53 * hash) + wallState_;
      }
      if (hasTroop()) {
        hash = (37 * hash) + TROOP_FIELD_NUMBER;
        hash = (53 * hash) + getTroop().hashCode();
      }
      if (hasBattle()) {
        hash = (37 * hash) + BATTLE_FIELD_NUMBER;
        hash = (53 * hash) + getBattle().hashCode();
      }
      if (hasClanSname()) {
        hash = (37 * hash) + CLANSNAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSname().hashCode();
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasGarrison()) {
        hash = (37 * hash) + GARRISON_FIELD_NUMBER;
        hash = (53 * hash) + getGarrison().hashCode();
      }
      if (hasBuffSys()) {
        hash = (37 * hash) + BUFFSYS_FIELD_NUMBER;
        hash = (53 * hash) + getBuffSys().hashCode();
      }
      if (hasPeaceShield()) {
        hash = (37 * hash) + PEACESHIELD_FIELD_NUMBER;
        hash = (53 * hash) + getPeaceShield().hashCode();
      }
      if (hasDevBuffSys()) {
        hash = (37 * hash) + DEVBUFFSYS_FIELD_NUMBER;
        hash = (53 * hash) + getDevBuffSys().hashCode();
      }
      if (hasAscendReason()) {
        hash = (37 * hash) + ASCENDREASON_FIELD_NUMBER;
        hash = (53 * hash) + ascendReason_;
      }
      if (hasSafeGuard()) {
        hash = (37 * hash) + SAFEGUARD_FIELD_NUMBER;
        hash = (53 * hash) + getSafeGuard().hashCode();
      }
      if (hasCardHead()) {
        hash = (37 * hash) + CARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getCardHead().hashCode();
      }
      if (hasArrow()) {
        hash = (37 * hash) + ARROW_FIELD_NUMBER;
        hash = (53 * hash) + getArrow().hashCode();
      }
      if (hasEraLevel()) {
        hash = (37 * hash) + ERALEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getEraLevel();
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasGuardTowerHp()) {
        hash = (37 * hash) + GUARDTOWERHP_FIELD_NUMBER;
        hash = (53 * hash) + getGuardTowerHp();
      }
      if (hasExpression()) {
        hash = (37 * hash) + EXPRESSION_FIELD_NUMBER;
        hash = (53 * hash) + getExpression().hashCode();
      }
      if (hasPFlagId()) {
        hash = (37 * hash) + PFLAGID_FIELD_NUMBER;
        hash = (53 * hash) + getPFlagId();
      }
      if (hasDressTemplateId()) {
        hash = (37 * hash) + DRESSTEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getDressTemplateId();
      }
      if (hasCityKingdomModel()) {
        hash = (37 * hash) + CITYKINGDOMMODEL_FIELD_NUMBER;
        hash = (53 * hash) + getCityKingdomModel().hashCode();
      }
      if (hasNameplateId()) {
        hash = (37 * hash) + NAMEPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getNameplateId();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.CityPB.CityEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CityEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CityEntityPB)
        com.yorha.proto.CityPB.CityEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.CityPB.CityEntityPB.class, com.yorha.proto.CityPB.CityEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.CityPB.CityEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getTroopFieldBuilder();
          getBattleFieldBuilder();
          getGarrisonFieldBuilder();
          getBuffSysFieldBuilder();
          getPeaceShieldFieldBuilder();
          getDevBuffSysFieldBuilder();
          getSafeGuardFieldBuilder();
          getCardHeadFieldBuilder();
          getArrowFieldBuilder();
          getExpressionFieldBuilder();
          getCityKingdomModelFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        level_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        camp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        wallState_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        if (troopBuilder_ == null) {
          troop_ = null;
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        if (battleBuilder_ == null) {
          battle_ = null;
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        clanSname_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        if (garrisonBuilder_ == null) {
          garrison_ = null;
        } else {
          garrisonBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        if (peaceShieldBuilder_ == null) {
          peaceShield_ = null;
        } else {
          peaceShieldBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000800);
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = null;
        } else {
          devBuffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00001000);
        ascendReason_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        if (safeGuardBuilder_ == null) {
          safeGuard_ = null;
        } else {
          safeGuardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00004000);
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00008000);
        if (arrowBuilder_ == null) {
          arrow_ = null;
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00010000);
        eraLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00020000);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00040000);
        guardTowerHp_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        if (expressionBuilder_ == null) {
          expression_ = null;
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00100000);
        pFlagId_ = 0;
        bitField0_ = (bitField0_ & ~0x00200000);
        dressTemplateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00400000);
        if (cityKingdomModelBuilder_ == null) {
          cityKingdomModel_ = null;
        } else {
          cityKingdomModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00800000);
        nameplateId_ = 0;
        bitField0_ = (bitField0_ & ~0x01000000);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x02000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityEntityPB build() {
        com.yorha.proto.CityPB.CityEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityEntityPB buildPartial() {
        com.yorha.proto.CityPB.CityEntityPB result = new com.yorha.proto.CityPB.CityEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.level_ = level_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.camp_ = camp_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.wallState_ = wallState_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          if (troopBuilder_ == null) {
            result.troop_ = troop_;
          } else {
            result.troop_ = troopBuilder_.build();
          }
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          if (battleBuilder_ == null) {
            result.battle_ = battle_;
          } else {
            result.battle_ = battleBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.clanSname_ = clanSname_;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          if (garrisonBuilder_ == null) {
            result.garrison_ = garrison_;
          } else {
            result.garrison_ = garrisonBuilder_.build();
          }
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          if (buffSysBuilder_ == null) {
            result.buffSys_ = buffSys_;
          } else {
            result.buffSys_ = buffSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          if (peaceShieldBuilder_ == null) {
            result.peaceShield_ = peaceShield_;
          } else {
            result.peaceShield_ = peaceShieldBuilder_.build();
          }
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          if (devBuffSysBuilder_ == null) {
            result.devBuffSys_ = devBuffSys_;
          } else {
            result.devBuffSys_ = devBuffSysBuilder_.build();
          }
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          to_bitField0_ |= 0x00002000;
        }
        result.ascendReason_ = ascendReason_;
        if (((from_bitField0_ & 0x00004000) != 0)) {
          if (safeGuardBuilder_ == null) {
            result.safeGuard_ = safeGuard_;
          } else {
            result.safeGuard_ = safeGuardBuilder_.build();
          }
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          if (cardHeadBuilder_ == null) {
            result.cardHead_ = cardHead_;
          } else {
            result.cardHead_ = cardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          if (arrowBuilder_ == null) {
            result.arrow_ = arrow_;
          } else {
            result.arrow_ = arrowBuilder_.build();
          }
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.eraLevel_ = eraLevel_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.guardTowerHp_ = guardTowerHp_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          if (expressionBuilder_ == null) {
            result.expression_ = expression_;
          } else {
            result.expression_ = expressionBuilder_.build();
          }
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.pFlagId_ = pFlagId_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.dressTemplateId_ = dressTemplateId_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          if (cityKingdomModelBuilder_ == null) {
            result.cityKingdomModel_ = cityKingdomModel_;
          } else {
            result.cityKingdomModel_ = cityKingdomModelBuilder_.build();
          }
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.nameplateId_ = nameplateId_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x02000000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.CityPB.CityEntityPB) {
          return mergeFrom((com.yorha.proto.CityPB.CityEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.CityPB.CityEntityPB other) {
        if (other == com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance()) return this;
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        if (other.hasLevel()) {
          setLevel(other.getLevel());
        }
        if (other.hasCamp()) {
          setCamp(other.getCamp());
        }
        if (other.hasWallState()) {
          setWallState(other.getWallState());
        }
        if (other.hasTroop()) {
          mergeTroop(other.getTroop());
        }
        if (other.hasBattle()) {
          mergeBattle(other.getBattle());
        }
        if (other.hasClanSname()) {
          bitField0_ |= 0x00000080;
          clanSname_ = other.clanSname_;
          onChanged();
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasGarrison()) {
          mergeGarrison(other.getGarrison());
        }
        if (other.hasBuffSys()) {
          mergeBuffSys(other.getBuffSys());
        }
        if (other.hasPeaceShield()) {
          mergePeaceShield(other.getPeaceShield());
        }
        if (other.hasDevBuffSys()) {
          mergeDevBuffSys(other.getDevBuffSys());
        }
        if (other.hasAscendReason()) {
          setAscendReason(other.getAscendReason());
        }
        if (other.hasSafeGuard()) {
          mergeSafeGuard(other.getSafeGuard());
        }
        if (other.hasCardHead()) {
          mergeCardHead(other.getCardHead());
        }
        if (other.hasArrow()) {
          mergeArrow(other.getArrow());
        }
        if (other.hasEraLevel()) {
          setEraLevel(other.getEraLevel());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasGuardTowerHp()) {
          setGuardTowerHp(other.getGuardTowerHp());
        }
        if (other.hasExpression()) {
          mergeExpression(other.getExpression());
        }
        if (other.hasPFlagId()) {
          setPFlagId(other.getPFlagId());
        }
        if (other.hasDressTemplateId()) {
          setDressTemplateId(other.getDressTemplateId());
        }
        if (other.hasCityKingdomModel()) {
          mergeCityKingdomModel(other.getCityKingdomModel());
        }
        if (other.hasNameplateId()) {
          setNameplateId(other.getNameplateId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.CityPB.CityEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.CityPB.CityEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.PointPB point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       * @return The point.
       */
      public com.yorha.proto.StructPB.PointPB getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public Builder setPoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public Builder setPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public Builder mergePoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            point_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPointBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private long ownerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 2;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 2;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 2;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000002;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ownerId_ = 0L;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       * 城堡等级
       * </pre>
       *
       * <code>optional int32 level = 3;</code>
       * @return Whether the level field is set.
       */
      @java.lang.Override
      public boolean hasLevel() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 城堡等级
       * </pre>
       *
       * <code>optional int32 level = 3;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       * 城堡等级
       * </pre>
       *
       * <code>optional int32 level = 3;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        bitField0_ |= 0x00000004;
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 城堡等级
       * </pre>
       *
       * <code>optional int32 level = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        level_ = 0;
        onChanged();
        return this;
      }

      private int camp_ = 0;
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 4;</code>
       * @return Whether the camp field is set.
       */
      @java.lang.Override public boolean hasCamp() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 4;</code>
       * @return The camp.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Camp getCamp() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
        return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 4;</code>
       * @param value The camp to set.
       * @return This builder for chaining.
       */
      public Builder setCamp(com.yorha.proto.CommonEnum.Camp value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        camp_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCamp() {
        bitField0_ = (bitField0_ & ~0x00000008);
        camp_ = 0;
        onChanged();
        return this;
      }

      private int wallState_ = 0;
      /**
       * <pre>
       * 城墙状态  正常/冒烟/燃烧
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityWallState wallState = 5;</code>
       * @return Whether the wallState field is set.
       */
      @java.lang.Override public boolean hasWallState() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 城墙状态  正常/冒烟/燃烧
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityWallState wallState = 5;</code>
       * @return The wallState.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.CityWallState getWallState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.CityWallState result = com.yorha.proto.CommonEnum.CityWallState.valueOf(wallState_);
        return result == null ? com.yorha.proto.CommonEnum.CityWallState.CS_NORMAL : result;
      }
      /**
       * <pre>
       * 城墙状态  正常/冒烟/燃烧
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityWallState wallState = 5;</code>
       * @param value The wallState to set.
       * @return This builder for chaining.
       */
      public Builder setWallState(com.yorha.proto.CommonEnum.CityWallState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000010;
        wallState_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 城墙状态  正常/冒烟/燃烧
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityWallState wallState = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearWallState() {
        bitField0_ = (bitField0_ & ~0x00000010);
        wallState_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPlayerPB.TroopPB troop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> troopBuilder_;
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       * @return Whether the troop field is set.
       */
      public boolean hasTroop() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       * @return The troop.
       */
      public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
        if (troopBuilder_ == null) {
          return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        } else {
          return troopBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public Builder setTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          troop_ = value;
          onChanged();
        } else {
          troopBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public Builder setTroop(
          com.yorha.proto.StructPlayerPB.TroopPB.Builder builderForValue) {
        if (troopBuilder_ == null) {
          troop_ = builderForValue.build();
          onChanged();
        } else {
          troopBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public Builder mergeTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
              troop_ != null &&
              troop_ != com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance()) {
            troop_ =
              com.yorha.proto.StructPlayerPB.TroopPB.newBuilder(troop_).mergeFrom(value).buildPartial();
          } else {
            troop_ = value;
          }
          onChanged();
        } else {
          troopBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public Builder clearTroop() {
        if (troopBuilder_ == null) {
          troop_ = null;
          onChanged();
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPB.Builder getTroopBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getTroopFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
        if (troopBuilder_ != null) {
          return troopBuilder_.getMessageOrBuilder();
        } else {
          return troop_ == null ?
              com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> 
          getTroopFieldBuilder() {
        if (troopBuilder_ == null) {
          troopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder>(
                  getTroop(),
                  getParentForChildren(),
                  isClean());
          troop_ = null;
        }
        return troopBuilder_;
      }

      private com.yorha.proto.StructBattlePB.BattlePB battle_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> battleBuilder_;
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       * @return Whether the battle field is set.
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       * @return The battle.
       */
      public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
        if (battleBuilder_ == null) {
          return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        } else {
          return battleBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public Builder setBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battle_ = value;
          onChanged();
        } else {
          battleBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public Builder setBattle(
          com.yorha.proto.StructBattlePB.BattlePB.Builder builderForValue) {
        if (battleBuilder_ == null) {
          battle_ = builderForValue.build();
          onChanged();
        } else {
          battleBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public Builder mergeBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
              battle_ != null &&
              battle_ != com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance()) {
            battle_ =
              com.yorha.proto.StructBattlePB.BattlePB.newBuilder(battle_).mergeFrom(value).buildPartial();
          } else {
            battle_ = value;
          }
          onChanged();
        } else {
          battleBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public Builder clearBattle() {
        if (battleBuilder_ == null) {
          battle_ = null;
          onChanged();
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePB.Builder getBattleBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getBattleFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
        if (battleBuilder_ != null) {
          return battleBuilder_.getMessageOrBuilder();
        } else {
          return battle_ == null ?
              com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> 
          getBattleFieldBuilder() {
        if (battleBuilder_ == null) {
          battleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder>(
                  getBattle(),
                  getParentForChildren(),
                  isClean());
          battle_ = null;
        }
        return battleBuilder_;
      }

      private java.lang.Object clanSname_ = "";
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 8;</code>
       * @return Whether the clanSname field is set.
       */
      public boolean hasClanSname() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 8;</code>
       * @return The clanSname.
       */
      public java.lang.String getClanSname() {
        java.lang.Object ref = clanSname_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSname_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 8;</code>
       * @return The bytes for clanSname.
       */
      public com.google.protobuf.ByteString
          getClanSnameBytes() {
        java.lang.Object ref = clanSname_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSname_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 8;</code>
       * @param value The clanSname to set.
       * @return This builder for chaining.
       */
      public Builder setClanSname(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        clanSname_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSname() {
        bitField0_ = (bitField0_ & ~0x00000080);
        clanSname_ = getDefaultInstance().getClanSname();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 8;</code>
       * @param value The bytes for clanSname to set.
       * @return This builder for chaining.
       */
      public Builder setClanSnameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        clanSname_ = value;
        onChanged();
        return this;
      }

      private long clanId_ ;
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 9;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 9;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 9;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000100;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.CityGarrisonPB garrison_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.CityGarrisonPB, com.yorha.proto.StructPB.CityGarrisonPB.Builder, com.yorha.proto.StructPB.CityGarrisonPBOrBuilder> garrisonBuilder_;
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       * @return Whether the garrison field is set.
       */
      public boolean hasGarrison() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       * @return The garrison.
       */
      public com.yorha.proto.StructPB.CityGarrisonPB getGarrison() {
        if (garrisonBuilder_ == null) {
          return garrison_ == null ? com.yorha.proto.StructPB.CityGarrisonPB.getDefaultInstance() : garrison_;
        } else {
          return garrisonBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       */
      public Builder setGarrison(com.yorha.proto.StructPB.CityGarrisonPB value) {
        if (garrisonBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          garrison_ = value;
          onChanged();
        } else {
          garrisonBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       */
      public Builder setGarrison(
          com.yorha.proto.StructPB.CityGarrisonPB.Builder builderForValue) {
        if (garrisonBuilder_ == null) {
          garrison_ = builderForValue.build();
          onChanged();
        } else {
          garrisonBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       */
      public Builder mergeGarrison(com.yorha.proto.StructPB.CityGarrisonPB value) {
        if (garrisonBuilder_ == null) {
          if (((bitField0_ & 0x00000200) != 0) &&
              garrison_ != null &&
              garrison_ != com.yorha.proto.StructPB.CityGarrisonPB.getDefaultInstance()) {
            garrison_ =
              com.yorha.proto.StructPB.CityGarrisonPB.newBuilder(garrison_).mergeFrom(value).buildPartial();
          } else {
            garrison_ = value;
          }
          onChanged();
        } else {
          garrisonBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       */
      public Builder clearGarrison() {
        if (garrisonBuilder_ == null) {
          garrison_ = null;
          onChanged();
        } else {
          garrisonBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       */
      public com.yorha.proto.StructPB.CityGarrisonPB.Builder getGarrisonBuilder() {
        bitField0_ |= 0x00000200;
        onChanged();
        return getGarrisonFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       */
      public com.yorha.proto.StructPB.CityGarrisonPBOrBuilder getGarrisonOrBuilder() {
        if (garrisonBuilder_ != null) {
          return garrisonBuilder_.getMessageOrBuilder();
        } else {
          return garrison_ == null ?
              com.yorha.proto.StructPB.CityGarrisonPB.getDefaultInstance() : garrison_;
        }
      }
      /**
       * <pre>
       * 城池owner的守军数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityGarrisonPB garrison = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.CityGarrisonPB, com.yorha.proto.StructPB.CityGarrisonPB.Builder, com.yorha.proto.StructPB.CityGarrisonPBOrBuilder> 
          getGarrisonFieldBuilder() {
        if (garrisonBuilder_ == null) {
          garrisonBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.CityGarrisonPB, com.yorha.proto.StructPB.CityGarrisonPB.Builder, com.yorha.proto.StructPB.CityGarrisonPBOrBuilder>(
                  getGarrison(),
                  getParentForChildren(),
                  isClean());
          garrison_ = null;
        }
        return garrisonBuilder_;
      }

      private com.yorha.proto.StructBattlePB.BuffSysPB buffSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder> buffSysBuilder_;
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       * @return Whether the buffSys field is set.
       */
      public boolean hasBuffSys() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       * @return The buffSys.
       */
      public com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys() {
        if (buffSysBuilder_ == null) {
          return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
        } else {
          return buffSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       */
      public Builder setBuffSys(com.yorha.proto.StructBattlePB.BuffSysPB value) {
        if (buffSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buffSys_ = value;
          onChanged();
        } else {
          buffSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       */
      public Builder setBuffSys(
          com.yorha.proto.StructBattlePB.BuffSysPB.Builder builderForValue) {
        if (buffSysBuilder_ == null) {
          buffSys_ = builderForValue.build();
          onChanged();
        } else {
          buffSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       */
      public Builder mergeBuffSys(com.yorha.proto.StructBattlePB.BuffSysPB value) {
        if (buffSysBuilder_ == null) {
          if (((bitField0_ & 0x00000400) != 0) &&
              buffSys_ != null &&
              buffSys_ != com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance()) {
            buffSys_ =
              com.yorha.proto.StructBattlePB.BuffSysPB.newBuilder(buffSys_).mergeFrom(value).buildPartial();
          } else {
            buffSys_ = value;
          }
          onChanged();
        } else {
          buffSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       */
      public Builder clearBuffSys() {
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
          onChanged();
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       */
      public com.yorha.proto.StructBattlePB.BuffSysPB.Builder getBuffSysBuilder() {
        bitField0_ |= 0x00000400;
        onChanged();
        return getBuffSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       */
      public com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder() {
        if (buffSysBuilder_ != null) {
          return buffSysBuilder_.getMessageOrBuilder();
        } else {
          return buffSys_ == null ?
              com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder> 
          getBuffSysFieldBuilder() {
        if (buffSysBuilder_ == null) {
          buffSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder>(
                  getBuffSys(),
                  getParentForChildren(),
                  isClean());
          buffSys_ = null;
        }
        return buffSysBuilder_;
      }

      private com.yorha.proto.StructBattlePB.PeaceShieldPB peaceShield_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.PeaceShieldPB, com.yorha.proto.StructBattlePB.PeaceShieldPB.Builder, com.yorha.proto.StructBattlePB.PeaceShieldPBOrBuilder> peaceShieldBuilder_;
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       * @return Whether the peaceShield field is set.
       */
      public boolean hasPeaceShield() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       * @return The peaceShield.
       */
      public com.yorha.proto.StructBattlePB.PeaceShieldPB getPeaceShield() {
        if (peaceShieldBuilder_ == null) {
          return peaceShield_ == null ? com.yorha.proto.StructBattlePB.PeaceShieldPB.getDefaultInstance() : peaceShield_;
        } else {
          return peaceShieldBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       */
      public Builder setPeaceShield(com.yorha.proto.StructBattlePB.PeaceShieldPB value) {
        if (peaceShieldBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          peaceShield_ = value;
          onChanged();
        } else {
          peaceShieldBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000800;
        return this;
      }
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       */
      public Builder setPeaceShield(
          com.yorha.proto.StructBattlePB.PeaceShieldPB.Builder builderForValue) {
        if (peaceShieldBuilder_ == null) {
          peaceShield_ = builderForValue.build();
          onChanged();
        } else {
          peaceShieldBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000800;
        return this;
      }
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       */
      public Builder mergePeaceShield(com.yorha.proto.StructBattlePB.PeaceShieldPB value) {
        if (peaceShieldBuilder_ == null) {
          if (((bitField0_ & 0x00000800) != 0) &&
              peaceShield_ != null &&
              peaceShield_ != com.yorha.proto.StructBattlePB.PeaceShieldPB.getDefaultInstance()) {
            peaceShield_ =
              com.yorha.proto.StructBattlePB.PeaceShieldPB.newBuilder(peaceShield_).mergeFrom(value).buildPartial();
          } else {
            peaceShield_ = value;
          }
          onChanged();
        } else {
          peaceShieldBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000800;
        return this;
      }
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       */
      public Builder clearPeaceShield() {
        if (peaceShieldBuilder_ == null) {
          peaceShield_ = null;
          onChanged();
        } else {
          peaceShieldBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000800);
        return this;
      }
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       */
      public com.yorha.proto.StructBattlePB.PeaceShieldPB.Builder getPeaceShieldBuilder() {
        bitField0_ |= 0x00000800;
        onChanged();
        return getPeaceShieldFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       */
      public com.yorha.proto.StructBattlePB.PeaceShieldPBOrBuilder getPeaceShieldOrBuilder() {
        if (peaceShieldBuilder_ != null) {
          return peaceShieldBuilder_.getMessageOrBuilder();
        } else {
          return peaceShield_ == null ?
              com.yorha.proto.StructBattlePB.PeaceShieldPB.getDefaultInstance() : peaceShield_;
        }
      }
      /**
       * <pre>
       * 和平护盾
       * </pre>
       *
       * <code>optional .com.yorha.proto.PeaceShieldPB peaceShield = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.PeaceShieldPB, com.yorha.proto.StructBattlePB.PeaceShieldPB.Builder, com.yorha.proto.StructBattlePB.PeaceShieldPBOrBuilder> 
          getPeaceShieldFieldBuilder() {
        if (peaceShieldBuilder_ == null) {
          peaceShieldBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.PeaceShieldPB, com.yorha.proto.StructBattlePB.PeaceShieldPB.Builder, com.yorha.proto.StructBattlePB.PeaceShieldPBOrBuilder>(
                  getPeaceShield(),
                  getParentForChildren(),
                  isClean());
          peaceShield_ = null;
        }
        return peaceShieldBuilder_;
      }

      private com.yorha.proto.StructBattlePB.SceneDevBuffSysPB devBuffSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.SceneDevBuffSysPB, com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder, com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder> devBuffSysBuilder_;
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       * @return Whether the devBuffSys field is set.
       */
      public boolean hasDevBuffSys() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       * @return The devBuffSys.
       */
      public com.yorha.proto.StructBattlePB.SceneDevBuffSysPB getDevBuffSys() {
        if (devBuffSysBuilder_ == null) {
          return devBuffSys_ == null ? com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance() : devBuffSys_;
        } else {
          return devBuffSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       */
      public Builder setDevBuffSys(com.yorha.proto.StructBattlePB.SceneDevBuffSysPB value) {
        if (devBuffSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          devBuffSys_ = value;
          onChanged();
        } else {
          devBuffSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       */
      public Builder setDevBuffSys(
          com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder builderForValue) {
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = builderForValue.build();
          onChanged();
        } else {
          devBuffSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       */
      public Builder mergeDevBuffSys(com.yorha.proto.StructBattlePB.SceneDevBuffSysPB value) {
        if (devBuffSysBuilder_ == null) {
          if (((bitField0_ & 0x00001000) != 0) &&
              devBuffSys_ != null &&
              devBuffSys_ != com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance()) {
            devBuffSys_ =
              com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.newBuilder(devBuffSys_).mergeFrom(value).buildPartial();
          } else {
            devBuffSys_ = value;
          }
          onChanged();
        } else {
          devBuffSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       */
      public Builder clearDevBuffSys() {
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = null;
          onChanged();
        } else {
          devBuffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00001000);
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       */
      public com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder getDevBuffSysBuilder() {
        bitField0_ |= 0x00001000;
        onChanged();
        return getDevBuffSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       */
      public com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder getDevBuffSysOrBuilder() {
        if (devBuffSysBuilder_ != null) {
          return devBuffSysBuilder_.getMessageOrBuilder();
        } else {
          return devBuffSys_ == null ?
              com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance() : devBuffSys_;
        }
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 14;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.SceneDevBuffSysPB, com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder, com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder> 
          getDevBuffSysFieldBuilder() {
        if (devBuffSysBuilder_ == null) {
          devBuffSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.SceneDevBuffSysPB, com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder, com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder>(
                  getDevBuffSys(),
                  getParentForChildren(),
                  isClean());
          devBuffSys_ = null;
        }
        return devBuffSysBuilder_;
      }

      private int ascendReason_ = 0;
      /**
       * <pre>
       * 升天理由 None即没有升天
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityAscendReason ascendReason = 15;</code>
       * @return Whether the ascendReason field is set.
       */
      @java.lang.Override public boolean hasAscendReason() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 升天理由 None即没有升天
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityAscendReason ascendReason = 15;</code>
       * @return The ascendReason.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.CityAscendReason getAscendReason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.CityAscendReason result = com.yorha.proto.CommonEnum.CityAscendReason.valueOf(ascendReason_);
        return result == null ? com.yorha.proto.CommonEnum.CityAscendReason.CAR_NONE : result;
      }
      /**
       * <pre>
       * 升天理由 None即没有升天
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityAscendReason ascendReason = 15;</code>
       * @param value The ascendReason to set.
       * @return This builder for chaining.
       */
      public Builder setAscendReason(com.yorha.proto.CommonEnum.CityAscendReason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00002000;
        ascendReason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 升天理由 None即没有升天
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityAscendReason ascendReason = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearAscendReason() {
        bitField0_ = (bitField0_ & ~0x00002000);
        ascendReason_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.SpecialSafeGuardPB safeGuard_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.SpecialSafeGuardPB, com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder, com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder> safeGuardBuilder_;
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       * @return Whether the safeGuard field is set.
       */
      public boolean hasSafeGuard() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       * @return The safeGuard.
       */
      public com.yorha.proto.StructPB.SpecialSafeGuardPB getSafeGuard() {
        if (safeGuardBuilder_ == null) {
          return safeGuard_ == null ? com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance() : safeGuard_;
        } else {
          return safeGuardBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       */
      public Builder setSafeGuard(com.yorha.proto.StructPB.SpecialSafeGuardPB value) {
        if (safeGuardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          safeGuard_ = value;
          onChanged();
        } else {
          safeGuardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       */
      public Builder setSafeGuard(
          com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder builderForValue) {
        if (safeGuardBuilder_ == null) {
          safeGuard_ = builderForValue.build();
          onChanged();
        } else {
          safeGuardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       */
      public Builder mergeSafeGuard(com.yorha.proto.StructPB.SpecialSafeGuardPB value) {
        if (safeGuardBuilder_ == null) {
          if (((bitField0_ & 0x00004000) != 0) &&
              safeGuard_ != null &&
              safeGuard_ != com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance()) {
            safeGuard_ =
              com.yorha.proto.StructPB.SpecialSafeGuardPB.newBuilder(safeGuard_).mergeFrom(value).buildPartial();
          } else {
            safeGuard_ = value;
          }
          onChanged();
        } else {
          safeGuardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       */
      public Builder clearSafeGuard() {
        if (safeGuardBuilder_ == null) {
          safeGuard_ = null;
          onChanged();
        } else {
          safeGuardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00004000);
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       */
      public com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder getSafeGuardBuilder() {
        bitField0_ |= 0x00004000;
        onChanged();
        return getSafeGuardFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       */
      public com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder getSafeGuardOrBuilder() {
        if (safeGuardBuilder_ != null) {
          return safeGuardBuilder_.getMessageOrBuilder();
        } else {
          return safeGuard_ == null ?
              com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance() : safeGuard_;
        }
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.SpecialSafeGuardPB, com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder, com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder> 
          getSafeGuardFieldBuilder() {
        if (safeGuardBuilder_ == null) {
          safeGuardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.SpecialSafeGuardPB, com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder, com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder>(
                  getSafeGuard(),
                  getParentForChildren(),
                  isClean());
          safeGuard_ = null;
        }
        return safeGuardBuilder_;
      }

      private com.yorha.proto.StructPB.PlayerCardHeadPB cardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> cardHeadBuilder_;
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       * @return Whether the cardHead field is set.
       */
      public boolean hasCardHead() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       * @return The cardHead.
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead() {
        if (cardHeadBuilder_ == null) {
          return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
        } else {
          return cardHeadBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       */
      public Builder setCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (cardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardHead_ = value;
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       */
      public Builder setCardHead(
          com.yorha.proto.StructPB.PlayerCardHeadPB.Builder builderForValue) {
        if (cardHeadBuilder_ == null) {
          cardHead_ = builderForValue.build();
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       */
      public Builder mergeCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (cardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00008000) != 0) &&
              cardHead_ != null &&
              cardHead_ != com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance()) {
            cardHead_ =
              com.yorha.proto.StructPB.PlayerCardHeadPB.newBuilder(cardHead_).mergeFrom(value).buildPartial();
          } else {
            cardHead_ = value;
          }
          onChanged();
        } else {
          cardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       */
      public Builder clearCardHead() {
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
          onChanged();
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00008000);
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB.Builder getCardHeadBuilder() {
        bitField0_ |= 0x00008000;
        onChanged();
        return getCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder() {
        if (cardHeadBuilder_ != null) {
          return cardHeadBuilder_.getMessageOrBuilder();
        } else {
          return cardHead_ == null ?
              com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
        }
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 17;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> 
          getCardHeadFieldBuilder() {
        if (cardHeadBuilder_ == null) {
          cardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder>(
                  getCardHead(),
                  getParentForChildren(),
                  isClean());
          cardHead_ = null;
        }
        return cardHeadBuilder_;
      }

      private com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB arrow_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder> arrowBuilder_;
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       * @return Whether the arrow field is set.
       */
      public boolean hasArrow() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       * @return The arrow.
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow() {
        if (arrowBuilder_ == null) {
          return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
        } else {
          return arrowBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       */
      public Builder setArrow(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB value) {
        if (arrowBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          arrow_ = value;
          onChanged();
        } else {
          arrowBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00010000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       */
      public Builder setArrow(
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder builderForValue) {
        if (arrowBuilder_ == null) {
          arrow_ = builderForValue.build();
          onChanged();
        } else {
          arrowBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00010000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       */
      public Builder mergeArrow(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB value) {
        if (arrowBuilder_ == null) {
          if (((bitField0_ & 0x00010000) != 0) &&
              arrow_ != null &&
              arrow_ != com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance()) {
            arrow_ =
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.newBuilder(arrow_).mergeFrom(value).buildPartial();
          } else {
            arrow_ = value;
          }
          onChanged();
        } else {
          arrowBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00010000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       */
      public Builder clearArrow() {
        if (arrowBuilder_ == null) {
          arrow_ = null;
          onChanged();
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00010000);
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder getArrowBuilder() {
        bitField0_ |= 0x00010000;
        onChanged();
        return getArrowFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder() {
        if (arrowBuilder_ != null) {
          return arrowBuilder_.getMessageOrBuilder();
        } else {
          return arrow_ == null ?
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
        }
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 18;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder> 
          getArrowFieldBuilder() {
        if (arrowBuilder_ == null) {
          arrowBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder>(
                  getArrow(),
                  getParentForChildren(),
                  isClean());
          arrow_ = null;
        }
        return arrowBuilder_;
      }

      private int eraLevel_ ;
      /**
       * <pre>
       * 时代等级
       * </pre>
       *
       * <code>optional int32 eraLevel = 19;</code>
       * @return Whether the eraLevel field is set.
       */
      @java.lang.Override
      public boolean hasEraLevel() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 时代等级
       * </pre>
       *
       * <code>optional int32 eraLevel = 19;</code>
       * @return The eraLevel.
       */
      @java.lang.Override
      public int getEraLevel() {
        return eraLevel_;
      }
      /**
       * <pre>
       * 时代等级
       * </pre>
       *
       * <code>optional int32 eraLevel = 19;</code>
       * @param value The eraLevel to set.
       * @return This builder for chaining.
       */
      public Builder setEraLevel(int value) {
        bitField0_ |= 0x00020000;
        eraLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 时代等级
       * </pre>
       *
       * <code>optional int32 eraLevel = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearEraLevel() {
        bitField0_ = (bitField0_ & ~0x00020000);
        eraLevel_ = 0;
        onChanged();
        return this;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 配置id
       * </pre>
       *
       * <code>optional int32 templateId = 20;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * 配置id
       * </pre>
       *
       * <code>optional int32 templateId = 20;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 配置id
       * </pre>
       *
       * <code>optional int32 templateId = 20;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00040000;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 配置id
       * </pre>
       *
       * <code>optional int32 templateId = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00040000);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private int guardTowerHp_ ;
      /**
       * <pre>
       * 警戒塔血量
       * </pre>
       *
       * <code>optional int32 guardTowerHp = 21;</code>
       * @return Whether the guardTowerHp field is set.
       */
      @java.lang.Override
      public boolean hasGuardTowerHp() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 警戒塔血量
       * </pre>
       *
       * <code>optional int32 guardTowerHp = 21;</code>
       * @return The guardTowerHp.
       */
      @java.lang.Override
      public int getGuardTowerHp() {
        return guardTowerHp_;
      }
      /**
       * <pre>
       * 警戒塔血量
       * </pre>
       *
       * <code>optional int32 guardTowerHp = 21;</code>
       * @param value The guardTowerHp to set.
       * @return This builder for chaining.
       */
      public Builder setGuardTowerHp(int value) {
        bitField0_ |= 0x00080000;
        guardTowerHp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 警戒塔血量
       * </pre>
       *
       * <code>optional int32 guardTowerHp = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearGuardTowerHp() {
        bitField0_ = (bitField0_ & ~0x00080000);
        guardTowerHp_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.ExpressionPB expression_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder> expressionBuilder_;
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       * @return Whether the expression field is set.
       */
      public boolean hasExpression() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       * @return The expression.
       */
      public com.yorha.proto.StructPB.ExpressionPB getExpression() {
        if (expressionBuilder_ == null) {
          return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
        } else {
          return expressionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public Builder setExpression(com.yorha.proto.StructPB.ExpressionPB value) {
        if (expressionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          expression_ = value;
          onChanged();
        } else {
          expressionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00100000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public Builder setExpression(
          com.yorha.proto.StructPB.ExpressionPB.Builder builderForValue) {
        if (expressionBuilder_ == null) {
          expression_ = builderForValue.build();
          onChanged();
        } else {
          expressionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00100000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public Builder mergeExpression(com.yorha.proto.StructPB.ExpressionPB value) {
        if (expressionBuilder_ == null) {
          if (((bitField0_ & 0x00100000) != 0) &&
              expression_ != null &&
              expression_ != com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance()) {
            expression_ =
              com.yorha.proto.StructPB.ExpressionPB.newBuilder(expression_).mergeFrom(value).buildPartial();
          } else {
            expression_ = value;
          }
          onChanged();
        } else {
          expressionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00100000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public Builder clearExpression() {
        if (expressionBuilder_ == null) {
          expression_ = null;
          onChanged();
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00100000);
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public com.yorha.proto.StructPB.ExpressionPB.Builder getExpressionBuilder() {
        bitField0_ |= 0x00100000;
        onChanged();
        return getExpressionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder() {
        if (expressionBuilder_ != null) {
          return expressionBuilder_.getMessageOrBuilder();
        } else {
          return expression_ == null ?
              com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder> 
          getExpressionFieldBuilder() {
        if (expressionBuilder_ == null) {
          expressionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder>(
                  getExpression(),
                  getParentForChildren(),
                  isClean());
          expression_ = null;
        }
        return expressionBuilder_;
      }

      private int pFlagId_ ;
      /**
       * <pre>
       * 个人旗帜id
       * </pre>
       *
       * <code>optional int32 pFlagId = 23;</code>
       * @return Whether the pFlagId field is set.
       */
      @java.lang.Override
      public boolean hasPFlagId() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 个人旗帜id
       * </pre>
       *
       * <code>optional int32 pFlagId = 23;</code>
       * @return The pFlagId.
       */
      @java.lang.Override
      public int getPFlagId() {
        return pFlagId_;
      }
      /**
       * <pre>
       * 个人旗帜id
       * </pre>
       *
       * <code>optional int32 pFlagId = 23;</code>
       * @param value The pFlagId to set.
       * @return This builder for chaining.
       */
      public Builder setPFlagId(int value) {
        bitField0_ |= 0x00200000;
        pFlagId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 个人旗帜id
       * </pre>
       *
       * <code>optional int32 pFlagId = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearPFlagId() {
        bitField0_ = (bitField0_ & ~0x00200000);
        pFlagId_ = 0;
        onChanged();
        return this;
      }

      private int dressTemplateId_ ;
      /**
       * <pre>
       * 皮肤配表id
       * </pre>
       *
       * <code>optional int32 dressTemplateId = 24;</code>
       * @return Whether the dressTemplateId field is set.
       */
      @java.lang.Override
      public boolean hasDressTemplateId() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * 皮肤配表id
       * </pre>
       *
       * <code>optional int32 dressTemplateId = 24;</code>
       * @return The dressTemplateId.
       */
      @java.lang.Override
      public int getDressTemplateId() {
        return dressTemplateId_;
      }
      /**
       * <pre>
       * 皮肤配表id
       * </pre>
       *
       * <code>optional int32 dressTemplateId = 24;</code>
       * @param value The dressTemplateId to set.
       * @return This builder for chaining.
       */
      public Builder setDressTemplateId(int value) {
        bitField0_ |= 0x00400000;
        dressTemplateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 皮肤配表id
       * </pre>
       *
       * <code>optional int32 dressTemplateId = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearDressTemplateId() {
        bitField0_ = (bitField0_ & ~0x00400000);
        dressTemplateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.CityPB.CityKingdomModelPB cityKingdomModel_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CityPB.CityKingdomModelPB, com.yorha.proto.CityPB.CityKingdomModelPB.Builder, com.yorha.proto.CityPB.CityKingdomModelPBOrBuilder> cityKingdomModelBuilder_;
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       * @return Whether the cityKingdomModel field is set.
       */
      public boolean hasCityKingdomModel() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       * @return The cityKingdomModel.
       */
      public com.yorha.proto.CityPB.CityKingdomModelPB getCityKingdomModel() {
        if (cityKingdomModelBuilder_ == null) {
          return cityKingdomModel_ == null ? com.yorha.proto.CityPB.CityKingdomModelPB.getDefaultInstance() : cityKingdomModel_;
        } else {
          return cityKingdomModelBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       */
      public Builder setCityKingdomModel(com.yorha.proto.CityPB.CityKingdomModelPB value) {
        if (cityKingdomModelBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cityKingdomModel_ = value;
          onChanged();
        } else {
          cityKingdomModelBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00800000;
        return this;
      }
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       */
      public Builder setCityKingdomModel(
          com.yorha.proto.CityPB.CityKingdomModelPB.Builder builderForValue) {
        if (cityKingdomModelBuilder_ == null) {
          cityKingdomModel_ = builderForValue.build();
          onChanged();
        } else {
          cityKingdomModelBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00800000;
        return this;
      }
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       */
      public Builder mergeCityKingdomModel(com.yorha.proto.CityPB.CityKingdomModelPB value) {
        if (cityKingdomModelBuilder_ == null) {
          if (((bitField0_ & 0x00800000) != 0) &&
              cityKingdomModel_ != null &&
              cityKingdomModel_ != com.yorha.proto.CityPB.CityKingdomModelPB.getDefaultInstance()) {
            cityKingdomModel_ =
              com.yorha.proto.CityPB.CityKingdomModelPB.newBuilder(cityKingdomModel_).mergeFrom(value).buildPartial();
          } else {
            cityKingdomModel_ = value;
          }
          onChanged();
        } else {
          cityKingdomModelBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00800000;
        return this;
      }
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       */
      public Builder clearCityKingdomModel() {
        if (cityKingdomModelBuilder_ == null) {
          cityKingdomModel_ = null;
          onChanged();
        } else {
          cityKingdomModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00800000);
        return this;
      }
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       */
      public com.yorha.proto.CityPB.CityKingdomModelPB.Builder getCityKingdomModelBuilder() {
        bitField0_ |= 0x00800000;
        onChanged();
        return getCityKingdomModelFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       */
      public com.yorha.proto.CityPB.CityKingdomModelPBOrBuilder getCityKingdomModelOrBuilder() {
        if (cityKingdomModelBuilder_ != null) {
          return cityKingdomModelBuilder_.getMessageOrBuilder();
        } else {
          return cityKingdomModel_ == null ?
              com.yorha.proto.CityPB.CityKingdomModelPB.getDefaultInstance() : cityKingdomModel_;
        }
      }
      /**
       * <pre>
       * 玩家主堡身上的王国信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityKingdomModelPB cityKingdomModel = 26;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CityPB.CityKingdomModelPB, com.yorha.proto.CityPB.CityKingdomModelPB.Builder, com.yorha.proto.CityPB.CityKingdomModelPBOrBuilder> 
          getCityKingdomModelFieldBuilder() {
        if (cityKingdomModelBuilder_ == null) {
          cityKingdomModelBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CityPB.CityKingdomModelPB, com.yorha.proto.CityPB.CityKingdomModelPB.Builder, com.yorha.proto.CityPB.CityKingdomModelPBOrBuilder>(
                  getCityKingdomModel(),
                  getParentForChildren(),
                  isClean());
          cityKingdomModel_ = null;
        }
        return cityKingdomModelBuilder_;
      }

      private int nameplateId_ ;
      /**
       * <code>optional int32 nameplateId = 27;</code>
       * @return Whether the nameplateId field is set.
       */
      @java.lang.Override
      public boolean hasNameplateId() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <code>optional int32 nameplateId = 27;</code>
       * @return The nameplateId.
       */
      @java.lang.Override
      public int getNameplateId() {
        return nameplateId_;
      }
      /**
       * <code>optional int32 nameplateId = 27;</code>
       * @param value The nameplateId to set.
       * @return This builder for chaining.
       */
      public Builder setNameplateId(int value) {
        bitField0_ |= 0x01000000;
        nameplateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 nameplateId = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearNameplateId() {
        bitField0_ = (bitField0_ & ~0x01000000);
        nameplateId_ = 0;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 所属玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 29;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <pre>
       * 所属玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 29;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 所属玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 29;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x02000000;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x02000000);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CityEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CityEntityPB)
    private static final com.yorha.proto.CityPB.CityEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.CityPB.CityEntityPB();
    }

    public static com.yorha.proto.CityPB.CityEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CityEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<CityEntityPB>() {
      @java.lang.Override
      public CityEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CityEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CityEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.CityPB.CityEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityKingdomModelPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CityKingdomModelPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 officeId = 1;</code>
     * @return Whether the officeId field is set.
     */
    boolean hasOfficeId();
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 officeId = 1;</code>
     * @return The officeId.
     */
    int getOfficeId();

    /**
     * <pre>
     * 王国技能作用在主堡上需要的信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
     * @return Whether the skillMap field is set.
     */
    boolean hasSkillMap();
    /**
     * <pre>
     * 王国技能作用在主堡上需要的信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
     * @return The skillMap.
     */
    com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB getSkillMap();
    /**
     * <pre>
     * 王国技能作用在主堡上需要的信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
     */
    com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPBOrBuilder getSkillMapOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CityKingdomModelPB}
   */
  public static final class CityKingdomModelPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CityKingdomModelPB)
      CityKingdomModelPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CityKingdomModelPB.newBuilder() to construct.
    private CityKingdomModelPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CityKingdomModelPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CityKingdomModelPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CityKingdomModelPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              officeId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = skillMap_.toBuilder();
              }
              skillMap_ = input.readMessage(com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(skillMap_);
                skillMap_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomModelPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomModelPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.CityPB.CityKingdomModelPB.class, com.yorha.proto.CityPB.CityKingdomModelPB.Builder.class);
    }

    private int bitField0_;
    public static final int OFFICEID_FIELD_NUMBER = 1;
    private int officeId_;
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 officeId = 1;</code>
     * @return Whether the officeId field is set.
     */
    @java.lang.Override
    public boolean hasOfficeId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 officeId = 1;</code>
     * @return The officeId.
     */
    @java.lang.Override
    public int getOfficeId() {
      return officeId_;
    }

    public static final int SKILLMAP_FIELD_NUMBER = 2;
    private com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB skillMap_;
    /**
     * <pre>
     * 王国技能作用在主堡上需要的信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
     * @return Whether the skillMap field is set.
     */
    @java.lang.Override
    public boolean hasSkillMap() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 王国技能作用在主堡上需要的信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
     * @return The skillMap.
     */
    @java.lang.Override
    public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB getSkillMap() {
      return skillMap_ == null ? com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.getDefaultInstance() : skillMap_;
    }
    /**
     * <pre>
     * 王国技能作用在主堡上需要的信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPBOrBuilder getSkillMapOrBuilder() {
      return skillMap_ == null ? com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.getDefaultInstance() : skillMap_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, officeId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getSkillMap());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, officeId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSkillMap());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.CityPB.CityKingdomModelPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.CityPB.CityKingdomModelPB other = (com.yorha.proto.CityPB.CityKingdomModelPB) obj;

      if (hasOfficeId() != other.hasOfficeId()) return false;
      if (hasOfficeId()) {
        if (getOfficeId()
            != other.getOfficeId()) return false;
      }
      if (hasSkillMap() != other.hasSkillMap()) return false;
      if (hasSkillMap()) {
        if (!getSkillMap()
            .equals(other.getSkillMap())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOfficeId()) {
        hash = (37 * hash) + OFFICEID_FIELD_NUMBER;
        hash = (53 * hash) + getOfficeId();
      }
      if (hasSkillMap()) {
        hash = (37 * hash) + SKILLMAP_FIELD_NUMBER;
        hash = (53 * hash) + getSkillMap().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityKingdomModelPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.CityPB.CityKingdomModelPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CityKingdomModelPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CityKingdomModelPB)
        com.yorha.proto.CityPB.CityKingdomModelPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomModelPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomModelPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.CityPB.CityKingdomModelPB.class, com.yorha.proto.CityPB.CityKingdomModelPB.Builder.class);
      }

      // Construct using com.yorha.proto.CityPB.CityKingdomModelPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSkillMapFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        officeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (skillMapBuilder_ == null) {
          skillMap_ = null;
        } else {
          skillMapBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomModelPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityKingdomModelPB getDefaultInstanceForType() {
        return com.yorha.proto.CityPB.CityKingdomModelPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityKingdomModelPB build() {
        com.yorha.proto.CityPB.CityKingdomModelPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityKingdomModelPB buildPartial() {
        com.yorha.proto.CityPB.CityKingdomModelPB result = new com.yorha.proto.CityPB.CityKingdomModelPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.officeId_ = officeId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (skillMapBuilder_ == null) {
            result.skillMap_ = skillMap_;
          } else {
            result.skillMap_ = skillMapBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.CityPB.CityKingdomModelPB) {
          return mergeFrom((com.yorha.proto.CityPB.CityKingdomModelPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.CityPB.CityKingdomModelPB other) {
        if (other == com.yorha.proto.CityPB.CityKingdomModelPB.getDefaultInstance()) return this;
        if (other.hasOfficeId()) {
          setOfficeId(other.getOfficeId());
        }
        if (other.hasSkillMap()) {
          mergeSkillMap(other.getSkillMap());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.CityPB.CityKingdomModelPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.CityPB.CityKingdomModelPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int officeId_ ;
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 officeId = 1;</code>
       * @return Whether the officeId field is set.
       */
      @java.lang.Override
      public boolean hasOfficeId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 officeId = 1;</code>
       * @return The officeId.
       */
      @java.lang.Override
      public int getOfficeId() {
        return officeId_;
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 officeId = 1;</code>
       * @param value The officeId to set.
       * @return This builder for chaining.
       */
      public Builder setOfficeId(int value) {
        bitField0_ |= 0x00000001;
        officeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 officeId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOfficeId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        officeId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB skillMap_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB, com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.Builder, com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPBOrBuilder> skillMapBuilder_;
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       * @return Whether the skillMap field is set.
       */
      public boolean hasSkillMap() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       * @return The skillMap.
       */
      public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB getSkillMap() {
        if (skillMapBuilder_ == null) {
          return skillMap_ == null ? com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.getDefaultInstance() : skillMap_;
        } else {
          return skillMapBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       */
      public Builder setSkillMap(com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB value) {
        if (skillMapBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          skillMap_ = value;
          onChanged();
        } else {
          skillMapBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       */
      public Builder setSkillMap(
          com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.Builder builderForValue) {
        if (skillMapBuilder_ == null) {
          skillMap_ = builderForValue.build();
          onChanged();
        } else {
          skillMapBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       */
      public Builder mergeSkillMap(com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB value) {
        if (skillMapBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              skillMap_ != null &&
              skillMap_ != com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.getDefaultInstance()) {
            skillMap_ =
              com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.newBuilder(skillMap_).mergeFrom(value).buildPartial();
          } else {
            skillMap_ = value;
          }
          onChanged();
        } else {
          skillMapBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       */
      public Builder clearSkillMap() {
        if (skillMapBuilder_ == null) {
          skillMap_ = null;
          onChanged();
        } else {
          skillMapBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       */
      public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.Builder getSkillMapBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSkillMapFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       */
      public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPBOrBuilder getSkillMapOrBuilder() {
        if (skillMapBuilder_ != null) {
          return skillMapBuilder_.getMessageOrBuilder();
        } else {
          return skillMap_ == null ?
              com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.getDefaultInstance() : skillMap_;
        }
      }
      /**
       * <pre>
       * 王国技能作用在主堡上需要的信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CityKingdomSkillInfoMapPB skillMap = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB, com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.Builder, com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPBOrBuilder> 
          getSkillMapFieldBuilder() {
        if (skillMapBuilder_ == null) {
          skillMapBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB, com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.Builder, com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPBOrBuilder>(
                  getSkillMap(),
                  getParentForChildren(),
                  isClean());
          skillMap_ = null;
        }
        return skillMapBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CityKingdomModelPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CityKingdomModelPB)
    private static final com.yorha.proto.CityPB.CityKingdomModelPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.CityPB.CityKingdomModelPB();
    }

    public static com.yorha.proto.CityPB.CityKingdomModelPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CityKingdomModelPB>
        PARSER = new com.google.protobuf.AbstractParser<CityKingdomModelPB>() {
      @java.lang.Override
      public CityKingdomModelPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CityKingdomModelPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CityKingdomModelPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityKingdomModelPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.CityPB.CityKingdomModelPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityKingdomSkillInfoPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CityKingdomSkillInfoPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return Whether the skillId field is set.
     */
    boolean hasSkillId();
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return The skillId.
     */
    int getSkillId();

    /**
     * <pre>
     * 技能结束的时间戳
     * </pre>
     *
     * <code>optional int64 skillEndTsMs = 2;</code>
     * @return Whether the skillEndTsMs field is set.
     */
    boolean hasSkillEndTsMs();
    /**
     * <pre>
     * 技能结束的时间戳
     * </pre>
     *
     * <code>optional int64 skillEndTsMs = 2;</code>
     * @return The skillEndTsMs.
     */
    long getSkillEndTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CityKingdomSkillInfoPB}
   */
  public static final class CityKingdomSkillInfoPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CityKingdomSkillInfoPB)
      CityKingdomSkillInfoPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CityKingdomSkillInfoPB.newBuilder() to construct.
    private CityKingdomSkillInfoPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CityKingdomSkillInfoPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CityKingdomSkillInfoPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CityKingdomSkillInfoPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              skillId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              skillEndTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomSkillInfoPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomSkillInfoPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.CityPB.CityKingdomSkillInfoPB.class, com.yorha.proto.CityPB.CityKingdomSkillInfoPB.Builder.class);
    }

    private int bitField0_;
    public static final int SKILLID_FIELD_NUMBER = 1;
    private int skillId_;
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return Whether the skillId field is set.
     */
    @java.lang.Override
    public boolean hasSkillId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return The skillId.
     */
    @java.lang.Override
    public int getSkillId() {
      return skillId_;
    }

    public static final int SKILLENDTSMS_FIELD_NUMBER = 2;
    private long skillEndTsMs_;
    /**
     * <pre>
     * 技能结束的时间戳
     * </pre>
     *
     * <code>optional int64 skillEndTsMs = 2;</code>
     * @return Whether the skillEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasSkillEndTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 技能结束的时间戳
     * </pre>
     *
     * <code>optional int64 skillEndTsMs = 2;</code>
     * @return The skillEndTsMs.
     */
    @java.lang.Override
    public long getSkillEndTsMs() {
      return skillEndTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, skillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, skillEndTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, skillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, skillEndTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.CityPB.CityKingdomSkillInfoPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.CityPB.CityKingdomSkillInfoPB other = (com.yorha.proto.CityPB.CityKingdomSkillInfoPB) obj;

      if (hasSkillId() != other.hasSkillId()) return false;
      if (hasSkillId()) {
        if (getSkillId()
            != other.getSkillId()) return false;
      }
      if (hasSkillEndTsMs() != other.hasSkillEndTsMs()) return false;
      if (hasSkillEndTsMs()) {
        if (getSkillEndTsMs()
            != other.getSkillEndTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkillId()) {
        hash = (37 * hash) + SKILLID_FIELD_NUMBER;
        hash = (53 * hash) + getSkillId();
      }
      if (hasSkillEndTsMs()) {
        hash = (37 * hash) + SKILLENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSkillEndTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.CityPB.CityKingdomSkillInfoPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CityKingdomSkillInfoPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CityKingdomSkillInfoPB)
        com.yorha.proto.CityPB.CityKingdomSkillInfoPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomSkillInfoPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomSkillInfoPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.CityPB.CityKingdomSkillInfoPB.class, com.yorha.proto.CityPB.CityKingdomSkillInfoPB.Builder.class);
      }

      // Construct using com.yorha.proto.CityPB.CityKingdomSkillInfoPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skillId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        skillEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_CityKingdomSkillInfoPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDefaultInstanceForType() {
        return com.yorha.proto.CityPB.CityKingdomSkillInfoPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityKingdomSkillInfoPB build() {
        com.yorha.proto.CityPB.CityKingdomSkillInfoPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.CityKingdomSkillInfoPB buildPartial() {
        com.yorha.proto.CityPB.CityKingdomSkillInfoPB result = new com.yorha.proto.CityPB.CityKingdomSkillInfoPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.skillId_ = skillId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.skillEndTsMs_ = skillEndTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.CityPB.CityKingdomSkillInfoPB) {
          return mergeFrom((com.yorha.proto.CityPB.CityKingdomSkillInfoPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.CityPB.CityKingdomSkillInfoPB other) {
        if (other == com.yorha.proto.CityPB.CityKingdomSkillInfoPB.getDefaultInstance()) return this;
        if (other.hasSkillId()) {
          setSkillId(other.getSkillId());
        }
        if (other.hasSkillEndTsMs()) {
          setSkillEndTsMs(other.getSkillEndTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.CityPB.CityKingdomSkillInfoPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.CityPB.CityKingdomSkillInfoPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int skillId_ ;
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return Whether the skillId field is set.
       */
      @java.lang.Override
      public boolean hasSkillId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return The skillId.
       */
      @java.lang.Override
      public int getSkillId() {
        return skillId_;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @param value The skillId to set.
       * @return This builder for chaining.
       */
      public Builder setSkillId(int value) {
        bitField0_ |= 0x00000001;
        skillId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        skillId_ = 0;
        onChanged();
        return this;
      }

      private long skillEndTsMs_ ;
      /**
       * <pre>
       * 技能结束的时间戳
       * </pre>
       *
       * <code>optional int64 skillEndTsMs = 2;</code>
       * @return Whether the skillEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasSkillEndTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 技能结束的时间戳
       * </pre>
       *
       * <code>optional int64 skillEndTsMs = 2;</code>
       * @return The skillEndTsMs.
       */
      @java.lang.Override
      public long getSkillEndTsMs() {
        return skillEndTsMs_;
      }
      /**
       * <pre>
       * 技能结束的时间戳
       * </pre>
       *
       * <code>optional int64 skillEndTsMs = 2;</code>
       * @param value The skillEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setSkillEndTsMs(long value) {
        bitField0_ |= 0x00000002;
        skillEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能结束的时间戳
       * </pre>
       *
       * <code>optional int64 skillEndTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        skillEndTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CityKingdomSkillInfoPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CityKingdomSkillInfoPB)
    private static final com.yorha.proto.CityPB.CityKingdomSkillInfoPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.CityPB.CityKingdomSkillInfoPB();
    }

    public static com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CityKingdomSkillInfoPB>
        PARSER = new com.google.protobuf.AbstractParser<CityKingdomSkillInfoPB>() {
      @java.lang.Override
      public CityKingdomSkillInfoPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CityKingdomSkillInfoPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CityKingdomSkillInfoPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityKingdomSkillInfoPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int32CityKingdomSkillInfoMapPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32CityKingdomSkillInfoMapPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */
    boolean containsDatas(
        int key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>
    getDatas();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>
    getDatasMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */

    com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDatasOrDefault(
        int key,
        com.yorha.proto.CityPB.CityKingdomSkillInfoPB defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */

    com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDatasOrThrow(
        int key);

    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Integer> getDeleteKeysList();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    int getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32CityKingdomSkillInfoMapPB}
   */
  public static final class Int32CityKingdomSkillInfoMapPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32CityKingdomSkillInfoMapPB)
      Int32CityKingdomSkillInfoMapPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32CityKingdomSkillInfoMapPB.newBuilder() to construct.
    private Int32CityKingdomSkillInfoMapPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32CityKingdomSkillInfoMapPB() {
      deleteKeys_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32CityKingdomSkillInfoMapPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32CityKingdomSkillInfoMapPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.CityPB.internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.CityPB.internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.class, com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>newDefaultInstance(
                  com.yorha.proto.CityPB.internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.CityPB.CityKingdomSkillInfoPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> datas_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        int key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDatasOrDefault(
        int key,
        com.yorha.proto.CityPB.CityKingdomSkillInfoPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDatasOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList deleteKeys_;
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public int getDeleteKeys(int index) {
      return deleteKeys_.getInt(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt32(2, deleteKeys_.getInt(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(deleteKeys_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB other = (com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32CityKingdomSkillInfoMapPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32CityKingdomSkillInfoMapPB)
        com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.class, com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.Builder.class);
      }

      // Construct using com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.CityPB.internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB getDefaultInstanceForType() {
        return com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB build() {
        com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB buildPartial() {
        com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB result = new com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB) {
          return mergeFrom((com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB other) {
        if (other == com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> datas_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          int key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDatasOrDefault(
          int key,
          com.yorha.proto.CityPB.CityKingdomSkillInfoPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CityPB.CityKingdomSkillInfoPB getDatasOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          int key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
       */
      public Builder putDatas(
          int key,
          com.yorha.proto.CityPB.CityKingdomSkillInfoPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CityKingdomSkillInfoPB&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Integer, com.yorha.proto.CityPB.CityKingdomSkillInfoPB> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.IntList deleteKeys_ = emptyIntList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Integer>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public int getDeleteKeys(int index) {
        return deleteKeys_.getInt(index);
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32CityKingdomSkillInfoMapPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32CityKingdomSkillInfoMapPB)
    private static final com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB();
    }

    public static com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32CityKingdomSkillInfoMapPB>
        PARSER = new com.google.protobuf.AbstractParser<Int32CityKingdomSkillInfoMapPB>() {
      @java.lang.Override
      public Int32CityKingdomSkillInfoMapPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32CityKingdomSkillInfoMapPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32CityKingdomSkillInfoMapPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32CityKingdomSkillInfoMapPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.CityPB.Int32CityKingdomSkillInfoMapPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CityEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CityEntityPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CityKingdomModelPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CityKingdomModelPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CityKingdomSkillInfoPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CityKingdomSkillInfoPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_DatasEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\036cs_proto/gen/city/cityPB.proto\022\017com.yo" +
      "rha.proto\032\"cs_proto/gen/common/structPB." +
      "proto\032)cs_proto/gen/common/struct_battle" +
      "PB.proto\032)cs_proto/gen/common/struct_pla" +
      "yerPB.proto\032%ss_proto/gen/common/common_" +
      "enum.proto\"\317\007\n\014CityEntityPB\022\'\n\005point\030\001 \001" +
      "(\0132\030.com.yorha.proto.PointPB\022\017\n\007ownerId\030" +
      "\002 \001(\003\022\r\n\005level\030\003 \001(\005\022#\n\004camp\030\004 \001(\0162\025.com" +
      ".yorha.proto.Camp\0221\n\twallState\030\005 \001(\0162\036.c" +
      "om.yorha.proto.CityWallState\022\'\n\005troop\030\006 " +
      "\001(\0132\030.com.yorha.proto.TroopPB\022)\n\006battle\030" +
      "\007 \001(\0132\031.com.yorha.proto.BattlePB\022\021\n\tclan" +
      "Sname\030\010 \001(\t\022\016\n\006clanId\030\t \001(\003\0221\n\010garrison\030" +
      "\013 \001(\0132\037.com.yorha.proto.CityGarrisonPB\022+" +
      "\n\007buffSys\030\014 \001(\0132\032.com.yorha.proto.BuffSy" +
      "sPB\0223\n\013peaceShield\030\r \001(\0132\036.com.yorha.pro" +
      "to.PeaceShieldPB\0226\n\ndevBuffSys\030\016 \001(\0132\".c" +
      "om.yorha.proto.SceneDevBuffSysPB\0227\n\014asce" +
      "ndReason\030\017 \001(\0162!.com.yorha.proto.CityAsc" +
      "endReason\0226\n\tsafeGuard\030\020 \001(\0132#.com.yorha" +
      ".proto.SpecialSafeGuardPB\0223\n\010cardHead\030\021 " +
      "\001(\0132!.com.yorha.proto.PlayerCardHeadPB\0227" +
      "\n\005arrow\030\022 \001(\0132(.com.yorha.proto.Int64Arm" +
      "yArrowItemMapPB\022\020\n\010eraLevel\030\023 \001(\005\022\022\n\ntem" +
      "plateId\030\024 \001(\005\022\024\n\014guardTowerHp\030\025 \001(\005\0221\n\ne" +
      "xpression\030\026 \001(\0132\035.com.yorha.proto.Expres" +
      "sionPB\022\017\n\007pFlagId\030\027 \001(\005\022\027\n\017dressTemplate" +
      "Id\030\030 \001(\005\022=\n\020cityKingdomModel\030\032 \001(\0132#.com" +
      ".yorha.proto.CityKingdomModelPB\022\023\n\013namep" +
      "lateId\030\033 \001(\005\022\016\n\006zoneId\030\035 \001(\005\"i\n\022CityKing" +
      "domModelPB\022\020\n\010officeId\030\001 \001(\005\022A\n\010skillMap" +
      "\030\002 \001(\0132/.com.yorha.proto.Int32CityKingdo" +
      "mSkillInfoMapPB\"?\n\026CityKingdomSkillInfoP" +
      "B\022\017\n\007skillId\030\001 \001(\005\022\024\n\014skillEndTsMs\030\002 \001(\003" +
      "\"\351\001\n\036Int32CityKingdomSkillInfoMapPB\022I\n\005d" +
      "atas\030\001 \003(\0132:.com.yorha.proto.Int32CityKi" +
      "ngdomSkillInfoMapPB.DatasEntry\022\022\n\ndelete" +
      "Keys\030\002 \003(\005\022\021\n\tclearFlag\030\003 \001(\010\032U\n\nDatasEn" +
      "try\022\013\n\003key\030\001 \001(\005\0226\n\005value\030\002 \001(\0132\'.com.yo" +
      "rha.proto.CityKingdomSkillInfoPB:\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.StructBattlePB.getDescriptor(),
          com.yorha.proto.StructPlayerPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_CityEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_CityEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CityEntityPB_descriptor,
        new java.lang.String[] { "Point", "OwnerId", "Level", "Camp", "WallState", "Troop", "Battle", "ClanSname", "ClanId", "Garrison", "BuffSys", "PeaceShield", "DevBuffSys", "AscendReason", "SafeGuard", "CardHead", "Arrow", "EraLevel", "TemplateId", "GuardTowerHp", "Expression", "PFlagId", "DressTemplateId", "CityKingdomModel", "NameplateId", "ZoneId", });
    internal_static_com_yorha_proto_CityKingdomModelPB_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_CityKingdomModelPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CityKingdomModelPB_descriptor,
        new java.lang.String[] { "OfficeId", "SkillMap", });
    internal_static_com_yorha_proto_CityKingdomSkillInfoPB_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_CityKingdomSkillInfoPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CityKingdomSkillInfoPB_descriptor,
        new java.lang.String[] { "SkillId", "SkillEndTsMs", });
    internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32CityKingdomSkillInfoMapPB_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.StructBattlePB.getDescriptor();
    com.yorha.proto.StructPlayerPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

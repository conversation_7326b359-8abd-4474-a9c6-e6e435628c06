// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/entity/entity_attr.proto

package com.yorha.proto;

public final class EntityAttrOuterClass {
  private EntityAttrOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code com.yorha.proto.EntityType}
   */
  public enum EntityType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 未知
     * </pre>
     *
     * <code>ET_Unknown = 0;</code>
     */
    ET_Unknown(0),
    /**
     * <pre>
     * 行军
     * </pre>
     *
     * <code>ET_Army = 1;</code>
     */
    ET_Army(1),
    /**
     * <pre>
     * 城池
     * </pre>
     *
     * <code>ET_City = 2;</code>
     */
    ET_City(2),
    /**
     * <pre>
     * 玩家
     * </pre>
     *
     * <code>ET_Player = 3;</code>
     */
    ET_Player(3),
    /**
     * <pre>
     * 野怪
     * </pre>
     *
     * <code>ET_Monster = 4;</code>
     */
    ET_Monster(4),
    /**
     * <pre>
     * 大世界
     * </pre>
     *
     * <code>ET_BigScene = 5;</code>
     */
    ET_BigScene(5),
    /**
     * <pre>
     * 联盟
     * </pre>
     *
     * <code>ET_Clan = 6;</code>
     */
    ET_Clan(6),
    /**
     * <pre>
     * 拾取物
     * </pre>
     *
     * <code>ET_DropObject = 7;</code>
     */
    ET_DropObject(7),
    /**
     * <pre>
     * 地图建筑
     * </pre>
     *
     * <code>ET_MapBuilding = 8;</code>
     */
    ET_MapBuilding(8),
    /**
     * <pre>
     * 侦察机
     * </pre>
     *
     * <code>ET_SpyPlane = 9;</code>
     */
    ET_SpyPlane(9),
    /**
     * <pre>
     * 资源田
     * </pre>
     *
     * <code>ET_ResBuilding = 10;</code>
     */
    ET_ResBuilding(10),
    /**
     * <pre>
     * 小服信息
     * </pre>
     *
     * <code>ET_Zone = 11;</code>
     */
    ET_Zone(11),
    /**
     * <pre>
     * 副本场景数据
     * </pre>
     *
     * <code>ET_Dungeon = 12;</code>
     */
    ET_Dungeon(12),
    /**
     * <pre>
     * 超武附属建筑
     * </pre>
     *
     * <code>ET_Outbuilding = 15;</code>
     */
    ET_Outbuilding(15),
    /**
     * <pre>
     * 山洞
     * </pre>
     *
     * <code>ET_Cave = 16;</code>
     */
    ET_Cave(16),
    /**
     * <pre>
     * 物流飞机，用于资源援助
     * </pre>
     *
     * <code>ET_LogisticsPlane = 18;</code>
     */
    ET_LogisticsPlane(18),
    /**
     * <pre>
     * 军团资源田
     * </pre>
     *
     * <code>ET_ClanResBuilding = 19;</code>
     */
    ET_ClanResBuilding(19),
    /**
     * <pre>
     * 副本建筑
     * </pre>
     *
     * <code>ET_DungeonBuilding = 20;</code>
     */
    ET_DungeonBuilding(20),
    /**
     * <pre>
     * 区域技能
     * </pre>
     *
     * <code>ET_AreaSkill = 21;</code>
     */
    ET_AreaSkill(21),
    /**
     * <pre>
     * 客户端用
     * </pre>
     *
     * <code>ET_BuildingGroup = 22;</code>
     */
    ET_BuildingGroup(22),
    ;

    /**
     * <pre>
     * 未知
     * </pre>
     *
     * <code>ET_Unknown = 0;</code>
     */
    public static final int ET_Unknown_VALUE = 0;
    /**
     * <pre>
     * 行军
     * </pre>
     *
     * <code>ET_Army = 1;</code>
     */
    public static final int ET_Army_VALUE = 1;
    /**
     * <pre>
     * 城池
     * </pre>
     *
     * <code>ET_City = 2;</code>
     */
    public static final int ET_City_VALUE = 2;
    /**
     * <pre>
     * 玩家
     * </pre>
     *
     * <code>ET_Player = 3;</code>
     */
    public static final int ET_Player_VALUE = 3;
    /**
     * <pre>
     * 野怪
     * </pre>
     *
     * <code>ET_Monster = 4;</code>
     */
    public static final int ET_Monster_VALUE = 4;
    /**
     * <pre>
     * 大世界
     * </pre>
     *
     * <code>ET_BigScene = 5;</code>
     */
    public static final int ET_BigScene_VALUE = 5;
    /**
     * <pre>
     * 联盟
     * </pre>
     *
     * <code>ET_Clan = 6;</code>
     */
    public static final int ET_Clan_VALUE = 6;
    /**
     * <pre>
     * 拾取物
     * </pre>
     *
     * <code>ET_DropObject = 7;</code>
     */
    public static final int ET_DropObject_VALUE = 7;
    /**
     * <pre>
     * 地图建筑
     * </pre>
     *
     * <code>ET_MapBuilding = 8;</code>
     */
    public static final int ET_MapBuilding_VALUE = 8;
    /**
     * <pre>
     * 侦察机
     * </pre>
     *
     * <code>ET_SpyPlane = 9;</code>
     */
    public static final int ET_SpyPlane_VALUE = 9;
    /**
     * <pre>
     * 资源田
     * </pre>
     *
     * <code>ET_ResBuilding = 10;</code>
     */
    public static final int ET_ResBuilding_VALUE = 10;
    /**
     * <pre>
     * 小服信息
     * </pre>
     *
     * <code>ET_Zone = 11;</code>
     */
    public static final int ET_Zone_VALUE = 11;
    /**
     * <pre>
     * 副本场景数据
     * </pre>
     *
     * <code>ET_Dungeon = 12;</code>
     */
    public static final int ET_Dungeon_VALUE = 12;
    /**
     * <pre>
     * 超武附属建筑
     * </pre>
     *
     * <code>ET_Outbuilding = 15;</code>
     */
    public static final int ET_Outbuilding_VALUE = 15;
    /**
     * <pre>
     * 山洞
     * </pre>
     *
     * <code>ET_Cave = 16;</code>
     */
    public static final int ET_Cave_VALUE = 16;
    /**
     * <pre>
     * 物流飞机，用于资源援助
     * </pre>
     *
     * <code>ET_LogisticsPlane = 18;</code>
     */
    public static final int ET_LogisticsPlane_VALUE = 18;
    /**
     * <pre>
     * 军团资源田
     * </pre>
     *
     * <code>ET_ClanResBuilding = 19;</code>
     */
    public static final int ET_ClanResBuilding_VALUE = 19;
    /**
     * <pre>
     * 副本建筑
     * </pre>
     *
     * <code>ET_DungeonBuilding = 20;</code>
     */
    public static final int ET_DungeonBuilding_VALUE = 20;
    /**
     * <pre>
     * 区域技能
     * </pre>
     *
     * <code>ET_AreaSkill = 21;</code>
     */
    public static final int ET_AreaSkill_VALUE = 21;
    /**
     * <pre>
     * 客户端用
     * </pre>
     *
     * <code>ET_BuildingGroup = 22;</code>
     */
    public static final int ET_BuildingGroup_VALUE = 22;


    public final int getNumber() {
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static EntityType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static EntityType forNumber(int value) {
      switch (value) {
        case 0: return ET_Unknown;
        case 1: return ET_Army;
        case 2: return ET_City;
        case 3: return ET_Player;
        case 4: return ET_Monster;
        case 5: return ET_BigScene;
        case 6: return ET_Clan;
        case 7: return ET_DropObject;
        case 8: return ET_MapBuilding;
        case 9: return ET_SpyPlane;
        case 10: return ET_ResBuilding;
        case 11: return ET_Zone;
        case 12: return ET_Dungeon;
        case 15: return ET_Outbuilding;
        case 16: return ET_Cave;
        case 18: return ET_LogisticsPlane;
        case 19: return ET_ClanResBuilding;
        case 20: return ET_DungeonBuilding;
        case 21: return ET_AreaSkill;
        case 22: return ET_BuildingGroup;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<EntityType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        EntityType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<EntityType>() {
            public EntityType findValueByNumber(int number) {
              return EntityType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.yorha.proto.EntityAttrOuterClass.getDescriptor().getEnumTypes().get(0);
    }

    private static final EntityType[] VALUES = values();

    public static EntityType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private EntityType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:com.yorha.proto.EntityType)
  }

  public interface EntityAttrOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.EntityAttr)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return Whether the entityType field is set.
     */
    boolean hasEntityType();
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return The entityType.
     */
    com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType();

    /**
     * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
     * @return Whether the armyAttr field is set.
     */
    boolean hasArmyAttr();
    /**
     * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
     * @return The armyAttr.
     */
    com.yorha.proto.ArmyPB.ArmyEntityPB getArmyAttr();
    /**
     * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
     */
    com.yorha.proto.ArmyPB.ArmyEntityPBOrBuilder getArmyAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
     * @return Whether the cityAttr field is set.
     */
    boolean hasCityAttr();
    /**
     * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
     * @return The cityAttr.
     */
    com.yorha.proto.CityPB.CityEntityPB getCityAttr();
    /**
     * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
     */
    com.yorha.proto.CityPB.CityEntityPBOrBuilder getCityAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
     * @return Whether the playerAttr field is set.
     */
    boolean hasPlayerAttr();
    /**
     * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
     * @return The playerAttr.
     */
    com.yorha.proto.PlayerPB.PlayerEntityPB getPlayerAttr();
    /**
     * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
     */
    com.yorha.proto.PlayerPB.PlayerEntityPBOrBuilder getPlayerAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
     * @return Whether the monsterAttr field is set.
     */
    boolean hasMonsterAttr();
    /**
     * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
     * @return The monsterAttr.
     */
    com.yorha.proto.MonsterPB.MonsterEntityPB getMonsterAttr();
    /**
     * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
     */
    com.yorha.proto.MonsterPB.MonsterEntityPBOrBuilder getMonsterAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
     * @return Whether the clanAttr field is set.
     */
    boolean hasClanAttr();
    /**
     * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
     * @return The clanAttr.
     */
    com.yorha.proto.ClanPB.ClanEntityPB getClanAttr();
    /**
     * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
     */
    com.yorha.proto.ClanPB.ClanEntityPBOrBuilder getClanAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
     * @return Whether the dropObjectAttr field is set.
     */
    boolean hasDropObjectAttr();
    /**
     * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
     * @return The dropObjectAttr.
     */
    com.yorha.proto.DropObjectPB.DropObjectEntityPB getDropObjectAttr();
    /**
     * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
     */
    com.yorha.proto.DropObjectPB.DropObjectEntityPBOrBuilder getDropObjectAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
     * @return Whether the mapBuildingAttr field is set.
     */
    boolean hasMapBuildingAttr();
    /**
     * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
     * @return The mapBuildingAttr.
     */
    com.yorha.proto.MapBuildingPB.MapBuildingEntityPB getMapBuildingAttr();
    /**
     * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
     */
    com.yorha.proto.MapBuildingPB.MapBuildingEntityPBOrBuilder getMapBuildingAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
     * @return Whether the spyPlane field is set.
     */
    boolean hasSpyPlane();
    /**
     * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
     * @return The spyPlane.
     */
    com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB getSpyPlane();
    /**
     * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
     */
    com.yorha.proto.SpyPlanePB.SpyPlaneEntityPBOrBuilder getSpyPlaneOrBuilder();

    /**
     * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
     * @return Whether the resBuildingAttr field is set.
     */
    boolean hasResBuildingAttr();
    /**
     * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
     * @return The resBuildingAttr.
     */
    com.yorha.proto.ResBuildingPB.ResBuildingEntityPB getResBuildingAttr();
    /**
     * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
     */
    com.yorha.proto.ResBuildingPB.ResBuildingEntityPBOrBuilder getResBuildingAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
     * @return Whether the zoneInfo field is set.
     */
    boolean hasZoneInfo();
    /**
     * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
     * @return The zoneInfo.
     */
    com.yorha.proto.ZonePB.ZoneInfoEntityPB getZoneInfo();
    /**
     * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
     */
    com.yorha.proto.ZonePB.ZoneInfoEntityPBOrBuilder getZoneInfoOrBuilder();

    /**
     * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
     * @return Whether the dungeonSceneAttr field is set.
     */
    boolean hasDungeonSceneAttr();
    /**
     * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
     * @return The dungeonSceneAttr.
     */
    com.yorha.proto.DungeonPB.DungeonSceneEntityPB getDungeonSceneAttr();
    /**
     * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
     */
    com.yorha.proto.DungeonPB.DungeonSceneEntityPBOrBuilder getDungeonSceneAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
     * @return Whether the outbuildingAttr field is set.
     */
    boolean hasOutbuildingAttr();
    /**
     * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
     * @return The outbuildingAttr.
     */
    com.yorha.proto.OutbuildingPB.OutbuildingEntityPB getOutbuildingAttr();
    /**
     * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
     */
    com.yorha.proto.OutbuildingPB.OutbuildingEntityPBOrBuilder getOutbuildingAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
     * @return Whether the caveAttr field is set.
     */
    boolean hasCaveAttr();
    /**
     * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
     * @return The caveAttr.
     */
    com.yorha.proto.CavePB.CaveEntityPB getCaveAttr();
    /**
     * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
     */
    com.yorha.proto.CavePB.CaveEntityPBOrBuilder getCaveAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
     * @return Whether the logisticsPlaneAttr field is set.
     */
    boolean hasLogisticsPlaneAttr();
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
     * @return The logisticsPlaneAttr.
     */
    com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB getLogisticsPlaneAttr();
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
     */
    com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPBOrBuilder getLogisticsPlaneAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
     * @return Whether the clanResBuildingAttr field is set.
     */
    boolean hasClanResBuildingAttr();
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
     * @return The clanResBuildingAttr.
     */
    com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB getClanResBuildingAttr();
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
     */
    com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPBOrBuilder getClanResBuildingAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
     * @return Whether the dungeonBuildingAttr field is set.
     */
    boolean hasDungeonBuildingAttr();
    /**
     * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
     * @return The dungeonBuildingAttr.
     */
    com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB getDungeonBuildingAttr();
    /**
     * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
     */
    com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPBOrBuilder getDungeonBuildingAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
     * @return Whether the areaSkillAttr field is set.
     */
    boolean hasAreaSkillAttr();
    /**
     * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
     * @return The areaSkillAttr.
     */
    com.yorha.proto.AreaSkillPB.AreaSkillEntityPB getAreaSkillAttr();
    /**
     * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
     */
    com.yorha.proto.AreaSkillPB.AreaSkillEntityPBOrBuilder getAreaSkillAttrOrBuilder();

    public com.yorha.proto.EntityAttrOuterClass.EntityAttr.EntityAttrCase getEntityAttrCase();
  }
  /**
   * Protobuf type {@code com.yorha.proto.EntityAttr}
   */
  public static final class EntityAttr extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.EntityAttr)
      EntityAttrOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EntityAttr.newBuilder() to construct.
    private EntityAttr(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EntityAttr() {
      entityType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EntityAttr();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private EntityAttr(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              entityId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.EntityAttrOuterClass.EntityType value = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                entityType_ = rawValue;
              }
              break;
            }
            case 90: {
              com.yorha.proto.ArmyPB.ArmyEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 11) {
                subBuilder = ((com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.ArmyPB.ArmyEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 11;
              break;
            }
            case 98: {
              com.yorha.proto.CityPB.CityEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 12) {
                subBuilder = ((com.yorha.proto.CityPB.CityEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.CityPB.CityEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.CityPB.CityEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 12;
              break;
            }
            case 106: {
              com.yorha.proto.PlayerPB.PlayerEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 13) {
                subBuilder = ((com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.PlayerPB.PlayerEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 13;
              break;
            }
            case 114: {
              com.yorha.proto.MonsterPB.MonsterEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 14) {
                subBuilder = ((com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.MonsterPB.MonsterEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 14;
              break;
            }
            case 122: {
              com.yorha.proto.ClanPB.ClanEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 15) {
                subBuilder = ((com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.ClanPB.ClanEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 15;
              break;
            }
            case 130: {
              com.yorha.proto.DropObjectPB.DropObjectEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 16) {
                subBuilder = ((com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.DropObjectPB.DropObjectEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 16;
              break;
            }
            case 138: {
              com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 17) {
                subBuilder = ((com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 17;
              break;
            }
            case 146: {
              com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 18) {
                subBuilder = ((com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 18;
              break;
            }
            case 154: {
              com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 19) {
                subBuilder = ((com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 19;
              break;
            }
            case 162: {
              com.yorha.proto.ZonePB.ZoneInfoEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 20) {
                subBuilder = ((com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.ZonePB.ZoneInfoEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 20;
              break;
            }
            case 170: {
              com.yorha.proto.DungeonPB.DungeonSceneEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 21) {
                subBuilder = ((com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.DungeonPB.DungeonSceneEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 21;
              break;
            }
            case 186: {
              com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 23) {
                subBuilder = ((com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 23;
              break;
            }
            case 194: {
              com.yorha.proto.CavePB.CaveEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 24) {
                subBuilder = ((com.yorha.proto.CavePB.CaveEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.CavePB.CaveEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.CavePB.CaveEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 24;
              break;
            }
            case 202: {
              com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 25) {
                subBuilder = ((com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 25;
              break;
            }
            case 210: {
              com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 26) {
                subBuilder = ((com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 26;
              break;
            }
            case 218: {
              com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 27) {
                subBuilder = ((com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 27;
              break;
            }
            case 226: {
              com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.Builder subBuilder = null;
              if (entityAttrCase_ == 28) {
                subBuilder = ((com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 28;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.EntityAttrOuterClass.internal_static_com_yorha_proto_EntityAttr_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.EntityAttrOuterClass.internal_static_com_yorha_proto_EntityAttr_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.EntityAttrOuterClass.EntityAttr.class, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder.class);
    }

    private int bitField0_;
    private int entityAttrCase_ = 0;
    private java.lang.Object entityAttr_;
    public enum EntityAttrCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      ARMYATTR(11),
      CITYATTR(12),
      PLAYERATTR(13),
      MONSTERATTR(14),
      CLANATTR(15),
      DROPOBJECTATTR(16),
      MAPBUILDINGATTR(17),
      SPYPLANE(18),
      RESBUILDINGATTR(19),
      ZONEINFO(20),
      DUNGEONSCENEATTR(21),
      OUTBUILDINGATTR(23),
      CAVEATTR(24),
      LOGISTICSPLANEATTR(25),
      CLANRESBUILDINGATTR(26),
      DUNGEONBUILDINGATTR(27),
      AREASKILLATTR(28),
      ENTITYATTR_NOT_SET(0);
      private final int value;
      private EntityAttrCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static EntityAttrCase valueOf(int value) {
        return forNumber(value);
      }

      public static EntityAttrCase forNumber(int value) {
        switch (value) {
          case 11: return ARMYATTR;
          case 12: return CITYATTR;
          case 13: return PLAYERATTR;
          case 14: return MONSTERATTR;
          case 15: return CLANATTR;
          case 16: return DROPOBJECTATTR;
          case 17: return MAPBUILDINGATTR;
          case 18: return SPYPLANE;
          case 19: return RESBUILDINGATTR;
          case 20: return ZONEINFO;
          case 21: return DUNGEONSCENEATTR;
          case 23: return OUTBUILDINGATTR;
          case 24: return CAVEATTR;
          case 25: return LOGISTICSPLANEATTR;
          case 26: return CLANRESBUILDINGATTR;
          case 27: return DUNGEONBUILDINGATTR;
          case 28: return AREASKILLATTR;
          case 0: return ENTITYATTR_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public EntityAttrCase
    getEntityAttrCase() {
      return EntityAttrCase.forNumber(
          entityAttrCase_);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int ENTITYTYPE_FIELD_NUMBER = 2;
    private int entityType_;
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return Whether the entityType field is set.
     */
    @java.lang.Override public boolean hasEntityType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return The entityType.
     */
    @java.lang.Override public com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.EntityAttrOuterClass.EntityType result = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(entityType_);
      return result == null ? com.yorha.proto.EntityAttrOuterClass.EntityType.ET_Unknown : result;
    }

    public static final int ARMYATTR_FIELD_NUMBER = 11;
    /**
     * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
     * @return Whether the armyAttr field is set.
     */
    @java.lang.Override
    public boolean hasArmyAttr() {
      return entityAttrCase_ == 11;
    }
    /**
     * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
     * @return The armyAttr.
     */
    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyEntityPB getArmyAttr() {
      if (entityAttrCase_ == 11) {
         return (com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_;
      }
      return com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyEntityPBOrBuilder getArmyAttrOrBuilder() {
      if (entityAttrCase_ == 11) {
         return (com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_;
      }
      return com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance();
    }

    public static final int CITYATTR_FIELD_NUMBER = 12;
    /**
     * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
     * @return Whether the cityAttr field is set.
     */
    @java.lang.Override
    public boolean hasCityAttr() {
      return entityAttrCase_ == 12;
    }
    /**
     * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
     * @return The cityAttr.
     */
    @java.lang.Override
    public com.yorha.proto.CityPB.CityEntityPB getCityAttr() {
      if (entityAttrCase_ == 12) {
         return (com.yorha.proto.CityPB.CityEntityPB) entityAttr_;
      }
      return com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CityPB.CityEntityPBOrBuilder getCityAttrOrBuilder() {
      if (entityAttrCase_ == 12) {
         return (com.yorha.proto.CityPB.CityEntityPB) entityAttr_;
      }
      return com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance();
    }

    public static final int PLAYERATTR_FIELD_NUMBER = 13;
    /**
     * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
     * @return Whether the playerAttr field is set.
     */
    @java.lang.Override
    public boolean hasPlayerAttr() {
      return entityAttrCase_ == 13;
    }
    /**
     * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
     * @return The playerAttr.
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPB.PlayerEntityPB getPlayerAttr() {
      if (entityAttrCase_ == 13) {
         return (com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_;
      }
      return com.yorha.proto.PlayerPB.PlayerEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPB.PlayerEntityPBOrBuilder getPlayerAttrOrBuilder() {
      if (entityAttrCase_ == 13) {
         return (com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_;
      }
      return com.yorha.proto.PlayerPB.PlayerEntityPB.getDefaultInstance();
    }

    public static final int MONSTERATTR_FIELD_NUMBER = 14;
    /**
     * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
     * @return Whether the monsterAttr field is set.
     */
    @java.lang.Override
    public boolean hasMonsterAttr() {
      return entityAttrCase_ == 14;
    }
    /**
     * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
     * @return The monsterAttr.
     */
    @java.lang.Override
    public com.yorha.proto.MonsterPB.MonsterEntityPB getMonsterAttr() {
      if (entityAttrCase_ == 14) {
         return (com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_;
      }
      return com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MonsterPB.MonsterEntityPBOrBuilder getMonsterAttrOrBuilder() {
      if (entityAttrCase_ == 14) {
         return (com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_;
      }
      return com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance();
    }

    public static final int CLANATTR_FIELD_NUMBER = 15;
    /**
     * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
     * @return Whether the clanAttr field is set.
     */
    @java.lang.Override
    public boolean hasClanAttr() {
      return entityAttrCase_ == 15;
    }
    /**
     * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
     * @return The clanAttr.
     */
    @java.lang.Override
    public com.yorha.proto.ClanPB.ClanEntityPB getClanAttr() {
      if (entityAttrCase_ == 15) {
         return (com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_;
      }
      return com.yorha.proto.ClanPB.ClanEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ClanPB.ClanEntityPBOrBuilder getClanAttrOrBuilder() {
      if (entityAttrCase_ == 15) {
         return (com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_;
      }
      return com.yorha.proto.ClanPB.ClanEntityPB.getDefaultInstance();
    }

    public static final int DROPOBJECTATTR_FIELD_NUMBER = 16;
    /**
     * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
     * @return Whether the dropObjectAttr field is set.
     */
    @java.lang.Override
    public boolean hasDropObjectAttr() {
      return entityAttrCase_ == 16;
    }
    /**
     * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
     * @return The dropObjectAttr.
     */
    @java.lang.Override
    public com.yorha.proto.DropObjectPB.DropObjectEntityPB getDropObjectAttr() {
      if (entityAttrCase_ == 16) {
         return (com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_;
      }
      return com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
     */
    @java.lang.Override
    public com.yorha.proto.DropObjectPB.DropObjectEntityPBOrBuilder getDropObjectAttrOrBuilder() {
      if (entityAttrCase_ == 16) {
         return (com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_;
      }
      return com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance();
    }

    public static final int MAPBUILDINGATTR_FIELD_NUMBER = 17;
    /**
     * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
     * @return Whether the mapBuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasMapBuildingAttr() {
      return entityAttrCase_ == 17;
    }
    /**
     * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
     * @return The mapBuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.MapBuildingEntityPB getMapBuildingAttr() {
      if (entityAttrCase_ == 17) {
         return (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.MapBuildingEntityPBOrBuilder getMapBuildingAttrOrBuilder() {
      if (entityAttrCase_ == 17) {
         return (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance();
    }

    public static final int SPYPLANE_FIELD_NUMBER = 18;
    /**
     * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
     * @return Whether the spyPlane field is set.
     */
    @java.lang.Override
    public boolean hasSpyPlane() {
      return entityAttrCase_ == 18;
    }
    /**
     * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
     * @return The spyPlane.
     */
    @java.lang.Override
    public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB getSpyPlane() {
      if (entityAttrCase_ == 18) {
         return (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_;
      }
      return com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPBOrBuilder getSpyPlaneOrBuilder() {
      if (entityAttrCase_ == 18) {
         return (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_;
      }
      return com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance();
    }

    public static final int RESBUILDINGATTR_FIELD_NUMBER = 19;
    /**
     * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
     * @return Whether the resBuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasResBuildingAttr() {
      return entityAttrCase_ == 19;
    }
    /**
     * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
     * @return The resBuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.ResBuildingPB.ResBuildingEntityPB getResBuildingAttr() {
      if (entityAttrCase_ == 19) {
         return (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ResBuildingPB.ResBuildingEntityPBOrBuilder getResBuildingAttrOrBuilder() {
      if (entityAttrCase_ == 19) {
         return (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance();
    }

    public static final int ZONEINFO_FIELD_NUMBER = 20;
    /**
     * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
     * @return Whether the zoneInfo field is set.
     */
    @java.lang.Override
    public boolean hasZoneInfo() {
      return entityAttrCase_ == 20;
    }
    /**
     * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
     * @return The zoneInfo.
     */
    @java.lang.Override
    public com.yorha.proto.ZonePB.ZoneInfoEntityPB getZoneInfo() {
      if (entityAttrCase_ == 20) {
         return (com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_;
      }
      return com.yorha.proto.ZonePB.ZoneInfoEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ZonePB.ZoneInfoEntityPBOrBuilder getZoneInfoOrBuilder() {
      if (entityAttrCase_ == 20) {
         return (com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_;
      }
      return com.yorha.proto.ZonePB.ZoneInfoEntityPB.getDefaultInstance();
    }

    public static final int DUNGEONSCENEATTR_FIELD_NUMBER = 21;
    /**
     * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
     * @return Whether the dungeonSceneAttr field is set.
     */
    @java.lang.Override
    public boolean hasDungeonSceneAttr() {
      return entityAttrCase_ == 21;
    }
    /**
     * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
     * @return The dungeonSceneAttr.
     */
    @java.lang.Override
    public com.yorha.proto.DungeonPB.DungeonSceneEntityPB getDungeonSceneAttr() {
      if (entityAttrCase_ == 21) {
         return (com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_;
      }
      return com.yorha.proto.DungeonPB.DungeonSceneEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
     */
    @java.lang.Override
    public com.yorha.proto.DungeonPB.DungeonSceneEntityPBOrBuilder getDungeonSceneAttrOrBuilder() {
      if (entityAttrCase_ == 21) {
         return (com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_;
      }
      return com.yorha.proto.DungeonPB.DungeonSceneEntityPB.getDefaultInstance();
    }

    public static final int OUTBUILDINGATTR_FIELD_NUMBER = 23;
    /**
     * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
     * @return Whether the outbuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasOutbuildingAttr() {
      return entityAttrCase_ == 23;
    }
    /**
     * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
     * @return The outbuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.OutbuildingPB.OutbuildingEntityPB getOutbuildingAttr() {
      if (entityAttrCase_ == 23) {
         return (com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
     */
    @java.lang.Override
    public com.yorha.proto.OutbuildingPB.OutbuildingEntityPBOrBuilder getOutbuildingAttrOrBuilder() {
      if (entityAttrCase_ == 23) {
         return (com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.getDefaultInstance();
    }

    public static final int CAVEATTR_FIELD_NUMBER = 24;
    /**
     * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
     * @return Whether the caveAttr field is set.
     */
    @java.lang.Override
    public boolean hasCaveAttr() {
      return entityAttrCase_ == 24;
    }
    /**
     * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
     * @return The caveAttr.
     */
    @java.lang.Override
    public com.yorha.proto.CavePB.CaveEntityPB getCaveAttr() {
      if (entityAttrCase_ == 24) {
         return (com.yorha.proto.CavePB.CaveEntityPB) entityAttr_;
      }
      return com.yorha.proto.CavePB.CaveEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CavePB.CaveEntityPBOrBuilder getCaveAttrOrBuilder() {
      if (entityAttrCase_ == 24) {
         return (com.yorha.proto.CavePB.CaveEntityPB) entityAttr_;
      }
      return com.yorha.proto.CavePB.CaveEntityPB.getDefaultInstance();
    }

    public static final int LOGISTICSPLANEATTR_FIELD_NUMBER = 25;
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
     * @return Whether the logisticsPlaneAttr field is set.
     */
    @java.lang.Override
    public boolean hasLogisticsPlaneAttr() {
      return entityAttrCase_ == 25;
    }
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
     * @return The logisticsPlaneAttr.
     */
    @java.lang.Override
    public com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB getLogisticsPlaneAttr() {
      if (entityAttrCase_ == 25) {
         return (com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_;
      }
      return com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
     */
    @java.lang.Override
    public com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPBOrBuilder getLogisticsPlaneAttrOrBuilder() {
      if (entityAttrCase_ == 25) {
         return (com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_;
      }
      return com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.getDefaultInstance();
    }

    public static final int CLANRESBUILDINGATTR_FIELD_NUMBER = 26;
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
     * @return Whether the clanResBuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasClanResBuildingAttr() {
      return entityAttrCase_ == 26;
    }
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
     * @return The clanResBuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB getClanResBuildingAttr() {
      if (entityAttrCase_ == 26) {
         return (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPBOrBuilder getClanResBuildingAttrOrBuilder() {
      if (entityAttrCase_ == 26) {
         return (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance();
    }

    public static final int DUNGEONBUILDINGATTR_FIELD_NUMBER = 27;
    /**
     * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
     * @return Whether the dungeonBuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasDungeonBuildingAttr() {
      return entityAttrCase_ == 27;
    }
    /**
     * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
     * @return The dungeonBuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB getDungeonBuildingAttr() {
      if (entityAttrCase_ == 27) {
         return (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
     */
    @java.lang.Override
    public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPBOrBuilder getDungeonBuildingAttrOrBuilder() {
      if (entityAttrCase_ == 27) {
         return (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_;
      }
      return com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance();
    }

    public static final int AREASKILLATTR_FIELD_NUMBER = 28;
    /**
     * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
     * @return Whether the areaSkillAttr field is set.
     */
    @java.lang.Override
    public boolean hasAreaSkillAttr() {
      return entityAttrCase_ == 28;
    }
    /**
     * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
     * @return The areaSkillAttr.
     */
    @java.lang.Override
    public com.yorha.proto.AreaSkillPB.AreaSkillEntityPB getAreaSkillAttr() {
      if (entityAttrCase_ == 28) {
         return (com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_;
      }
      return com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
     */
    @java.lang.Override
    public com.yorha.proto.AreaSkillPB.AreaSkillEntityPBOrBuilder getAreaSkillAttrOrBuilder() {
      if (entityAttrCase_ == 28) {
         return (com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_;
      }
      return com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.getDefaultInstance();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, entityType_);
      }
      if (entityAttrCase_ == 11) {
        output.writeMessage(11, (com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 12) {
        output.writeMessage(12, (com.yorha.proto.CityPB.CityEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 13) {
        output.writeMessage(13, (com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 14) {
        output.writeMessage(14, (com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 15) {
        output.writeMessage(15, (com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 16) {
        output.writeMessage(16, (com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 17) {
        output.writeMessage(17, (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 18) {
        output.writeMessage(18, (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 19) {
        output.writeMessage(19, (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 20) {
        output.writeMessage(20, (com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 21) {
        output.writeMessage(21, (com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 23) {
        output.writeMessage(23, (com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 24) {
        output.writeMessage(24, (com.yorha.proto.CavePB.CaveEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 25) {
        output.writeMessage(25, (com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 26) {
        output.writeMessage(26, (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 27) {
        output.writeMessage(27, (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 28) {
        output.writeMessage(28, (com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, entityType_);
      }
      if (entityAttrCase_ == 11) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, (com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 12) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, (com.yorha.proto.CityPB.CityEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 13) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, (com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 14) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(14, (com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 15) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, (com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 16) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, (com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 17) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(17, (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 18) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(18, (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 19) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(19, (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 20) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(20, (com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 21) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(21, (com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 23) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(23, (com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 24) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(24, (com.yorha.proto.CavePB.CaveEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 25) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(25, (com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 26) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(26, (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 27) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(27, (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_);
      }
      if (entityAttrCase_ == 28) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(28, (com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.EntityAttrOuterClass.EntityAttr)) {
        return super.equals(obj);
      }
      com.yorha.proto.EntityAttrOuterClass.EntityAttr other = (com.yorha.proto.EntityAttrOuterClass.EntityAttr) obj;

      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (hasEntityType() != other.hasEntityType()) return false;
      if (hasEntityType()) {
        if (entityType_ != other.entityType_) return false;
      }
      if (!getEntityAttrCase().equals(other.getEntityAttrCase())) return false;
      switch (entityAttrCase_) {
        case 11:
          if (!getArmyAttr()
              .equals(other.getArmyAttr())) return false;
          break;
        case 12:
          if (!getCityAttr()
              .equals(other.getCityAttr())) return false;
          break;
        case 13:
          if (!getPlayerAttr()
              .equals(other.getPlayerAttr())) return false;
          break;
        case 14:
          if (!getMonsterAttr()
              .equals(other.getMonsterAttr())) return false;
          break;
        case 15:
          if (!getClanAttr()
              .equals(other.getClanAttr())) return false;
          break;
        case 16:
          if (!getDropObjectAttr()
              .equals(other.getDropObjectAttr())) return false;
          break;
        case 17:
          if (!getMapBuildingAttr()
              .equals(other.getMapBuildingAttr())) return false;
          break;
        case 18:
          if (!getSpyPlane()
              .equals(other.getSpyPlane())) return false;
          break;
        case 19:
          if (!getResBuildingAttr()
              .equals(other.getResBuildingAttr())) return false;
          break;
        case 20:
          if (!getZoneInfo()
              .equals(other.getZoneInfo())) return false;
          break;
        case 21:
          if (!getDungeonSceneAttr()
              .equals(other.getDungeonSceneAttr())) return false;
          break;
        case 23:
          if (!getOutbuildingAttr()
              .equals(other.getOutbuildingAttr())) return false;
          break;
        case 24:
          if (!getCaveAttr()
              .equals(other.getCaveAttr())) return false;
          break;
        case 25:
          if (!getLogisticsPlaneAttr()
              .equals(other.getLogisticsPlaneAttr())) return false;
          break;
        case 26:
          if (!getClanResBuildingAttr()
              .equals(other.getClanResBuildingAttr())) return false;
          break;
        case 27:
          if (!getDungeonBuildingAttr()
              .equals(other.getDungeonBuildingAttr())) return false;
          break;
        case 28:
          if (!getAreaSkillAttr()
              .equals(other.getAreaSkillAttr())) return false;
          break;
        case 0:
        default:
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      if (hasEntityType()) {
        hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
        hash = (53 * hash) + entityType_;
      }
      switch (entityAttrCase_) {
        case 11:
          hash = (37 * hash) + ARMYATTR_FIELD_NUMBER;
          hash = (53 * hash) + getArmyAttr().hashCode();
          break;
        case 12:
          hash = (37 * hash) + CITYATTR_FIELD_NUMBER;
          hash = (53 * hash) + getCityAttr().hashCode();
          break;
        case 13:
          hash = (37 * hash) + PLAYERATTR_FIELD_NUMBER;
          hash = (53 * hash) + getPlayerAttr().hashCode();
          break;
        case 14:
          hash = (37 * hash) + MONSTERATTR_FIELD_NUMBER;
          hash = (53 * hash) + getMonsterAttr().hashCode();
          break;
        case 15:
          hash = (37 * hash) + CLANATTR_FIELD_NUMBER;
          hash = (53 * hash) + getClanAttr().hashCode();
          break;
        case 16:
          hash = (37 * hash) + DROPOBJECTATTR_FIELD_NUMBER;
          hash = (53 * hash) + getDropObjectAttr().hashCode();
          break;
        case 17:
          hash = (37 * hash) + MAPBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getMapBuildingAttr().hashCode();
          break;
        case 18:
          hash = (37 * hash) + SPYPLANE_FIELD_NUMBER;
          hash = (53 * hash) + getSpyPlane().hashCode();
          break;
        case 19:
          hash = (37 * hash) + RESBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getResBuildingAttr().hashCode();
          break;
        case 20:
          hash = (37 * hash) + ZONEINFO_FIELD_NUMBER;
          hash = (53 * hash) + getZoneInfo().hashCode();
          break;
        case 21:
          hash = (37 * hash) + DUNGEONSCENEATTR_FIELD_NUMBER;
          hash = (53 * hash) + getDungeonSceneAttr().hashCode();
          break;
        case 23:
          hash = (37 * hash) + OUTBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getOutbuildingAttr().hashCode();
          break;
        case 24:
          hash = (37 * hash) + CAVEATTR_FIELD_NUMBER;
          hash = (53 * hash) + getCaveAttr().hashCode();
          break;
        case 25:
          hash = (37 * hash) + LOGISTICSPLANEATTR_FIELD_NUMBER;
          hash = (53 * hash) + getLogisticsPlaneAttr().hashCode();
          break;
        case 26:
          hash = (37 * hash) + CLANRESBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getClanResBuildingAttr().hashCode();
          break;
        case 27:
          hash = (37 * hash) + DUNGEONBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getDungeonBuildingAttr().hashCode();
          break;
        case 28:
          hash = (37 * hash) + AREASKILLATTR_FIELD_NUMBER;
          hash = (53 * hash) + getAreaSkillAttr().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.EntityAttrOuterClass.EntityAttr prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.EntityAttr}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.EntityAttr)
        com.yorha.proto.EntityAttrOuterClass.EntityAttrOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.EntityAttrOuterClass.internal_static_com_yorha_proto_EntityAttr_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.EntityAttrOuterClass.internal_static_com_yorha_proto_EntityAttr_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.EntityAttrOuterClass.EntityAttr.class, com.yorha.proto.EntityAttrOuterClass.EntityAttr.Builder.class);
      }

      // Construct using com.yorha.proto.EntityAttrOuterClass.EntityAttr.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        entityType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        entityAttrCase_ = 0;
        entityAttr_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.EntityAttrOuterClass.internal_static_com_yorha_proto_EntityAttr_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr getDefaultInstanceForType() {
        return com.yorha.proto.EntityAttrOuterClass.EntityAttr.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr build() {
        com.yorha.proto.EntityAttrOuterClass.EntityAttr result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.EntityAttrOuterClass.EntityAttr buildPartial() {
        com.yorha.proto.EntityAttrOuterClass.EntityAttr result = new com.yorha.proto.EntityAttrOuterClass.EntityAttr(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.entityType_ = entityType_;
        if (entityAttrCase_ == 11) {
          if (armyAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = armyAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 12) {
          if (cityAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = cityAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 13) {
          if (playerAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = playerAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 14) {
          if (monsterAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = monsterAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 15) {
          if (clanAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = clanAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 16) {
          if (dropObjectAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = dropObjectAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 17) {
          if (mapBuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = mapBuildingAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 18) {
          if (spyPlaneBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = spyPlaneBuilder_.build();
          }
        }
        if (entityAttrCase_ == 19) {
          if (resBuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = resBuildingAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 20) {
          if (zoneInfoBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = zoneInfoBuilder_.build();
          }
        }
        if (entityAttrCase_ == 21) {
          if (dungeonSceneAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = dungeonSceneAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 23) {
          if (outbuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = outbuildingAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 24) {
          if (caveAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = caveAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 25) {
          if (logisticsPlaneAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = logisticsPlaneAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 26) {
          if (clanResBuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = clanResBuildingAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 27) {
          if (dungeonBuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = dungeonBuildingAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 28) {
          if (areaSkillAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = areaSkillAttrBuilder_.build();
          }
        }
        result.bitField0_ = to_bitField0_;
        result.entityAttrCase_ = entityAttrCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.EntityAttrOuterClass.EntityAttr) {
          return mergeFrom((com.yorha.proto.EntityAttrOuterClass.EntityAttr)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.EntityAttrOuterClass.EntityAttr other) {
        if (other == com.yorha.proto.EntityAttrOuterClass.EntityAttr.getDefaultInstance()) return this;
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        if (other.hasEntityType()) {
          setEntityType(other.getEntityType());
        }
        switch (other.getEntityAttrCase()) {
          case ARMYATTR: {
            mergeArmyAttr(other.getArmyAttr());
            break;
          }
          case CITYATTR: {
            mergeCityAttr(other.getCityAttr());
            break;
          }
          case PLAYERATTR: {
            mergePlayerAttr(other.getPlayerAttr());
            break;
          }
          case MONSTERATTR: {
            mergeMonsterAttr(other.getMonsterAttr());
            break;
          }
          case CLANATTR: {
            mergeClanAttr(other.getClanAttr());
            break;
          }
          case DROPOBJECTATTR: {
            mergeDropObjectAttr(other.getDropObjectAttr());
            break;
          }
          case MAPBUILDINGATTR: {
            mergeMapBuildingAttr(other.getMapBuildingAttr());
            break;
          }
          case SPYPLANE: {
            mergeSpyPlane(other.getSpyPlane());
            break;
          }
          case RESBUILDINGATTR: {
            mergeResBuildingAttr(other.getResBuildingAttr());
            break;
          }
          case ZONEINFO: {
            mergeZoneInfo(other.getZoneInfo());
            break;
          }
          case DUNGEONSCENEATTR: {
            mergeDungeonSceneAttr(other.getDungeonSceneAttr());
            break;
          }
          case OUTBUILDINGATTR: {
            mergeOutbuildingAttr(other.getOutbuildingAttr());
            break;
          }
          case CAVEATTR: {
            mergeCaveAttr(other.getCaveAttr());
            break;
          }
          case LOGISTICSPLANEATTR: {
            mergeLogisticsPlaneAttr(other.getLogisticsPlaneAttr());
            break;
          }
          case CLANRESBUILDINGATTR: {
            mergeClanResBuildingAttr(other.getClanResBuildingAttr());
            break;
          }
          case DUNGEONBUILDINGATTR: {
            mergeDungeonBuildingAttr(other.getDungeonBuildingAttr());
            break;
          }
          case AREASKILLATTR: {
            mergeAreaSkillAttr(other.getAreaSkillAttr());
            break;
          }
          case ENTITYATTR_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.EntityAttrOuterClass.EntityAttr parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.EntityAttrOuterClass.EntityAttr) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int entityAttrCase_ = 0;
      private java.lang.Object entityAttr_;
      public EntityAttrCase
          getEntityAttrCase() {
        return EntityAttrCase.forNumber(
            entityAttrCase_);
      }

      public Builder clearEntityAttr() {
        entityAttrCase_ = 0;
        entityAttr_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private long entityId_ ;
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000001;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int entityType_ = 0;
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return Whether the entityType field is set.
       */
      @java.lang.Override public boolean hasEntityType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return The entityType.
       */
      @java.lang.Override
      public com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.EntityAttrOuterClass.EntityType result = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(entityType_);
        return result == null ? com.yorha.proto.EntityAttrOuterClass.EntityType.ET_Unknown : result;
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @param value The entityType to set.
       * @return This builder for chaining.
       */
      public Builder setEntityType(com.yorha.proto.EntityAttrOuterClass.EntityType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        entityType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        entityType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ArmyPB.ArmyEntityPB, com.yorha.proto.ArmyPB.ArmyEntityPB.Builder, com.yorha.proto.ArmyPB.ArmyEntityPBOrBuilder> armyAttrBuilder_;
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       * @return Whether the armyAttr field is set.
       */
      @java.lang.Override
      public boolean hasArmyAttr() {
        return entityAttrCase_ == 11;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       * @return The armyAttr.
       */
      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyEntityPB getArmyAttr() {
        if (armyAttrBuilder_ == null) {
          if (entityAttrCase_ == 11) {
            return (com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_;
          }
          return com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 11) {
            return armyAttrBuilder_.getMessage();
          }
          return com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       */
      public Builder setArmyAttr(com.yorha.proto.ArmyPB.ArmyEntityPB value) {
        if (armyAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          armyAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       */
      public Builder setArmyAttr(
          com.yorha.proto.ArmyPB.ArmyEntityPB.Builder builderForValue) {
        if (armyAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          armyAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       */
      public Builder mergeArmyAttr(com.yorha.proto.ArmyPB.ArmyEntityPB value) {
        if (armyAttrBuilder_ == null) {
          if (entityAttrCase_ == 11 &&
              entityAttr_ != com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.ArmyPB.ArmyEntityPB.newBuilder((com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 11) {
            armyAttrBuilder_.mergeFrom(value);
          }
          armyAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       */
      public Builder clearArmyAttr() {
        if (armyAttrBuilder_ == null) {
          if (entityAttrCase_ == 11) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 11) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          armyAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       */
      public com.yorha.proto.ArmyPB.ArmyEntityPB.Builder getArmyAttrBuilder() {
        return getArmyAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       */
      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyEntityPBOrBuilder getArmyAttrOrBuilder() {
        if ((entityAttrCase_ == 11) && (armyAttrBuilder_ != null)) {
          return armyAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 11) {
            return (com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_;
          }
          return com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ArmyEntityPB armyAttr = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ArmyPB.ArmyEntityPB, com.yorha.proto.ArmyPB.ArmyEntityPB.Builder, com.yorha.proto.ArmyPB.ArmyEntityPBOrBuilder> 
          getArmyAttrFieldBuilder() {
        if (armyAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 11)) {
            entityAttr_ = com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance();
          }
          armyAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ArmyPB.ArmyEntityPB, com.yorha.proto.ArmyPB.ArmyEntityPB.Builder, com.yorha.proto.ArmyPB.ArmyEntityPBOrBuilder>(
                  (com.yorha.proto.ArmyPB.ArmyEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 11;
        onChanged();;
        return armyAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CityPB.CityEntityPB, com.yorha.proto.CityPB.CityEntityPB.Builder, com.yorha.proto.CityPB.CityEntityPBOrBuilder> cityAttrBuilder_;
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       * @return Whether the cityAttr field is set.
       */
      @java.lang.Override
      public boolean hasCityAttr() {
        return entityAttrCase_ == 12;
      }
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       * @return The cityAttr.
       */
      @java.lang.Override
      public com.yorha.proto.CityPB.CityEntityPB getCityAttr() {
        if (cityAttrBuilder_ == null) {
          if (entityAttrCase_ == 12) {
            return (com.yorha.proto.CityPB.CityEntityPB) entityAttr_;
          }
          return com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 12) {
            return cityAttrBuilder_.getMessage();
          }
          return com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       */
      public Builder setCityAttr(com.yorha.proto.CityPB.CityEntityPB value) {
        if (cityAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          cityAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       */
      public Builder setCityAttr(
          com.yorha.proto.CityPB.CityEntityPB.Builder builderForValue) {
        if (cityAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          cityAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       */
      public Builder mergeCityAttr(com.yorha.proto.CityPB.CityEntityPB value) {
        if (cityAttrBuilder_ == null) {
          if (entityAttrCase_ == 12 &&
              entityAttr_ != com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.CityPB.CityEntityPB.newBuilder((com.yorha.proto.CityPB.CityEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 12) {
            cityAttrBuilder_.mergeFrom(value);
          }
          cityAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       */
      public Builder clearCityAttr() {
        if (cityAttrBuilder_ == null) {
          if (entityAttrCase_ == 12) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 12) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          cityAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       */
      public com.yorha.proto.CityPB.CityEntityPB.Builder getCityAttrBuilder() {
        return getCityAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       */
      @java.lang.Override
      public com.yorha.proto.CityPB.CityEntityPBOrBuilder getCityAttrOrBuilder() {
        if ((entityAttrCase_ == 12) && (cityAttrBuilder_ != null)) {
          return cityAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 12) {
            return (com.yorha.proto.CityPB.CityEntityPB) entityAttr_;
          }
          return com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.CityEntityPB cityAttr = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CityPB.CityEntityPB, com.yorha.proto.CityPB.CityEntityPB.Builder, com.yorha.proto.CityPB.CityEntityPBOrBuilder> 
          getCityAttrFieldBuilder() {
        if (cityAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 12)) {
            entityAttr_ = com.yorha.proto.CityPB.CityEntityPB.getDefaultInstance();
          }
          cityAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CityPB.CityEntityPB, com.yorha.proto.CityPB.CityEntityPB.Builder, com.yorha.proto.CityPB.CityEntityPBOrBuilder>(
                  (com.yorha.proto.CityPB.CityEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 12;
        onChanged();;
        return cityAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPB.PlayerEntityPB, com.yorha.proto.PlayerPB.PlayerEntityPB.Builder, com.yorha.proto.PlayerPB.PlayerEntityPBOrBuilder> playerAttrBuilder_;
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       * @return Whether the playerAttr field is set.
       */
      @java.lang.Override
      public boolean hasPlayerAttr() {
        return entityAttrCase_ == 13;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       * @return The playerAttr.
       */
      @java.lang.Override
      public com.yorha.proto.PlayerPB.PlayerEntityPB getPlayerAttr() {
        if (playerAttrBuilder_ == null) {
          if (entityAttrCase_ == 13) {
            return (com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_;
          }
          return com.yorha.proto.PlayerPB.PlayerEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 13) {
            return playerAttrBuilder_.getMessage();
          }
          return com.yorha.proto.PlayerPB.PlayerEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       */
      public Builder setPlayerAttr(com.yorha.proto.PlayerPB.PlayerEntityPB value) {
        if (playerAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          playerAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       */
      public Builder setPlayerAttr(
          com.yorha.proto.PlayerPB.PlayerEntityPB.Builder builderForValue) {
        if (playerAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          playerAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       */
      public Builder mergePlayerAttr(com.yorha.proto.PlayerPB.PlayerEntityPB value) {
        if (playerAttrBuilder_ == null) {
          if (entityAttrCase_ == 13 &&
              entityAttr_ != com.yorha.proto.PlayerPB.PlayerEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.PlayerPB.PlayerEntityPB.newBuilder((com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 13) {
            playerAttrBuilder_.mergeFrom(value);
          }
          playerAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       */
      public Builder clearPlayerAttr() {
        if (playerAttrBuilder_ == null) {
          if (entityAttrCase_ == 13) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 13) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          playerAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       */
      public com.yorha.proto.PlayerPB.PlayerEntityPB.Builder getPlayerAttrBuilder() {
        return getPlayerAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       */
      @java.lang.Override
      public com.yorha.proto.PlayerPB.PlayerEntityPBOrBuilder getPlayerAttrOrBuilder() {
        if ((entityAttrCase_ == 13) && (playerAttrBuilder_ != null)) {
          return playerAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 13) {
            return (com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_;
          }
          return com.yorha.proto.PlayerPB.PlayerEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.PlayerEntityPB playerAttr = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPB.PlayerEntityPB, com.yorha.proto.PlayerPB.PlayerEntityPB.Builder, com.yorha.proto.PlayerPB.PlayerEntityPBOrBuilder> 
          getPlayerAttrFieldBuilder() {
        if (playerAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 13)) {
            entityAttr_ = com.yorha.proto.PlayerPB.PlayerEntityPB.getDefaultInstance();
          }
          playerAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.PlayerPB.PlayerEntityPB, com.yorha.proto.PlayerPB.PlayerEntityPB.Builder, com.yorha.proto.PlayerPB.PlayerEntityPBOrBuilder>(
                  (com.yorha.proto.PlayerPB.PlayerEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 13;
        onChanged();;
        return playerAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MonsterPB.MonsterEntityPB, com.yorha.proto.MonsterPB.MonsterEntityPB.Builder, com.yorha.proto.MonsterPB.MonsterEntityPBOrBuilder> monsterAttrBuilder_;
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       * @return Whether the monsterAttr field is set.
       */
      @java.lang.Override
      public boolean hasMonsterAttr() {
        return entityAttrCase_ == 14;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       * @return The monsterAttr.
       */
      @java.lang.Override
      public com.yorha.proto.MonsterPB.MonsterEntityPB getMonsterAttr() {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 14) {
            return (com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_;
          }
          return com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 14) {
            return monsterAttrBuilder_.getMessage();
          }
          return com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       */
      public Builder setMonsterAttr(com.yorha.proto.MonsterPB.MonsterEntityPB value) {
        if (monsterAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          monsterAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 14;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       */
      public Builder setMonsterAttr(
          com.yorha.proto.MonsterPB.MonsterEntityPB.Builder builderForValue) {
        if (monsterAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          monsterAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 14;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       */
      public Builder mergeMonsterAttr(com.yorha.proto.MonsterPB.MonsterEntityPB value) {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 14 &&
              entityAttr_ != com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.MonsterPB.MonsterEntityPB.newBuilder((com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 14) {
            monsterAttrBuilder_.mergeFrom(value);
          }
          monsterAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 14;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       */
      public Builder clearMonsterAttr() {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 14) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 14) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          monsterAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       */
      public com.yorha.proto.MonsterPB.MonsterEntityPB.Builder getMonsterAttrBuilder() {
        return getMonsterAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       */
      @java.lang.Override
      public com.yorha.proto.MonsterPB.MonsterEntityPBOrBuilder getMonsterAttrOrBuilder() {
        if ((entityAttrCase_ == 14) && (monsterAttrBuilder_ != null)) {
          return monsterAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 14) {
            return (com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_;
          }
          return com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.MonsterEntityPB monsterAttr = 14;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MonsterPB.MonsterEntityPB, com.yorha.proto.MonsterPB.MonsterEntityPB.Builder, com.yorha.proto.MonsterPB.MonsterEntityPBOrBuilder> 
          getMonsterAttrFieldBuilder() {
        if (monsterAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 14)) {
            entityAttr_ = com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance();
          }
          monsterAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MonsterPB.MonsterEntityPB, com.yorha.proto.MonsterPB.MonsterEntityPB.Builder, com.yorha.proto.MonsterPB.MonsterEntityPBOrBuilder>(
                  (com.yorha.proto.MonsterPB.MonsterEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 14;
        onChanged();;
        return monsterAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanPB.ClanEntityPB, com.yorha.proto.ClanPB.ClanEntityPB.Builder, com.yorha.proto.ClanPB.ClanEntityPBOrBuilder> clanAttrBuilder_;
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       * @return Whether the clanAttr field is set.
       */
      @java.lang.Override
      public boolean hasClanAttr() {
        return entityAttrCase_ == 15;
      }
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       * @return The clanAttr.
       */
      @java.lang.Override
      public com.yorha.proto.ClanPB.ClanEntityPB getClanAttr() {
        if (clanAttrBuilder_ == null) {
          if (entityAttrCase_ == 15) {
            return (com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_;
          }
          return com.yorha.proto.ClanPB.ClanEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 15) {
            return clanAttrBuilder_.getMessage();
          }
          return com.yorha.proto.ClanPB.ClanEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       */
      public Builder setClanAttr(com.yorha.proto.ClanPB.ClanEntityPB value) {
        if (clanAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          clanAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       */
      public Builder setClanAttr(
          com.yorha.proto.ClanPB.ClanEntityPB.Builder builderForValue) {
        if (clanAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          clanAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       */
      public Builder mergeClanAttr(com.yorha.proto.ClanPB.ClanEntityPB value) {
        if (clanAttrBuilder_ == null) {
          if (entityAttrCase_ == 15 &&
              entityAttr_ != com.yorha.proto.ClanPB.ClanEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.ClanPB.ClanEntityPB.newBuilder((com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 15) {
            clanAttrBuilder_.mergeFrom(value);
          }
          clanAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       */
      public Builder clearClanAttr() {
        if (clanAttrBuilder_ == null) {
          if (entityAttrCase_ == 15) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 15) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          clanAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       */
      public com.yorha.proto.ClanPB.ClanEntityPB.Builder getClanAttrBuilder() {
        return getClanAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       */
      @java.lang.Override
      public com.yorha.proto.ClanPB.ClanEntityPBOrBuilder getClanAttrOrBuilder() {
        if ((entityAttrCase_ == 15) && (clanAttrBuilder_ != null)) {
          return clanAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 15) {
            return (com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_;
          }
          return com.yorha.proto.ClanPB.ClanEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ClanEntityPB clanAttr = 15;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanPB.ClanEntityPB, com.yorha.proto.ClanPB.ClanEntityPB.Builder, com.yorha.proto.ClanPB.ClanEntityPBOrBuilder> 
          getClanAttrFieldBuilder() {
        if (clanAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 15)) {
            entityAttr_ = com.yorha.proto.ClanPB.ClanEntityPB.getDefaultInstance();
          }
          clanAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ClanPB.ClanEntityPB, com.yorha.proto.ClanPB.ClanEntityPB.Builder, com.yorha.proto.ClanPB.ClanEntityPBOrBuilder>(
                  (com.yorha.proto.ClanPB.ClanEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 15;
        onChanged();;
        return clanAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.DropObjectPB.DropObjectEntityPB, com.yorha.proto.DropObjectPB.DropObjectEntityPB.Builder, com.yorha.proto.DropObjectPB.DropObjectEntityPBOrBuilder> dropObjectAttrBuilder_;
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       * @return Whether the dropObjectAttr field is set.
       */
      @java.lang.Override
      public boolean hasDropObjectAttr() {
        return entityAttrCase_ == 16;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       * @return The dropObjectAttr.
       */
      @java.lang.Override
      public com.yorha.proto.DropObjectPB.DropObjectEntityPB getDropObjectAttr() {
        if (dropObjectAttrBuilder_ == null) {
          if (entityAttrCase_ == 16) {
            return (com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_;
          }
          return com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 16) {
            return dropObjectAttrBuilder_.getMessage();
          }
          return com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       */
      public Builder setDropObjectAttr(com.yorha.proto.DropObjectPB.DropObjectEntityPB value) {
        if (dropObjectAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          dropObjectAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       */
      public Builder setDropObjectAttr(
          com.yorha.proto.DropObjectPB.DropObjectEntityPB.Builder builderForValue) {
        if (dropObjectAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          dropObjectAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       */
      public Builder mergeDropObjectAttr(com.yorha.proto.DropObjectPB.DropObjectEntityPB value) {
        if (dropObjectAttrBuilder_ == null) {
          if (entityAttrCase_ == 16 &&
              entityAttr_ != com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.DropObjectPB.DropObjectEntityPB.newBuilder((com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 16) {
            dropObjectAttrBuilder_.mergeFrom(value);
          }
          dropObjectAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       */
      public Builder clearDropObjectAttr() {
        if (dropObjectAttrBuilder_ == null) {
          if (entityAttrCase_ == 16) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 16) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          dropObjectAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       */
      public com.yorha.proto.DropObjectPB.DropObjectEntityPB.Builder getDropObjectAttrBuilder() {
        return getDropObjectAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       */
      @java.lang.Override
      public com.yorha.proto.DropObjectPB.DropObjectEntityPBOrBuilder getDropObjectAttrOrBuilder() {
        if ((entityAttrCase_ == 16) && (dropObjectAttrBuilder_ != null)) {
          return dropObjectAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 16) {
            return (com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_;
          }
          return com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntityPB dropObjectAttr = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.DropObjectPB.DropObjectEntityPB, com.yorha.proto.DropObjectPB.DropObjectEntityPB.Builder, com.yorha.proto.DropObjectPB.DropObjectEntityPBOrBuilder> 
          getDropObjectAttrFieldBuilder() {
        if (dropObjectAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 16)) {
            entityAttr_ = com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance();
          }
          dropObjectAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.DropObjectPB.DropObjectEntityPB, com.yorha.proto.DropObjectPB.DropObjectEntityPB.Builder, com.yorha.proto.DropObjectPB.DropObjectEntityPBOrBuilder>(
                  (com.yorha.proto.DropObjectPB.DropObjectEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 16;
        onChanged();;
        return dropObjectAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuildingPB.MapBuildingEntityPB, com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.Builder, com.yorha.proto.MapBuildingPB.MapBuildingEntityPBOrBuilder> mapBuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       * @return Whether the mapBuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasMapBuildingAttr() {
        return entityAttrCase_ == 17;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       * @return The mapBuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.MapBuildingEntityPB getMapBuildingAttr() {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 17) {
            return (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 17) {
            return mapBuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       */
      public Builder setMapBuildingAttr(com.yorha.proto.MapBuildingPB.MapBuildingEntityPB value) {
        if (mapBuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          mapBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       */
      public Builder setMapBuildingAttr(
          com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.Builder builderForValue) {
        if (mapBuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          mapBuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       */
      public Builder mergeMapBuildingAttr(com.yorha.proto.MapBuildingPB.MapBuildingEntityPB value) {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 17 &&
              entityAttr_ != com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.newBuilder((com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 17) {
            mapBuildingAttrBuilder_.mergeFrom(value);
          }
          mapBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       */
      public Builder clearMapBuildingAttr() {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 17) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 17) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          mapBuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       */
      public com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.Builder getMapBuildingAttrBuilder() {
        return getMapBuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       */
      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.MapBuildingEntityPBOrBuilder getMapBuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 17) && (mapBuildingAttrBuilder_ != null)) {
          return mapBuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 17) {
            return (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntityPB mapBuildingAttr = 17;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuildingPB.MapBuildingEntityPB, com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.Builder, com.yorha.proto.MapBuildingPB.MapBuildingEntityPBOrBuilder> 
          getMapBuildingAttrFieldBuilder() {
        if (mapBuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 17)) {
            entityAttr_ = com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance();
          }
          mapBuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MapBuildingPB.MapBuildingEntityPB, com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.Builder, com.yorha.proto.MapBuildingPB.MapBuildingEntityPBOrBuilder>(
                  (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 17;
        onChanged();;
        return mapBuildingAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB, com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.Builder, com.yorha.proto.SpyPlanePB.SpyPlaneEntityPBOrBuilder> spyPlaneBuilder_;
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       * @return Whether the spyPlane field is set.
       */
      @java.lang.Override
      public boolean hasSpyPlane() {
        return entityAttrCase_ == 18;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       * @return The spyPlane.
       */
      @java.lang.Override
      public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB getSpyPlane() {
        if (spyPlaneBuilder_ == null) {
          if (entityAttrCase_ == 18) {
            return (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_;
          }
          return com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 18) {
            return spyPlaneBuilder_.getMessage();
          }
          return com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       */
      public Builder setSpyPlane(com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB value) {
        if (spyPlaneBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          spyPlaneBuilder_.setMessage(value);
        }
        entityAttrCase_ = 18;
        return this;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       */
      public Builder setSpyPlane(
          com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.Builder builderForValue) {
        if (spyPlaneBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          spyPlaneBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 18;
        return this;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       */
      public Builder mergeSpyPlane(com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB value) {
        if (spyPlaneBuilder_ == null) {
          if (entityAttrCase_ == 18 &&
              entityAttr_ != com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.newBuilder((com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 18) {
            spyPlaneBuilder_.mergeFrom(value);
          }
          spyPlaneBuilder_.setMessage(value);
        }
        entityAttrCase_ = 18;
        return this;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       */
      public Builder clearSpyPlane() {
        if (spyPlaneBuilder_ == null) {
          if (entityAttrCase_ == 18) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 18) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          spyPlaneBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       */
      public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.Builder getSpyPlaneBuilder() {
        return getSpyPlaneFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       */
      @java.lang.Override
      public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPBOrBuilder getSpyPlaneOrBuilder() {
        if ((entityAttrCase_ == 18) && (spyPlaneBuilder_ != null)) {
          return spyPlaneBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 18) {
            return (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_;
          }
          return com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntityPB spyPlane = 18;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB, com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.Builder, com.yorha.proto.SpyPlanePB.SpyPlaneEntityPBOrBuilder> 
          getSpyPlaneFieldBuilder() {
        if (spyPlaneBuilder_ == null) {
          if (!(entityAttrCase_ == 18)) {
            entityAttr_ = com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance();
          }
          spyPlaneBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB, com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.Builder, com.yorha.proto.SpyPlanePB.SpyPlaneEntityPBOrBuilder>(
                  (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 18;
        onChanged();;
        return spyPlaneBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ResBuildingPB.ResBuildingEntityPB, com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.Builder, com.yorha.proto.ResBuildingPB.ResBuildingEntityPBOrBuilder> resBuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       * @return Whether the resBuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasResBuildingAttr() {
        return entityAttrCase_ == 19;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       * @return The resBuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.ResBuildingPB.ResBuildingEntityPB getResBuildingAttr() {
        if (resBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 19) {
            return (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 19) {
            return resBuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       */
      public Builder setResBuildingAttr(com.yorha.proto.ResBuildingPB.ResBuildingEntityPB value) {
        if (resBuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          resBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 19;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       */
      public Builder setResBuildingAttr(
          com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.Builder builderForValue) {
        if (resBuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          resBuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 19;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       */
      public Builder mergeResBuildingAttr(com.yorha.proto.ResBuildingPB.ResBuildingEntityPB value) {
        if (resBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 19 &&
              entityAttr_ != com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.newBuilder((com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 19) {
            resBuildingAttrBuilder_.mergeFrom(value);
          }
          resBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 19;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       */
      public Builder clearResBuildingAttr() {
        if (resBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 19) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 19) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          resBuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       */
      public com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.Builder getResBuildingAttrBuilder() {
        return getResBuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       */
      @java.lang.Override
      public com.yorha.proto.ResBuildingPB.ResBuildingEntityPBOrBuilder getResBuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 19) && (resBuildingAttrBuilder_ != null)) {
          return resBuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 19) {
            return (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntityPB resBuildingAttr = 19;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ResBuildingPB.ResBuildingEntityPB, com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.Builder, com.yorha.proto.ResBuildingPB.ResBuildingEntityPBOrBuilder> 
          getResBuildingAttrFieldBuilder() {
        if (resBuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 19)) {
            entityAttr_ = com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance();
          }
          resBuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ResBuildingPB.ResBuildingEntityPB, com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.Builder, com.yorha.proto.ResBuildingPB.ResBuildingEntityPBOrBuilder>(
                  (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 19;
        onChanged();;
        return resBuildingAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ZonePB.ZoneInfoEntityPB, com.yorha.proto.ZonePB.ZoneInfoEntityPB.Builder, com.yorha.proto.ZonePB.ZoneInfoEntityPBOrBuilder> zoneInfoBuilder_;
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       * @return Whether the zoneInfo field is set.
       */
      @java.lang.Override
      public boolean hasZoneInfo() {
        return entityAttrCase_ == 20;
      }
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       * @return The zoneInfo.
       */
      @java.lang.Override
      public com.yorha.proto.ZonePB.ZoneInfoEntityPB getZoneInfo() {
        if (zoneInfoBuilder_ == null) {
          if (entityAttrCase_ == 20) {
            return (com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_;
          }
          return com.yorha.proto.ZonePB.ZoneInfoEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 20) {
            return zoneInfoBuilder_.getMessage();
          }
          return com.yorha.proto.ZonePB.ZoneInfoEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       */
      public Builder setZoneInfo(com.yorha.proto.ZonePB.ZoneInfoEntityPB value) {
        if (zoneInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          zoneInfoBuilder_.setMessage(value);
        }
        entityAttrCase_ = 20;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       */
      public Builder setZoneInfo(
          com.yorha.proto.ZonePB.ZoneInfoEntityPB.Builder builderForValue) {
        if (zoneInfoBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          zoneInfoBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 20;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       */
      public Builder mergeZoneInfo(com.yorha.proto.ZonePB.ZoneInfoEntityPB value) {
        if (zoneInfoBuilder_ == null) {
          if (entityAttrCase_ == 20 &&
              entityAttr_ != com.yorha.proto.ZonePB.ZoneInfoEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.ZonePB.ZoneInfoEntityPB.newBuilder((com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 20) {
            zoneInfoBuilder_.mergeFrom(value);
          }
          zoneInfoBuilder_.setMessage(value);
        }
        entityAttrCase_ = 20;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       */
      public Builder clearZoneInfo() {
        if (zoneInfoBuilder_ == null) {
          if (entityAttrCase_ == 20) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 20) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          zoneInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       */
      public com.yorha.proto.ZonePB.ZoneInfoEntityPB.Builder getZoneInfoBuilder() {
        return getZoneInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       */
      @java.lang.Override
      public com.yorha.proto.ZonePB.ZoneInfoEntityPBOrBuilder getZoneInfoOrBuilder() {
        if ((entityAttrCase_ == 20) && (zoneInfoBuilder_ != null)) {
          return zoneInfoBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 20) {
            return (com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_;
          }
          return com.yorha.proto.ZonePB.ZoneInfoEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ZoneInfoEntityPB ZoneInfo = 20;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ZonePB.ZoneInfoEntityPB, com.yorha.proto.ZonePB.ZoneInfoEntityPB.Builder, com.yorha.proto.ZonePB.ZoneInfoEntityPBOrBuilder> 
          getZoneInfoFieldBuilder() {
        if (zoneInfoBuilder_ == null) {
          if (!(entityAttrCase_ == 20)) {
            entityAttr_ = com.yorha.proto.ZonePB.ZoneInfoEntityPB.getDefaultInstance();
          }
          zoneInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ZonePB.ZoneInfoEntityPB, com.yorha.proto.ZonePB.ZoneInfoEntityPB.Builder, com.yorha.proto.ZonePB.ZoneInfoEntityPBOrBuilder>(
                  (com.yorha.proto.ZonePB.ZoneInfoEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 20;
        onChanged();;
        return zoneInfoBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.DungeonPB.DungeonSceneEntityPB, com.yorha.proto.DungeonPB.DungeonSceneEntityPB.Builder, com.yorha.proto.DungeonPB.DungeonSceneEntityPBOrBuilder> dungeonSceneAttrBuilder_;
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       * @return Whether the dungeonSceneAttr field is set.
       */
      @java.lang.Override
      public boolean hasDungeonSceneAttr() {
        return entityAttrCase_ == 21;
      }
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       * @return The dungeonSceneAttr.
       */
      @java.lang.Override
      public com.yorha.proto.DungeonPB.DungeonSceneEntityPB getDungeonSceneAttr() {
        if (dungeonSceneAttrBuilder_ == null) {
          if (entityAttrCase_ == 21) {
            return (com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_;
          }
          return com.yorha.proto.DungeonPB.DungeonSceneEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 21) {
            return dungeonSceneAttrBuilder_.getMessage();
          }
          return com.yorha.proto.DungeonPB.DungeonSceneEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       */
      public Builder setDungeonSceneAttr(com.yorha.proto.DungeonPB.DungeonSceneEntityPB value) {
        if (dungeonSceneAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          dungeonSceneAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 21;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       */
      public Builder setDungeonSceneAttr(
          com.yorha.proto.DungeonPB.DungeonSceneEntityPB.Builder builderForValue) {
        if (dungeonSceneAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          dungeonSceneAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 21;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       */
      public Builder mergeDungeonSceneAttr(com.yorha.proto.DungeonPB.DungeonSceneEntityPB value) {
        if (dungeonSceneAttrBuilder_ == null) {
          if (entityAttrCase_ == 21 &&
              entityAttr_ != com.yorha.proto.DungeonPB.DungeonSceneEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.DungeonPB.DungeonSceneEntityPB.newBuilder((com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 21) {
            dungeonSceneAttrBuilder_.mergeFrom(value);
          }
          dungeonSceneAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 21;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       */
      public Builder clearDungeonSceneAttr() {
        if (dungeonSceneAttrBuilder_ == null) {
          if (entityAttrCase_ == 21) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 21) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          dungeonSceneAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       */
      public com.yorha.proto.DungeonPB.DungeonSceneEntityPB.Builder getDungeonSceneAttrBuilder() {
        return getDungeonSceneAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       */
      @java.lang.Override
      public com.yorha.proto.DungeonPB.DungeonSceneEntityPBOrBuilder getDungeonSceneAttrOrBuilder() {
        if ((entityAttrCase_ == 21) && (dungeonSceneAttrBuilder_ != null)) {
          return dungeonSceneAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 21) {
            return (com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_;
          }
          return com.yorha.proto.DungeonPB.DungeonSceneEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.DungeonSceneEntityPB dungeonSceneAttr = 21;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.DungeonPB.DungeonSceneEntityPB, com.yorha.proto.DungeonPB.DungeonSceneEntityPB.Builder, com.yorha.proto.DungeonPB.DungeonSceneEntityPBOrBuilder> 
          getDungeonSceneAttrFieldBuilder() {
        if (dungeonSceneAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 21)) {
            entityAttr_ = com.yorha.proto.DungeonPB.DungeonSceneEntityPB.getDefaultInstance();
          }
          dungeonSceneAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.DungeonPB.DungeonSceneEntityPB, com.yorha.proto.DungeonPB.DungeonSceneEntityPB.Builder, com.yorha.proto.DungeonPB.DungeonSceneEntityPBOrBuilder>(
                  (com.yorha.proto.DungeonPB.DungeonSceneEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 21;
        onChanged();;
        return dungeonSceneAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.OutbuildingPB.OutbuildingEntityPB, com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.Builder, com.yorha.proto.OutbuildingPB.OutbuildingEntityPBOrBuilder> outbuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       * @return Whether the outbuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasOutbuildingAttr() {
        return entityAttrCase_ == 23;
      }
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       * @return The outbuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.OutbuildingPB.OutbuildingEntityPB getOutbuildingAttr() {
        if (outbuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 23) {
            return (com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 23) {
            return outbuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       */
      public Builder setOutbuildingAttr(com.yorha.proto.OutbuildingPB.OutbuildingEntityPB value) {
        if (outbuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          outbuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 23;
        return this;
      }
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       */
      public Builder setOutbuildingAttr(
          com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.Builder builderForValue) {
        if (outbuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          outbuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 23;
        return this;
      }
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       */
      public Builder mergeOutbuildingAttr(com.yorha.proto.OutbuildingPB.OutbuildingEntityPB value) {
        if (outbuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 23 &&
              entityAttr_ != com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.newBuilder((com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 23) {
            outbuildingAttrBuilder_.mergeFrom(value);
          }
          outbuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 23;
        return this;
      }
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       */
      public Builder clearOutbuildingAttr() {
        if (outbuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 23) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 23) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          outbuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       */
      public com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.Builder getOutbuildingAttrBuilder() {
        return getOutbuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       */
      @java.lang.Override
      public com.yorha.proto.OutbuildingPB.OutbuildingEntityPBOrBuilder getOutbuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 23) && (outbuildingAttrBuilder_ != null)) {
          return outbuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 23) {
            return (com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.OutbuildingEntityPB outbuildingAttr = 23;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.OutbuildingPB.OutbuildingEntityPB, com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.Builder, com.yorha.proto.OutbuildingPB.OutbuildingEntityPBOrBuilder> 
          getOutbuildingAttrFieldBuilder() {
        if (outbuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 23)) {
            entityAttr_ = com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.getDefaultInstance();
          }
          outbuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.OutbuildingPB.OutbuildingEntityPB, com.yorha.proto.OutbuildingPB.OutbuildingEntityPB.Builder, com.yorha.proto.OutbuildingPB.OutbuildingEntityPBOrBuilder>(
                  (com.yorha.proto.OutbuildingPB.OutbuildingEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 23;
        onChanged();;
        return outbuildingAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CavePB.CaveEntityPB, com.yorha.proto.CavePB.CaveEntityPB.Builder, com.yorha.proto.CavePB.CaveEntityPBOrBuilder> caveAttrBuilder_;
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       * @return Whether the caveAttr field is set.
       */
      @java.lang.Override
      public boolean hasCaveAttr() {
        return entityAttrCase_ == 24;
      }
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       * @return The caveAttr.
       */
      @java.lang.Override
      public com.yorha.proto.CavePB.CaveEntityPB getCaveAttr() {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 24) {
            return (com.yorha.proto.CavePB.CaveEntityPB) entityAttr_;
          }
          return com.yorha.proto.CavePB.CaveEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 24) {
            return caveAttrBuilder_.getMessage();
          }
          return com.yorha.proto.CavePB.CaveEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       */
      public Builder setCaveAttr(com.yorha.proto.CavePB.CaveEntityPB value) {
        if (caveAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          caveAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 24;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       */
      public Builder setCaveAttr(
          com.yorha.proto.CavePB.CaveEntityPB.Builder builderForValue) {
        if (caveAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          caveAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 24;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       */
      public Builder mergeCaveAttr(com.yorha.proto.CavePB.CaveEntityPB value) {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 24 &&
              entityAttr_ != com.yorha.proto.CavePB.CaveEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.CavePB.CaveEntityPB.newBuilder((com.yorha.proto.CavePB.CaveEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 24) {
            caveAttrBuilder_.mergeFrom(value);
          }
          caveAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 24;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       */
      public Builder clearCaveAttr() {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 24) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 24) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          caveAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       */
      public com.yorha.proto.CavePB.CaveEntityPB.Builder getCaveAttrBuilder() {
        return getCaveAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       */
      @java.lang.Override
      public com.yorha.proto.CavePB.CaveEntityPBOrBuilder getCaveAttrOrBuilder() {
        if ((entityAttrCase_ == 24) && (caveAttrBuilder_ != null)) {
          return caveAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 24) {
            return (com.yorha.proto.CavePB.CaveEntityPB) entityAttr_;
          }
          return com.yorha.proto.CavePB.CaveEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.CaveEntityPB caveAttr = 24;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CavePB.CaveEntityPB, com.yorha.proto.CavePB.CaveEntityPB.Builder, com.yorha.proto.CavePB.CaveEntityPBOrBuilder> 
          getCaveAttrFieldBuilder() {
        if (caveAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 24)) {
            entityAttr_ = com.yorha.proto.CavePB.CaveEntityPB.getDefaultInstance();
          }
          caveAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CavePB.CaveEntityPB, com.yorha.proto.CavePB.CaveEntityPB.Builder, com.yorha.proto.CavePB.CaveEntityPBOrBuilder>(
                  (com.yorha.proto.CavePB.CaveEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 24;
        onChanged();;
        return caveAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB, com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.Builder, com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPBOrBuilder> logisticsPlaneAttrBuilder_;
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       * @return Whether the logisticsPlaneAttr field is set.
       */
      @java.lang.Override
      public boolean hasLogisticsPlaneAttr() {
        return entityAttrCase_ == 25;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       * @return The logisticsPlaneAttr.
       */
      @java.lang.Override
      public com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB getLogisticsPlaneAttr() {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (entityAttrCase_ == 25) {
            return (com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_;
          }
          return com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 25) {
            return logisticsPlaneAttrBuilder_.getMessage();
          }
          return com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       */
      public Builder setLogisticsPlaneAttr(com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB value) {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          logisticsPlaneAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 25;
        return this;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       */
      public Builder setLogisticsPlaneAttr(
          com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.Builder builderForValue) {
        if (logisticsPlaneAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          logisticsPlaneAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 25;
        return this;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       */
      public Builder mergeLogisticsPlaneAttr(com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB value) {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (entityAttrCase_ == 25 &&
              entityAttr_ != com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.newBuilder((com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 25) {
            logisticsPlaneAttrBuilder_.mergeFrom(value);
          }
          logisticsPlaneAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 25;
        return this;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       */
      public Builder clearLogisticsPlaneAttr() {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (entityAttrCase_ == 25) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 25) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          logisticsPlaneAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       */
      public com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.Builder getLogisticsPlaneAttrBuilder() {
        return getLogisticsPlaneAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       */
      @java.lang.Override
      public com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPBOrBuilder getLogisticsPlaneAttrOrBuilder() {
        if ((entityAttrCase_ == 25) && (logisticsPlaneAttrBuilder_ != null)) {
          return logisticsPlaneAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 25) {
            return (com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_;
          }
          return com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntityPB logisticsPlaneAttr = 25;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB, com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.Builder, com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPBOrBuilder> 
          getLogisticsPlaneAttrFieldBuilder() {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 25)) {
            entityAttr_ = com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.getDefaultInstance();
          }
          logisticsPlaneAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB, com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB.Builder, com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPBOrBuilder>(
                  (com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 25;
        onChanged();;
        return logisticsPlaneAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB, com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.Builder, com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPBOrBuilder> clanResBuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       * @return Whether the clanResBuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasClanResBuildingAttr() {
        return entityAttrCase_ == 26;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       * @return The clanResBuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB getClanResBuildingAttr() {
        if (clanResBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 26) {
            return (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 26) {
            return clanResBuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       */
      public Builder setClanResBuildingAttr(com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB value) {
        if (clanResBuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          clanResBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 26;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       */
      public Builder setClanResBuildingAttr(
          com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.Builder builderForValue) {
        if (clanResBuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          clanResBuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 26;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       */
      public Builder mergeClanResBuildingAttr(com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB value) {
        if (clanResBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 26 &&
              entityAttr_ != com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.newBuilder((com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 26) {
            clanResBuildingAttrBuilder_.mergeFrom(value);
          }
          clanResBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 26;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       */
      public Builder clearClanResBuildingAttr() {
        if (clanResBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 26) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 26) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          clanResBuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       */
      public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.Builder getClanResBuildingAttrBuilder() {
        return getClanResBuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       */
      @java.lang.Override
      public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPBOrBuilder getClanResBuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 26) && (clanResBuildingAttrBuilder_ != null)) {
          return clanResBuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 26) {
            return (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntityPB clanResBuildingAttr = 26;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB, com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.Builder, com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPBOrBuilder> 
          getClanResBuildingAttrFieldBuilder() {
        if (clanResBuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 26)) {
            entityAttr_ = com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance();
          }
          clanResBuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB, com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.Builder, com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPBOrBuilder>(
                  (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 26;
        onChanged();;
        return clanResBuildingAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB, com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.Builder, com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPBOrBuilder> dungeonBuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       * @return Whether the dungeonBuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasDungeonBuildingAttr() {
        return entityAttrCase_ == 27;
      }
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       * @return The dungeonBuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB getDungeonBuildingAttr() {
        if (dungeonBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 27) {
            return (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 27) {
            return dungeonBuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       */
      public Builder setDungeonBuildingAttr(com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB value) {
        if (dungeonBuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          dungeonBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 27;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       */
      public Builder setDungeonBuildingAttr(
          com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.Builder builderForValue) {
        if (dungeonBuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          dungeonBuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 27;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       */
      public Builder mergeDungeonBuildingAttr(com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB value) {
        if (dungeonBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 27 &&
              entityAttr_ != com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.newBuilder((com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 27) {
            dungeonBuildingAttrBuilder_.mergeFrom(value);
          }
          dungeonBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 27;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       */
      public Builder clearDungeonBuildingAttr() {
        if (dungeonBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 27) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 27) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          dungeonBuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       */
      public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.Builder getDungeonBuildingAttrBuilder() {
        return getDungeonBuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       */
      @java.lang.Override
      public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPBOrBuilder getDungeonBuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 27) && (dungeonBuildingAttrBuilder_ != null)) {
          return dungeonBuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 27) {
            return (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_;
          }
          return com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.DungeonBuildingEntityPB dungeonBuildingAttr = 27;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB, com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.Builder, com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPBOrBuilder> 
          getDungeonBuildingAttrFieldBuilder() {
        if (dungeonBuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 27)) {
            entityAttr_ = com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance();
          }
          dungeonBuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB, com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.Builder, com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPBOrBuilder>(
                  (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 27;
        onChanged();;
        return dungeonBuildingAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.AreaSkillPB.AreaSkillEntityPB, com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.Builder, com.yorha.proto.AreaSkillPB.AreaSkillEntityPBOrBuilder> areaSkillAttrBuilder_;
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       * @return Whether the areaSkillAttr field is set.
       */
      @java.lang.Override
      public boolean hasAreaSkillAttr() {
        return entityAttrCase_ == 28;
      }
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       * @return The areaSkillAttr.
       */
      @java.lang.Override
      public com.yorha.proto.AreaSkillPB.AreaSkillEntityPB getAreaSkillAttr() {
        if (areaSkillAttrBuilder_ == null) {
          if (entityAttrCase_ == 28) {
            return (com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_;
          }
          return com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 28) {
            return areaSkillAttrBuilder_.getMessage();
          }
          return com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       */
      public Builder setAreaSkillAttr(com.yorha.proto.AreaSkillPB.AreaSkillEntityPB value) {
        if (areaSkillAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          areaSkillAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 28;
        return this;
      }
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       */
      public Builder setAreaSkillAttr(
          com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.Builder builderForValue) {
        if (areaSkillAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          areaSkillAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 28;
        return this;
      }
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       */
      public Builder mergeAreaSkillAttr(com.yorha.proto.AreaSkillPB.AreaSkillEntityPB value) {
        if (areaSkillAttrBuilder_ == null) {
          if (entityAttrCase_ == 28 &&
              entityAttr_ != com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.newBuilder((com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 28) {
            areaSkillAttrBuilder_.mergeFrom(value);
          }
          areaSkillAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 28;
        return this;
      }
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       */
      public Builder clearAreaSkillAttr() {
        if (areaSkillAttrBuilder_ == null) {
          if (entityAttrCase_ == 28) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 28) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          areaSkillAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       */
      public com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.Builder getAreaSkillAttrBuilder() {
        return getAreaSkillAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       */
      @java.lang.Override
      public com.yorha.proto.AreaSkillPB.AreaSkillEntityPBOrBuilder getAreaSkillAttrOrBuilder() {
        if ((entityAttrCase_ == 28) && (areaSkillAttrBuilder_ != null)) {
          return areaSkillAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 28) {
            return (com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_;
          }
          return com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.AreaSkillEntityPB areaSkillAttr = 28;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.AreaSkillPB.AreaSkillEntityPB, com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.Builder, com.yorha.proto.AreaSkillPB.AreaSkillEntityPBOrBuilder> 
          getAreaSkillAttrFieldBuilder() {
        if (areaSkillAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 28)) {
            entityAttr_ = com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.getDefaultInstance();
          }
          areaSkillAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.AreaSkillPB.AreaSkillEntityPB, com.yorha.proto.AreaSkillPB.AreaSkillEntityPB.Builder, com.yorha.proto.AreaSkillPB.AreaSkillEntityPBOrBuilder>(
                  (com.yorha.proto.AreaSkillPB.AreaSkillEntityPB) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 28;
        onChanged();;
        return areaSkillAttrBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.EntityAttr)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.EntityAttr)
    private static final com.yorha.proto.EntityAttrOuterClass.EntityAttr DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.EntityAttrOuterClass.EntityAttr();
    }

    public static com.yorha.proto.EntityAttrOuterClass.EntityAttr getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<EntityAttr>
        PARSER = new com.google.protobuf.AbstractParser<EntityAttr>() {
      @java.lang.Override
      public EntityAttr parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EntityAttr(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<EntityAttr> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EntityAttr> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.EntityAttrOuterClass.EntityAttr getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_EntityAttr_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_EntityAttr_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n%ss_proto/gen/entity/entity_attr.proto\022" +
      "\017com.yorha.proto\032)cs_proto/gen/areaSkill" +
      "/area_skillPB.proto\032\036cs_proto/gen/army/a" +
      "rmyPB.proto\032\036cs_proto/gen/cave/cavePB.pr" +
      "oto\032\036cs_proto/gen/city/cityPB.proto\0324cs_" +
      "proto/gen/clanResBuilding/clanResBuildin" +
      "gPB.proto\032\036cs_proto/gen/clan/clanPB.prot" +
      "o\032+cs_proto/gen/dropObject/drop_objectPB" +
      ".proto\0324cs_proto/gen/dungeonBuilding/dun" +
      "geonBuildingPB.proto\032$cs_proto/gen/dunge" +
      "on/dungeonPB.proto\0322cs_proto/gen/logisti" +
      "csPlane/logisticsPlanePB.proto\032-cs_proto" +
      "/gen/mapBuilding/map_buildingPB.proto\032$c" +
      "s_proto/gen/monster/monsterPB.proto\032,cs_" +
      "proto/gen/outbuilding/outbuildingPB.prot" +
      "o\032\"cs_proto/gen/player/playerPB.proto\032,c" +
      "s_proto/gen/resBuilding/resBuildingPB.pr" +
      "oto\032&cs_proto/gen/spyPlane/spyPlanePB.pr" +
      "oto\032\036cs_proto/gen/zone/zonePB.proto\"\342\010\n\n" +
      "EntityAttr\022\020\n\010entityId\030\001 \001(\003\022/\n\nentityTy" +
      "pe\030\002 \001(\0162\033.com.yorha.proto.EntityType\0221\n" +
      "\010armyAttr\030\013 \001(\0132\035.com.yorha.proto.ArmyEn" +
      "tityPBH\000\0221\n\010cityAttr\030\014 \001(\0132\035.com.yorha.p" +
      "roto.CityEntityPBH\000\0225\n\nplayerAttr\030\r \001(\0132" +
      "\037.com.yorha.proto.PlayerEntityPBH\000\0227\n\013mo" +
      "nsterAttr\030\016 \001(\0132 .com.yorha.proto.Monste" +
      "rEntityPBH\000\0221\n\010clanAttr\030\017 \001(\0132\035.com.yorh" +
      "a.proto.ClanEntityPBH\000\022=\n\016dropObjectAttr" +
      "\030\020 \001(\0132#.com.yorha.proto.DropObjectEntit" +
      "yPBH\000\022?\n\017mapBuildingAttr\030\021 \001(\0132$.com.yor" +
      "ha.proto.MapBuildingEntityPBH\000\0225\n\010spyPla" +
      "ne\030\022 \001(\0132!.com.yorha.proto.SpyPlaneEntit" +
      "yPBH\000\022?\n\017resBuildingAttr\030\023 \001(\0132$.com.yor" +
      "ha.proto.ResBuildingEntityPBH\000\0225\n\010ZoneIn" +
      "fo\030\024 \001(\0132!.com.yorha.proto.ZoneInfoEntit" +
      "yPBH\000\022A\n\020dungeonSceneAttr\030\025 \001(\0132%.com.yo" +
      "rha.proto.DungeonSceneEntityPBH\000\022?\n\017outb" +
      "uildingAttr\030\027 \001(\0132$.com.yorha.proto.Outb" +
      "uildingEntityPBH\000\0221\n\010caveAttr\030\030 \001(\0132\035.co" +
      "m.yorha.proto.CaveEntityPBH\000\022E\n\022logistic" +
      "sPlaneAttr\030\031 \001(\0132\'.com.yorha.proto.Logis" +
      "ticsPlaneEntityPBH\000\022G\n\023clanResBuildingAt" +
      "tr\030\032 \001(\0132(.com.yorha.proto.ClanResBuildi" +
      "ngEntityPBH\000\022G\n\023dungeonBuildingAttr\030\033 \001(" +
      "\0132(.com.yorha.proto.DungeonBuildingEntit" +
      "yPBH\000\022;\n\rareaSkillAttr\030\034 \001(\0132\".com.yorha" +
      ".proto.AreaSkillEntityPBH\000B\014\n\nentityAttr" +
      "*\354\002\n\nEntityType\022\016\n\nET_Unknown\020\000\022\013\n\007ET_Ar" +
      "my\020\001\022\013\n\007ET_City\020\002\022\r\n\tET_Player\020\003\022\016\n\nET_M" +
      "onster\020\004\022\017\n\013ET_BigScene\020\005\022\013\n\007ET_Clan\020\006\022\021" +
      "\n\rET_DropObject\020\007\022\022\n\016ET_MapBuilding\020\010\022\017\n" +
      "\013ET_SpyPlane\020\t\022\022\n\016ET_ResBuilding\020\n\022\013\n\007ET" +
      "_Zone\020\013\022\016\n\nET_Dungeon\020\014\022\022\n\016ET_Outbuildin" +
      "g\020\017\022\013\n\007ET_Cave\020\020\022\025\n\021ET_LogisticsPlane\020\022\022" +
      "\026\n\022ET_ClanResBuilding\020\023\022\026\n\022ET_DungeonBui" +
      "lding\020\024\022\020\n\014ET_AreaSkill\020\025\022\024\n\020ET_Building" +
      "Group\020\026B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.AreaSkillPB.getDescriptor(),
          com.yorha.proto.ArmyPB.getDescriptor(),
          com.yorha.proto.CavePB.getDescriptor(),
          com.yorha.proto.CityPB.getDescriptor(),
          com.yorha.proto.ClanResBuildingPB.getDescriptor(),
          com.yorha.proto.ClanPB.getDescriptor(),
          com.yorha.proto.DropObjectPB.getDescriptor(),
          com.yorha.proto.DungeonBuildingPB.getDescriptor(),
          com.yorha.proto.DungeonPB.getDescriptor(),
          com.yorha.proto.LogisticsPlanePB.getDescriptor(),
          com.yorha.proto.MapBuildingPB.getDescriptor(),
          com.yorha.proto.MonsterPB.getDescriptor(),
          com.yorha.proto.OutbuildingPB.getDescriptor(),
          com.yorha.proto.PlayerPB.getDescriptor(),
          com.yorha.proto.ResBuildingPB.getDescriptor(),
          com.yorha.proto.SpyPlanePB.getDescriptor(),
          com.yorha.proto.ZonePB.getDescriptor(),
        });
    internal_static_com_yorha_proto_EntityAttr_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_EntityAttr_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_EntityAttr_descriptor,
        new java.lang.String[] { "EntityId", "EntityType", "ArmyAttr", "CityAttr", "PlayerAttr", "MonsterAttr", "ClanAttr", "DropObjectAttr", "MapBuildingAttr", "SpyPlane", "ResBuildingAttr", "ZoneInfo", "DungeonSceneAttr", "OutbuildingAttr", "CaveAttr", "LogisticsPlaneAttr", "ClanResBuildingAttr", "DungeonBuildingAttr", "AreaSkillAttr", "EntityAttr", });
    com.yorha.proto.AreaSkillPB.getDescriptor();
    com.yorha.proto.ArmyPB.getDescriptor();
    com.yorha.proto.CavePB.getDescriptor();
    com.yorha.proto.CityPB.getDescriptor();
    com.yorha.proto.ClanResBuildingPB.getDescriptor();
    com.yorha.proto.ClanPB.getDescriptor();
    com.yorha.proto.DropObjectPB.getDescriptor();
    com.yorha.proto.DungeonBuildingPB.getDescriptor();
    com.yorha.proto.DungeonPB.getDescriptor();
    com.yorha.proto.LogisticsPlanePB.getDescriptor();
    com.yorha.proto.MapBuildingPB.getDescriptor();
    com.yorha.proto.MonsterPB.getDescriptor();
    com.yorha.proto.OutbuildingPB.getDescriptor();
    com.yorha.proto.PlayerPB.getDescriptor();
    com.yorha.proto.ResBuildingPB.getDescriptor();
    com.yorha.proto.SpyPlanePB.getDescriptor();
    com.yorha.proto.ZonePB.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

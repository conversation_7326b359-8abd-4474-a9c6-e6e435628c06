// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clan/ss_clan_store.proto

package com.yorha.proto;

public final class SsClanStore {
  private SsClanStore() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FetchClanStoreAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanStoreAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    boolean hasInterfaceType();
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanStoreAsk}
   */
  public static final class FetchClanStoreAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanStoreAsk)
      FetchClanStoreAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanStoreAsk.newBuilder() to construct.
    private FetchClanStoreAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanStoreAsk() {
      interfaceType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanStoreAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanStoreAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanStoreInterfaceType value = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                interfaceType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanStore.FetchClanStoreAsk.class, com.yorha.proto.SsClanStore.FetchClanStoreAsk.Builder.class);
    }

    private int bitField0_;
    public static final int INTERFACETYPE_FIELD_NUMBER = 1;
    private int interfaceType_;
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    @java.lang.Override public boolean hasInterfaceType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, interfaceType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, interfaceType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanStore.FetchClanStoreAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanStore.FetchClanStoreAsk other = (com.yorha.proto.SsClanStore.FetchClanStoreAsk) obj;

      if (hasInterfaceType() != other.hasInterfaceType()) return false;
      if (hasInterfaceType()) {
        if (interfaceType_ != other.interfaceType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInterfaceType()) {
        hash = (37 * hash) + INTERFACETYPE_FIELD_NUMBER;
        hash = (53 * hash) + interfaceType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanStore.FetchClanStoreAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanStoreAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanStoreAsk)
        com.yorha.proto.SsClanStore.FetchClanStoreAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanStore.FetchClanStoreAsk.class, com.yorha.proto.SsClanStore.FetchClanStoreAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanStore.FetchClanStoreAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        interfaceType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanStore.FetchClanStoreAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreAsk build() {
        com.yorha.proto.SsClanStore.FetchClanStoreAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreAsk buildPartial() {
        com.yorha.proto.SsClanStore.FetchClanStoreAsk result = new com.yorha.proto.SsClanStore.FetchClanStoreAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.interfaceType_ = interfaceType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanStore.FetchClanStoreAsk) {
          return mergeFrom((com.yorha.proto.SsClanStore.FetchClanStoreAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanStore.FetchClanStoreAsk other) {
        if (other == com.yorha.proto.SsClanStore.FetchClanStoreAsk.getDefaultInstance()) return this;
        if (other.hasInterfaceType()) {
          setInterfaceType(other.getInterfaceType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanStore.FetchClanStoreAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanStore.FetchClanStoreAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int interfaceType_ = 0;
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return Whether the interfaceType field is set.
       */
      @java.lang.Override public boolean hasInterfaceType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return The interfaceType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @param value The interfaceType to set.
       * @return This builder for chaining.
       */
      public Builder setInterfaceType(com.yorha.proto.CommonEnum.ClanStoreInterfaceType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        interfaceType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInterfaceType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        interfaceType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanStoreAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanStoreAsk)
    private static final com.yorha.proto.SsClanStore.FetchClanStoreAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanStore.FetchClanStoreAsk();
    }

    public static com.yorha.proto.SsClanStore.FetchClanStoreAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanStoreAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanStoreAsk>() {
      @java.lang.Override
      public FetchClanStoreAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanStoreAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanStoreAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanStoreAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanStore.FetchClanStoreAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanStoreAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanStoreAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */
    int getStoreInfoCount();
    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */
    boolean containsStoreInfo(
        int key);
    /**
     * Use {@link #getStoreInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>
    getStoreInfo();
    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>
    getStoreInfoMap();
    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */

    com.yorha.proto.Struct.ClanStoreItemInfo getStoreInfoOrDefault(
        int key,
        com.yorha.proto.Struct.ClanStoreItemInfo defaultValue);
    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */

    com.yorha.proto.Struct.ClanStoreItemInfo getStoreInfoOrThrow(
        int key);

    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return Whether the clanScore field is set.
     */
    boolean hasClanScore();
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return The clanScore.
     */
    long getClanScore();

    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return Whether the territoryLv field is set.
     */
    boolean hasTerritoryLv();
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return The territoryLv.
     */
    int getTerritoryLv();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanStoreAns}
   */
  public static final class FetchClanStoreAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanStoreAns)
      FetchClanStoreAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanStoreAns.newBuilder() to construct.
    private FetchClanStoreAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanStoreAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanStoreAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanStoreAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                storeInfo_ = com.google.protobuf.MapField.newMapField(
                    StoreInfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>
              storeInfo__ = input.readMessage(
                  StoreInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              storeInfo_.getMutableMap().put(
                  storeInfo__.getKey(), storeInfo__.getValue());
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              clanScore_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              territoryLv_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetStoreInfo();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanStore.FetchClanStoreAns.class, com.yorha.proto.SsClanStore.FetchClanStoreAns.Builder.class);
    }

    private int bitField0_;
    public static final int STOREINFO_FIELD_NUMBER = 1;
    private static final class StoreInfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>newDefaultInstance(
                  com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAns_StoreInfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.Struct.ClanStoreItemInfo.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> storeInfo_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>
    internalGetStoreInfo() {
      if (storeInfo_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            StoreInfoDefaultEntryHolder.defaultEntry);
      }
      return storeInfo_;
    }

    public int getStoreInfoCount() {
      return internalGetStoreInfo().getMap().size();
    }
    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */

    @java.lang.Override
    public boolean containsStoreInfo(
        int key) {
      
      return internalGetStoreInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getStoreInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> getStoreInfo() {
      return getStoreInfoMap();
    }
    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> getStoreInfoMap() {
      return internalGetStoreInfo().getMap();
    }
    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.Struct.ClanStoreItemInfo getStoreInfoOrDefault(
        int key,
        com.yorha.proto.Struct.ClanStoreItemInfo defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> map =
          internalGetStoreInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 商店道具列表，道具id + 个数
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.Struct.ClanStoreItemInfo getStoreInfoOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> map =
          internalGetStoreInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int CLANSCORE_FIELD_NUMBER = 2;
    private long clanScore_;
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return Whether the clanScore field is set.
     */
    @java.lang.Override
    public boolean hasClanScore() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return The clanScore.
     */
    @java.lang.Override
    public long getClanScore() {
      return clanScore_;
    }

    public static final int TERRITORYLV_FIELD_NUMBER = 3;
    private int territoryLv_;
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return Whether the territoryLv field is set.
     */
    @java.lang.Override
    public boolean hasTerritoryLv() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return The territoryLv.
     */
    @java.lang.Override
    public int getTerritoryLv() {
      return territoryLv_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetStoreInfo(),
          StoreInfoDefaultEntryHolder.defaultEntry,
          1);
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(2, clanScore_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, territoryLv_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> entry
           : internalGetStoreInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>
        storeInfo__ = StoreInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, storeInfo__);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, clanScore_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, territoryLv_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanStore.FetchClanStoreAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanStore.FetchClanStoreAns other = (com.yorha.proto.SsClanStore.FetchClanStoreAns) obj;

      if (!internalGetStoreInfo().equals(
          other.internalGetStoreInfo())) return false;
      if (hasClanScore() != other.hasClanScore()) return false;
      if (hasClanScore()) {
        if (getClanScore()
            != other.getClanScore()) return false;
      }
      if (hasTerritoryLv() != other.hasTerritoryLv()) return false;
      if (hasTerritoryLv()) {
        if (getTerritoryLv()
            != other.getTerritoryLv()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetStoreInfo().getMap().isEmpty()) {
        hash = (37 * hash) + STOREINFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetStoreInfo().hashCode();
      }
      if (hasClanScore()) {
        hash = (37 * hash) + CLANSCORE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanScore());
      }
      if (hasTerritoryLv()) {
        hash = (37 * hash) + TERRITORYLV_FIELD_NUMBER;
        hash = (53 * hash) + getTerritoryLv();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanStore.FetchClanStoreAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanStoreAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanStoreAns)
        com.yorha.proto.SsClanStore.FetchClanStoreAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetStoreInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableStoreInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanStore.FetchClanStoreAns.class, com.yorha.proto.SsClanStore.FetchClanStoreAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanStore.FetchClanStoreAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableStoreInfo().clear();
        clanScore_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        territoryLv_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanStore.FetchClanStoreAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreAns build() {
        com.yorha.proto.SsClanStore.FetchClanStoreAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreAns buildPartial() {
        com.yorha.proto.SsClanStore.FetchClanStoreAns result = new com.yorha.proto.SsClanStore.FetchClanStoreAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.storeInfo_ = internalGetStoreInfo();
        result.storeInfo_.makeImmutable();
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.clanScore_ = clanScore_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.territoryLv_ = territoryLv_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanStore.FetchClanStoreAns) {
          return mergeFrom((com.yorha.proto.SsClanStore.FetchClanStoreAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanStore.FetchClanStoreAns other) {
        if (other == com.yorha.proto.SsClanStore.FetchClanStoreAns.getDefaultInstance()) return this;
        internalGetMutableStoreInfo().mergeFrom(
            other.internalGetStoreInfo());
        if (other.hasClanScore()) {
          setClanScore(other.getClanScore());
        }
        if (other.hasTerritoryLv()) {
          setTerritoryLv(other.getTerritoryLv());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanStore.FetchClanStoreAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanStore.FetchClanStoreAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> storeInfo_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>
      internalGetStoreInfo() {
        if (storeInfo_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              StoreInfoDefaultEntryHolder.defaultEntry);
        }
        return storeInfo_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>
      internalGetMutableStoreInfo() {
        onChanged();;
        if (storeInfo_ == null) {
          storeInfo_ = com.google.protobuf.MapField.newMapField(
              StoreInfoDefaultEntryHolder.defaultEntry);
        }
        if (!storeInfo_.isMutable()) {
          storeInfo_ = storeInfo_.copy();
        }
        return storeInfo_;
      }

      public int getStoreInfoCount() {
        return internalGetStoreInfo().getMap().size();
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
       */

      @java.lang.Override
      public boolean containsStoreInfo(
          int key) {
        
        return internalGetStoreInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getStoreInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> getStoreInfo() {
        return getStoreInfoMap();
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> getStoreInfoMap() {
        return internalGetStoreInfo().getMap();
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.Struct.ClanStoreItemInfo getStoreInfoOrDefault(
          int key,
          com.yorha.proto.Struct.ClanStoreItemInfo defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> map =
            internalGetStoreInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.Struct.ClanStoreItemInfo getStoreInfoOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> map =
            internalGetStoreInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearStoreInfo() {
        internalGetMutableStoreInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
       */

      public Builder removeStoreInfo(
          int key) {
        
        internalGetMutableStoreInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo>
      getMutableStoreInfo() {
        return internalGetMutableStoreInfo().getMutableMap();
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
       */
      public Builder putStoreInfo(
          int key,
          com.yorha.proto.Struct.ClanStoreItemInfo value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableStoreInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfo&gt; storeInfo = 1;</code>
       */

      public Builder putAllStoreInfo(
          java.util.Map<java.lang.Integer, com.yorha.proto.Struct.ClanStoreItemInfo> values) {
        internalGetMutableStoreInfo().getMutableMap()
            .putAll(values);
        return this;
      }

      private long clanScore_ ;
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return Whether the clanScore field is set.
       */
      @java.lang.Override
      public boolean hasClanScore() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return The clanScore.
       */
      @java.lang.Override
      public long getClanScore() {
        return clanScore_;
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @param value The clanScore to set.
       * @return This builder for chaining.
       */
      public Builder setClanScore(long value) {
        bitField0_ |= 0x00000002;
        clanScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanScore() {
        bitField0_ = (bitField0_ & ~0x00000002);
        clanScore_ = 0L;
        onChanged();
        return this;
      }

      private int territoryLv_ ;
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return Whether the territoryLv field is set.
       */
      @java.lang.Override
      public boolean hasTerritoryLv() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return The territoryLv.
       */
      @java.lang.Override
      public int getTerritoryLv() {
        return territoryLv_;
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @param value The territoryLv to set.
       * @return This builder for chaining.
       */
      public Builder setTerritoryLv(int value) {
        bitField0_ |= 0x00000004;
        territoryLv_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTerritoryLv() {
        bitField0_ = (bitField0_ & ~0x00000004);
        territoryLv_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanStoreAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanStoreAns)
    private static final com.yorha.proto.SsClanStore.FetchClanStoreAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanStore.FetchClanStoreAns();
    }

    public static com.yorha.proto.SsClanStore.FetchClanStoreAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanStoreAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanStoreAns>() {
      @java.lang.Override
      public FetchClanStoreAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanStoreAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanStoreAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanStoreAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanStore.FetchClanStoreAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanStoreRecordAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanStoreRecordAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 需要的记录类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    boolean hasInterfaceType();
    /**
     * <pre>
     * 需要的记录类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanStoreRecordAsk}
   */
  public static final class FetchClanStoreRecordAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanStoreRecordAsk)
      FetchClanStoreRecordAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanStoreRecordAsk.newBuilder() to construct.
    private FetchClanStoreRecordAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanStoreRecordAsk() {
      interfaceType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanStoreRecordAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanStoreRecordAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanStoreInterfaceType value = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                interfaceType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk.class, com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk.Builder.class);
    }

    private int bitField0_;
    public static final int INTERFACETYPE_FIELD_NUMBER = 1;
    private int interfaceType_;
    /**
     * <pre>
     * 需要的记录类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    @java.lang.Override public boolean hasInterfaceType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 需要的记录类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, interfaceType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, interfaceType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk other = (com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk) obj;

      if (hasInterfaceType() != other.hasInterfaceType()) return false;
      if (hasInterfaceType()) {
        if (interfaceType_ != other.interfaceType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInterfaceType()) {
        hash = (37 * hash) + INTERFACETYPE_FIELD_NUMBER;
        hash = (53 * hash) + interfaceType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanStoreRecordAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanStoreRecordAsk)
        com.yorha.proto.SsClanStore.FetchClanStoreRecordAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk.class, com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        interfaceType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk build() {
        com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk buildPartial() {
        com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk result = new com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.interfaceType_ = interfaceType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk) {
          return mergeFrom((com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk other) {
        if (other == com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk.getDefaultInstance()) return this;
        if (other.hasInterfaceType()) {
          setInterfaceType(other.getInterfaceType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int interfaceType_ = 0;
      /**
       * <pre>
       * 需要的记录类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return Whether the interfaceType field is set.
       */
      @java.lang.Override public boolean hasInterfaceType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 需要的记录类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return The interfaceType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
      }
      /**
       * <pre>
       * 需要的记录类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @param value The interfaceType to set.
       * @return This builder for chaining.
       */
      public Builder setInterfaceType(com.yorha.proto.CommonEnum.ClanStoreInterfaceType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        interfaceType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要的记录类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInterfaceType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        interfaceType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanStoreRecordAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanStoreRecordAsk)
    private static final com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk();
    }

    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanStoreRecordAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanStoreRecordAsk>() {
      @java.lang.Override
      public FetchClanStoreRecordAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanStoreRecordAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanStoreRecordAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanStoreRecordAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanStore.FetchClanStoreRecordAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanStoreRecordAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanStoreRecordAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    java.util.List<com.yorha.proto.Struct.ClanStoreLogItem> 
        getRecordsList();
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    com.yorha.proto.Struct.ClanStoreLogItem getRecords(int index);
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    int getRecordsCount();
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.Struct.ClanStoreLogItemOrBuilder> 
        getRecordsOrBuilderList();
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    com.yorha.proto.Struct.ClanStoreLogItemOrBuilder getRecordsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanStoreRecordAns}
   */
  public static final class FetchClanStoreRecordAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanStoreRecordAns)
      FetchClanStoreRecordAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanStoreRecordAns.newBuilder() to construct.
    private FetchClanStoreRecordAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanStoreRecordAns() {
      records_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanStoreRecordAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanStoreRecordAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                records_ = new java.util.ArrayList<com.yorha.proto.Struct.ClanStoreLogItem>();
                mutable_bitField0_ |= 0x00000001;
              }
              records_.add(
                  input.readMessage(com.yorha.proto.Struct.ClanStoreLogItem.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          records_ = java.util.Collections.unmodifiableList(records_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanStore.FetchClanStoreRecordAns.class, com.yorha.proto.SsClanStore.FetchClanStoreRecordAns.Builder.class);
    }

    public static final int RECORDS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.Struct.ClanStoreLogItem> records_;
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.Struct.ClanStoreLogItem> getRecordsList() {
      return records_;
    }
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.Struct.ClanStoreLogItemOrBuilder> 
        getRecordsOrBuilderList() {
      return records_;
    }
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    @java.lang.Override
    public int getRecordsCount() {
      return records_.size();
    }
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ClanStoreLogItem getRecords(int index) {
      return records_.get(index);
    }
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ClanStoreLogItemOrBuilder getRecordsOrBuilder(
        int index) {
      return records_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < records_.size(); i++) {
        output.writeMessage(1, records_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < records_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, records_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanStore.FetchClanStoreRecordAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanStore.FetchClanStoreRecordAns other = (com.yorha.proto.SsClanStore.FetchClanStoreRecordAns) obj;

      if (!getRecordsList()
          .equals(other.getRecordsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRecordsCount() > 0) {
        hash = (37 * hash) + RECORDS_FIELD_NUMBER;
        hash = (53 * hash) + getRecordsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanStore.FetchClanStoreRecordAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanStoreRecordAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanStoreRecordAns)
        com.yorha.proto.SsClanStore.FetchClanStoreRecordAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanStore.FetchClanStoreRecordAns.class, com.yorha.proto.SsClanStore.FetchClanStoreRecordAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanStore.FetchClanStoreRecordAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRecordsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (recordsBuilder_ == null) {
          records_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          recordsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_FetchClanStoreRecordAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreRecordAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanStore.FetchClanStoreRecordAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreRecordAns build() {
        com.yorha.proto.SsClanStore.FetchClanStoreRecordAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.FetchClanStoreRecordAns buildPartial() {
        com.yorha.proto.SsClanStore.FetchClanStoreRecordAns result = new com.yorha.proto.SsClanStore.FetchClanStoreRecordAns(this);
        int from_bitField0_ = bitField0_;
        if (recordsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            records_ = java.util.Collections.unmodifiableList(records_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.records_ = records_;
        } else {
          result.records_ = recordsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanStore.FetchClanStoreRecordAns) {
          return mergeFrom((com.yorha.proto.SsClanStore.FetchClanStoreRecordAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanStore.FetchClanStoreRecordAns other) {
        if (other == com.yorha.proto.SsClanStore.FetchClanStoreRecordAns.getDefaultInstance()) return this;
        if (recordsBuilder_ == null) {
          if (!other.records_.isEmpty()) {
            if (records_.isEmpty()) {
              records_ = other.records_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRecordsIsMutable();
              records_.addAll(other.records_);
            }
            onChanged();
          }
        } else {
          if (!other.records_.isEmpty()) {
            if (recordsBuilder_.isEmpty()) {
              recordsBuilder_.dispose();
              recordsBuilder_ = null;
              records_ = other.records_;
              bitField0_ = (bitField0_ & ~0x00000001);
              recordsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRecordsFieldBuilder() : null;
            } else {
              recordsBuilder_.addAllMessages(other.records_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanStore.FetchClanStoreRecordAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanStore.FetchClanStoreRecordAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.Struct.ClanStoreLogItem> records_ =
        java.util.Collections.emptyList();
      private void ensureRecordsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          records_ = new java.util.ArrayList<com.yorha.proto.Struct.ClanStoreLogItem>(records_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.ClanStoreLogItem, com.yorha.proto.Struct.ClanStoreLogItem.Builder, com.yorha.proto.Struct.ClanStoreLogItemOrBuilder> recordsBuilder_;

      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public java.util.List<com.yorha.proto.Struct.ClanStoreLogItem> getRecordsList() {
        if (recordsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(records_);
        } else {
          return recordsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public int getRecordsCount() {
        if (recordsBuilder_ == null) {
          return records_.size();
        } else {
          return recordsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public com.yorha.proto.Struct.ClanStoreLogItem getRecords(int index) {
        if (recordsBuilder_ == null) {
          return records_.get(index);
        } else {
          return recordsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder setRecords(
          int index, com.yorha.proto.Struct.ClanStoreLogItem value) {
        if (recordsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordsIsMutable();
          records_.set(index, value);
          onChanged();
        } else {
          recordsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder setRecords(
          int index, com.yorha.proto.Struct.ClanStoreLogItem.Builder builderForValue) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          records_.set(index, builderForValue.build());
          onChanged();
        } else {
          recordsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder addRecords(com.yorha.proto.Struct.ClanStoreLogItem value) {
        if (recordsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordsIsMutable();
          records_.add(value);
          onChanged();
        } else {
          recordsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder addRecords(
          int index, com.yorha.proto.Struct.ClanStoreLogItem value) {
        if (recordsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordsIsMutable();
          records_.add(index, value);
          onChanged();
        } else {
          recordsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder addRecords(
          com.yorha.proto.Struct.ClanStoreLogItem.Builder builderForValue) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          records_.add(builderForValue.build());
          onChanged();
        } else {
          recordsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder addRecords(
          int index, com.yorha.proto.Struct.ClanStoreLogItem.Builder builderForValue) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          records_.add(index, builderForValue.build());
          onChanged();
        } else {
          recordsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder addAllRecords(
          java.lang.Iterable<? extends com.yorha.proto.Struct.ClanStoreLogItem> values) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, records_);
          onChanged();
        } else {
          recordsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder clearRecords() {
        if (recordsBuilder_ == null) {
          records_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          recordsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public Builder removeRecords(int index) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          records_.remove(index);
          onChanged();
        } else {
          recordsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public com.yorha.proto.Struct.ClanStoreLogItem.Builder getRecordsBuilder(
          int index) {
        return getRecordsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public com.yorha.proto.Struct.ClanStoreLogItemOrBuilder getRecordsOrBuilder(
          int index) {
        if (recordsBuilder_ == null) {
          return records_.get(index);  } else {
          return recordsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.Struct.ClanStoreLogItemOrBuilder> 
           getRecordsOrBuilderList() {
        if (recordsBuilder_ != null) {
          return recordsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(records_);
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public com.yorha.proto.Struct.ClanStoreLogItem.Builder addRecordsBuilder() {
        return getRecordsFieldBuilder().addBuilder(
            com.yorha.proto.Struct.ClanStoreLogItem.getDefaultInstance());
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public com.yorha.proto.Struct.ClanStoreLogItem.Builder addRecordsBuilder(
          int index) {
        return getRecordsFieldBuilder().addBuilder(
            index, com.yorha.proto.Struct.ClanStoreLogItem.getDefaultInstance());
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItem records = 1;</code>
       */
      public java.util.List<com.yorha.proto.Struct.ClanStoreLogItem.Builder> 
           getRecordsBuilderList() {
        return getRecordsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.ClanStoreLogItem, com.yorha.proto.Struct.ClanStoreLogItem.Builder, com.yorha.proto.Struct.ClanStoreLogItemOrBuilder> 
          getRecordsFieldBuilder() {
        if (recordsBuilder_ == null) {
          recordsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.Struct.ClanStoreLogItem, com.yorha.proto.Struct.ClanStoreLogItem.Builder, com.yorha.proto.Struct.ClanStoreLogItemOrBuilder>(
                  records_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          records_ = null;
        }
        return recordsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanStoreRecordAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanStoreRecordAns)
    private static final com.yorha.proto.SsClanStore.FetchClanStoreRecordAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanStore.FetchClanStoreRecordAns();
    }

    public static com.yorha.proto.SsClanStore.FetchClanStoreRecordAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanStoreRecordAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanStoreRecordAns>() {
      @java.lang.Override
      public FetchClanStoreRecordAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanStoreRecordAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanStoreRecordAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanStoreRecordAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanStore.FetchClanStoreRecordAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OperateClanStoreItemAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OperateClanStoreItemAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    boolean hasInterfaceType();
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType();

    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 2;</code>
     * @return Whether the itemId field is set.
     */
    boolean hasItemId();
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 2;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <pre>
     * 道具数目
     * </pre>
     *
     * <code>optional int32 itemNum = 3;</code>
     * @return Whether the itemNum field is set.
     */
    boolean hasItemNum();
    /**
     * <pre>
     * 道具数目
     * </pre>
     *
     * <code>optional int32 itemNum = 3;</code>
     * @return The itemNum.
     */
    int getItemNum();

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 4;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 4;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OperateClanStoreItemAsk}
   */
  public static final class OperateClanStoreItemAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OperateClanStoreItemAsk)
      OperateClanStoreItemAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OperateClanStoreItemAsk.newBuilder() to construct.
    private OperateClanStoreItemAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OperateClanStoreItemAsk() {
      interfaceType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OperateClanStoreItemAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OperateClanStoreItemAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanStoreInterfaceType value = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                interfaceType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              itemId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              itemNum_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanStore.OperateClanStoreItemAsk.class, com.yorha.proto.SsClanStore.OperateClanStoreItemAsk.Builder.class);
    }

    private int bitField0_;
    public static final int INTERFACETYPE_FIELD_NUMBER = 1;
    private int interfaceType_;
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    @java.lang.Override public boolean hasInterfaceType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
    }

    public static final int ITEMID_FIELD_NUMBER = 2;
    private int itemId_;
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 2;</code>
     * @return Whether the itemId field is set.
     */
    @java.lang.Override
    public boolean hasItemId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 2;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int ITEMNUM_FIELD_NUMBER = 3;
    private int itemNum_;
    /**
     * <pre>
     * 道具数目
     * </pre>
     *
     * <code>optional int32 itemNum = 3;</code>
     * @return Whether the itemNum field is set.
     */
    @java.lang.Override
    public boolean hasItemNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 道具数目
     * </pre>
     *
     * <code>optional int32 itemNum = 3;</code>
     * @return The itemNum.
     */
    @java.lang.Override
    public int getItemNum() {
      return itemNum_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 4;
    private long playerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 4;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 4;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, interfaceType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, itemId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, itemNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, interfaceType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, itemId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, itemNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanStore.OperateClanStoreItemAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanStore.OperateClanStoreItemAsk other = (com.yorha.proto.SsClanStore.OperateClanStoreItemAsk) obj;

      if (hasInterfaceType() != other.hasInterfaceType()) return false;
      if (hasInterfaceType()) {
        if (interfaceType_ != other.interfaceType_) return false;
      }
      if (hasItemId() != other.hasItemId()) return false;
      if (hasItemId()) {
        if (getItemId()
            != other.getItemId()) return false;
      }
      if (hasItemNum() != other.hasItemNum()) return false;
      if (hasItemNum()) {
        if (getItemNum()
            != other.getItemNum()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInterfaceType()) {
        hash = (37 * hash) + INTERFACETYPE_FIELD_NUMBER;
        hash = (53 * hash) + interfaceType_;
      }
      if (hasItemId()) {
        hash = (37 * hash) + ITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getItemId();
      }
      if (hasItemNum()) {
        hash = (37 * hash) + ITEMNUM_FIELD_NUMBER;
        hash = (53 * hash) + getItemNum();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanStore.OperateClanStoreItemAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OperateClanStoreItemAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OperateClanStoreItemAsk)
        com.yorha.proto.SsClanStore.OperateClanStoreItemAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanStore.OperateClanStoreItemAsk.class, com.yorha.proto.SsClanStore.OperateClanStoreItemAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanStore.OperateClanStoreItemAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        interfaceType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        itemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        itemNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.OperateClanStoreItemAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanStore.OperateClanStoreItemAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.OperateClanStoreItemAsk build() {
        com.yorha.proto.SsClanStore.OperateClanStoreItemAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.OperateClanStoreItemAsk buildPartial() {
        com.yorha.proto.SsClanStore.OperateClanStoreItemAsk result = new com.yorha.proto.SsClanStore.OperateClanStoreItemAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.interfaceType_ = interfaceType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.itemId_ = itemId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.itemNum_ = itemNum_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanStore.OperateClanStoreItemAsk) {
          return mergeFrom((com.yorha.proto.SsClanStore.OperateClanStoreItemAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanStore.OperateClanStoreItemAsk other) {
        if (other == com.yorha.proto.SsClanStore.OperateClanStoreItemAsk.getDefaultInstance()) return this;
        if (other.hasInterfaceType()) {
          setInterfaceType(other.getInterfaceType());
        }
        if (other.hasItemId()) {
          setItemId(other.getItemId());
        }
        if (other.hasItemNum()) {
          setItemNum(other.getItemNum());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanStore.OperateClanStoreItemAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanStore.OperateClanStoreItemAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int interfaceType_ = 0;
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return Whether the interfaceType field is set.
       */
      @java.lang.Override public boolean hasInterfaceType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return The interfaceType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @param value The interfaceType to set.
       * @return This builder for chaining.
       */
      public Builder setInterfaceType(com.yorha.proto.CommonEnum.ClanStoreInterfaceType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        interfaceType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInterfaceType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        interfaceType_ = 0;
        onChanged();
        return this;
      }

      private int itemId_ ;
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 2;</code>
       * @return Whether the itemId field is set.
       */
      @java.lang.Override
      public boolean hasItemId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 2;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 2;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        bitField0_ |= 0x00000002;
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        itemId_ = 0;
        onChanged();
        return this;
      }

      private int itemNum_ ;
      /**
       * <pre>
       * 道具数目
       * </pre>
       *
       * <code>optional int32 itemNum = 3;</code>
       * @return Whether the itemNum field is set.
       */
      @java.lang.Override
      public boolean hasItemNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 道具数目
       * </pre>
       *
       * <code>optional int32 itemNum = 3;</code>
       * @return The itemNum.
       */
      @java.lang.Override
      public int getItemNum() {
        return itemNum_;
      }
      /**
       * <pre>
       * 道具数目
       * </pre>
       *
       * <code>optional int32 itemNum = 3;</code>
       * @param value The itemNum to set.
       * @return This builder for chaining.
       */
      public Builder setItemNum(int value) {
        bitField0_ |= 0x00000004;
        itemNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 道具数目
       * </pre>
       *
       * <code>optional int32 itemNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        itemNum_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 4;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 4;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 4;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000008;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OperateClanStoreItemAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OperateClanStoreItemAsk)
    private static final com.yorha.proto.SsClanStore.OperateClanStoreItemAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanStore.OperateClanStoreItemAsk();
    }

    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OperateClanStoreItemAsk>
        PARSER = new com.google.protobuf.AbstractParser<OperateClanStoreItemAsk>() {
      @java.lang.Override
      public OperateClanStoreItemAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OperateClanStoreItemAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OperateClanStoreItemAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OperateClanStoreItemAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanStore.OperateClanStoreItemAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OperateClanStoreItemAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OperateClanStoreItemAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 依赖返回的枚举来显示是否购买成功或显示对应tips
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
     * @return Whether the returnType field is set.
     */
    boolean hasReturnType();
    /**
     * <pre>
     * 依赖返回的枚举来显示是否购买成功或显示对应tips
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
     * @return The returnType.
     */
    com.yorha.proto.CommonEnum.ClanStoreOperateReturnType getReturnType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OperateClanStoreItemAns}
   */
  public static final class OperateClanStoreItemAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OperateClanStoreItemAns)
      OperateClanStoreItemAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OperateClanStoreItemAns.newBuilder() to construct.
    private OperateClanStoreItemAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OperateClanStoreItemAns() {
      returnType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OperateClanStoreItemAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OperateClanStoreItemAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanStoreOperateReturnType value = com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                returnType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanStore.OperateClanStoreItemAns.class, com.yorha.proto.SsClanStore.OperateClanStoreItemAns.Builder.class);
    }

    private int bitField0_;
    public static final int RETURNTYPE_FIELD_NUMBER = 1;
    private int returnType_;
    /**
     * <pre>
     * 依赖返回的枚举来显示是否购买成功或显示对应tips
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
     * @return Whether the returnType field is set.
     */
    @java.lang.Override public boolean hasReturnType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 依赖返回的枚举来显示是否购买成功或显示对应tips
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
     * @return The returnType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanStoreOperateReturnType getReturnType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanStoreOperateReturnType result = com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.valueOf(returnType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.CSORT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, returnType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, returnType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanStore.OperateClanStoreItemAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanStore.OperateClanStoreItemAns other = (com.yorha.proto.SsClanStore.OperateClanStoreItemAns) obj;

      if (hasReturnType() != other.hasReturnType()) return false;
      if (hasReturnType()) {
        if (returnType_ != other.returnType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReturnType()) {
        hash = (37 * hash) + RETURNTYPE_FIELD_NUMBER;
        hash = (53 * hash) + returnType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanStore.OperateClanStoreItemAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OperateClanStoreItemAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OperateClanStoreItemAns)
        com.yorha.proto.SsClanStore.OperateClanStoreItemAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanStore.OperateClanStoreItemAns.class, com.yorha.proto.SsClanStore.OperateClanStoreItemAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanStore.OperateClanStoreItemAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        returnType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanStore.internal_static_com_yorha_proto_OperateClanStoreItemAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.OperateClanStoreItemAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanStore.OperateClanStoreItemAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.OperateClanStoreItemAns build() {
        com.yorha.proto.SsClanStore.OperateClanStoreItemAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanStore.OperateClanStoreItemAns buildPartial() {
        com.yorha.proto.SsClanStore.OperateClanStoreItemAns result = new com.yorha.proto.SsClanStore.OperateClanStoreItemAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.returnType_ = returnType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanStore.OperateClanStoreItemAns) {
          return mergeFrom((com.yorha.proto.SsClanStore.OperateClanStoreItemAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanStore.OperateClanStoreItemAns other) {
        if (other == com.yorha.proto.SsClanStore.OperateClanStoreItemAns.getDefaultInstance()) return this;
        if (other.hasReturnType()) {
          setReturnType(other.getReturnType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanStore.OperateClanStoreItemAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanStore.OperateClanStoreItemAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int returnType_ = 0;
      /**
       * <pre>
       * 依赖返回的枚举来显示是否购买成功或显示对应tips
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
       * @return Whether the returnType field is set.
       */
      @java.lang.Override public boolean hasReturnType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 依赖返回的枚举来显示是否购买成功或显示对应tips
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
       * @return The returnType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanStoreOperateReturnType getReturnType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanStoreOperateReturnType result = com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.valueOf(returnType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.CSORT_NONE : result;
      }
      /**
       * <pre>
       * 依赖返回的枚举来显示是否购买成功或显示对应tips
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
       * @param value The returnType to set.
       * @return This builder for chaining.
       */
      public Builder setReturnType(com.yorha.proto.CommonEnum.ClanStoreOperateReturnType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        returnType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 依赖返回的枚举来显示是否购买成功或显示对应tips
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearReturnType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        returnType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OperateClanStoreItemAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OperateClanStoreItemAns)
    private static final com.yorha.proto.SsClanStore.OperateClanStoreItemAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanStore.OperateClanStoreItemAns();
    }

    public static com.yorha.proto.SsClanStore.OperateClanStoreItemAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OperateClanStoreItemAns>
        PARSER = new com.google.protobuf.AbstractParser<OperateClanStoreItemAns>() {
      @java.lang.Override
      public OperateClanStoreItemAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OperateClanStoreItemAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OperateClanStoreItemAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OperateClanStoreItemAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanStore.OperateClanStoreItemAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanStoreAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanStoreAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanStoreAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanStoreAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanStoreAns_StoreInfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanStoreAns_StoreInfoEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanStoreRecordAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanStoreRecordAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanStoreRecordAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanStoreRecordAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OperateClanStoreItemAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OperateClanStoreItemAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OperateClanStoreItemAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OperateClanStoreItemAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n%ss_proto/gen/clan/ss_clan_store.proto\022" +
      "\017com.yorha.proto\032%ss_proto/gen/common/co" +
      "mmon_enum.proto\032 ss_proto/gen/common/str" +
      "uct.proto\"S\n\021FetchClanStoreAsk\022>\n\rinterf" +
      "aceType\030\001 \001(\0162\'.com.yorha.proto.ClanStor" +
      "eInterfaceType\"\327\001\n\021FetchClanStoreAns\022D\n\t" +
      "storeInfo\030\001 \003(\01321.com.yorha.proto.FetchC" +
      "lanStoreAns.StoreInfoEntry\022\021\n\tclanScore\030" +
      "\002 \001(\003\022\023\n\013territoryLv\030\003 \001(\005\032T\n\016StoreInfoE" +
      "ntry\022\013\n\003key\030\001 \001(\005\0221\n\005value\030\002 \001(\0132\".com.y" +
      "orha.proto.ClanStoreItemInfo:\0028\001\"Y\n\027Fetc" +
      "hClanStoreRecordAsk\022>\n\rinterfaceType\030\001 \001" +
      "(\0162\'.com.yorha.proto.ClanStoreInterfaceT" +
      "ype\"M\n\027FetchClanStoreRecordAns\0222\n\007record" +
      "s\030\001 \003(\0132!.com.yorha.proto.ClanStoreLogIt" +
      "em\"\214\001\n\027OperateClanStoreItemAsk\022>\n\rinterf" +
      "aceType\030\001 \001(\0162\'.com.yorha.proto.ClanStor" +
      "eInterfaceType\022\016\n\006itemId\030\002 \001(\005\022\017\n\007itemNu" +
      "m\030\003 \001(\005\022\020\n\010playerId\030\004 \001(\003\"Z\n\027OperateClan" +
      "StoreItemAns\022?\n\nreturnType\030\001 \001(\0162+.com.y" +
      "orha.proto.ClanStoreOperateReturnTypeB\002H" +
      "\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_FetchClanStoreAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_FetchClanStoreAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanStoreAsk_descriptor,
        new java.lang.String[] { "InterfaceType", });
    internal_static_com_yorha_proto_FetchClanStoreAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_FetchClanStoreAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanStoreAns_descriptor,
        new java.lang.String[] { "StoreInfo", "ClanScore", "TerritoryLv", });
    internal_static_com_yorha_proto_FetchClanStoreAns_StoreInfoEntry_descriptor =
      internal_static_com_yorha_proto_FetchClanStoreAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_FetchClanStoreAns_StoreInfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanStoreAns_StoreInfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_FetchClanStoreRecordAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_FetchClanStoreRecordAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanStoreRecordAsk_descriptor,
        new java.lang.String[] { "InterfaceType", });
    internal_static_com_yorha_proto_FetchClanStoreRecordAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_FetchClanStoreRecordAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanStoreRecordAns_descriptor,
        new java.lang.String[] { "Records", });
    internal_static_com_yorha_proto_OperateClanStoreItemAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_OperateClanStoreItemAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OperateClanStoreItemAsk_descriptor,
        new java.lang.String[] { "InterfaceType", "ItemId", "ItemNum", "PlayerId", });
    internal_static_com_yorha_proto_OperateClanStoreItemAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_OperateClanStoreItemAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OperateClanStoreItemAns_descriptor,
        new java.lang.String[] { "ReturnType", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

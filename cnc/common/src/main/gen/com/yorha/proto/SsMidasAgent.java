// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/midasAgent/ss_midas_agent.proto

package com.yorha.proto;

public final class SsMidasAgent {
  private SsMidasAgent() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MidasRespHeaderOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasRespHeader)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    boolean hasRet();
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    int getRet();

    /**
     * <code>optional string err_code = 2;</code>
     * @return Whether the errCode field is set.
     */
    boolean hasErrCode();
    /**
     * <code>optional string err_code = 2;</code>
     * @return The errCode.
     */
    java.lang.String getErrCode();
    /**
     * <code>optional string err_code = 2;</code>
     * @return The bytes for errCode.
     */
    com.google.protobuf.ByteString
        getErrCodeBytes();

    /**
     * <code>optional string msg = 3;</code>
     * @return Whether the msg field is set.
     */
    boolean hasMsg();
    /**
     * <code>optional string msg = 3;</code>
     * @return The msg.
     */
    java.lang.String getMsg();
    /**
     * <code>optional string msg = 3;</code>
     * @return The bytes for msg.
     */
    com.google.protobuf.ByteString
        getMsgBytes();

    /**
     * <pre>
     * 游戏币数量（包含赠送游戏币）
     * </pre>
     *
     * <code>optional int64 balance = 4;</code>
     * @return Whether the balance field is set.
     */
    boolean hasBalance();
    /**
     * <pre>
     * 游戏币数量（包含赠送游戏币）
     * </pre>
     *
     * <code>optional int64 balance = 4;</code>
     * @return The balance.
     */
    long getBalance();

    /**
     * <pre>
     * 赠送游戏币数量（balance中赠送游戏币数量）
     * </pre>
     *
     * <code>optional int64 gen_balance = 5;</code>
     * @return Whether the genBalance field is set.
     */
    boolean hasGenBalance();
    /**
     * <pre>
     * 赠送游戏币数量（balance中赠送游戏币数量）
     * </pre>
     *
     * <code>optional int64 gen_balance = 5;</code>
     * @return The genBalance.
     */
    long getGenBalance();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasRespHeader}
   */
  public static final class MidasRespHeader extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasRespHeader)
      MidasRespHeaderOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasRespHeader.newBuilder() to construct.
    private MidasRespHeader(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasRespHeader() {
      errCode_ = "";
      msg_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasRespHeader();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasRespHeader(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ret_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              errCode_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              msg_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              balance_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              genBalance_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRespHeader_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRespHeader_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasRespHeader.class, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder.class);
    }

    private int bitField0_;
    public static final int RET_FIELD_NUMBER = 1;
    private int ret_;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }

    public static final int ERR_CODE_FIELD_NUMBER = 2;
    private volatile java.lang.Object errCode_;
    /**
     * <code>optional string err_code = 2;</code>
     * @return Whether the errCode field is set.
     */
    @java.lang.Override
    public boolean hasErrCode() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string err_code = 2;</code>
     * @return The errCode.
     */
    @java.lang.Override
    public java.lang.String getErrCode() {
      java.lang.Object ref = errCode_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          errCode_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string err_code = 2;</code>
     * @return The bytes for errCode.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getErrCodeBytes() {
      java.lang.Object ref = errCode_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MSG_FIELD_NUMBER = 3;
    private volatile java.lang.Object msg_;
    /**
     * <code>optional string msg = 3;</code>
     * @return Whether the msg field is set.
     */
    @java.lang.Override
    public boolean hasMsg() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string msg = 3;</code>
     * @return The msg.
     */
    @java.lang.Override
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          msg_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string msg = 3;</code>
     * @return The bytes for msg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BALANCE_FIELD_NUMBER = 4;
    private long balance_;
    /**
     * <pre>
     * 游戏币数量（包含赠送游戏币）
     * </pre>
     *
     * <code>optional int64 balance = 4;</code>
     * @return Whether the balance field is set.
     */
    @java.lang.Override
    public boolean hasBalance() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 游戏币数量（包含赠送游戏币）
     * </pre>
     *
     * <code>optional int64 balance = 4;</code>
     * @return The balance.
     */
    @java.lang.Override
    public long getBalance() {
      return balance_;
    }

    public static final int GEN_BALANCE_FIELD_NUMBER = 5;
    private long genBalance_;
    /**
     * <pre>
     * 赠送游戏币数量（balance中赠送游戏币数量）
     * </pre>
     *
     * <code>optional int64 gen_balance = 5;</code>
     * @return Whether the genBalance field is set.
     */
    @java.lang.Override
    public boolean hasGenBalance() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 赠送游戏币数量（balance中赠送游戏币数量）
     * </pre>
     *
     * <code>optional int64 gen_balance = 5;</code>
     * @return The genBalance.
     */
    @java.lang.Override
    public long getGenBalance() {
      return genBalance_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, ret_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errCode_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, msg_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, balance_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, genBalance_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, ret_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errCode_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, msg_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, balance_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, genBalance_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasRespHeader)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasRespHeader other = (com.yorha.proto.SsMidasAgent.MidasRespHeader) obj;

      if (hasRet() != other.hasRet()) return false;
      if (hasRet()) {
        if (getRet()
            != other.getRet()) return false;
      }
      if (hasErrCode() != other.hasErrCode()) return false;
      if (hasErrCode()) {
        if (!getErrCode()
            .equals(other.getErrCode())) return false;
      }
      if (hasMsg() != other.hasMsg()) return false;
      if (hasMsg()) {
        if (!getMsg()
            .equals(other.getMsg())) return false;
      }
      if (hasBalance() != other.hasBalance()) return false;
      if (hasBalance()) {
        if (getBalance()
            != other.getBalance()) return false;
      }
      if (hasGenBalance() != other.hasGenBalance()) return false;
      if (hasGenBalance()) {
        if (getGenBalance()
            != other.getGenBalance()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRet()) {
        hash = (37 * hash) + RET_FIELD_NUMBER;
        hash = (53 * hash) + getRet();
      }
      if (hasErrCode()) {
        hash = (37 * hash) + ERR_CODE_FIELD_NUMBER;
        hash = (53 * hash) + getErrCode().hashCode();
      }
      if (hasMsg()) {
        hash = (37 * hash) + MSG_FIELD_NUMBER;
        hash = (53 * hash) + getMsg().hashCode();
      }
      if (hasBalance()) {
        hash = (37 * hash) + BALANCE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBalance());
      }
      if (hasGenBalance()) {
        hash = (37 * hash) + GEN_BALANCE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getGenBalance());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRespHeader parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasRespHeader prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasRespHeader}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasRespHeader)
        com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRespHeader_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRespHeader_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasRespHeader.class, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasRespHeader.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ret_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        errCode_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        msg_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        balance_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        genBalance_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRespHeader_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRespHeader getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRespHeader build() {
        com.yorha.proto.SsMidasAgent.MidasRespHeader result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRespHeader buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasRespHeader result = new com.yorha.proto.SsMidasAgent.MidasRespHeader(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ret_ = ret_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.errCode_ = errCode_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.msg_ = msg_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.balance_ = balance_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.genBalance_ = genBalance_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasRespHeader) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasRespHeader)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasRespHeader other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance()) return this;
        if (other.hasRet()) {
          setRet(other.getRet());
        }
        if (other.hasErrCode()) {
          bitField0_ |= 0x00000002;
          errCode_ = other.errCode_;
          onChanged();
        }
        if (other.hasMsg()) {
          bitField0_ |= 0x00000004;
          msg_ = other.msg_;
          onChanged();
        }
        if (other.hasBalance()) {
          setBalance(other.getBalance());
        }
        if (other.hasGenBalance()) {
          setGenBalance(other.getGenBalance());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasRespHeader parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasRespHeader) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int ret_ ;
      /**
       * <code>optional int32 ret = 1;</code>
       * @return Whether the ret field is set.
       */
      @java.lang.Override
      public boolean hasRet() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @return The ret.
       */
      @java.lang.Override
      public int getRet() {
        return ret_;
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @param value The ret to set.
       * @return This builder for chaining.
       */
      public Builder setRet(int value) {
        bitField0_ |= 0x00000001;
        ret_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRet() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ret_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object errCode_ = "";
      /**
       * <code>optional string err_code = 2;</code>
       * @return Whether the errCode field is set.
       */
      public boolean hasErrCode() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string err_code = 2;</code>
       * @return The errCode.
       */
      public java.lang.String getErrCode() {
        java.lang.Object ref = errCode_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            errCode_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string err_code = 2;</code>
       * @return The bytes for errCode.
       */
      public com.google.protobuf.ByteString
          getErrCodeBytes() {
        java.lang.Object ref = errCode_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          errCode_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string err_code = 2;</code>
       * @param value The errCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrCode(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        errCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string err_code = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrCode() {
        bitField0_ = (bitField0_ & ~0x00000002);
        errCode_ = getDefaultInstance().getErrCode();
        onChanged();
        return this;
      }
      /**
       * <code>optional string err_code = 2;</code>
       * @param value The bytes for errCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrCodeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        errCode_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object msg_ = "";
      /**
       * <code>optional string msg = 3;</code>
       * @return Whether the msg field is set.
       */
      public boolean hasMsg() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string msg = 3;</code>
       * @return The msg.
       */
      public java.lang.String getMsg() {
        java.lang.Object ref = msg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            msg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string msg = 3;</code>
       * @return The bytes for msg.
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
        java.lang.Object ref = msg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string msg = 3;</code>
       * @param value The msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        msg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string msg = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsg() {
        bitField0_ = (bitField0_ & ~0x00000004);
        msg_ = getDefaultInstance().getMsg();
        onChanged();
        return this;
      }
      /**
       * <code>optional string msg = 3;</code>
       * @param value The bytes for msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        msg_ = value;
        onChanged();
        return this;
      }

      private long balance_ ;
      /**
       * <pre>
       * 游戏币数量（包含赠送游戏币）
       * </pre>
       *
       * <code>optional int64 balance = 4;</code>
       * @return Whether the balance field is set.
       */
      @java.lang.Override
      public boolean hasBalance() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 游戏币数量（包含赠送游戏币）
       * </pre>
       *
       * <code>optional int64 balance = 4;</code>
       * @return The balance.
       */
      @java.lang.Override
      public long getBalance() {
        return balance_;
      }
      /**
       * <pre>
       * 游戏币数量（包含赠送游戏币）
       * </pre>
       *
       * <code>optional int64 balance = 4;</code>
       * @param value The balance to set.
       * @return This builder for chaining.
       */
      public Builder setBalance(long value) {
        bitField0_ |= 0x00000008;
        balance_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 游戏币数量（包含赠送游戏币）
       * </pre>
       *
       * <code>optional int64 balance = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBalance() {
        bitField0_ = (bitField0_ & ~0x00000008);
        balance_ = 0L;
        onChanged();
        return this;
      }

      private long genBalance_ ;
      /**
       * <pre>
       * 赠送游戏币数量（balance中赠送游戏币数量）
       * </pre>
       *
       * <code>optional int64 gen_balance = 5;</code>
       * @return Whether the genBalance field is set.
       */
      @java.lang.Override
      public boolean hasGenBalance() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 赠送游戏币数量（balance中赠送游戏币数量）
       * </pre>
       *
       * <code>optional int64 gen_balance = 5;</code>
       * @return The genBalance.
       */
      @java.lang.Override
      public long getGenBalance() {
        return genBalance_;
      }
      /**
       * <pre>
       * 赠送游戏币数量（balance中赠送游戏币数量）
       * </pre>
       *
       * <code>optional int64 gen_balance = 5;</code>
       * @param value The genBalance to set.
       * @return This builder for chaining.
       */
      public Builder setGenBalance(long value) {
        bitField0_ |= 0x00000010;
        genBalance_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 赠送游戏币数量（balance中赠送游戏币数量）
       * </pre>
       *
       * <code>optional int64 gen_balance = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearGenBalance() {
        bitField0_ = (bitField0_ & ~0x00000010);
        genBalance_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasRespHeader)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasRespHeader)
    private static final com.yorha.proto.SsMidasAgent.MidasRespHeader DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasRespHeader();
    }

    public static com.yorha.proto.SsMidasAgent.MidasRespHeader getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasRespHeader>
        PARSER = new com.google.protobuf.AbstractParser<MidasRespHeader>() {
      @java.lang.Override
      public MidasRespHeader parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasRespHeader(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasRespHeader> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasRespHeader> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeader getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasReqHeaderOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasReqHeader)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    int getZoneId();

    /**
     * <code>optional string pf = 4;</code>
     * @return Whether the pf field is set.
     */
    boolean hasPf();
    /**
     * <code>optional string pf = 4;</code>
     * @return The pf.
     */
    java.lang.String getPf();
    /**
     * <code>optional string pf = 4;</code>
     * @return The bytes for pf.
     */
    com.google.protobuf.ByteString
        getPfBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasReqHeader}
   */
  public static final class MidasReqHeader extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasReqHeader)
      MidasReqHeaderOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasReqHeader.newBuilder() to construct.
    private MidasReqHeader(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasReqHeader() {
      openId_ = "";
      pf_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasReqHeader();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasReqHeader(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              openId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              playerId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              zoneId_ = input.readInt32();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              pf_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasReqHeader_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasReqHeader_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasReqHeader.class, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder.class);
    }

    private int bitField0_;
    public static final int OPENID_FIELD_NUMBER = 1;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLAYERID_FIELD_NUMBER = 2;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 3;
    private int zoneId_;
    /**
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    public static final int PF_FIELD_NUMBER = 4;
    private volatile java.lang.Object pf_;
    /**
     * <code>optional string pf = 4;</code>
     * @return Whether the pf field is set.
     */
    @java.lang.Override
    public boolean hasPf() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string pf = 4;</code>
     * @return The pf.
     */
    @java.lang.Override
    public java.lang.String getPf() {
      java.lang.Object ref = pf_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pf_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string pf = 4;</code>
     * @return The bytes for pf.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPfBytes() {
      java.lang.Object ref = pf_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pf_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, playerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, zoneId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, pf_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, playerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, zoneId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, pf_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasReqHeader)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasReqHeader other = (com.yorha.proto.SsMidasAgent.MidasReqHeader) obj;

      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (hasPf() != other.hasPf()) return false;
      if (hasPf()) {
        if (!getPf()
            .equals(other.getPf())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      if (hasPf()) {
        hash = (37 * hash) + PF_FIELD_NUMBER;
        hash = (53 * hash) + getPf().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasReqHeader parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasReqHeader prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasReqHeader}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasReqHeader)
        com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasReqHeader_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasReqHeader_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasReqHeader.class, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasReqHeader.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        pf_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasReqHeader_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasReqHeader getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasReqHeader build() {
        com.yorha.proto.SsMidasAgent.MidasReqHeader result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasReqHeader buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasReqHeader result = new com.yorha.proto.SsMidasAgent.MidasReqHeader(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.pf_ = pf_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasReqHeader) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasReqHeader)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasReqHeader other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance()) return this;
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000001;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        if (other.hasPf()) {
          bitField0_ |= 0x00000008;
          pf_ = other.pf_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasReqHeader parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasReqHeader) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 1;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000002;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <code>optional int32 zoneId = 3;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 zoneId = 3;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <code>optional int32 zoneId = 3;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000004;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        zoneId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object pf_ = "";
      /**
       * <code>optional string pf = 4;</code>
       * @return Whether the pf field is set.
       */
      public boolean hasPf() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string pf = 4;</code>
       * @return The pf.
       */
      public java.lang.String getPf() {
        java.lang.Object ref = pf_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            pf_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string pf = 4;</code>
       * @return The bytes for pf.
       */
      public com.google.protobuf.ByteString
          getPfBytes() {
        java.lang.Object ref = pf_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          pf_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string pf = 4;</code>
       * @param value The pf to set.
       * @return This builder for chaining.
       */
      public Builder setPf(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        pf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string pf = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPf() {
        bitField0_ = (bitField0_ & ~0x00000008);
        pf_ = getDefaultInstance().getPf();
        onChanged();
        return this;
      }
      /**
       * <code>optional string pf = 4;</code>
       * @param value The bytes for pf to set.
       * @return This builder for chaining.
       */
      public Builder setPfBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        pf_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasReqHeader)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasReqHeader)
    private static final com.yorha.proto.SsMidasAgent.MidasReqHeader DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasReqHeader();
    }

    public static com.yorha.proto.SsMidasAgent.MidasReqHeader getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasReqHeader>
        PARSER = new com.google.protobuf.AbstractParser<MidasReqHeader>() {
      @java.lang.Override
      public MidasReqHeader parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasReqHeader(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasReqHeader> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasReqHeader> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeader getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasQueryAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasQueryAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    boolean hasHeader();
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return The header.
     */
    com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader();
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     */
    com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasQueryAsk}
   */
  public static final class MidasQueryAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasQueryAsk)
      MidasQueryAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasQueryAsk.newBuilder() to construct.
    private MidasQueryAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasQueryAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasQueryAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasQueryAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.yorha.proto.SsMidasAgent.MidasReqHeader.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasQueryAsk.class, com.yorha.proto.SsMidasAgent.MidasQueryAsk.Builder.class);
    }

    private int bitField0_;
    public static final int HEADER_FIELD_NUMBER = 1;
    private com.yorha.proto.SsMidasAgent.MidasReqHeader header_;
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    @java.lang.Override
    public boolean hasHeader() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return The header.
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getHeader());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasQueryAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasQueryAsk other = (com.yorha.proto.SsMidasAgent.MidasQueryAsk) obj;

      if (hasHeader() != other.hasHeader()) return false;
      if (hasHeader()) {
        if (!getHeader()
            .equals(other.getHeader())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasQueryAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasQueryAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasQueryAsk)
        com.yorha.proto.SsMidasAgent.MidasQueryAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasQueryAsk.class, com.yorha.proto.SsMidasAgent.MidasQueryAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasQueryAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeaderFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasQueryAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasQueryAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasQueryAsk build() {
        com.yorha.proto.SsMidasAgent.MidasQueryAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasQueryAsk buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasQueryAsk result = new com.yorha.proto.SsMidasAgent.MidasQueryAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasQueryAsk) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasQueryAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasQueryAsk other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasQueryAsk.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasQueryAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasQueryAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsMidasAgent.MidasReqHeader header_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder> headerBuilder_;
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       * @return Whether the header field is set.
       */
      public boolean hasHeader() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       * @return The header.
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder setHeader(com.yorha.proto.SsMidasAgent.MidasReqHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder setHeader(
          com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder mergeHeader(com.yorha.proto.SsMidasAgent.MidasReqHeader value) {
        if (headerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              header_ != null &&
              header_ != com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance()) {
            header_ =
              com.yorha.proto.SsMidasAgent.MidasReqHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder getHeaderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasQueryAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasQueryAsk)
    private static final com.yorha.proto.SsMidasAgent.MidasQueryAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasQueryAsk();
    }

    public static com.yorha.proto.SsMidasAgent.MidasQueryAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasQueryAsk>
        PARSER = new com.google.protobuf.AbstractParser<MidasQueryAsk>() {
      @java.lang.Override
      public MidasQueryAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasQueryAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasQueryAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasQueryAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasQueryAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasQueryAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasQueryAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    boolean hasHeader();
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return The header.
     */
    com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader();
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     */
    com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     * 是否满足历史首次充值，1：满足，0：不满足
     * </pre>
     *
     * <code>optional int32 first_save = 4;</code>
     * @return Whether the firstSave field is set.
     */
    boolean hasFirstSave();
    /**
     * <pre>
     * 是否满足历史首次充值，1：满足，0：不满足
     * </pre>
     *
     * <code>optional int32 first_save = 4;</code>
     * @return The firstSave.
     */
    int getFirstSave();

    /**
     * <pre>
     * 累计充值金额的游戏币数量.
     * </pre>
     *
     * <code>optional int64 save_amt = 5;</code>
     * @return Whether the saveAmt field is set.
     */
    boolean hasSaveAmt();
    /**
     * <pre>
     * 累计充值金额的游戏币数量.
     * </pre>
     *
     * <code>optional int64 save_amt = 5;</code>
     * @return The saveAmt.
     */
    long getSaveAmt();

    /**
     * <pre>
     * 历史总游戏币数量.
     * </pre>
     *
     * <code>optional int64 save_sum = 6;</code>
     * @return Whether the saveSum field is set.
     */
    boolean hasSaveSum();
    /**
     * <pre>
     * 历史总游戏币数量.
     * </pre>
     *
     * <code>optional int64 save_sum = 6;</code>
     * @return The saveSum.
     */
    long getSaveSum();

    /**
     * <pre>
     * 历史总消费游戏币数量.
     * </pre>
     *
     * <code>optional int64 cost_sum = 7;</code>
     * @return Whether the costSum field is set.
     */
    boolean hasCostSum();
    /**
     * <pre>
     * 历史总消费游戏币数量.
     * </pre>
     *
     * <code>optional int64 cost_sum = 7;</code>
     * @return The costSum.
     */
    long getCostSum();

    /**
     * <pre>
     * 历史累计收到赠送数量
     * </pre>
     *
     * <code>optional int64 present_sum = 8;</code>
     * @return Whether the presentSum field is set.
     */
    boolean hasPresentSum();
    /**
     * <pre>
     * 历史累计收到赠送数量
     * </pre>
     *
     * <code>optional int64 present_sum = 8;</code>
     * @return The presentSum.
     */
    long getPresentSum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasQueryAns}
   */
  public static final class MidasQueryAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasQueryAns)
      MidasQueryAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasQueryAns.newBuilder() to construct.
    private MidasQueryAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasQueryAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasQueryAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasQueryAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.yorha.proto.SsMidasAgent.MidasRespHeader.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000002;
              firstSave_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000004;
              saveAmt_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000008;
              saveSum_ = input.readInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000010;
              costSum_ = input.readInt64();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000020;
              presentSum_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasQueryAns.class, com.yorha.proto.SsMidasAgent.MidasQueryAns.Builder.class);
    }

    private int bitField0_;
    public static final int HEADER_FIELD_NUMBER = 1;
    private com.yorha.proto.SsMidasAgent.MidasRespHeader header_;
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    @java.lang.Override
    public boolean hasHeader() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return The header.
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
    }

    public static final int FIRST_SAVE_FIELD_NUMBER = 4;
    private int firstSave_;
    /**
     * <pre>
     * 是否满足历史首次充值，1：满足，0：不满足
     * </pre>
     *
     * <code>optional int32 first_save = 4;</code>
     * @return Whether the firstSave field is set.
     */
    @java.lang.Override
    public boolean hasFirstSave() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 是否满足历史首次充值，1：满足，0：不满足
     * </pre>
     *
     * <code>optional int32 first_save = 4;</code>
     * @return The firstSave.
     */
    @java.lang.Override
    public int getFirstSave() {
      return firstSave_;
    }

    public static final int SAVE_AMT_FIELD_NUMBER = 5;
    private long saveAmt_;
    /**
     * <pre>
     * 累计充值金额的游戏币数量.
     * </pre>
     *
     * <code>optional int64 save_amt = 5;</code>
     * @return Whether the saveAmt field is set.
     */
    @java.lang.Override
    public boolean hasSaveAmt() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 累计充值金额的游戏币数量.
     * </pre>
     *
     * <code>optional int64 save_amt = 5;</code>
     * @return The saveAmt.
     */
    @java.lang.Override
    public long getSaveAmt() {
      return saveAmt_;
    }

    public static final int SAVE_SUM_FIELD_NUMBER = 6;
    private long saveSum_;
    /**
     * <pre>
     * 历史总游戏币数量.
     * </pre>
     *
     * <code>optional int64 save_sum = 6;</code>
     * @return Whether the saveSum field is set.
     */
    @java.lang.Override
    public boolean hasSaveSum() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 历史总游戏币数量.
     * </pre>
     *
     * <code>optional int64 save_sum = 6;</code>
     * @return The saveSum.
     */
    @java.lang.Override
    public long getSaveSum() {
      return saveSum_;
    }

    public static final int COST_SUM_FIELD_NUMBER = 7;
    private long costSum_;
    /**
     * <pre>
     * 历史总消费游戏币数量.
     * </pre>
     *
     * <code>optional int64 cost_sum = 7;</code>
     * @return Whether the costSum field is set.
     */
    @java.lang.Override
    public boolean hasCostSum() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 历史总消费游戏币数量.
     * </pre>
     *
     * <code>optional int64 cost_sum = 7;</code>
     * @return The costSum.
     */
    @java.lang.Override
    public long getCostSum() {
      return costSum_;
    }

    public static final int PRESENT_SUM_FIELD_NUMBER = 8;
    private long presentSum_;
    /**
     * <pre>
     * 历史累计收到赠送数量
     * </pre>
     *
     * <code>optional int64 present_sum = 8;</code>
     * @return Whether the presentSum field is set.
     */
    @java.lang.Override
    public boolean hasPresentSum() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 历史累计收到赠送数量
     * </pre>
     *
     * <code>optional int64 present_sum = 8;</code>
     * @return The presentSum.
     */
    @java.lang.Override
    public long getPresentSum() {
      return presentSum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(4, firstSave_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(5, saveAmt_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(6, saveSum_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(7, costSum_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt64(8, presentSum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, firstSave_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, saveAmt_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, saveSum_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, costSum_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, presentSum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasQueryAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasQueryAns other = (com.yorha.proto.SsMidasAgent.MidasQueryAns) obj;

      if (hasHeader() != other.hasHeader()) return false;
      if (hasHeader()) {
        if (!getHeader()
            .equals(other.getHeader())) return false;
      }
      if (hasFirstSave() != other.hasFirstSave()) return false;
      if (hasFirstSave()) {
        if (getFirstSave()
            != other.getFirstSave()) return false;
      }
      if (hasSaveAmt() != other.hasSaveAmt()) return false;
      if (hasSaveAmt()) {
        if (getSaveAmt()
            != other.getSaveAmt()) return false;
      }
      if (hasSaveSum() != other.hasSaveSum()) return false;
      if (hasSaveSum()) {
        if (getSaveSum()
            != other.getSaveSum()) return false;
      }
      if (hasCostSum() != other.hasCostSum()) return false;
      if (hasCostSum()) {
        if (getCostSum()
            != other.getCostSum()) return false;
      }
      if (hasPresentSum() != other.hasPresentSum()) return false;
      if (hasPresentSum()) {
        if (getPresentSum()
            != other.getPresentSum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      if (hasFirstSave()) {
        hash = (37 * hash) + FIRST_SAVE_FIELD_NUMBER;
        hash = (53 * hash) + getFirstSave();
      }
      if (hasSaveAmt()) {
        hash = (37 * hash) + SAVE_AMT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSaveAmt());
      }
      if (hasSaveSum()) {
        hash = (37 * hash) + SAVE_SUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSaveSum());
      }
      if (hasCostSum()) {
        hash = (37 * hash) + COST_SUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCostSum());
      }
      if (hasPresentSum()) {
        hash = (37 * hash) + PRESENT_SUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPresentSum());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasQueryAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasQueryAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasQueryAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasQueryAns)
        com.yorha.proto.SsMidasAgent.MidasQueryAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasQueryAns.class, com.yorha.proto.SsMidasAgent.MidasQueryAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasQueryAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeaderFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        firstSave_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        saveAmt_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        saveSum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        costSum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        presentSum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasQueryAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasQueryAns getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasQueryAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasQueryAns build() {
        com.yorha.proto.SsMidasAgent.MidasQueryAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasQueryAns buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasQueryAns result = new com.yorha.proto.SsMidasAgent.MidasQueryAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.firstSave_ = firstSave_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.saveAmt_ = saveAmt_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.saveSum_ = saveSum_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.costSum_ = costSum_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.presentSum_ = presentSum_;
          to_bitField0_ |= 0x00000020;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasQueryAns) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasQueryAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasQueryAns other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasQueryAns.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.hasFirstSave()) {
          setFirstSave(other.getFirstSave());
        }
        if (other.hasSaveAmt()) {
          setSaveAmt(other.getSaveAmt());
        }
        if (other.hasSaveSum()) {
          setSaveSum(other.getSaveSum());
        }
        if (other.hasCostSum()) {
          setCostSum(other.getCostSum());
        }
        if (other.hasPresentSum()) {
          setPresentSum(other.getPresentSum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasQueryAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasQueryAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsMidasAgent.MidasRespHeader header_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder> headerBuilder_;
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       * @return Whether the header field is set.
       */
      public boolean hasHeader() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       * @return The header.
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder setHeader(com.yorha.proto.SsMidasAgent.MidasRespHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder setHeader(
          com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder mergeHeader(com.yorha.proto.SsMidasAgent.MidasRespHeader value) {
        if (headerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              header_ != null &&
              header_ != com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance()) {
            header_ =
              com.yorha.proto.SsMidasAgent.MidasRespHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder getHeaderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private int firstSave_ ;
      /**
       * <pre>
       * 是否满足历史首次充值，1：满足，0：不满足
       * </pre>
       *
       * <code>optional int32 first_save = 4;</code>
       * @return Whether the firstSave field is set.
       */
      @java.lang.Override
      public boolean hasFirstSave() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 是否满足历史首次充值，1：满足，0：不满足
       * </pre>
       *
       * <code>optional int32 first_save = 4;</code>
       * @return The firstSave.
       */
      @java.lang.Override
      public int getFirstSave() {
        return firstSave_;
      }
      /**
       * <pre>
       * 是否满足历史首次充值，1：满足，0：不满足
       * </pre>
       *
       * <code>optional int32 first_save = 4;</code>
       * @param value The firstSave to set.
       * @return This builder for chaining.
       */
      public Builder setFirstSave(int value) {
        bitField0_ |= 0x00000002;
        firstSave_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否满足历史首次充值，1：满足，0：不满足
       * </pre>
       *
       * <code>optional int32 first_save = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearFirstSave() {
        bitField0_ = (bitField0_ & ~0x00000002);
        firstSave_ = 0;
        onChanged();
        return this;
      }

      private long saveAmt_ ;
      /**
       * <pre>
       * 累计充值金额的游戏币数量.
       * </pre>
       *
       * <code>optional int64 save_amt = 5;</code>
       * @return Whether the saveAmt field is set.
       */
      @java.lang.Override
      public boolean hasSaveAmt() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 累计充值金额的游戏币数量.
       * </pre>
       *
       * <code>optional int64 save_amt = 5;</code>
       * @return The saveAmt.
       */
      @java.lang.Override
      public long getSaveAmt() {
        return saveAmt_;
      }
      /**
       * <pre>
       * 累计充值金额的游戏币数量.
       * </pre>
       *
       * <code>optional int64 save_amt = 5;</code>
       * @param value The saveAmt to set.
       * @return This builder for chaining.
       */
      public Builder setSaveAmt(long value) {
        bitField0_ |= 0x00000004;
        saveAmt_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 累计充值金额的游戏币数量.
       * </pre>
       *
       * <code>optional int64 save_amt = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSaveAmt() {
        bitField0_ = (bitField0_ & ~0x00000004);
        saveAmt_ = 0L;
        onChanged();
        return this;
      }

      private long saveSum_ ;
      /**
       * <pre>
       * 历史总游戏币数量.
       * </pre>
       *
       * <code>optional int64 save_sum = 6;</code>
       * @return Whether the saveSum field is set.
       */
      @java.lang.Override
      public boolean hasSaveSum() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 历史总游戏币数量.
       * </pre>
       *
       * <code>optional int64 save_sum = 6;</code>
       * @return The saveSum.
       */
      @java.lang.Override
      public long getSaveSum() {
        return saveSum_;
      }
      /**
       * <pre>
       * 历史总游戏币数量.
       * </pre>
       *
       * <code>optional int64 save_sum = 6;</code>
       * @param value The saveSum to set.
       * @return This builder for chaining.
       */
      public Builder setSaveSum(long value) {
        bitField0_ |= 0x00000008;
        saveSum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 历史总游戏币数量.
       * </pre>
       *
       * <code>optional int64 save_sum = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearSaveSum() {
        bitField0_ = (bitField0_ & ~0x00000008);
        saveSum_ = 0L;
        onChanged();
        return this;
      }

      private long costSum_ ;
      /**
       * <pre>
       * 历史总消费游戏币数量.
       * </pre>
       *
       * <code>optional int64 cost_sum = 7;</code>
       * @return Whether the costSum field is set.
       */
      @java.lang.Override
      public boolean hasCostSum() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 历史总消费游戏币数量.
       * </pre>
       *
       * <code>optional int64 cost_sum = 7;</code>
       * @return The costSum.
       */
      @java.lang.Override
      public long getCostSum() {
        return costSum_;
      }
      /**
       * <pre>
       * 历史总消费游戏币数量.
       * </pre>
       *
       * <code>optional int64 cost_sum = 7;</code>
       * @param value The costSum to set.
       * @return This builder for chaining.
       */
      public Builder setCostSum(long value) {
        bitField0_ |= 0x00000010;
        costSum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 历史总消费游戏币数量.
       * </pre>
       *
       * <code>optional int64 cost_sum = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearCostSum() {
        bitField0_ = (bitField0_ & ~0x00000010);
        costSum_ = 0L;
        onChanged();
        return this;
      }

      private long presentSum_ ;
      /**
       * <pre>
       * 历史累计收到赠送数量
       * </pre>
       *
       * <code>optional int64 present_sum = 8;</code>
       * @return Whether the presentSum field is set.
       */
      @java.lang.Override
      public boolean hasPresentSum() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 历史累计收到赠送数量
       * </pre>
       *
       * <code>optional int64 present_sum = 8;</code>
       * @return The presentSum.
       */
      @java.lang.Override
      public long getPresentSum() {
        return presentSum_;
      }
      /**
       * <pre>
       * 历史累计收到赠送数量
       * </pre>
       *
       * <code>optional int64 present_sum = 8;</code>
       * @param value The presentSum to set.
       * @return This builder for chaining.
       */
      public Builder setPresentSum(long value) {
        bitField0_ |= 0x00000020;
        presentSum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 历史累计收到赠送数量
       * </pre>
       *
       * <code>optional int64 present_sum = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearPresentSum() {
        bitField0_ = (bitField0_ & ~0x00000020);
        presentSum_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasQueryAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasQueryAns)
    private static final com.yorha.proto.SsMidasAgent.MidasQueryAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasQueryAns();
    }

    public static com.yorha.proto.SsMidasAgent.MidasQueryAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasQueryAns>
        PARSER = new com.google.protobuf.AbstractParser<MidasQueryAns>() {
      @java.lang.Override
      public MidasQueryAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasQueryAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasQueryAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasQueryAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasQueryAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasConsumeAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasConsumeAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    boolean hasHeader();
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return The header.
     */
    com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader();
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     */
    com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <code>optional string billno = 2;</code>
     * @return Whether the billno field is set.
     */
    boolean hasBillno();
    /**
     * <code>optional string billno = 2;</code>
     * @return The billno.
     */
    java.lang.String getBillno();
    /**
     * <code>optional string billno = 2;</code>
     * @return The bytes for billno.
     */
    com.google.protobuf.ByteString
        getBillnoBytes();

    /**
     * <code>optional int64 amount = 3;</code>
     * @return Whether the amount field is set.
     */
    boolean hasAmount();
    /**
     * <code>optional int64 amount = 3;</code>
     * @return The amount.
     */
    long getAmount();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasConsumeAsk}
   */
  public static final class MidasConsumeAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasConsumeAsk)
      MidasConsumeAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasConsumeAsk.newBuilder() to construct.
    private MidasConsumeAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasConsumeAsk() {
      billno_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasConsumeAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasConsumeAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.yorha.proto.SsMidasAgent.MidasReqHeader.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              billno_ = bs;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              amount_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasConsumeAsk.class, com.yorha.proto.SsMidasAgent.MidasConsumeAsk.Builder.class);
    }

    private int bitField0_;
    public static final int HEADER_FIELD_NUMBER = 1;
    private com.yorha.proto.SsMidasAgent.MidasReqHeader header_;
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    @java.lang.Override
    public boolean hasHeader() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return The header.
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
    }

    public static final int BILLNO_FIELD_NUMBER = 2;
    private volatile java.lang.Object billno_;
    /**
     * <code>optional string billno = 2;</code>
     * @return Whether the billno field is set.
     */
    @java.lang.Override
    public boolean hasBillno() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string billno = 2;</code>
     * @return The billno.
     */
    @java.lang.Override
    public java.lang.String getBillno() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          billno_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string billno = 2;</code>
     * @return The bytes for billno.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBillnoBytes() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        billno_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int AMOUNT_FIELD_NUMBER = 3;
    private long amount_;
    /**
     * <code>optional int64 amount = 3;</code>
     * @return Whether the amount field is set.
     */
    @java.lang.Override
    public boolean hasAmount() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 amount = 3;</code>
     * @return The amount.
     */
    @java.lang.Override
    public long getAmount() {
      return amount_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, billno_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, amount_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, billno_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, amount_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasConsumeAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasConsumeAsk other = (com.yorha.proto.SsMidasAgent.MidasConsumeAsk) obj;

      if (hasHeader() != other.hasHeader()) return false;
      if (hasHeader()) {
        if (!getHeader()
            .equals(other.getHeader())) return false;
      }
      if (hasBillno() != other.hasBillno()) return false;
      if (hasBillno()) {
        if (!getBillno()
            .equals(other.getBillno())) return false;
      }
      if (hasAmount() != other.hasAmount()) return false;
      if (hasAmount()) {
        if (getAmount()
            != other.getAmount()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      if (hasBillno()) {
        hash = (37 * hash) + BILLNO_FIELD_NUMBER;
        hash = (53 * hash) + getBillno().hashCode();
      }
      if (hasAmount()) {
        hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getAmount());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasConsumeAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasConsumeAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasConsumeAsk)
        com.yorha.proto.SsMidasAgent.MidasConsumeAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasConsumeAsk.class, com.yorha.proto.SsMidasAgent.MidasConsumeAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasConsumeAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeaderFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        billno_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        amount_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasConsumeAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasConsumeAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasConsumeAsk build() {
        com.yorha.proto.SsMidasAgent.MidasConsumeAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasConsumeAsk buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasConsumeAsk result = new com.yorha.proto.SsMidasAgent.MidasConsumeAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.billno_ = billno_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.amount_ = amount_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasConsumeAsk) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasConsumeAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasConsumeAsk other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasConsumeAsk.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.hasBillno()) {
          bitField0_ |= 0x00000002;
          billno_ = other.billno_;
          onChanged();
        }
        if (other.hasAmount()) {
          setAmount(other.getAmount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasConsumeAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasConsumeAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsMidasAgent.MidasReqHeader header_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder> headerBuilder_;
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       * @return Whether the header field is set.
       */
      public boolean hasHeader() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       * @return The header.
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder setHeader(com.yorha.proto.SsMidasAgent.MidasReqHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder setHeader(
          com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder mergeHeader(com.yorha.proto.SsMidasAgent.MidasReqHeader value) {
        if (headerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              header_ != null &&
              header_ != com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance()) {
            header_ =
              com.yorha.proto.SsMidasAgent.MidasReqHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder getHeaderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private java.lang.Object billno_ = "";
      /**
       * <code>optional string billno = 2;</code>
       * @return Whether the billno field is set.
       */
      public boolean hasBillno() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return The billno.
       */
      public java.lang.String getBillno() {
        java.lang.Object ref = billno_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            billno_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return The bytes for billno.
       */
      public com.google.protobuf.ByteString
          getBillnoBytes() {
        java.lang.Object ref = billno_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          billno_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string billno = 2;</code>
       * @param value The billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillno(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBillno() {
        bitField0_ = (bitField0_ & ~0x00000002);
        billno_ = getDefaultInstance().getBillno();
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 2;</code>
       * @param value The bytes for billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillnoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }

      private long amount_ ;
      /**
       * <code>optional int64 amount = 3;</code>
       * @return Whether the amount field is set.
       */
      @java.lang.Override
      public boolean hasAmount() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 amount = 3;</code>
       * @return The amount.
       */
      @java.lang.Override
      public long getAmount() {
        return amount_;
      }
      /**
       * <code>optional int64 amount = 3;</code>
       * @param value The amount to set.
       * @return This builder for chaining.
       */
      public Builder setAmount(long value) {
        bitField0_ |= 0x00000004;
        amount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 amount = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAmount() {
        bitField0_ = (bitField0_ & ~0x00000004);
        amount_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasConsumeAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasConsumeAsk)
    private static final com.yorha.proto.SsMidasAgent.MidasConsumeAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasConsumeAsk();
    }

    public static com.yorha.proto.SsMidasAgent.MidasConsumeAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasConsumeAsk>
        PARSER = new com.google.protobuf.AbstractParser<MidasConsumeAsk>() {
      @java.lang.Override
      public MidasConsumeAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasConsumeAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasConsumeAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasConsumeAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasConsumeAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasConsumeAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasConsumeAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    boolean hasHeader();
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return The header.
     */
    com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader();
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     */
    com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return Whether the billno field is set.
     */
    boolean hasBillno();
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return The billno.
     */
    java.lang.String getBillno();
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return The bytes for billno.
     */
    com.google.protobuf.ByteString
        getBillnoBytes();

    /**
     * <pre>
     * 本次扣的赠送币的数量
     * </pre>
     *
     * <code>optional int64 used_gen_amt = 5;</code>
     * @return Whether the usedGenAmt field is set.
     */
    boolean hasUsedGenAmt();
    /**
     * <pre>
     * 本次扣的赠送币的数量
     * </pre>
     *
     * <code>optional int64 used_gen_amt = 5;</code>
     * @return The usedGenAmt.
     */
    long getUsedGenAmt();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasConsumeAns}
   */
  public static final class MidasConsumeAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasConsumeAns)
      MidasConsumeAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasConsumeAns.newBuilder() to construct.
    private MidasConsumeAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasConsumeAns() {
      billno_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasConsumeAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasConsumeAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.yorha.proto.SsMidasAgent.MidasRespHeader.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              billno_ = bs;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000004;
              usedGenAmt_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasConsumeAns.class, com.yorha.proto.SsMidasAgent.MidasConsumeAns.Builder.class);
    }

    private int bitField0_;
    public static final int HEADER_FIELD_NUMBER = 1;
    private com.yorha.proto.SsMidasAgent.MidasRespHeader header_;
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    @java.lang.Override
    public boolean hasHeader() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return The header.
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
    }

    public static final int BILLNO_FIELD_NUMBER = 4;
    private volatile java.lang.Object billno_;
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return Whether the billno field is set.
     */
    @java.lang.Override
    public boolean hasBillno() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return The billno.
     */
    @java.lang.Override
    public java.lang.String getBillno() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          billno_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return The bytes for billno.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBillnoBytes() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        billno_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USED_GEN_AMT_FIELD_NUMBER = 5;
    private long usedGenAmt_;
    /**
     * <pre>
     * 本次扣的赠送币的数量
     * </pre>
     *
     * <code>optional int64 used_gen_amt = 5;</code>
     * @return Whether the usedGenAmt field is set.
     */
    @java.lang.Override
    public boolean hasUsedGenAmt() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 本次扣的赠送币的数量
     * </pre>
     *
     * <code>optional int64 used_gen_amt = 5;</code>
     * @return The usedGenAmt.
     */
    @java.lang.Override
    public long getUsedGenAmt() {
      return usedGenAmt_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, billno_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(5, usedGenAmt_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, billno_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, usedGenAmt_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasConsumeAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasConsumeAns other = (com.yorha.proto.SsMidasAgent.MidasConsumeAns) obj;

      if (hasHeader() != other.hasHeader()) return false;
      if (hasHeader()) {
        if (!getHeader()
            .equals(other.getHeader())) return false;
      }
      if (hasBillno() != other.hasBillno()) return false;
      if (hasBillno()) {
        if (!getBillno()
            .equals(other.getBillno())) return false;
      }
      if (hasUsedGenAmt() != other.hasUsedGenAmt()) return false;
      if (hasUsedGenAmt()) {
        if (getUsedGenAmt()
            != other.getUsedGenAmt()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      if (hasBillno()) {
        hash = (37 * hash) + BILLNO_FIELD_NUMBER;
        hash = (53 * hash) + getBillno().hashCode();
      }
      if (hasUsedGenAmt()) {
        hash = (37 * hash) + USED_GEN_AMT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUsedGenAmt());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasConsumeAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasConsumeAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasConsumeAns)
        com.yorha.proto.SsMidasAgent.MidasConsumeAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasConsumeAns.class, com.yorha.proto.SsMidasAgent.MidasConsumeAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasConsumeAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeaderFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        billno_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        usedGenAmt_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasConsumeAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasConsumeAns getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasConsumeAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasConsumeAns build() {
        com.yorha.proto.SsMidasAgent.MidasConsumeAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasConsumeAns buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasConsumeAns result = new com.yorha.proto.SsMidasAgent.MidasConsumeAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.billno_ = billno_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.usedGenAmt_ = usedGenAmt_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasConsumeAns) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasConsumeAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasConsumeAns other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasConsumeAns.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.hasBillno()) {
          bitField0_ |= 0x00000002;
          billno_ = other.billno_;
          onChanged();
        }
        if (other.hasUsedGenAmt()) {
          setUsedGenAmt(other.getUsedGenAmt());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasConsumeAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasConsumeAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsMidasAgent.MidasRespHeader header_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder> headerBuilder_;
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       * @return Whether the header field is set.
       */
      public boolean hasHeader() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       * @return The header.
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder setHeader(com.yorha.proto.SsMidasAgent.MidasRespHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder setHeader(
          com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder mergeHeader(com.yorha.proto.SsMidasAgent.MidasRespHeader value) {
        if (headerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              header_ != null &&
              header_ != com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance()) {
            header_ =
              com.yorha.proto.SsMidasAgent.MidasRespHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder getHeaderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private java.lang.Object billno_ = "";
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @return Whether the billno field is set.
       */
      public boolean hasBillno() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @return The billno.
       */
      public java.lang.String getBillno() {
        java.lang.Object ref = billno_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            billno_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @return The bytes for billno.
       */
      public com.google.protobuf.ByteString
          getBillnoBytes() {
        java.lang.Object ref = billno_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          billno_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @param value The billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillno(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBillno() {
        bitField0_ = (bitField0_ & ~0x00000002);
        billno_ = getDefaultInstance().getBillno();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @param value The bytes for billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillnoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }

      private long usedGenAmt_ ;
      /**
       * <pre>
       * 本次扣的赠送币的数量
       * </pre>
       *
       * <code>optional int64 used_gen_amt = 5;</code>
       * @return Whether the usedGenAmt field is set.
       */
      @java.lang.Override
      public boolean hasUsedGenAmt() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 本次扣的赠送币的数量
       * </pre>
       *
       * <code>optional int64 used_gen_amt = 5;</code>
       * @return The usedGenAmt.
       */
      @java.lang.Override
      public long getUsedGenAmt() {
        return usedGenAmt_;
      }
      /**
       * <pre>
       * 本次扣的赠送币的数量
       * </pre>
       *
       * <code>optional int64 used_gen_amt = 5;</code>
       * @param value The usedGenAmt to set.
       * @return This builder for chaining.
       */
      public Builder setUsedGenAmt(long value) {
        bitField0_ |= 0x00000004;
        usedGenAmt_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次扣的赠送币的数量
       * </pre>
       *
       * <code>optional int64 used_gen_amt = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsedGenAmt() {
        bitField0_ = (bitField0_ & ~0x00000004);
        usedGenAmt_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasConsumeAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasConsumeAns)
    private static final com.yorha.proto.SsMidasAgent.MidasConsumeAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasConsumeAns();
    }

    public static com.yorha.proto.SsMidasAgent.MidasConsumeAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasConsumeAns>
        PARSER = new com.google.protobuf.AbstractParser<MidasConsumeAns>() {
      @java.lang.Override
      public MidasConsumeAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasConsumeAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasConsumeAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasConsumeAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasConsumeAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasRollbackConsumeAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasRollbackConsumeAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    boolean hasHeader();
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return The header.
     */
    com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader();
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     */
    com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <code>optional string billno = 2;</code>
     * @return Whether the billno field is set.
     */
    boolean hasBillno();
    /**
     * <code>optional string billno = 2;</code>
     * @return The billno.
     */
    java.lang.String getBillno();
    /**
     * <code>optional string billno = 2;</code>
     * @return The bytes for billno.
     */
    com.google.protobuf.ByteString
        getBillnoBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasRollbackConsumeAsk}
   */
  public static final class MidasRollbackConsumeAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasRollbackConsumeAsk)
      MidasRollbackConsumeAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasRollbackConsumeAsk.newBuilder() to construct.
    private MidasRollbackConsumeAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasRollbackConsumeAsk() {
      billno_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasRollbackConsumeAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasRollbackConsumeAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.yorha.proto.SsMidasAgent.MidasReqHeader.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              billno_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk.class, com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk.Builder.class);
    }

    private int bitField0_;
    public static final int HEADER_FIELD_NUMBER = 1;
    private com.yorha.proto.SsMidasAgent.MidasReqHeader header_;
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    @java.lang.Override
    public boolean hasHeader() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return The header.
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
    }

    public static final int BILLNO_FIELD_NUMBER = 2;
    private volatile java.lang.Object billno_;
    /**
     * <code>optional string billno = 2;</code>
     * @return Whether the billno field is set.
     */
    @java.lang.Override
    public boolean hasBillno() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string billno = 2;</code>
     * @return The billno.
     */
    @java.lang.Override
    public java.lang.String getBillno() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          billno_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string billno = 2;</code>
     * @return The bytes for billno.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBillnoBytes() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        billno_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, billno_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, billno_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk other = (com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk) obj;

      if (hasHeader() != other.hasHeader()) return false;
      if (hasHeader()) {
        if (!getHeader()
            .equals(other.getHeader())) return false;
      }
      if (hasBillno() != other.hasBillno()) return false;
      if (hasBillno()) {
        if (!getBillno()
            .equals(other.getBillno())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      if (hasBillno()) {
        hash = (37 * hash) + BILLNO_FIELD_NUMBER;
        hash = (53 * hash) + getBillno().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasRollbackConsumeAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasRollbackConsumeAsk)
        com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk.class, com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeaderFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        billno_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk build() {
        com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk result = new com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.billno_ = billno_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.hasBillno()) {
          bitField0_ |= 0x00000002;
          billno_ = other.billno_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsMidasAgent.MidasReqHeader header_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder> headerBuilder_;
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       * @return Whether the header field is set.
       */
      public boolean hasHeader() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       * @return The header.
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder setHeader(com.yorha.proto.SsMidasAgent.MidasReqHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder setHeader(
          com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder mergeHeader(com.yorha.proto.SsMidasAgent.MidasReqHeader value) {
        if (headerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              header_ != null &&
              header_ != com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance()) {
            header_ =
              com.yorha.proto.SsMidasAgent.MidasReqHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder getHeaderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private java.lang.Object billno_ = "";
      /**
       * <code>optional string billno = 2;</code>
       * @return Whether the billno field is set.
       */
      public boolean hasBillno() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return The billno.
       */
      public java.lang.String getBillno() {
        java.lang.Object ref = billno_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            billno_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return The bytes for billno.
       */
      public com.google.protobuf.ByteString
          getBillnoBytes() {
        java.lang.Object ref = billno_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          billno_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string billno = 2;</code>
       * @param value The billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillno(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBillno() {
        bitField0_ = (bitField0_ & ~0x00000002);
        billno_ = getDefaultInstance().getBillno();
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 2;</code>
       * @param value The bytes for billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillnoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasRollbackConsumeAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasRollbackConsumeAsk)
    private static final com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk();
    }

    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasRollbackConsumeAsk>
        PARSER = new com.google.protobuf.AbstractParser<MidasRollbackConsumeAsk>() {
      @java.lang.Override
      public MidasRollbackConsumeAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasRollbackConsumeAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasRollbackConsumeAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasRollbackConsumeAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasRollbackConsumeAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasRollbackConsumeAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    boolean hasHeader();
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return The header.
     */
    com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader();
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     */
    com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return Whether the billno field is set.
     */
    boolean hasBillno();
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return The billno.
     */
    java.lang.String getBillno();
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return The bytes for billno.
     */
    com.google.protobuf.ByteString
        getBillnoBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasRollbackConsumeAns}
   */
  public static final class MidasRollbackConsumeAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasRollbackConsumeAns)
      MidasRollbackConsumeAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasRollbackConsumeAns.newBuilder() to construct.
    private MidasRollbackConsumeAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasRollbackConsumeAns() {
      billno_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasRollbackConsumeAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasRollbackConsumeAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.yorha.proto.SsMidasAgent.MidasRespHeader.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              billno_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns.class, com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns.Builder.class);
    }

    private int bitField0_;
    public static final int HEADER_FIELD_NUMBER = 1;
    private com.yorha.proto.SsMidasAgent.MidasRespHeader header_;
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    @java.lang.Override
    public boolean hasHeader() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return The header.
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
    }

    public static final int BILLNO_FIELD_NUMBER = 4;
    private volatile java.lang.Object billno_;
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return Whether the billno field is set.
     */
    @java.lang.Override
    public boolean hasBillno() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return The billno.
     */
    @java.lang.Override
    public java.lang.String getBillno() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          billno_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string billno = 4;</code>
     * @return The bytes for billno.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBillnoBytes() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        billno_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, billno_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, billno_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns other = (com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns) obj;

      if (hasHeader() != other.hasHeader()) return false;
      if (hasHeader()) {
        if (!getHeader()
            .equals(other.getHeader())) return false;
      }
      if (hasBillno() != other.hasBillno()) return false;
      if (hasBillno()) {
        if (!getBillno()
            .equals(other.getBillno())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      if (hasBillno()) {
        hash = (37 * hash) + BILLNO_FIELD_NUMBER;
        hash = (53 * hash) + getBillno().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasRollbackConsumeAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasRollbackConsumeAns)
        com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns.class, com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeaderFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        billno_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasRollbackConsumeAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns build() {
        com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns result = new com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.billno_ = billno_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.hasBillno()) {
          bitField0_ |= 0x00000002;
          billno_ = other.billno_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsMidasAgent.MidasRespHeader header_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder> headerBuilder_;
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       * @return Whether the header field is set.
       */
      public boolean hasHeader() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       * @return The header.
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder setHeader(com.yorha.proto.SsMidasAgent.MidasRespHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder setHeader(
          com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder mergeHeader(com.yorha.proto.SsMidasAgent.MidasRespHeader value) {
        if (headerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              header_ != null &&
              header_ != com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance()) {
            header_ =
              com.yorha.proto.SsMidasAgent.MidasRespHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder getHeaderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private java.lang.Object billno_ = "";
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @return Whether the billno field is set.
       */
      public boolean hasBillno() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @return The billno.
       */
      public java.lang.String getBillno() {
        java.lang.Object ref = billno_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            billno_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @return The bytes for billno.
       */
      public com.google.protobuf.ByteString
          getBillnoBytes() {
        java.lang.Object ref = billno_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          billno_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @param value The billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillno(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBillno() {
        bitField0_ = (bitField0_ & ~0x00000002);
        billno_ = getDefaultInstance().getBillno();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string billno = 4;</code>
       * @param value The bytes for billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillnoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasRollbackConsumeAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasRollbackConsumeAns)
    private static final com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns();
    }

    public static com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasRollbackConsumeAns>
        PARSER = new com.google.protobuf.AbstractParser<MidasRollbackConsumeAns>() {
      @java.lang.Override
      public MidasRollbackConsumeAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasRollbackConsumeAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasRollbackConsumeAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasRollbackConsumeAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRollbackConsumeAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasPresentAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasPresentAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    boolean hasHeader();
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return The header.
     */
    com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader();
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     */
    com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <code>optional string billno = 2;</code>
     * @return Whether the billno field is set.
     */
    boolean hasBillno();
    /**
     * <code>optional string billno = 2;</code>
     * @return The billno.
     */
    java.lang.String getBillno();
    /**
     * <code>optional string billno = 2;</code>
     * @return The bytes for billno.
     */
    com.google.protobuf.ByteString
        getBillnoBytes();

    /**
     * <code>optional int64 amount = 3;</code>
     * @return Whether the amount field is set.
     */
    boolean hasAmount();
    /**
     * <code>optional int64 amount = 3;</code>
     * @return The amount.
     */
    long getAmount();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasPresentAsk}
   */
  public static final class MidasPresentAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasPresentAsk)
      MidasPresentAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasPresentAsk.newBuilder() to construct.
    private MidasPresentAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasPresentAsk() {
      billno_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasPresentAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasPresentAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.yorha.proto.SsMidasAgent.MidasReqHeader.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              billno_ = bs;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              amount_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasPresentAsk.class, com.yorha.proto.SsMidasAgent.MidasPresentAsk.Builder.class);
    }

    private int bitField0_;
    public static final int HEADER_FIELD_NUMBER = 1;
    private com.yorha.proto.SsMidasAgent.MidasReqHeader header_;
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    @java.lang.Override
    public boolean hasHeader() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     * @return The header.
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
    }

    public static final int BILLNO_FIELD_NUMBER = 2;
    private volatile java.lang.Object billno_;
    /**
     * <code>optional string billno = 2;</code>
     * @return Whether the billno field is set.
     */
    @java.lang.Override
    public boolean hasBillno() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string billno = 2;</code>
     * @return The billno.
     */
    @java.lang.Override
    public java.lang.String getBillno() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          billno_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string billno = 2;</code>
     * @return The bytes for billno.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBillnoBytes() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        billno_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int AMOUNT_FIELD_NUMBER = 3;
    private long amount_;
    /**
     * <code>optional int64 amount = 3;</code>
     * @return Whether the amount field is set.
     */
    @java.lang.Override
    public boolean hasAmount() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 amount = 3;</code>
     * @return The amount.
     */
    @java.lang.Override
    public long getAmount() {
      return amount_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, billno_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, amount_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, billno_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, amount_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasPresentAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasPresentAsk other = (com.yorha.proto.SsMidasAgent.MidasPresentAsk) obj;

      if (hasHeader() != other.hasHeader()) return false;
      if (hasHeader()) {
        if (!getHeader()
            .equals(other.getHeader())) return false;
      }
      if (hasBillno() != other.hasBillno()) return false;
      if (hasBillno()) {
        if (!getBillno()
            .equals(other.getBillno())) return false;
      }
      if (hasAmount() != other.hasAmount()) return false;
      if (hasAmount()) {
        if (getAmount()
            != other.getAmount()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      if (hasBillno()) {
        hash = (37 * hash) + BILLNO_FIELD_NUMBER;
        hash = (53 * hash) + getBillno().hashCode();
      }
      if (hasAmount()) {
        hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getAmount());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasPresentAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasPresentAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasPresentAsk)
        com.yorha.proto.SsMidasAgent.MidasPresentAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasPresentAsk.class, com.yorha.proto.SsMidasAgent.MidasPresentAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasPresentAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeaderFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        billno_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        amount_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasPresentAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasPresentAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasPresentAsk build() {
        com.yorha.proto.SsMidasAgent.MidasPresentAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasPresentAsk buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasPresentAsk result = new com.yorha.proto.SsMidasAgent.MidasPresentAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.billno_ = billno_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.amount_ = amount_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasPresentAsk) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasPresentAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasPresentAsk other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasPresentAsk.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.hasBillno()) {
          bitField0_ |= 0x00000002;
          billno_ = other.billno_;
          onChanged();
        }
        if (other.hasAmount()) {
          setAmount(other.getAmount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasPresentAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasPresentAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsMidasAgent.MidasReqHeader header_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder> headerBuilder_;
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       * @return Whether the header field is set.
       */
      public boolean hasHeader() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       * @return The header.
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder setHeader(com.yorha.proto.SsMidasAgent.MidasReqHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder setHeader(
          com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder mergeHeader(com.yorha.proto.SsMidasAgent.MidasReqHeader value) {
        if (headerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              header_ != null &&
              header_ != com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance()) {
            header_ =
              com.yorha.proto.SsMidasAgent.MidasReqHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder getHeaderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.yorha.proto.SsMidasAgent.MidasReqHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasReqHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsMidasAgent.MidasReqHeader, com.yorha.proto.SsMidasAgent.MidasReqHeader.Builder, com.yorha.proto.SsMidasAgent.MidasReqHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private java.lang.Object billno_ = "";
      /**
       * <code>optional string billno = 2;</code>
       * @return Whether the billno field is set.
       */
      public boolean hasBillno() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return The billno.
       */
      public java.lang.String getBillno() {
        java.lang.Object ref = billno_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            billno_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return The bytes for billno.
       */
      public com.google.protobuf.ByteString
          getBillnoBytes() {
        java.lang.Object ref = billno_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          billno_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string billno = 2;</code>
       * @param value The billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillno(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBillno() {
        bitField0_ = (bitField0_ & ~0x00000002);
        billno_ = getDefaultInstance().getBillno();
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 2;</code>
       * @param value The bytes for billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillnoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }

      private long amount_ ;
      /**
       * <code>optional int64 amount = 3;</code>
       * @return Whether the amount field is set.
       */
      @java.lang.Override
      public boolean hasAmount() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 amount = 3;</code>
       * @return The amount.
       */
      @java.lang.Override
      public long getAmount() {
        return amount_;
      }
      /**
       * <code>optional int64 amount = 3;</code>
       * @param value The amount to set.
       * @return This builder for chaining.
       */
      public Builder setAmount(long value) {
        bitField0_ |= 0x00000004;
        amount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 amount = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAmount() {
        bitField0_ = (bitField0_ & ~0x00000004);
        amount_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasPresentAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasPresentAsk)
    private static final com.yorha.proto.SsMidasAgent.MidasPresentAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasPresentAsk();
    }

    public static com.yorha.proto.SsMidasAgent.MidasPresentAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasPresentAsk>
        PARSER = new com.google.protobuf.AbstractParser<MidasPresentAsk>() {
      @java.lang.Override
      public MidasPresentAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasPresentAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasPresentAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasPresentAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasPresentAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasPresentAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasPresentAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    boolean hasHeader();
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return The header.
     */
    com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader();
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     */
    com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder();

    /**
     * <code>optional string billno = 2;</code>
     * @return Whether the billno field is set.
     */
    boolean hasBillno();
    /**
     * <code>optional string billno = 2;</code>
     * @return The billno.
     */
    java.lang.String getBillno();
    /**
     * <code>optional string billno = 2;</code>
     * @return The bytes for billno.
     */
    com.google.protobuf.ByteString
        getBillnoBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasPresentAns}
   */
  public static final class MidasPresentAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasPresentAns)
      MidasPresentAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasPresentAns.newBuilder() to construct.
    private MidasPresentAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasPresentAns() {
      billno_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasPresentAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasPresentAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = header_.toBuilder();
              }
              header_ = input.readMessage(com.yorha.proto.SsMidasAgent.MidasRespHeader.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(header_);
                header_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              billno_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasPresentAns.class, com.yorha.proto.SsMidasAgent.MidasPresentAns.Builder.class);
    }

    private int bitField0_;
    public static final int HEADER_FIELD_NUMBER = 1;
    private com.yorha.proto.SsMidasAgent.MidasRespHeader header_;
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return Whether the header field is set.
     */
    @java.lang.Override
    public boolean hasHeader() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     * @return The header.
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
    }
    /**
     * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder() {
      return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
    }

    public static final int BILLNO_FIELD_NUMBER = 2;
    private volatile java.lang.Object billno_;
    /**
     * <code>optional string billno = 2;</code>
     * @return Whether the billno field is set.
     */
    @java.lang.Override
    public boolean hasBillno() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string billno = 2;</code>
     * @return The billno.
     */
    @java.lang.Override
    public java.lang.String getBillno() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          billno_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string billno = 2;</code>
     * @return The bytes for billno.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBillnoBytes() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        billno_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, billno_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getHeader());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, billno_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasPresentAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasPresentAns other = (com.yorha.proto.SsMidasAgent.MidasPresentAns) obj;

      if (hasHeader() != other.hasHeader()) return false;
      if (hasHeader()) {
        if (!getHeader()
            .equals(other.getHeader())) return false;
      }
      if (hasBillno() != other.hasBillno()) return false;
      if (hasBillno()) {
        if (!getBillno()
            .equals(other.getBillno())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeader()) {
        hash = (37 * hash) + HEADER_FIELD_NUMBER;
        hash = (53 * hash) + getHeader().hashCode();
      }
      if (hasBillno()) {
        hash = (37 * hash) + BILLNO_FIELD_NUMBER;
        hash = (53 * hash) + getBillno().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasPresentAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasPresentAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasPresentAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasPresentAns)
        com.yorha.proto.SsMidasAgent.MidasPresentAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasPresentAns.class, com.yorha.proto.SsMidasAgent.MidasPresentAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasPresentAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHeaderFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (headerBuilder_ == null) {
          header_ = null;
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        billno_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasPresentAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasPresentAns getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasPresentAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasPresentAns build() {
        com.yorha.proto.SsMidasAgent.MidasPresentAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasPresentAns buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasPresentAns result = new com.yorha.proto.SsMidasAgent.MidasPresentAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (headerBuilder_ == null) {
            result.header_ = header_;
          } else {
            result.header_ = headerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.billno_ = billno_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasPresentAns) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasPresentAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasPresentAns other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasPresentAns.getDefaultInstance()) return this;
        if (other.hasHeader()) {
          mergeHeader(other.getHeader());
        }
        if (other.hasBillno()) {
          bitField0_ |= 0x00000002;
          billno_ = other.billno_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasPresentAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasPresentAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsMidasAgent.MidasRespHeader header_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder> headerBuilder_;
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       * @return Whether the header field is set.
       */
      public boolean hasHeader() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       * @return The header.
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeader getHeader() {
        if (headerBuilder_ == null) {
          return header_ == null ? com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
        } else {
          return headerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder setHeader(com.yorha.proto.SsMidasAgent.MidasRespHeader value) {
        if (headerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          header_ = value;
          onChanged();
        } else {
          headerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder setHeader(
          com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder builderForValue) {
        if (headerBuilder_ == null) {
          header_ = builderForValue.build();
          onChanged();
        } else {
          headerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder mergeHeader(com.yorha.proto.SsMidasAgent.MidasRespHeader value) {
        if (headerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              header_ != null &&
              header_ != com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance()) {
            header_ =
              com.yorha.proto.SsMidasAgent.MidasRespHeader.newBuilder(header_).mergeFrom(value).buildPartial();
          } else {
            header_ = value;
          }
          onChanged();
        } else {
          headerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public Builder clearHeader() {
        if (headerBuilder_ == null) {
          header_ = null;
          onChanged();
        } else {
          headerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder getHeaderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getHeaderFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      public com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder getHeaderOrBuilder() {
        if (headerBuilder_ != null) {
          return headerBuilder_.getMessageOrBuilder();
        } else {
          return header_ == null ?
              com.yorha.proto.SsMidasAgent.MidasRespHeader.getDefaultInstance() : header_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MidasRespHeader header = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder> 
          getHeaderFieldBuilder() {
        if (headerBuilder_ == null) {
          headerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsMidasAgent.MidasRespHeader, com.yorha.proto.SsMidasAgent.MidasRespHeader.Builder, com.yorha.proto.SsMidasAgent.MidasRespHeaderOrBuilder>(
                  getHeader(),
                  getParentForChildren(),
                  isClean());
          header_ = null;
        }
        return headerBuilder_;
      }

      private java.lang.Object billno_ = "";
      /**
       * <code>optional string billno = 2;</code>
       * @return Whether the billno field is set.
       */
      public boolean hasBillno() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return The billno.
       */
      public java.lang.String getBillno() {
        java.lang.Object ref = billno_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            billno_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return The bytes for billno.
       */
      public com.google.protobuf.ByteString
          getBillnoBytes() {
        java.lang.Object ref = billno_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          billno_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string billno = 2;</code>
       * @param value The billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillno(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBillno() {
        bitField0_ = (bitField0_ & ~0x00000002);
        billno_ = getDefaultInstance().getBillno();
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 2;</code>
       * @param value The bytes for billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillnoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        billno_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasPresentAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasPresentAns)
    private static final com.yorha.proto.SsMidasAgent.MidasPresentAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasPresentAns();
    }

    public static com.yorha.proto.SsMidasAgent.MidasPresentAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasPresentAns>
        PARSER = new com.google.protobuf.AbstractParser<MidasPresentAns>() {
      @java.lang.Override
      public MidasPresentAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasPresentAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasPresentAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasPresentAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasPresentAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasSwitchStressTestingModeCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasSwitchStressTestingModeCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool stressTesting = 1;</code>
     * @return Whether the stressTesting field is set.
     */
    boolean hasStressTesting();
    /**
     * <code>optional bool stressTesting = 1;</code>
     * @return The stressTesting.
     */
    boolean getStressTesting();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasSwitchStressTestingModeCmd}
   */
  public static final class MidasSwitchStressTestingModeCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasSwitchStressTestingModeCmd)
      MidasSwitchStressTestingModeCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasSwitchStressTestingModeCmd.newBuilder() to construct.
    private MidasSwitchStressTestingModeCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasSwitchStressTestingModeCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasSwitchStressTestingModeCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasSwitchStressTestingModeCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              stressTesting_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd.class, com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd.Builder.class);
    }

    private int bitField0_;
    public static final int STRESSTESTING_FIELD_NUMBER = 1;
    private boolean stressTesting_;
    /**
     * <code>optional bool stressTesting = 1;</code>
     * @return Whether the stressTesting field is set.
     */
    @java.lang.Override
    public boolean hasStressTesting() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool stressTesting = 1;</code>
     * @return The stressTesting.
     */
    @java.lang.Override
    public boolean getStressTesting() {
      return stressTesting_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, stressTesting_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, stressTesting_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd other = (com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd) obj;

      if (hasStressTesting() != other.hasStressTesting()) return false;
      if (hasStressTesting()) {
        if (getStressTesting()
            != other.getStressTesting()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasStressTesting()) {
        hash = (37 * hash) + STRESSTESTING_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getStressTesting());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasSwitchStressTestingModeCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasSwitchStressTestingModeCmd)
        com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd.class, com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        stressTesting_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsMidasAgent.internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd build() {
        com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd buildPartial() {
        com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd result = new com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.stressTesting_ = stressTesting_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd) {
          return mergeFrom((com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd other) {
        if (other == com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd.getDefaultInstance()) return this;
        if (other.hasStressTesting()) {
          setStressTesting(other.getStressTesting());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean stressTesting_ ;
      /**
       * <code>optional bool stressTesting = 1;</code>
       * @return Whether the stressTesting field is set.
       */
      @java.lang.Override
      public boolean hasStressTesting() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool stressTesting = 1;</code>
       * @return The stressTesting.
       */
      @java.lang.Override
      public boolean getStressTesting() {
        return stressTesting_;
      }
      /**
       * <code>optional bool stressTesting = 1;</code>
       * @param value The stressTesting to set.
       * @return This builder for chaining.
       */
      public Builder setStressTesting(boolean value) {
        bitField0_ |= 0x00000001;
        stressTesting_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool stressTesting = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStressTesting() {
        bitField0_ = (bitField0_ & ~0x00000001);
        stressTesting_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasSwitchStressTestingModeCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasSwitchStressTestingModeCmd)
    private static final com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd();
    }

    public static com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasSwitchStressTestingModeCmd>
        PARSER = new com.google.protobuf.AbstractParser<MidasSwitchStressTestingModeCmd>() {
      @java.lang.Override
      public MidasSwitchStressTestingModeCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasSwitchStressTestingModeCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasSwitchStressTestingModeCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasSwitchStressTestingModeCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsMidasAgent.MidasSwitchStressTestingModeCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasRespHeader_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasRespHeader_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasReqHeader_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasReqHeader_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasQueryAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasQueryAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasQueryAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasQueryAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasConsumeAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasConsumeAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasConsumeAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasConsumeAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasRollbackConsumeAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasRollbackConsumeAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasRollbackConsumeAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasRollbackConsumeAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasPresentAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasPresentAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasPresentAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasPresentAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n,ss_proto/gen/midasAgent/ss_midas_agent" +
      ".proto\022\017com.yorha.proto\"c\n\017MidasRespHead" +
      "er\022\013\n\003ret\030\001 \001(\005\022\020\n\010err_code\030\002 \001(\t\022\013\n\003msg" +
      "\030\003 \001(\t\022\017\n\007balance\030\004 \001(\003\022\023\n\013gen_balance\030\005" +
      " \001(\003\"N\n\016MidasReqHeader\022\016\n\006openId\030\001 \001(\t\022\020" +
      "\n\010playerId\030\002 \001(\003\022\016\n\006zoneId\030\003 \001(\005\022\n\n\002pf\030\004" +
      " \001(\t\"@\n\rMidasQueryAsk\022/\n\006header\030\001 \001(\0132\037." +
      "com.yorha.proto.MidasReqHeader\"\240\001\n\rMidas" +
      "QueryAns\0220\n\006header\030\001 \001(\0132 .com.yorha.pro" +
      "to.MidasRespHeader\022\022\n\nfirst_save\030\004 \001(\005\022\020" +
      "\n\010save_amt\030\005 \001(\003\022\020\n\010save_sum\030\006 \001(\003\022\020\n\010co" +
      "st_sum\030\007 \001(\003\022\023\n\013present_sum\030\010 \001(\003\"b\n\017Mid" +
      "asConsumeAsk\022/\n\006header\030\001 \001(\0132\037.com.yorha" +
      ".proto.MidasReqHeader\022\016\n\006billno\030\002 \001(\t\022\016\n" +
      "\006amount\030\003 \001(\003\"i\n\017MidasConsumeAns\0220\n\006head" +
      "er\030\001 \001(\0132 .com.yorha.proto.MidasRespHead" +
      "er\022\016\n\006billno\030\004 \001(\t\022\024\n\014used_gen_amt\030\005 \001(\003" +
      "\"Z\n\027MidasRollbackConsumeAsk\022/\n\006header\030\001 " +
      "\001(\0132\037.com.yorha.proto.MidasReqHeader\022\016\n\006" +
      "billno\030\002 \001(\t\"[\n\027MidasRollbackConsumeAns\022" +
      "0\n\006header\030\001 \001(\0132 .com.yorha.proto.MidasR" +
      "espHeader\022\016\n\006billno\030\004 \001(\t\"b\n\017MidasPresen" +
      "tAsk\022/\n\006header\030\001 \001(\0132\037.com.yorha.proto.M" +
      "idasReqHeader\022\016\n\006billno\030\002 \001(\t\022\016\n\006amount\030" +
      "\003 \001(\003\"S\n\017MidasPresentAns\0220\n\006header\030\001 \001(\013" +
      "2 .com.yorha.proto.MidasRespHeader\022\016\n\006bi" +
      "llno\030\002 \001(\t\"8\n\037MidasSwitchStressTestingMo" +
      "deCmd\022\025\n\rstressTesting\030\001 \001(\010B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_MidasRespHeader_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_MidasRespHeader_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasRespHeader_descriptor,
        new java.lang.String[] { "Ret", "ErrCode", "Msg", "Balance", "GenBalance", });
    internal_static_com_yorha_proto_MidasReqHeader_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_MidasReqHeader_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasReqHeader_descriptor,
        new java.lang.String[] { "OpenId", "PlayerId", "ZoneId", "Pf", });
    internal_static_com_yorha_proto_MidasQueryAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_MidasQueryAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasQueryAsk_descriptor,
        new java.lang.String[] { "Header", });
    internal_static_com_yorha_proto_MidasQueryAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_MidasQueryAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasQueryAns_descriptor,
        new java.lang.String[] { "Header", "FirstSave", "SaveAmt", "SaveSum", "CostSum", "PresentSum", });
    internal_static_com_yorha_proto_MidasConsumeAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_MidasConsumeAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasConsumeAsk_descriptor,
        new java.lang.String[] { "Header", "Billno", "Amount", });
    internal_static_com_yorha_proto_MidasConsumeAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_MidasConsumeAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasConsumeAns_descriptor,
        new java.lang.String[] { "Header", "Billno", "UsedGenAmt", });
    internal_static_com_yorha_proto_MidasRollbackConsumeAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_MidasRollbackConsumeAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasRollbackConsumeAsk_descriptor,
        new java.lang.String[] { "Header", "Billno", });
    internal_static_com_yorha_proto_MidasRollbackConsumeAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_MidasRollbackConsumeAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasRollbackConsumeAns_descriptor,
        new java.lang.String[] { "Header", "Billno", });
    internal_static_com_yorha_proto_MidasPresentAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_MidasPresentAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasPresentAsk_descriptor,
        new java.lang.String[] { "Header", "Billno", "Amount", });
    internal_static_com_yorha_proto_MidasPresentAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_MidasPresentAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasPresentAns_descriptor,
        new java.lang.String[] { "Header", "Billno", });
    internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasSwitchStressTestingModeCmd_descriptor,
        new java.lang.String[] { "StressTesting", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

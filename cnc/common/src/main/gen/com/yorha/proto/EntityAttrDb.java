// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/entity/entity_attr_db.proto

package com.yorha.proto;

public final class EntityAttrDb {
  private EntityAttrDb() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface EntityAttrDBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.EntityAttrDB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return Whether the entityType field is set.
     */
    boolean hasEntityType();
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return The entityType.
     */
    com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType();

    /**
     * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
     * @return Whether the armyAttr field is set.
     */
    boolean hasArmyAttr();
    /**
     * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
     * @return The armyAttr.
     */
    com.yorha.proto.Army.ArmyEntity getArmyAttr();
    /**
     * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
     */
    com.yorha.proto.Army.ArmyEntityOrBuilder getArmyAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
     * @return Whether the cityAttr field is set.
     */
    boolean hasCityAttr();
    /**
     * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
     * @return The cityAttr.
     */
    com.yorha.proto.City.CityEntity getCityAttr();
    /**
     * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
     */
    com.yorha.proto.City.CityEntityOrBuilder getCityAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
     * @return Whether the playerAttr field is set.
     */
    boolean hasPlayerAttr();
    /**
     * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
     * @return The playerAttr.
     */
    com.yorha.proto.Player.PlayerEntity getPlayerAttr();
    /**
     * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
     */
    com.yorha.proto.Player.PlayerEntityOrBuilder getPlayerAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
     * @return Whether the monsterAttr field is set.
     */
    boolean hasMonsterAttr();
    /**
     * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
     * @return The monsterAttr.
     */
    com.yorha.proto.Monster.MonsterEntity getMonsterAttr();
    /**
     * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
     */
    com.yorha.proto.Monster.MonsterEntityOrBuilder getMonsterAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
     * @return Whether the clanAttr field is set.
     */
    boolean hasClanAttr();
    /**
     * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
     * @return The clanAttr.
     */
    com.yorha.proto.Clan.ClanEntity getClanAttr();
    /**
     * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
     */
    com.yorha.proto.Clan.ClanEntityOrBuilder getClanAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
     * @return Whether the dropObjectAttr field is set.
     */
    boolean hasDropObjectAttr();
    /**
     * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
     * @return The dropObjectAttr.
     */
    com.yorha.proto.DropObject.DropObjectEntity getDropObjectAttr();
    /**
     * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
     */
    com.yorha.proto.DropObject.DropObjectEntityOrBuilder getDropObjectAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
     * @return Whether the mapBuildingAttr field is set.
     */
    boolean hasMapBuildingAttr();
    /**
     * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
     * @return The mapBuildingAttr.
     */
    com.yorha.proto.MapBuilding.MapBuildingEntity getMapBuildingAttr();
    /**
     * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
     */
    com.yorha.proto.MapBuilding.MapBuildingEntityOrBuilder getMapBuildingAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
     * @return Whether the spyPlane field is set.
     */
    boolean hasSpyPlane();
    /**
     * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
     * @return The spyPlane.
     */
    com.yorha.proto.SpyPlane.SpyPlaneEntity getSpyPlane();
    /**
     * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
     */
    com.yorha.proto.SpyPlane.SpyPlaneEntityOrBuilder getSpyPlaneOrBuilder();

    /**
     * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
     * @return Whether the resBuildingAttr field is set.
     */
    boolean hasResBuildingAttr();
    /**
     * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
     * @return The resBuildingAttr.
     */
    com.yorha.proto.ResBuilding.ResBuildingEntity getResBuildingAttr();
    /**
     * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
     */
    com.yorha.proto.ResBuilding.ResBuildingEntityOrBuilder getResBuildingAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
     * @return Whether the caveAttr field is set.
     */
    boolean hasCaveAttr();
    /**
     * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
     * @return The caveAttr.
     */
    com.yorha.proto.Cave.CaveEntity getCaveAttr();
    /**
     * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
     */
    com.yorha.proto.Cave.CaveEntityOrBuilder getCaveAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
     * @return Whether the logisticsPlaneAttr field is set.
     */
    boolean hasLogisticsPlaneAttr();
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
     * @return The logisticsPlaneAttr.
     */
    com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity getLogisticsPlaneAttr();
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
     */
    com.yorha.proto.LogisticsPlane.LogisticsPlaneEntityOrBuilder getLogisticsPlaneAttrOrBuilder();

    /**
     * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
     * @return Whether the clanResBuildingAttr field is set.
     */
    boolean hasClanResBuildingAttr();
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
     * @return The clanResBuildingAttr.
     */
    com.yorha.proto.ClanResBuilding.ClanResBuildingEntity getClanResBuildingAttr();
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
     */
    com.yorha.proto.ClanResBuilding.ClanResBuildingEntityOrBuilder getClanResBuildingAttrOrBuilder();

    public com.yorha.proto.EntityAttrDb.EntityAttrDB.EntityAttrCase getEntityAttrCase();
  }
  /**
   * Protobuf type {@code com.yorha.proto.EntityAttrDB}
   */
  public static final class EntityAttrDB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.EntityAttrDB)
      EntityAttrDBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EntityAttrDB.newBuilder() to construct.
    private EntityAttrDB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EntityAttrDB() {
      entityType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EntityAttrDB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private EntityAttrDB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              entityId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.EntityAttrOuterClass.EntityType value = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                entityType_ = rawValue;
              }
              break;
            }
            case 90: {
              com.yorha.proto.Army.ArmyEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 11) {
                subBuilder = ((com.yorha.proto.Army.ArmyEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.Army.ArmyEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.Army.ArmyEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 11;
              break;
            }
            case 98: {
              com.yorha.proto.City.CityEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 12) {
                subBuilder = ((com.yorha.proto.City.CityEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.City.CityEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.City.CityEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 12;
              break;
            }
            case 106: {
              com.yorha.proto.Player.PlayerEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 13) {
                subBuilder = ((com.yorha.proto.Player.PlayerEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.Player.PlayerEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.Player.PlayerEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 13;
              break;
            }
            case 114: {
              com.yorha.proto.Monster.MonsterEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 14) {
                subBuilder = ((com.yorha.proto.Monster.MonsterEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.Monster.MonsterEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.Monster.MonsterEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 14;
              break;
            }
            case 122: {
              com.yorha.proto.Clan.ClanEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 15) {
                subBuilder = ((com.yorha.proto.Clan.ClanEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.Clan.ClanEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.Clan.ClanEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 15;
              break;
            }
            case 130: {
              com.yorha.proto.DropObject.DropObjectEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 16) {
                subBuilder = ((com.yorha.proto.DropObject.DropObjectEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.DropObject.DropObjectEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.DropObject.DropObjectEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 16;
              break;
            }
            case 138: {
              com.yorha.proto.MapBuilding.MapBuildingEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 17) {
                subBuilder = ((com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.MapBuilding.MapBuildingEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 17;
              break;
            }
            case 146: {
              com.yorha.proto.SpyPlane.SpyPlaneEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 18) {
                subBuilder = ((com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.SpyPlane.SpyPlaneEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 18;
              break;
            }
            case 154: {
              com.yorha.proto.ResBuilding.ResBuildingEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 19) {
                subBuilder = ((com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.ResBuilding.ResBuildingEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 19;
              break;
            }
            case 170: {
              com.yorha.proto.Cave.CaveEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 21) {
                subBuilder = ((com.yorha.proto.Cave.CaveEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.Cave.CaveEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.Cave.CaveEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 21;
              break;
            }
            case 178: {
              com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 22) {
                subBuilder = ((com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 22;
              break;
            }
            case 186: {
              com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.Builder subBuilder = null;
              if (entityAttrCase_ == 23) {
                subBuilder = ((com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_).toBuilder();
              }
              entityAttr_ =
                  input.readMessage(com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_);
                entityAttr_ = subBuilder.buildPartial();
              }
              entityAttrCase_ = 23;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.EntityAttrDb.internal_static_com_yorha_proto_EntityAttrDB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.EntityAttrDb.internal_static_com_yorha_proto_EntityAttrDB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.EntityAttrDb.EntityAttrDB.class, com.yorha.proto.EntityAttrDb.EntityAttrDB.Builder.class);
    }

    private int bitField0_;
    private int entityAttrCase_ = 0;
    private java.lang.Object entityAttr_;
    public enum EntityAttrCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      ARMYATTR(11),
      CITYATTR(12),
      PLAYERATTR(13),
      MONSTERATTR(14),
      CLANATTR(15),
      DROPOBJECTATTR(16),
      MAPBUILDINGATTR(17),
      SPYPLANE(18),
      RESBUILDINGATTR(19),
      CAVEATTR(21),
      LOGISTICSPLANEATTR(22),
      CLANRESBUILDINGATTR(23),
      ENTITYATTR_NOT_SET(0);
      private final int value;
      private EntityAttrCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static EntityAttrCase valueOf(int value) {
        return forNumber(value);
      }

      public static EntityAttrCase forNumber(int value) {
        switch (value) {
          case 11: return ARMYATTR;
          case 12: return CITYATTR;
          case 13: return PLAYERATTR;
          case 14: return MONSTERATTR;
          case 15: return CLANATTR;
          case 16: return DROPOBJECTATTR;
          case 17: return MAPBUILDINGATTR;
          case 18: return SPYPLANE;
          case 19: return RESBUILDINGATTR;
          case 21: return CAVEATTR;
          case 22: return LOGISTICSPLANEATTR;
          case 23: return CLANRESBUILDINGATTR;
          case 0: return ENTITYATTR_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public EntityAttrCase
    getEntityAttrCase() {
      return EntityAttrCase.forNumber(
          entityAttrCase_);
    }

    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int ENTITYTYPE_FIELD_NUMBER = 2;
    private int entityType_;
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return Whether the entityType field is set.
     */
    @java.lang.Override public boolean hasEntityType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
     * @return The entityType.
     */
    @java.lang.Override public com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.EntityAttrOuterClass.EntityType result = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(entityType_);
      return result == null ? com.yorha.proto.EntityAttrOuterClass.EntityType.ET_Unknown : result;
    }

    public static final int ARMYATTR_FIELD_NUMBER = 11;
    /**
     * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
     * @return Whether the armyAttr field is set.
     */
    @java.lang.Override
    public boolean hasArmyAttr() {
      return entityAttrCase_ == 11;
    }
    /**
     * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
     * @return The armyAttr.
     */
    @java.lang.Override
    public com.yorha.proto.Army.ArmyEntity getArmyAttr() {
      if (entityAttrCase_ == 11) {
         return (com.yorha.proto.Army.ArmyEntity) entityAttr_;
      }
      return com.yorha.proto.Army.ArmyEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Army.ArmyEntityOrBuilder getArmyAttrOrBuilder() {
      if (entityAttrCase_ == 11) {
         return (com.yorha.proto.Army.ArmyEntity) entityAttr_;
      }
      return com.yorha.proto.Army.ArmyEntity.getDefaultInstance();
    }

    public static final int CITYATTR_FIELD_NUMBER = 12;
    /**
     * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
     * @return Whether the cityAttr field is set.
     */
    @java.lang.Override
    public boolean hasCityAttr() {
      return entityAttrCase_ == 12;
    }
    /**
     * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
     * @return The cityAttr.
     */
    @java.lang.Override
    public com.yorha.proto.City.CityEntity getCityAttr() {
      if (entityAttrCase_ == 12) {
         return (com.yorha.proto.City.CityEntity) entityAttr_;
      }
      return com.yorha.proto.City.CityEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
     */
    @java.lang.Override
    public com.yorha.proto.City.CityEntityOrBuilder getCityAttrOrBuilder() {
      if (entityAttrCase_ == 12) {
         return (com.yorha.proto.City.CityEntity) entityAttr_;
      }
      return com.yorha.proto.City.CityEntity.getDefaultInstance();
    }

    public static final int PLAYERATTR_FIELD_NUMBER = 13;
    /**
     * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
     * @return Whether the playerAttr field is set.
     */
    @java.lang.Override
    public boolean hasPlayerAttr() {
      return entityAttrCase_ == 13;
    }
    /**
     * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
     * @return The playerAttr.
     */
    @java.lang.Override
    public com.yorha.proto.Player.PlayerEntity getPlayerAttr() {
      if (entityAttrCase_ == 13) {
         return (com.yorha.proto.Player.PlayerEntity) entityAttr_;
      }
      return com.yorha.proto.Player.PlayerEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Player.PlayerEntityOrBuilder getPlayerAttrOrBuilder() {
      if (entityAttrCase_ == 13) {
         return (com.yorha.proto.Player.PlayerEntity) entityAttr_;
      }
      return com.yorha.proto.Player.PlayerEntity.getDefaultInstance();
    }

    public static final int MONSTERATTR_FIELD_NUMBER = 14;
    /**
     * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
     * @return Whether the monsterAttr field is set.
     */
    @java.lang.Override
    public boolean hasMonsterAttr() {
      return entityAttrCase_ == 14;
    }
    /**
     * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
     * @return The monsterAttr.
     */
    @java.lang.Override
    public com.yorha.proto.Monster.MonsterEntity getMonsterAttr() {
      if (entityAttrCase_ == 14) {
         return (com.yorha.proto.Monster.MonsterEntity) entityAttr_;
      }
      return com.yorha.proto.Monster.MonsterEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Monster.MonsterEntityOrBuilder getMonsterAttrOrBuilder() {
      if (entityAttrCase_ == 14) {
         return (com.yorha.proto.Monster.MonsterEntity) entityAttr_;
      }
      return com.yorha.proto.Monster.MonsterEntity.getDefaultInstance();
    }

    public static final int CLANATTR_FIELD_NUMBER = 15;
    /**
     * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
     * @return Whether the clanAttr field is set.
     */
    @java.lang.Override
    public boolean hasClanAttr() {
      return entityAttrCase_ == 15;
    }
    /**
     * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
     * @return The clanAttr.
     */
    @java.lang.Override
    public com.yorha.proto.Clan.ClanEntity getClanAttr() {
      if (entityAttrCase_ == 15) {
         return (com.yorha.proto.Clan.ClanEntity) entityAttr_;
      }
      return com.yorha.proto.Clan.ClanEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Clan.ClanEntityOrBuilder getClanAttrOrBuilder() {
      if (entityAttrCase_ == 15) {
         return (com.yorha.proto.Clan.ClanEntity) entityAttr_;
      }
      return com.yorha.proto.Clan.ClanEntity.getDefaultInstance();
    }

    public static final int DROPOBJECTATTR_FIELD_NUMBER = 16;
    /**
     * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
     * @return Whether the dropObjectAttr field is set.
     */
    @java.lang.Override
    public boolean hasDropObjectAttr() {
      return entityAttrCase_ == 16;
    }
    /**
     * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
     * @return The dropObjectAttr.
     */
    @java.lang.Override
    public com.yorha.proto.DropObject.DropObjectEntity getDropObjectAttr() {
      if (entityAttrCase_ == 16) {
         return (com.yorha.proto.DropObject.DropObjectEntity) entityAttr_;
      }
      return com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
     */
    @java.lang.Override
    public com.yorha.proto.DropObject.DropObjectEntityOrBuilder getDropObjectAttrOrBuilder() {
      if (entityAttrCase_ == 16) {
         return (com.yorha.proto.DropObject.DropObjectEntity) entityAttr_;
      }
      return com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance();
    }

    public static final int MAPBUILDINGATTR_FIELD_NUMBER = 17;
    /**
     * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
     * @return Whether the mapBuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasMapBuildingAttr() {
      return entityAttrCase_ == 17;
    }
    /**
     * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
     * @return The mapBuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.MapBuilding.MapBuildingEntity getMapBuildingAttr() {
      if (entityAttrCase_ == 17) {
         return (com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_;
      }
      return com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MapBuilding.MapBuildingEntityOrBuilder getMapBuildingAttrOrBuilder() {
      if (entityAttrCase_ == 17) {
         return (com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_;
      }
      return com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance();
    }

    public static final int SPYPLANE_FIELD_NUMBER = 18;
    /**
     * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
     * @return Whether the spyPlane field is set.
     */
    @java.lang.Override
    public boolean hasSpyPlane() {
      return entityAttrCase_ == 18;
    }
    /**
     * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
     * @return The spyPlane.
     */
    @java.lang.Override
    public com.yorha.proto.SpyPlane.SpyPlaneEntity getSpyPlane() {
      if (entityAttrCase_ == 18) {
         return (com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_;
      }
      return com.yorha.proto.SpyPlane.SpyPlaneEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SpyPlane.SpyPlaneEntityOrBuilder getSpyPlaneOrBuilder() {
      if (entityAttrCase_ == 18) {
         return (com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_;
      }
      return com.yorha.proto.SpyPlane.SpyPlaneEntity.getDefaultInstance();
    }

    public static final int RESBUILDINGATTR_FIELD_NUMBER = 19;
    /**
     * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
     * @return Whether the resBuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasResBuildingAttr() {
      return entityAttrCase_ == 19;
    }
    /**
     * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
     * @return The resBuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.ResBuilding.ResBuildingEntity getResBuildingAttr() {
      if (entityAttrCase_ == 19) {
         return (com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_;
      }
      return com.yorha.proto.ResBuilding.ResBuildingEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ResBuilding.ResBuildingEntityOrBuilder getResBuildingAttrOrBuilder() {
      if (entityAttrCase_ == 19) {
         return (com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_;
      }
      return com.yorha.proto.ResBuilding.ResBuildingEntity.getDefaultInstance();
    }

    public static final int CAVEATTR_FIELD_NUMBER = 21;
    /**
     * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
     * @return Whether the caveAttr field is set.
     */
    @java.lang.Override
    public boolean hasCaveAttr() {
      return entityAttrCase_ == 21;
    }
    /**
     * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
     * @return The caveAttr.
     */
    @java.lang.Override
    public com.yorha.proto.Cave.CaveEntity getCaveAttr() {
      if (entityAttrCase_ == 21) {
         return (com.yorha.proto.Cave.CaveEntity) entityAttr_;
      }
      return com.yorha.proto.Cave.CaveEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Cave.CaveEntityOrBuilder getCaveAttrOrBuilder() {
      if (entityAttrCase_ == 21) {
         return (com.yorha.proto.Cave.CaveEntity) entityAttr_;
      }
      return com.yorha.proto.Cave.CaveEntity.getDefaultInstance();
    }

    public static final int LOGISTICSPLANEATTR_FIELD_NUMBER = 22;
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
     * @return Whether the logisticsPlaneAttr field is set.
     */
    @java.lang.Override
    public boolean hasLogisticsPlaneAttr() {
      return entityAttrCase_ == 22;
    }
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
     * @return The logisticsPlaneAttr.
     */
    @java.lang.Override
    public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity getLogisticsPlaneAttr() {
      if (entityAttrCase_ == 22) {
         return (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_;
      }
      return com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
     */
    @java.lang.Override
    public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntityOrBuilder getLogisticsPlaneAttrOrBuilder() {
      if (entityAttrCase_ == 22) {
         return (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_;
      }
      return com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance();
    }

    public static final int CLANRESBUILDINGATTR_FIELD_NUMBER = 23;
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
     * @return Whether the clanResBuildingAttr field is set.
     */
    @java.lang.Override
    public boolean hasClanResBuildingAttr() {
      return entityAttrCase_ == 23;
    }
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
     * @return The clanResBuildingAttr.
     */
    @java.lang.Override
    public com.yorha.proto.ClanResBuilding.ClanResBuildingEntity getClanResBuildingAttr() {
      if (entityAttrCase_ == 23) {
         return (com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_;
      }
      return com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ClanResBuilding.ClanResBuildingEntityOrBuilder getClanResBuildingAttrOrBuilder() {
      if (entityAttrCase_ == 23) {
         return (com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_;
      }
      return com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.getDefaultInstance();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, entityType_);
      }
      if (entityAttrCase_ == 11) {
        output.writeMessage(11, (com.yorha.proto.Army.ArmyEntity) entityAttr_);
      }
      if (entityAttrCase_ == 12) {
        output.writeMessage(12, (com.yorha.proto.City.CityEntity) entityAttr_);
      }
      if (entityAttrCase_ == 13) {
        output.writeMessage(13, (com.yorha.proto.Player.PlayerEntity) entityAttr_);
      }
      if (entityAttrCase_ == 14) {
        output.writeMessage(14, (com.yorha.proto.Monster.MonsterEntity) entityAttr_);
      }
      if (entityAttrCase_ == 15) {
        output.writeMessage(15, (com.yorha.proto.Clan.ClanEntity) entityAttr_);
      }
      if (entityAttrCase_ == 16) {
        output.writeMessage(16, (com.yorha.proto.DropObject.DropObjectEntity) entityAttr_);
      }
      if (entityAttrCase_ == 17) {
        output.writeMessage(17, (com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_);
      }
      if (entityAttrCase_ == 18) {
        output.writeMessage(18, (com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_);
      }
      if (entityAttrCase_ == 19) {
        output.writeMessage(19, (com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_);
      }
      if (entityAttrCase_ == 21) {
        output.writeMessage(21, (com.yorha.proto.Cave.CaveEntity) entityAttr_);
      }
      if (entityAttrCase_ == 22) {
        output.writeMessage(22, (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_);
      }
      if (entityAttrCase_ == 23) {
        output.writeMessage(23, (com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, entityType_);
      }
      if (entityAttrCase_ == 11) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, (com.yorha.proto.Army.ArmyEntity) entityAttr_);
      }
      if (entityAttrCase_ == 12) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, (com.yorha.proto.City.CityEntity) entityAttr_);
      }
      if (entityAttrCase_ == 13) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, (com.yorha.proto.Player.PlayerEntity) entityAttr_);
      }
      if (entityAttrCase_ == 14) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(14, (com.yorha.proto.Monster.MonsterEntity) entityAttr_);
      }
      if (entityAttrCase_ == 15) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, (com.yorha.proto.Clan.ClanEntity) entityAttr_);
      }
      if (entityAttrCase_ == 16) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, (com.yorha.proto.DropObject.DropObjectEntity) entityAttr_);
      }
      if (entityAttrCase_ == 17) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(17, (com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_);
      }
      if (entityAttrCase_ == 18) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(18, (com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_);
      }
      if (entityAttrCase_ == 19) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(19, (com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_);
      }
      if (entityAttrCase_ == 21) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(21, (com.yorha.proto.Cave.CaveEntity) entityAttr_);
      }
      if (entityAttrCase_ == 22) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(22, (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_);
      }
      if (entityAttrCase_ == 23) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(23, (com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.EntityAttrDb.EntityAttrDB)) {
        return super.equals(obj);
      }
      com.yorha.proto.EntityAttrDb.EntityAttrDB other = (com.yorha.proto.EntityAttrDb.EntityAttrDB) obj;

      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (hasEntityType() != other.hasEntityType()) return false;
      if (hasEntityType()) {
        if (entityType_ != other.entityType_) return false;
      }
      if (!getEntityAttrCase().equals(other.getEntityAttrCase())) return false;
      switch (entityAttrCase_) {
        case 11:
          if (!getArmyAttr()
              .equals(other.getArmyAttr())) return false;
          break;
        case 12:
          if (!getCityAttr()
              .equals(other.getCityAttr())) return false;
          break;
        case 13:
          if (!getPlayerAttr()
              .equals(other.getPlayerAttr())) return false;
          break;
        case 14:
          if (!getMonsterAttr()
              .equals(other.getMonsterAttr())) return false;
          break;
        case 15:
          if (!getClanAttr()
              .equals(other.getClanAttr())) return false;
          break;
        case 16:
          if (!getDropObjectAttr()
              .equals(other.getDropObjectAttr())) return false;
          break;
        case 17:
          if (!getMapBuildingAttr()
              .equals(other.getMapBuildingAttr())) return false;
          break;
        case 18:
          if (!getSpyPlane()
              .equals(other.getSpyPlane())) return false;
          break;
        case 19:
          if (!getResBuildingAttr()
              .equals(other.getResBuildingAttr())) return false;
          break;
        case 21:
          if (!getCaveAttr()
              .equals(other.getCaveAttr())) return false;
          break;
        case 22:
          if (!getLogisticsPlaneAttr()
              .equals(other.getLogisticsPlaneAttr())) return false;
          break;
        case 23:
          if (!getClanResBuildingAttr()
              .equals(other.getClanResBuildingAttr())) return false;
          break;
        case 0:
        default:
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      if (hasEntityType()) {
        hash = (37 * hash) + ENTITYTYPE_FIELD_NUMBER;
        hash = (53 * hash) + entityType_;
      }
      switch (entityAttrCase_) {
        case 11:
          hash = (37 * hash) + ARMYATTR_FIELD_NUMBER;
          hash = (53 * hash) + getArmyAttr().hashCode();
          break;
        case 12:
          hash = (37 * hash) + CITYATTR_FIELD_NUMBER;
          hash = (53 * hash) + getCityAttr().hashCode();
          break;
        case 13:
          hash = (37 * hash) + PLAYERATTR_FIELD_NUMBER;
          hash = (53 * hash) + getPlayerAttr().hashCode();
          break;
        case 14:
          hash = (37 * hash) + MONSTERATTR_FIELD_NUMBER;
          hash = (53 * hash) + getMonsterAttr().hashCode();
          break;
        case 15:
          hash = (37 * hash) + CLANATTR_FIELD_NUMBER;
          hash = (53 * hash) + getClanAttr().hashCode();
          break;
        case 16:
          hash = (37 * hash) + DROPOBJECTATTR_FIELD_NUMBER;
          hash = (53 * hash) + getDropObjectAttr().hashCode();
          break;
        case 17:
          hash = (37 * hash) + MAPBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getMapBuildingAttr().hashCode();
          break;
        case 18:
          hash = (37 * hash) + SPYPLANE_FIELD_NUMBER;
          hash = (53 * hash) + getSpyPlane().hashCode();
          break;
        case 19:
          hash = (37 * hash) + RESBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getResBuildingAttr().hashCode();
          break;
        case 21:
          hash = (37 * hash) + CAVEATTR_FIELD_NUMBER;
          hash = (53 * hash) + getCaveAttr().hashCode();
          break;
        case 22:
          hash = (37 * hash) + LOGISTICSPLANEATTR_FIELD_NUMBER;
          hash = (53 * hash) + getLogisticsPlaneAttr().hashCode();
          break;
        case 23:
          hash = (37 * hash) + CLANRESBUILDINGATTR_FIELD_NUMBER;
          hash = (53 * hash) + getClanResBuildingAttr().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.EntityAttrDb.EntityAttrDB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.EntityAttrDb.EntityAttrDB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.EntityAttrDB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.EntityAttrDB)
        com.yorha.proto.EntityAttrDb.EntityAttrDBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.EntityAttrDb.internal_static_com_yorha_proto_EntityAttrDB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.EntityAttrDb.internal_static_com_yorha_proto_EntityAttrDB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.EntityAttrDb.EntityAttrDB.class, com.yorha.proto.EntityAttrDb.EntityAttrDB.Builder.class);
      }

      // Construct using com.yorha.proto.EntityAttrDb.EntityAttrDB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        entityType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        entityAttrCase_ = 0;
        entityAttr_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.EntityAttrDb.internal_static_com_yorha_proto_EntityAttrDB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.EntityAttrDb.EntityAttrDB getDefaultInstanceForType() {
        return com.yorha.proto.EntityAttrDb.EntityAttrDB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.EntityAttrDb.EntityAttrDB build() {
        com.yorha.proto.EntityAttrDb.EntityAttrDB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.EntityAttrDb.EntityAttrDB buildPartial() {
        com.yorha.proto.EntityAttrDb.EntityAttrDB result = new com.yorha.proto.EntityAttrDb.EntityAttrDB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.entityType_ = entityType_;
        if (entityAttrCase_ == 11) {
          if (armyAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = armyAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 12) {
          if (cityAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = cityAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 13) {
          if (playerAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = playerAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 14) {
          if (monsterAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = monsterAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 15) {
          if (clanAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = clanAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 16) {
          if (dropObjectAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = dropObjectAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 17) {
          if (mapBuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = mapBuildingAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 18) {
          if (spyPlaneBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = spyPlaneBuilder_.build();
          }
        }
        if (entityAttrCase_ == 19) {
          if (resBuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = resBuildingAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 21) {
          if (caveAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = caveAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 22) {
          if (logisticsPlaneAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = logisticsPlaneAttrBuilder_.build();
          }
        }
        if (entityAttrCase_ == 23) {
          if (clanResBuildingAttrBuilder_ == null) {
            result.entityAttr_ = entityAttr_;
          } else {
            result.entityAttr_ = clanResBuildingAttrBuilder_.build();
          }
        }
        result.bitField0_ = to_bitField0_;
        result.entityAttrCase_ = entityAttrCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.EntityAttrDb.EntityAttrDB) {
          return mergeFrom((com.yorha.proto.EntityAttrDb.EntityAttrDB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.EntityAttrDb.EntityAttrDB other) {
        if (other == com.yorha.proto.EntityAttrDb.EntityAttrDB.getDefaultInstance()) return this;
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        if (other.hasEntityType()) {
          setEntityType(other.getEntityType());
        }
        switch (other.getEntityAttrCase()) {
          case ARMYATTR: {
            mergeArmyAttr(other.getArmyAttr());
            break;
          }
          case CITYATTR: {
            mergeCityAttr(other.getCityAttr());
            break;
          }
          case PLAYERATTR: {
            mergePlayerAttr(other.getPlayerAttr());
            break;
          }
          case MONSTERATTR: {
            mergeMonsterAttr(other.getMonsterAttr());
            break;
          }
          case CLANATTR: {
            mergeClanAttr(other.getClanAttr());
            break;
          }
          case DROPOBJECTATTR: {
            mergeDropObjectAttr(other.getDropObjectAttr());
            break;
          }
          case MAPBUILDINGATTR: {
            mergeMapBuildingAttr(other.getMapBuildingAttr());
            break;
          }
          case SPYPLANE: {
            mergeSpyPlane(other.getSpyPlane());
            break;
          }
          case RESBUILDINGATTR: {
            mergeResBuildingAttr(other.getResBuildingAttr());
            break;
          }
          case CAVEATTR: {
            mergeCaveAttr(other.getCaveAttr());
            break;
          }
          case LOGISTICSPLANEATTR: {
            mergeLogisticsPlaneAttr(other.getLogisticsPlaneAttr());
            break;
          }
          case CLANRESBUILDINGATTR: {
            mergeClanResBuildingAttr(other.getClanResBuildingAttr());
            break;
          }
          case ENTITYATTR_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.EntityAttrDb.EntityAttrDB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.EntityAttrDb.EntityAttrDB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int entityAttrCase_ = 0;
      private java.lang.Object entityAttr_;
      public EntityAttrCase
          getEntityAttrCase() {
        return EntityAttrCase.forNumber(
            entityAttrCase_);
      }

      public Builder clearEntityAttr() {
        entityAttrCase_ = 0;
        entityAttr_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private long entityId_ ;
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000001;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int entityType_ = 0;
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return Whether the entityType field is set.
       */
      @java.lang.Override public boolean hasEntityType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return The entityType.
       */
      @java.lang.Override
      public com.yorha.proto.EntityAttrOuterClass.EntityType getEntityType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.EntityAttrOuterClass.EntityType result = com.yorha.proto.EntityAttrOuterClass.EntityType.valueOf(entityType_);
        return result == null ? com.yorha.proto.EntityAttrOuterClass.EntityType.ET_Unknown : result;
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @param value The entityType to set.
       * @return This builder for chaining.
       */
      public Builder setEntityType(com.yorha.proto.EntityAttrOuterClass.EntityType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        entityType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.EntityType entityType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        entityType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Army.ArmyEntity, com.yorha.proto.Army.ArmyEntity.Builder, com.yorha.proto.Army.ArmyEntityOrBuilder> armyAttrBuilder_;
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       * @return Whether the armyAttr field is set.
       */
      @java.lang.Override
      public boolean hasArmyAttr() {
        return entityAttrCase_ == 11;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       * @return The armyAttr.
       */
      @java.lang.Override
      public com.yorha.proto.Army.ArmyEntity getArmyAttr() {
        if (armyAttrBuilder_ == null) {
          if (entityAttrCase_ == 11) {
            return (com.yorha.proto.Army.ArmyEntity) entityAttr_;
          }
          return com.yorha.proto.Army.ArmyEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 11) {
            return armyAttrBuilder_.getMessage();
          }
          return com.yorha.proto.Army.ArmyEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       */
      public Builder setArmyAttr(com.yorha.proto.Army.ArmyEntity value) {
        if (armyAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          armyAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       */
      public Builder setArmyAttr(
          com.yorha.proto.Army.ArmyEntity.Builder builderForValue) {
        if (armyAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          armyAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       */
      public Builder mergeArmyAttr(com.yorha.proto.Army.ArmyEntity value) {
        if (armyAttrBuilder_ == null) {
          if (entityAttrCase_ == 11 &&
              entityAttr_ != com.yorha.proto.Army.ArmyEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.Army.ArmyEntity.newBuilder((com.yorha.proto.Army.ArmyEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 11) {
            armyAttrBuilder_.mergeFrom(value);
          }
          armyAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 11;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       */
      public Builder clearArmyAttr() {
        if (armyAttrBuilder_ == null) {
          if (entityAttrCase_ == 11) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 11) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          armyAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       */
      public com.yorha.proto.Army.ArmyEntity.Builder getArmyAttrBuilder() {
        return getArmyAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       */
      @java.lang.Override
      public com.yorha.proto.Army.ArmyEntityOrBuilder getArmyAttrOrBuilder() {
        if ((entityAttrCase_ == 11) && (armyAttrBuilder_ != null)) {
          return armyAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 11) {
            return (com.yorha.proto.Army.ArmyEntity) entityAttr_;
          }
          return com.yorha.proto.Army.ArmyEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ArmyEntity armyAttr = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Army.ArmyEntity, com.yorha.proto.Army.ArmyEntity.Builder, com.yorha.proto.Army.ArmyEntityOrBuilder> 
          getArmyAttrFieldBuilder() {
        if (armyAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 11)) {
            entityAttr_ = com.yorha.proto.Army.ArmyEntity.getDefaultInstance();
          }
          armyAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Army.ArmyEntity, com.yorha.proto.Army.ArmyEntity.Builder, com.yorha.proto.Army.ArmyEntityOrBuilder>(
                  (com.yorha.proto.Army.ArmyEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 11;
        onChanged();;
        return armyAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.City.CityEntity, com.yorha.proto.City.CityEntity.Builder, com.yorha.proto.City.CityEntityOrBuilder> cityAttrBuilder_;
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       * @return Whether the cityAttr field is set.
       */
      @java.lang.Override
      public boolean hasCityAttr() {
        return entityAttrCase_ == 12;
      }
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       * @return The cityAttr.
       */
      @java.lang.Override
      public com.yorha.proto.City.CityEntity getCityAttr() {
        if (cityAttrBuilder_ == null) {
          if (entityAttrCase_ == 12) {
            return (com.yorha.proto.City.CityEntity) entityAttr_;
          }
          return com.yorha.proto.City.CityEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 12) {
            return cityAttrBuilder_.getMessage();
          }
          return com.yorha.proto.City.CityEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       */
      public Builder setCityAttr(com.yorha.proto.City.CityEntity value) {
        if (cityAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          cityAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       */
      public Builder setCityAttr(
          com.yorha.proto.City.CityEntity.Builder builderForValue) {
        if (cityAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          cityAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       */
      public Builder mergeCityAttr(com.yorha.proto.City.CityEntity value) {
        if (cityAttrBuilder_ == null) {
          if (entityAttrCase_ == 12 &&
              entityAttr_ != com.yorha.proto.City.CityEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.City.CityEntity.newBuilder((com.yorha.proto.City.CityEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 12) {
            cityAttrBuilder_.mergeFrom(value);
          }
          cityAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 12;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       */
      public Builder clearCityAttr() {
        if (cityAttrBuilder_ == null) {
          if (entityAttrCase_ == 12) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 12) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          cityAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       */
      public com.yorha.proto.City.CityEntity.Builder getCityAttrBuilder() {
        return getCityAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       */
      @java.lang.Override
      public com.yorha.proto.City.CityEntityOrBuilder getCityAttrOrBuilder() {
        if ((entityAttrCase_ == 12) && (cityAttrBuilder_ != null)) {
          return cityAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 12) {
            return (com.yorha.proto.City.CityEntity) entityAttr_;
          }
          return com.yorha.proto.City.CityEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.CityEntity cityAttr = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.City.CityEntity, com.yorha.proto.City.CityEntity.Builder, com.yorha.proto.City.CityEntityOrBuilder> 
          getCityAttrFieldBuilder() {
        if (cityAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 12)) {
            entityAttr_ = com.yorha.proto.City.CityEntity.getDefaultInstance();
          }
          cityAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.City.CityEntity, com.yorha.proto.City.CityEntity.Builder, com.yorha.proto.City.CityEntityOrBuilder>(
                  (com.yorha.proto.City.CityEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 12;
        onChanged();;
        return cityAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Player.PlayerEntity, com.yorha.proto.Player.PlayerEntity.Builder, com.yorha.proto.Player.PlayerEntityOrBuilder> playerAttrBuilder_;
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       * @return Whether the playerAttr field is set.
       */
      @java.lang.Override
      public boolean hasPlayerAttr() {
        return entityAttrCase_ == 13;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       * @return The playerAttr.
       */
      @java.lang.Override
      public com.yorha.proto.Player.PlayerEntity getPlayerAttr() {
        if (playerAttrBuilder_ == null) {
          if (entityAttrCase_ == 13) {
            return (com.yorha.proto.Player.PlayerEntity) entityAttr_;
          }
          return com.yorha.proto.Player.PlayerEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 13) {
            return playerAttrBuilder_.getMessage();
          }
          return com.yorha.proto.Player.PlayerEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       */
      public Builder setPlayerAttr(com.yorha.proto.Player.PlayerEntity value) {
        if (playerAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          playerAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       */
      public Builder setPlayerAttr(
          com.yorha.proto.Player.PlayerEntity.Builder builderForValue) {
        if (playerAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          playerAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       */
      public Builder mergePlayerAttr(com.yorha.proto.Player.PlayerEntity value) {
        if (playerAttrBuilder_ == null) {
          if (entityAttrCase_ == 13 &&
              entityAttr_ != com.yorha.proto.Player.PlayerEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.Player.PlayerEntity.newBuilder((com.yorha.proto.Player.PlayerEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 13) {
            playerAttrBuilder_.mergeFrom(value);
          }
          playerAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 13;
        return this;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       */
      public Builder clearPlayerAttr() {
        if (playerAttrBuilder_ == null) {
          if (entityAttrCase_ == 13) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 13) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          playerAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       */
      public com.yorha.proto.Player.PlayerEntity.Builder getPlayerAttrBuilder() {
        return getPlayerAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       */
      @java.lang.Override
      public com.yorha.proto.Player.PlayerEntityOrBuilder getPlayerAttrOrBuilder() {
        if ((entityAttrCase_ == 13) && (playerAttrBuilder_ != null)) {
          return playerAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 13) {
            return (com.yorha.proto.Player.PlayerEntity) entityAttr_;
          }
          return com.yorha.proto.Player.PlayerEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.PlayerEntity playerAttr = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Player.PlayerEntity, com.yorha.proto.Player.PlayerEntity.Builder, com.yorha.proto.Player.PlayerEntityOrBuilder> 
          getPlayerAttrFieldBuilder() {
        if (playerAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 13)) {
            entityAttr_ = com.yorha.proto.Player.PlayerEntity.getDefaultInstance();
          }
          playerAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Player.PlayerEntity, com.yorha.proto.Player.PlayerEntity.Builder, com.yorha.proto.Player.PlayerEntityOrBuilder>(
                  (com.yorha.proto.Player.PlayerEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 13;
        onChanged();;
        return playerAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Monster.MonsterEntity, com.yorha.proto.Monster.MonsterEntity.Builder, com.yorha.proto.Monster.MonsterEntityOrBuilder> monsterAttrBuilder_;
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       * @return Whether the monsterAttr field is set.
       */
      @java.lang.Override
      public boolean hasMonsterAttr() {
        return entityAttrCase_ == 14;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       * @return The monsterAttr.
       */
      @java.lang.Override
      public com.yorha.proto.Monster.MonsterEntity getMonsterAttr() {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 14) {
            return (com.yorha.proto.Monster.MonsterEntity) entityAttr_;
          }
          return com.yorha.proto.Monster.MonsterEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 14) {
            return monsterAttrBuilder_.getMessage();
          }
          return com.yorha.proto.Monster.MonsterEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       */
      public Builder setMonsterAttr(com.yorha.proto.Monster.MonsterEntity value) {
        if (monsterAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          monsterAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 14;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       */
      public Builder setMonsterAttr(
          com.yorha.proto.Monster.MonsterEntity.Builder builderForValue) {
        if (monsterAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          monsterAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 14;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       */
      public Builder mergeMonsterAttr(com.yorha.proto.Monster.MonsterEntity value) {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 14 &&
              entityAttr_ != com.yorha.proto.Monster.MonsterEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.Monster.MonsterEntity.newBuilder((com.yorha.proto.Monster.MonsterEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 14) {
            monsterAttrBuilder_.mergeFrom(value);
          }
          monsterAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 14;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       */
      public Builder clearMonsterAttr() {
        if (monsterAttrBuilder_ == null) {
          if (entityAttrCase_ == 14) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 14) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          monsterAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       */
      public com.yorha.proto.Monster.MonsterEntity.Builder getMonsterAttrBuilder() {
        return getMonsterAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       */
      @java.lang.Override
      public com.yorha.proto.Monster.MonsterEntityOrBuilder getMonsterAttrOrBuilder() {
        if ((entityAttrCase_ == 14) && (monsterAttrBuilder_ != null)) {
          return monsterAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 14) {
            return (com.yorha.proto.Monster.MonsterEntity) entityAttr_;
          }
          return com.yorha.proto.Monster.MonsterEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.MonsterEntity monsterAttr = 14;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Monster.MonsterEntity, com.yorha.proto.Monster.MonsterEntity.Builder, com.yorha.proto.Monster.MonsterEntityOrBuilder> 
          getMonsterAttrFieldBuilder() {
        if (monsterAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 14)) {
            entityAttr_ = com.yorha.proto.Monster.MonsterEntity.getDefaultInstance();
          }
          monsterAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Monster.MonsterEntity, com.yorha.proto.Monster.MonsterEntity.Builder, com.yorha.proto.Monster.MonsterEntityOrBuilder>(
                  (com.yorha.proto.Monster.MonsterEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 14;
        onChanged();;
        return monsterAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Clan.ClanEntity, com.yorha.proto.Clan.ClanEntity.Builder, com.yorha.proto.Clan.ClanEntityOrBuilder> clanAttrBuilder_;
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       * @return Whether the clanAttr field is set.
       */
      @java.lang.Override
      public boolean hasClanAttr() {
        return entityAttrCase_ == 15;
      }
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       * @return The clanAttr.
       */
      @java.lang.Override
      public com.yorha.proto.Clan.ClanEntity getClanAttr() {
        if (clanAttrBuilder_ == null) {
          if (entityAttrCase_ == 15) {
            return (com.yorha.proto.Clan.ClanEntity) entityAttr_;
          }
          return com.yorha.proto.Clan.ClanEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 15) {
            return clanAttrBuilder_.getMessage();
          }
          return com.yorha.proto.Clan.ClanEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       */
      public Builder setClanAttr(com.yorha.proto.Clan.ClanEntity value) {
        if (clanAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          clanAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       */
      public Builder setClanAttr(
          com.yorha.proto.Clan.ClanEntity.Builder builderForValue) {
        if (clanAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          clanAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       */
      public Builder mergeClanAttr(com.yorha.proto.Clan.ClanEntity value) {
        if (clanAttrBuilder_ == null) {
          if (entityAttrCase_ == 15 &&
              entityAttr_ != com.yorha.proto.Clan.ClanEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.Clan.ClanEntity.newBuilder((com.yorha.proto.Clan.ClanEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 15) {
            clanAttrBuilder_.mergeFrom(value);
          }
          clanAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 15;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       */
      public Builder clearClanAttr() {
        if (clanAttrBuilder_ == null) {
          if (entityAttrCase_ == 15) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 15) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          clanAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       */
      public com.yorha.proto.Clan.ClanEntity.Builder getClanAttrBuilder() {
        return getClanAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       */
      @java.lang.Override
      public com.yorha.proto.Clan.ClanEntityOrBuilder getClanAttrOrBuilder() {
        if ((entityAttrCase_ == 15) && (clanAttrBuilder_ != null)) {
          return clanAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 15) {
            return (com.yorha.proto.Clan.ClanEntity) entityAttr_;
          }
          return com.yorha.proto.Clan.ClanEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ClanEntity clanAttr = 15;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Clan.ClanEntity, com.yorha.proto.Clan.ClanEntity.Builder, com.yorha.proto.Clan.ClanEntityOrBuilder> 
          getClanAttrFieldBuilder() {
        if (clanAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 15)) {
            entityAttr_ = com.yorha.proto.Clan.ClanEntity.getDefaultInstance();
          }
          clanAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Clan.ClanEntity, com.yorha.proto.Clan.ClanEntity.Builder, com.yorha.proto.Clan.ClanEntityOrBuilder>(
                  (com.yorha.proto.Clan.ClanEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 15;
        onChanged();;
        return clanAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.DropObject.DropObjectEntity, com.yorha.proto.DropObject.DropObjectEntity.Builder, com.yorha.proto.DropObject.DropObjectEntityOrBuilder> dropObjectAttrBuilder_;
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       * @return Whether the dropObjectAttr field is set.
       */
      @java.lang.Override
      public boolean hasDropObjectAttr() {
        return entityAttrCase_ == 16;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       * @return The dropObjectAttr.
       */
      @java.lang.Override
      public com.yorha.proto.DropObject.DropObjectEntity getDropObjectAttr() {
        if (dropObjectAttrBuilder_ == null) {
          if (entityAttrCase_ == 16) {
            return (com.yorha.proto.DropObject.DropObjectEntity) entityAttr_;
          }
          return com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 16) {
            return dropObjectAttrBuilder_.getMessage();
          }
          return com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       */
      public Builder setDropObjectAttr(com.yorha.proto.DropObject.DropObjectEntity value) {
        if (dropObjectAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          dropObjectAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       */
      public Builder setDropObjectAttr(
          com.yorha.proto.DropObject.DropObjectEntity.Builder builderForValue) {
        if (dropObjectAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          dropObjectAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       */
      public Builder mergeDropObjectAttr(com.yorha.proto.DropObject.DropObjectEntity value) {
        if (dropObjectAttrBuilder_ == null) {
          if (entityAttrCase_ == 16 &&
              entityAttr_ != com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.DropObject.DropObjectEntity.newBuilder((com.yorha.proto.DropObject.DropObjectEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 16) {
            dropObjectAttrBuilder_.mergeFrom(value);
          }
          dropObjectAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 16;
        return this;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       */
      public Builder clearDropObjectAttr() {
        if (dropObjectAttrBuilder_ == null) {
          if (entityAttrCase_ == 16) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 16) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          dropObjectAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       */
      public com.yorha.proto.DropObject.DropObjectEntity.Builder getDropObjectAttrBuilder() {
        return getDropObjectAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       */
      @java.lang.Override
      public com.yorha.proto.DropObject.DropObjectEntityOrBuilder getDropObjectAttrOrBuilder() {
        if ((entityAttrCase_ == 16) && (dropObjectAttrBuilder_ != null)) {
          return dropObjectAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 16) {
            return (com.yorha.proto.DropObject.DropObjectEntity) entityAttr_;
          }
          return com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.DropObjectEntity dropObjectAttr = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.DropObject.DropObjectEntity, com.yorha.proto.DropObject.DropObjectEntity.Builder, com.yorha.proto.DropObject.DropObjectEntityOrBuilder> 
          getDropObjectAttrFieldBuilder() {
        if (dropObjectAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 16)) {
            entityAttr_ = com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance();
          }
          dropObjectAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.DropObject.DropObjectEntity, com.yorha.proto.DropObject.DropObjectEntity.Builder, com.yorha.proto.DropObject.DropObjectEntityOrBuilder>(
                  (com.yorha.proto.DropObject.DropObjectEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 16;
        onChanged();;
        return dropObjectAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuilding.MapBuildingEntity, com.yorha.proto.MapBuilding.MapBuildingEntity.Builder, com.yorha.proto.MapBuilding.MapBuildingEntityOrBuilder> mapBuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       * @return Whether the mapBuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasMapBuildingAttr() {
        return entityAttrCase_ == 17;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       * @return The mapBuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.MapBuilding.MapBuildingEntity getMapBuildingAttr() {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 17) {
            return (com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_;
          }
          return com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 17) {
            return mapBuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       */
      public Builder setMapBuildingAttr(com.yorha.proto.MapBuilding.MapBuildingEntity value) {
        if (mapBuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          mapBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       */
      public Builder setMapBuildingAttr(
          com.yorha.proto.MapBuilding.MapBuildingEntity.Builder builderForValue) {
        if (mapBuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          mapBuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       */
      public Builder mergeMapBuildingAttr(com.yorha.proto.MapBuilding.MapBuildingEntity value) {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 17 &&
              entityAttr_ != com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.MapBuilding.MapBuildingEntity.newBuilder((com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 17) {
            mapBuildingAttrBuilder_.mergeFrom(value);
          }
          mapBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 17;
        return this;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       */
      public Builder clearMapBuildingAttr() {
        if (mapBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 17) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 17) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          mapBuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       */
      public com.yorha.proto.MapBuilding.MapBuildingEntity.Builder getMapBuildingAttrBuilder() {
        return getMapBuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       */
      @java.lang.Override
      public com.yorha.proto.MapBuilding.MapBuildingEntityOrBuilder getMapBuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 17) && (mapBuildingAttrBuilder_ != null)) {
          return mapBuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 17) {
            return (com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_;
          }
          return com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.MapBuildingEntity mapBuildingAttr = 17;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuilding.MapBuildingEntity, com.yorha.proto.MapBuilding.MapBuildingEntity.Builder, com.yorha.proto.MapBuilding.MapBuildingEntityOrBuilder> 
          getMapBuildingAttrFieldBuilder() {
        if (mapBuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 17)) {
            entityAttr_ = com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance();
          }
          mapBuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MapBuilding.MapBuildingEntity, com.yorha.proto.MapBuilding.MapBuildingEntity.Builder, com.yorha.proto.MapBuilding.MapBuildingEntityOrBuilder>(
                  (com.yorha.proto.MapBuilding.MapBuildingEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 17;
        onChanged();;
        return mapBuildingAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SpyPlane.SpyPlaneEntity, com.yorha.proto.SpyPlane.SpyPlaneEntity.Builder, com.yorha.proto.SpyPlane.SpyPlaneEntityOrBuilder> spyPlaneBuilder_;
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       * @return Whether the spyPlane field is set.
       */
      @java.lang.Override
      public boolean hasSpyPlane() {
        return entityAttrCase_ == 18;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       * @return The spyPlane.
       */
      @java.lang.Override
      public com.yorha.proto.SpyPlane.SpyPlaneEntity getSpyPlane() {
        if (spyPlaneBuilder_ == null) {
          if (entityAttrCase_ == 18) {
            return (com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_;
          }
          return com.yorha.proto.SpyPlane.SpyPlaneEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 18) {
            return spyPlaneBuilder_.getMessage();
          }
          return com.yorha.proto.SpyPlane.SpyPlaneEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       */
      public Builder setSpyPlane(com.yorha.proto.SpyPlane.SpyPlaneEntity value) {
        if (spyPlaneBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          spyPlaneBuilder_.setMessage(value);
        }
        entityAttrCase_ = 18;
        return this;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       */
      public Builder setSpyPlane(
          com.yorha.proto.SpyPlane.SpyPlaneEntity.Builder builderForValue) {
        if (spyPlaneBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          spyPlaneBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 18;
        return this;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       */
      public Builder mergeSpyPlane(com.yorha.proto.SpyPlane.SpyPlaneEntity value) {
        if (spyPlaneBuilder_ == null) {
          if (entityAttrCase_ == 18 &&
              entityAttr_ != com.yorha.proto.SpyPlane.SpyPlaneEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.SpyPlane.SpyPlaneEntity.newBuilder((com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 18) {
            spyPlaneBuilder_.mergeFrom(value);
          }
          spyPlaneBuilder_.setMessage(value);
        }
        entityAttrCase_ = 18;
        return this;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       */
      public Builder clearSpyPlane() {
        if (spyPlaneBuilder_ == null) {
          if (entityAttrCase_ == 18) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 18) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          spyPlaneBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       */
      public com.yorha.proto.SpyPlane.SpyPlaneEntity.Builder getSpyPlaneBuilder() {
        return getSpyPlaneFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       */
      @java.lang.Override
      public com.yorha.proto.SpyPlane.SpyPlaneEntityOrBuilder getSpyPlaneOrBuilder() {
        if ((entityAttrCase_ == 18) && (spyPlaneBuilder_ != null)) {
          return spyPlaneBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 18) {
            return (com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_;
          }
          return com.yorha.proto.SpyPlane.SpyPlaneEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.SpyPlaneEntity spyPlane = 18;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SpyPlane.SpyPlaneEntity, com.yorha.proto.SpyPlane.SpyPlaneEntity.Builder, com.yorha.proto.SpyPlane.SpyPlaneEntityOrBuilder> 
          getSpyPlaneFieldBuilder() {
        if (spyPlaneBuilder_ == null) {
          if (!(entityAttrCase_ == 18)) {
            entityAttr_ = com.yorha.proto.SpyPlane.SpyPlaneEntity.getDefaultInstance();
          }
          spyPlaneBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SpyPlane.SpyPlaneEntity, com.yorha.proto.SpyPlane.SpyPlaneEntity.Builder, com.yorha.proto.SpyPlane.SpyPlaneEntityOrBuilder>(
                  (com.yorha.proto.SpyPlane.SpyPlaneEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 18;
        onChanged();;
        return spyPlaneBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ResBuilding.ResBuildingEntity, com.yorha.proto.ResBuilding.ResBuildingEntity.Builder, com.yorha.proto.ResBuilding.ResBuildingEntityOrBuilder> resBuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       * @return Whether the resBuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasResBuildingAttr() {
        return entityAttrCase_ == 19;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       * @return The resBuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.ResBuilding.ResBuildingEntity getResBuildingAttr() {
        if (resBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 19) {
            return (com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_;
          }
          return com.yorha.proto.ResBuilding.ResBuildingEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 19) {
            return resBuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.ResBuilding.ResBuildingEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       */
      public Builder setResBuildingAttr(com.yorha.proto.ResBuilding.ResBuildingEntity value) {
        if (resBuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          resBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 19;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       */
      public Builder setResBuildingAttr(
          com.yorha.proto.ResBuilding.ResBuildingEntity.Builder builderForValue) {
        if (resBuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          resBuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 19;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       */
      public Builder mergeResBuildingAttr(com.yorha.proto.ResBuilding.ResBuildingEntity value) {
        if (resBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 19 &&
              entityAttr_ != com.yorha.proto.ResBuilding.ResBuildingEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.ResBuilding.ResBuildingEntity.newBuilder((com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 19) {
            resBuildingAttrBuilder_.mergeFrom(value);
          }
          resBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 19;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       */
      public Builder clearResBuildingAttr() {
        if (resBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 19) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 19) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          resBuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       */
      public com.yorha.proto.ResBuilding.ResBuildingEntity.Builder getResBuildingAttrBuilder() {
        return getResBuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       */
      @java.lang.Override
      public com.yorha.proto.ResBuilding.ResBuildingEntityOrBuilder getResBuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 19) && (resBuildingAttrBuilder_ != null)) {
          return resBuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 19) {
            return (com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_;
          }
          return com.yorha.proto.ResBuilding.ResBuildingEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ResBuildingEntity resBuildingAttr = 19;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ResBuilding.ResBuildingEntity, com.yorha.proto.ResBuilding.ResBuildingEntity.Builder, com.yorha.proto.ResBuilding.ResBuildingEntityOrBuilder> 
          getResBuildingAttrFieldBuilder() {
        if (resBuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 19)) {
            entityAttr_ = com.yorha.proto.ResBuilding.ResBuildingEntity.getDefaultInstance();
          }
          resBuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ResBuilding.ResBuildingEntity, com.yorha.proto.ResBuilding.ResBuildingEntity.Builder, com.yorha.proto.ResBuilding.ResBuildingEntityOrBuilder>(
                  (com.yorha.proto.ResBuilding.ResBuildingEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 19;
        onChanged();;
        return resBuildingAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Cave.CaveEntity, com.yorha.proto.Cave.CaveEntity.Builder, com.yorha.proto.Cave.CaveEntityOrBuilder> caveAttrBuilder_;
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       * @return Whether the caveAttr field is set.
       */
      @java.lang.Override
      public boolean hasCaveAttr() {
        return entityAttrCase_ == 21;
      }
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       * @return The caveAttr.
       */
      @java.lang.Override
      public com.yorha.proto.Cave.CaveEntity getCaveAttr() {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 21) {
            return (com.yorha.proto.Cave.CaveEntity) entityAttr_;
          }
          return com.yorha.proto.Cave.CaveEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 21) {
            return caveAttrBuilder_.getMessage();
          }
          return com.yorha.proto.Cave.CaveEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       */
      public Builder setCaveAttr(com.yorha.proto.Cave.CaveEntity value) {
        if (caveAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          caveAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 21;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       */
      public Builder setCaveAttr(
          com.yorha.proto.Cave.CaveEntity.Builder builderForValue) {
        if (caveAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          caveAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 21;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       */
      public Builder mergeCaveAttr(com.yorha.proto.Cave.CaveEntity value) {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 21 &&
              entityAttr_ != com.yorha.proto.Cave.CaveEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.Cave.CaveEntity.newBuilder((com.yorha.proto.Cave.CaveEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 21) {
            caveAttrBuilder_.mergeFrom(value);
          }
          caveAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 21;
        return this;
      }
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       */
      public Builder clearCaveAttr() {
        if (caveAttrBuilder_ == null) {
          if (entityAttrCase_ == 21) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 21) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          caveAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       */
      public com.yorha.proto.Cave.CaveEntity.Builder getCaveAttrBuilder() {
        return getCaveAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       */
      @java.lang.Override
      public com.yorha.proto.Cave.CaveEntityOrBuilder getCaveAttrOrBuilder() {
        if ((entityAttrCase_ == 21) && (caveAttrBuilder_ != null)) {
          return caveAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 21) {
            return (com.yorha.proto.Cave.CaveEntity) entityAttr_;
          }
          return com.yorha.proto.Cave.CaveEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.CaveEntity caveAttr = 21;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Cave.CaveEntity, com.yorha.proto.Cave.CaveEntity.Builder, com.yorha.proto.Cave.CaveEntityOrBuilder> 
          getCaveAttrFieldBuilder() {
        if (caveAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 21)) {
            entityAttr_ = com.yorha.proto.Cave.CaveEntity.getDefaultInstance();
          }
          caveAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Cave.CaveEntity, com.yorha.proto.Cave.CaveEntity.Builder, com.yorha.proto.Cave.CaveEntityOrBuilder>(
                  (com.yorha.proto.Cave.CaveEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 21;
        onChanged();;
        return caveAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity, com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.Builder, com.yorha.proto.LogisticsPlane.LogisticsPlaneEntityOrBuilder> logisticsPlaneAttrBuilder_;
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       * @return Whether the logisticsPlaneAttr field is set.
       */
      @java.lang.Override
      public boolean hasLogisticsPlaneAttr() {
        return entityAttrCase_ == 22;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       * @return The logisticsPlaneAttr.
       */
      @java.lang.Override
      public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity getLogisticsPlaneAttr() {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (entityAttrCase_ == 22) {
            return (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_;
          }
          return com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 22) {
            return logisticsPlaneAttrBuilder_.getMessage();
          }
          return com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       */
      public Builder setLogisticsPlaneAttr(com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity value) {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          logisticsPlaneAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 22;
        return this;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       */
      public Builder setLogisticsPlaneAttr(
          com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.Builder builderForValue) {
        if (logisticsPlaneAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          logisticsPlaneAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 22;
        return this;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       */
      public Builder mergeLogisticsPlaneAttr(com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity value) {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (entityAttrCase_ == 22 &&
              entityAttr_ != com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.newBuilder((com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 22) {
            logisticsPlaneAttrBuilder_.mergeFrom(value);
          }
          logisticsPlaneAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 22;
        return this;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       */
      public Builder clearLogisticsPlaneAttr() {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (entityAttrCase_ == 22) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 22) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          logisticsPlaneAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       */
      public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.Builder getLogisticsPlaneAttrBuilder() {
        return getLogisticsPlaneAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       */
      @java.lang.Override
      public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntityOrBuilder getLogisticsPlaneAttrOrBuilder() {
        if ((entityAttrCase_ == 22) && (logisticsPlaneAttrBuilder_ != null)) {
          return logisticsPlaneAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 22) {
            return (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_;
          }
          return com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.LogisticsPlaneEntity logisticsPlaneAttr = 22;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity, com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.Builder, com.yorha.proto.LogisticsPlane.LogisticsPlaneEntityOrBuilder> 
          getLogisticsPlaneAttrFieldBuilder() {
        if (logisticsPlaneAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 22)) {
            entityAttr_ = com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance();
          }
          logisticsPlaneAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity, com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.Builder, com.yorha.proto.LogisticsPlane.LogisticsPlaneEntityOrBuilder>(
                  (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 22;
        onChanged();;
        return logisticsPlaneAttrBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanResBuilding.ClanResBuildingEntity, com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.Builder, com.yorha.proto.ClanResBuilding.ClanResBuildingEntityOrBuilder> clanResBuildingAttrBuilder_;
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       * @return Whether the clanResBuildingAttr field is set.
       */
      @java.lang.Override
      public boolean hasClanResBuildingAttr() {
        return entityAttrCase_ == 23;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       * @return The clanResBuildingAttr.
       */
      @java.lang.Override
      public com.yorha.proto.ClanResBuilding.ClanResBuildingEntity getClanResBuildingAttr() {
        if (clanResBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 23) {
            return (com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_;
          }
          return com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.getDefaultInstance();
        } else {
          if (entityAttrCase_ == 23) {
            return clanResBuildingAttrBuilder_.getMessage();
          }
          return com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       */
      public Builder setClanResBuildingAttr(com.yorha.proto.ClanResBuilding.ClanResBuildingEntity value) {
        if (clanResBuildingAttrBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          entityAttr_ = value;
          onChanged();
        } else {
          clanResBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 23;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       */
      public Builder setClanResBuildingAttr(
          com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.Builder builderForValue) {
        if (clanResBuildingAttrBuilder_ == null) {
          entityAttr_ = builderForValue.build();
          onChanged();
        } else {
          clanResBuildingAttrBuilder_.setMessage(builderForValue.build());
        }
        entityAttrCase_ = 23;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       */
      public Builder mergeClanResBuildingAttr(com.yorha.proto.ClanResBuilding.ClanResBuildingEntity value) {
        if (clanResBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 23 &&
              entityAttr_ != com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.getDefaultInstance()) {
            entityAttr_ = com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.newBuilder((com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_)
                .mergeFrom(value).buildPartial();
          } else {
            entityAttr_ = value;
          }
          onChanged();
        } else {
          if (entityAttrCase_ == 23) {
            clanResBuildingAttrBuilder_.mergeFrom(value);
          }
          clanResBuildingAttrBuilder_.setMessage(value);
        }
        entityAttrCase_ = 23;
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       */
      public Builder clearClanResBuildingAttr() {
        if (clanResBuildingAttrBuilder_ == null) {
          if (entityAttrCase_ == 23) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
            onChanged();
          }
        } else {
          if (entityAttrCase_ == 23) {
            entityAttrCase_ = 0;
            entityAttr_ = null;
          }
          clanResBuildingAttrBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       */
      public com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.Builder getClanResBuildingAttrBuilder() {
        return getClanResBuildingAttrFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       */
      @java.lang.Override
      public com.yorha.proto.ClanResBuilding.ClanResBuildingEntityOrBuilder getClanResBuildingAttrOrBuilder() {
        if ((entityAttrCase_ == 23) && (clanResBuildingAttrBuilder_ != null)) {
          return clanResBuildingAttrBuilder_.getMessageOrBuilder();
        } else {
          if (entityAttrCase_ == 23) {
            return (com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_;
          }
          return com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.ClanResBuildingEntity clanResBuildingAttr = 23;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ClanResBuilding.ClanResBuildingEntity, com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.Builder, com.yorha.proto.ClanResBuilding.ClanResBuildingEntityOrBuilder> 
          getClanResBuildingAttrFieldBuilder() {
        if (clanResBuildingAttrBuilder_ == null) {
          if (!(entityAttrCase_ == 23)) {
            entityAttr_ = com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.getDefaultInstance();
          }
          clanResBuildingAttrBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ClanResBuilding.ClanResBuildingEntity, com.yorha.proto.ClanResBuilding.ClanResBuildingEntity.Builder, com.yorha.proto.ClanResBuilding.ClanResBuildingEntityOrBuilder>(
                  (com.yorha.proto.ClanResBuilding.ClanResBuildingEntity) entityAttr_,
                  getParentForChildren(),
                  isClean());
          entityAttr_ = null;
        }
        entityAttrCase_ = 23;
        onChanged();;
        return clanResBuildingAttrBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.EntityAttrDB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.EntityAttrDB)
    private static final com.yorha.proto.EntityAttrDb.EntityAttrDB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.EntityAttrDb.EntityAttrDB();
    }

    public static com.yorha.proto.EntityAttrDb.EntityAttrDB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<EntityAttrDB>
        PARSER = new com.google.protobuf.AbstractParser<EntityAttrDB>() {
      @java.lang.Override
      public EntityAttrDB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EntityAttrDB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<EntityAttrDB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EntityAttrDB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.EntityAttrDb.EntityAttrDB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_EntityAttrDB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_EntityAttrDB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n(ss_proto/gen/entity/entity_attr_db.pro" +
      "to\022\017com.yorha.proto\032\034ss_proto/gen/army/a" +
      "rmy.proto\032\034ss_proto/gen/cave/cave.proto\032" +
      "\034ss_proto/gen/city/city.proto\0322ss_proto/" +
      "gen/clanResBuilding/clanResBuilding.prot" +
      "o\032\034ss_proto/gen/clan/clan.proto\032)ss_prot" +
      "o/gen/dropObject/drop_object.proto\032%ss_p" +
      "roto/gen/entity/entity_attr.proto\0320ss_pr" +
      "oto/gen/logisticsPlane/logisticsPlane.pr" +
      "oto\032+ss_proto/gen/mapBuilding/map_buildi" +
      "ng.proto\032\"ss_proto/gen/monster/monster.p" +
      "roto\032 ss_proto/gen/player/player.proto\032*" +
      "ss_proto/gen/resBuilding/resBuilding.pro" +
      "to\032$ss_proto/gen/spyPlane/spyPlane.proto" +
      "\"\213\006\n\014EntityAttrDB\022\020\n\010entityId\030\001 \001(\003\022/\n\ne" +
      "ntityType\030\002 \001(\0162\033.com.yorha.proto.Entity" +
      "Type\022/\n\010armyAttr\030\013 \001(\0132\033.com.yorha.proto" +
      ".ArmyEntityH\000\022/\n\010cityAttr\030\014 \001(\0132\033.com.yo" +
      "rha.proto.CityEntityH\000\0223\n\nplayerAttr\030\r \001" +
      "(\0132\035.com.yorha.proto.PlayerEntityH\000\0225\n\013m" +
      "onsterAttr\030\016 \001(\0132\036.com.yorha.proto.Monst" +
      "erEntityH\000\022/\n\010clanAttr\030\017 \001(\0132\033.com.yorha" +
      ".proto.ClanEntityH\000\022;\n\016dropObjectAttr\030\020 " +
      "\001(\0132!.com.yorha.proto.DropObjectEntityH\000" +
      "\022=\n\017mapBuildingAttr\030\021 \001(\0132\".com.yorha.pr" +
      "oto.MapBuildingEntityH\000\0223\n\010spyPlane\030\022 \001(" +
      "\0132\037.com.yorha.proto.SpyPlaneEntityH\000\022=\n\017" +
      "resBuildingAttr\030\023 \001(\0132\".com.yorha.proto." +
      "ResBuildingEntityH\000\022/\n\010caveAttr\030\025 \001(\0132\033." +
      "com.yorha.proto.CaveEntityH\000\022C\n\022logistic" +
      "sPlaneAttr\030\026 \001(\0132%.com.yorha.proto.Logis" +
      "ticsPlaneEntityH\000\022E\n\023clanResBuildingAttr" +
      "\030\027 \001(\0132&.com.yorha.proto.ClanResBuilding" +
      "EntityH\000B\014\n\nentityAttrB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Army.getDescriptor(),
          com.yorha.proto.Cave.getDescriptor(),
          com.yorha.proto.City.getDescriptor(),
          com.yorha.proto.ClanResBuilding.getDescriptor(),
          com.yorha.proto.Clan.getDescriptor(),
          com.yorha.proto.DropObject.getDescriptor(),
          com.yorha.proto.EntityAttrOuterClass.getDescriptor(),
          com.yorha.proto.LogisticsPlane.getDescriptor(),
          com.yorha.proto.MapBuilding.getDescriptor(),
          com.yorha.proto.Monster.getDescriptor(),
          com.yorha.proto.Player.getDescriptor(),
          com.yorha.proto.ResBuilding.getDescriptor(),
          com.yorha.proto.SpyPlane.getDescriptor(),
        });
    internal_static_com_yorha_proto_EntityAttrDB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_EntityAttrDB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_EntityAttrDB_descriptor,
        new java.lang.String[] { "EntityId", "EntityType", "ArmyAttr", "CityAttr", "PlayerAttr", "MonsterAttr", "ClanAttr", "DropObjectAttr", "MapBuildingAttr", "SpyPlane", "ResBuildingAttr", "CaveAttr", "LogisticsPlaneAttr", "ClanResBuildingAttr", "EntityAttr", });
    com.yorha.proto.Army.getDescriptor();
    com.yorha.proto.Cave.getDescriptor();
    com.yorha.proto.City.getDescriptor();
    com.yorha.proto.ClanResBuilding.getDescriptor();
    com.yorha.proto.Clan.getDescriptor();
    com.yorha.proto.DropObject.getDescriptor();
    com.yorha.proto.EntityAttrOuterClass.getDescriptor();
    com.yorha.proto.LogisticsPlane.getDescriptor();
    com.yorha.proto.MapBuilding.getDescriptor();
    com.yorha.proto.Monster.getDescriptor();
    com.yorha.proto.Player.getDescriptor();
    com.yorha.proto.ResBuilding.getDescriptor();
    com.yorha.proto.SpyPlane.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/db/documents.proto

package com.yorha.proto;

public final class Documents {
  private Documents() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IdFactoryDBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.IdFactoryDB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * Id段的标记
     * </pre>
     *
     * <code>optional string Key = 1;</code>
     * @return Whether the key field is set.
     */
    boolean hasKey();
    /**
     * <pre>
     * Id段的标记
     * </pre>
     *
     * <code>optional string Key = 1;</code>
     * @return The key.
     */
    java.lang.String getKey();
    /**
     * <pre>
     * Id段的标记
     * </pre>
     *
     * <code>optional string Key = 1;</code>
     * @return The bytes for key.
     */
    com.google.protobuf.ByteString
        getKeyBytes();

    /**
     * <pre>
     * Id段所属zone
     * </pre>
     *
     * <code>optional int32 ZoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * Id段所属zone
     * </pre>
     *
     * <code>optional int32 ZoneId = 2;</code>
     * @return The zoneId.
     */
    int getZoneId();

    /**
     * <pre>
     * Id段的后置id
     * </pre>
     *
     * <code>optional int64 nextId = 3;</code>
     * @return Whether the nextId field is set.
     */
    boolean hasNextId();
    /**
     * <pre>
     * Id段的后置id
     * </pre>
     *
     * <code>optional int64 nextId = 3;</code>
     * @return The nextId.
     */
    long getNextId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.IdFactoryDB}
   */
  public static final class IdFactoryDB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.IdFactoryDB)
      IdFactoryDBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IdFactoryDB.newBuilder() to construct.
    private IdFactoryDB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IdFactoryDB() {
      key_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IdFactoryDB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IdFactoryDB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              key_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              zoneId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              nextId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Documents.internal_static_com_yorha_proto_IdFactoryDB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Documents.internal_static_com_yorha_proto_IdFactoryDB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Documents.IdFactoryDB.class, com.yorha.proto.Documents.IdFactoryDB.Builder.class);
    }

    private int bitField0_;
    public static final int KEY_FIELD_NUMBER = 1;
    private volatile java.lang.Object key_;
    /**
     * <pre>
     * Id段的标记
     * </pre>
     *
     * <code>optional string Key = 1;</code>
     * @return Whether the key field is set.
     */
    @java.lang.Override
    public boolean hasKey() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * Id段的标记
     * </pre>
     *
     * <code>optional string Key = 1;</code>
     * @return The key.
     */
    @java.lang.Override
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          key_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * Id段的标记
     * </pre>
     *
     * <code>optional string Key = 1;</code>
     * @return The bytes for key.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ZONEID_FIELD_NUMBER = 2;
    private int zoneId_;
    /**
     * <pre>
     * Id段所属zone
     * </pre>
     *
     * <code>optional int32 ZoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * Id段所属zone
     * </pre>
     *
     * <code>optional int32 ZoneId = 2;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    public static final int NEXTID_FIELD_NUMBER = 3;
    private long nextId_;
    /**
     * <pre>
     * Id段的后置id
     * </pre>
     *
     * <code>optional int64 nextId = 3;</code>
     * @return Whether the nextId field is set.
     */
    @java.lang.Override
    public boolean hasNextId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * Id段的后置id
     * </pre>
     *
     * <code>optional int64 nextId = 3;</code>
     * @return The nextId.
     */
    @java.lang.Override
    public long getNextId() {
      return nextId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, key_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, zoneId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, nextId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, key_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, zoneId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, nextId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Documents.IdFactoryDB)) {
        return super.equals(obj);
      }
      com.yorha.proto.Documents.IdFactoryDB other = (com.yorha.proto.Documents.IdFactoryDB) obj;

      if (hasKey() != other.hasKey()) return false;
      if (hasKey()) {
        if (!getKey()
            .equals(other.getKey())) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (hasNextId() != other.hasNextId()) return false;
      if (hasNextId()) {
        if (getNextId()
            != other.getNextId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKey()) {
        hash = (37 * hash) + KEY_FIELD_NUMBER;
        hash = (53 * hash) + getKey().hashCode();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      if (hasNextId()) {
        hash = (37 * hash) + NEXTID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getNextId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Documents.IdFactoryDB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Documents.IdFactoryDB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.IdFactoryDB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.IdFactoryDB)
        com.yorha.proto.Documents.IdFactoryDBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Documents.internal_static_com_yorha_proto_IdFactoryDB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Documents.internal_static_com_yorha_proto_IdFactoryDB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Documents.IdFactoryDB.class, com.yorha.proto.Documents.IdFactoryDB.Builder.class);
      }

      // Construct using com.yorha.proto.Documents.IdFactoryDB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        key_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        nextId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Documents.internal_static_com_yorha_proto_IdFactoryDB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Documents.IdFactoryDB getDefaultInstanceForType() {
        return com.yorha.proto.Documents.IdFactoryDB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Documents.IdFactoryDB build() {
        com.yorha.proto.Documents.IdFactoryDB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Documents.IdFactoryDB buildPartial() {
        com.yorha.proto.Documents.IdFactoryDB result = new com.yorha.proto.Documents.IdFactoryDB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.key_ = key_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.nextId_ = nextId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Documents.IdFactoryDB) {
          return mergeFrom((com.yorha.proto.Documents.IdFactoryDB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Documents.IdFactoryDB other) {
        if (other == com.yorha.proto.Documents.IdFactoryDB.getDefaultInstance()) return this;
        if (other.hasKey()) {
          bitField0_ |= 0x00000001;
          key_ = other.key_;
          onChanged();
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        if (other.hasNextId()) {
          setNextId(other.getNextId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Documents.IdFactoryDB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Documents.IdFactoryDB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object key_ = "";
      /**
       * <pre>
       * Id段的标记
       * </pre>
       *
       * <code>optional string Key = 1;</code>
       * @return Whether the key field is set.
       */
      public boolean hasKey() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * Id段的标记
       * </pre>
       *
       * <code>optional string Key = 1;</code>
       * @return The key.
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            key_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * Id段的标记
       * </pre>
       *
       * <code>optional string Key = 1;</code>
       * @return The bytes for key.
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * Id段的标记
       * </pre>
       *
       * <code>optional string Key = 1;</code>
       * @param value The key to set.
       * @return This builder for chaining.
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Id段的标记
       * </pre>
       *
       * <code>optional string Key = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKey() {
        bitField0_ = (bitField0_ & ~0x00000001);
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Id段的标记
       * </pre>
       *
       * <code>optional string Key = 1;</code>
       * @param value The bytes for key to set.
       * @return This builder for chaining.
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        key_ = value;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * Id段所属zone
       * </pre>
       *
       * <code>optional int32 ZoneId = 2;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * Id段所属zone
       * </pre>
       *
       * <code>optional int32 ZoneId = 2;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * Id段所属zone
       * </pre>
       *
       * <code>optional int32 ZoneId = 2;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000002;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Id段所属zone
       * </pre>
       *
       * <code>optional int32 ZoneId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        onChanged();
        return this;
      }

      private long nextId_ ;
      /**
       * <pre>
       * Id段的后置id
       * </pre>
       *
       * <code>optional int64 nextId = 3;</code>
       * @return Whether the nextId field is set.
       */
      @java.lang.Override
      public boolean hasNextId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * Id段的后置id
       * </pre>
       *
       * <code>optional int64 nextId = 3;</code>
       * @return The nextId.
       */
      @java.lang.Override
      public long getNextId() {
        return nextId_;
      }
      /**
       * <pre>
       * Id段的后置id
       * </pre>
       *
       * <code>optional int64 nextId = 3;</code>
       * @param value The nextId to set.
       * @return This builder for chaining.
       */
      public Builder setNextId(long value) {
        bitField0_ |= 0x00000004;
        nextId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Id段的后置id
       * </pre>
       *
       * <code>optional int64 nextId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        nextId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.IdFactoryDB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.IdFactoryDB)
    private static final com.yorha.proto.Documents.IdFactoryDB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Documents.IdFactoryDB();
    }

    public static com.yorha.proto.Documents.IdFactoryDB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IdFactoryDB>
        PARSER = new com.google.protobuf.AbstractParser<IdFactoryDB>() {
      @java.lang.Override
      public IdFactoryDB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IdFactoryDB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IdFactoryDB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IdFactoryDB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Documents.IdFactoryDB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AccountRoleDBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AccountRoleDB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 PlayerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 PlayerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional string OpenId = 2;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string OpenId = 2;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string OpenId = 2;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <pre>
     * 移民需要修改ZoneId
     * </pre>
     *
     * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
     * @return Whether the roleInfo field is set.
     */
    boolean hasRoleInfo();
    /**
     * <pre>
     * 移民需要修改ZoneId
     * </pre>
     *
     * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
     * @return The roleInfo.
     */
    com.yorha.proto.CommonMsg.AccountRole getRoleInfo();
    /**
     * <pre>
     * 移民需要修改ZoneId
     * </pre>
     *
     * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
     */
    com.yorha.proto.CommonMsg.AccountRoleOrBuilder getRoleInfoOrBuilder();

    /**
     * <pre>
     * 是否是星标
     * </pre>
     *
     * <code>optional int32 isStar = 4;</code>
     * @return Whether the isStar field is set.
     */
    boolean hasIsStar();
    /**
     * <pre>
     * 是否是星标
     * </pre>
     *
     * <code>optional int32 isStar = 4;</code>
     * @return The isStar.
     */
    int getIsStar();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AccountRoleDB}
   */
  public static final class AccountRoleDB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AccountRoleDB)
      AccountRoleDBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AccountRoleDB.newBuilder() to construct.
    private AccountRoleDB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AccountRoleDB() {
      openId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AccountRoleDB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AccountRoleDB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              openId_ = bs;
              break;
            }
            case 26: {
              com.yorha.proto.CommonMsg.AccountRole.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = roleInfo_.toBuilder();
              }
              roleInfo_ = input.readMessage(com.yorha.proto.CommonMsg.AccountRole.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(roleInfo_);
                roleInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              isStar_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Documents.internal_static_com_yorha_proto_AccountRoleDB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Documents.internal_static_com_yorha_proto_AccountRoleDB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Documents.AccountRoleDB.class, com.yorha.proto.Documents.AccountRoleDB.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 PlayerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 PlayerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int OPENID_FIELD_NUMBER = 2;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string OpenId = 2;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string OpenId = 2;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string OpenId = 2;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ROLEINFO_FIELD_NUMBER = 3;
    private com.yorha.proto.CommonMsg.AccountRole roleInfo_;
    /**
     * <pre>
     * 移民需要修改ZoneId
     * </pre>
     *
     * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
     * @return Whether the roleInfo field is set.
     */
    @java.lang.Override
    public boolean hasRoleInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 移民需要修改ZoneId
     * </pre>
     *
     * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
     * @return The roleInfo.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.AccountRole getRoleInfo() {
      return roleInfo_ == null ? com.yorha.proto.CommonMsg.AccountRole.getDefaultInstance() : roleInfo_;
    }
    /**
     * <pre>
     * 移民需要修改ZoneId
     * </pre>
     *
     * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.AccountRoleOrBuilder getRoleInfoOrBuilder() {
      return roleInfo_ == null ? com.yorha.proto.CommonMsg.AccountRole.getDefaultInstance() : roleInfo_;
    }

    public static final int ISSTAR_FIELD_NUMBER = 4;
    private int isStar_;
    /**
     * <pre>
     * 是否是星标
     * </pre>
     *
     * <code>optional int32 isStar = 4;</code>
     * @return Whether the isStar field is set.
     */
    @java.lang.Override
    public boolean hasIsStar() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 是否是星标
     * </pre>
     *
     * <code>optional int32 isStar = 4;</code>
     * @return The isStar.
     */
    @java.lang.Override
    public int getIsStar() {
      return isStar_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, openId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getRoleInfo());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, isStar_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, openId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getRoleInfo());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, isStar_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Documents.AccountRoleDB)) {
        return super.equals(obj);
      }
      com.yorha.proto.Documents.AccountRoleDB other = (com.yorha.proto.Documents.AccountRoleDB) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasRoleInfo() != other.hasRoleInfo()) return false;
      if (hasRoleInfo()) {
        if (!getRoleInfo()
            .equals(other.getRoleInfo())) return false;
      }
      if (hasIsStar() != other.hasIsStar()) return false;
      if (hasIsStar()) {
        if (getIsStar()
            != other.getIsStar()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasRoleInfo()) {
        hash = (37 * hash) + ROLEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRoleInfo().hashCode();
      }
      if (hasIsStar()) {
        hash = (37 * hash) + ISSTAR_FIELD_NUMBER;
        hash = (53 * hash) + getIsStar();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Documents.AccountRoleDB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Documents.AccountRoleDB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AccountRoleDB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AccountRoleDB)
        com.yorha.proto.Documents.AccountRoleDBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Documents.internal_static_com_yorha_proto_AccountRoleDB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Documents.internal_static_com_yorha_proto_AccountRoleDB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Documents.AccountRoleDB.class, com.yorha.proto.Documents.AccountRoleDB.Builder.class);
      }

      // Construct using com.yorha.proto.Documents.AccountRoleDB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRoleInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        if (roleInfoBuilder_ == null) {
          roleInfo_ = null;
        } else {
          roleInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        isStar_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Documents.internal_static_com_yorha_proto_AccountRoleDB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Documents.AccountRoleDB getDefaultInstanceForType() {
        return com.yorha.proto.Documents.AccountRoleDB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Documents.AccountRoleDB build() {
        com.yorha.proto.Documents.AccountRoleDB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Documents.AccountRoleDB buildPartial() {
        com.yorha.proto.Documents.AccountRoleDB result = new com.yorha.proto.Documents.AccountRoleDB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (roleInfoBuilder_ == null) {
            result.roleInfo_ = roleInfo_;
          } else {
            result.roleInfo_ = roleInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.isStar_ = isStar_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Documents.AccountRoleDB) {
          return mergeFrom((com.yorha.proto.Documents.AccountRoleDB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Documents.AccountRoleDB other) {
        if (other == com.yorha.proto.Documents.AccountRoleDB.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000002;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasRoleInfo()) {
          mergeRoleInfo(other.getRoleInfo());
        }
        if (other.hasIsStar()) {
          setIsStar(other.getIsStar());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Documents.AccountRoleDB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Documents.AccountRoleDB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 PlayerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 PlayerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 PlayerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 PlayerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string OpenId = 2;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string OpenId = 2;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string OpenId = 2;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string OpenId = 2;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string OpenId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string OpenId = 2;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        openId_ = value;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.AccountRole roleInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.AccountRole, com.yorha.proto.CommonMsg.AccountRole.Builder, com.yorha.proto.CommonMsg.AccountRoleOrBuilder> roleInfoBuilder_;
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       * @return Whether the roleInfo field is set.
       */
      public boolean hasRoleInfo() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       * @return The roleInfo.
       */
      public com.yorha.proto.CommonMsg.AccountRole getRoleInfo() {
        if (roleInfoBuilder_ == null) {
          return roleInfo_ == null ? com.yorha.proto.CommonMsg.AccountRole.getDefaultInstance() : roleInfo_;
        } else {
          return roleInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       */
      public Builder setRoleInfo(com.yorha.proto.CommonMsg.AccountRole value) {
        if (roleInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          roleInfo_ = value;
          onChanged();
        } else {
          roleInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       */
      public Builder setRoleInfo(
          com.yorha.proto.CommonMsg.AccountRole.Builder builderForValue) {
        if (roleInfoBuilder_ == null) {
          roleInfo_ = builderForValue.build();
          onChanged();
        } else {
          roleInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       */
      public Builder mergeRoleInfo(com.yorha.proto.CommonMsg.AccountRole value) {
        if (roleInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              roleInfo_ != null &&
              roleInfo_ != com.yorha.proto.CommonMsg.AccountRole.getDefaultInstance()) {
            roleInfo_ =
              com.yorha.proto.CommonMsg.AccountRole.newBuilder(roleInfo_).mergeFrom(value).buildPartial();
          } else {
            roleInfo_ = value;
          }
          onChanged();
        } else {
          roleInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       */
      public Builder clearRoleInfo() {
        if (roleInfoBuilder_ == null) {
          roleInfo_ = null;
          onChanged();
        } else {
          roleInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       */
      public com.yorha.proto.CommonMsg.AccountRole.Builder getRoleInfoBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getRoleInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       */
      public com.yorha.proto.CommonMsg.AccountRoleOrBuilder getRoleInfoOrBuilder() {
        if (roleInfoBuilder_ != null) {
          return roleInfoBuilder_.getMessageOrBuilder();
        } else {
          return roleInfo_ == null ?
              com.yorha.proto.CommonMsg.AccountRole.getDefaultInstance() : roleInfo_;
        }
      }
      /**
       * <pre>
       * 移民需要修改ZoneId
       * </pre>
       *
       * <code>optional .com.yorha.proto.AccountRole roleInfo = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.AccountRole, com.yorha.proto.CommonMsg.AccountRole.Builder, com.yorha.proto.CommonMsg.AccountRoleOrBuilder> 
          getRoleInfoFieldBuilder() {
        if (roleInfoBuilder_ == null) {
          roleInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.AccountRole, com.yorha.proto.CommonMsg.AccountRole.Builder, com.yorha.proto.CommonMsg.AccountRoleOrBuilder>(
                  getRoleInfo(),
                  getParentForChildren(),
                  isClean());
          roleInfo_ = null;
        }
        return roleInfoBuilder_;
      }

      private int isStar_ ;
      /**
       * <pre>
       * 是否是星标
       * </pre>
       *
       * <code>optional int32 isStar = 4;</code>
       * @return Whether the isStar field is set.
       */
      @java.lang.Override
      public boolean hasIsStar() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 是否是星标
       * </pre>
       *
       * <code>optional int32 isStar = 4;</code>
       * @return The isStar.
       */
      @java.lang.Override
      public int getIsStar() {
        return isStar_;
      }
      /**
       * <pre>
       * 是否是星标
       * </pre>
       *
       * <code>optional int32 isStar = 4;</code>
       * @param value The isStar to set.
       * @return This builder for chaining.
       */
      public Builder setIsStar(int value) {
        bitField0_ |= 0x00000008;
        isStar_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否是星标
       * </pre>
       *
       * <code>optional int32 isStar = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsStar() {
        bitField0_ = (bitField0_ & ~0x00000008);
        isStar_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AccountRoleDB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AccountRoleDB)
    private static final com.yorha.proto.Documents.AccountRoleDB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Documents.AccountRoleDB();
    }

    public static com.yorha.proto.Documents.AccountRoleDB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AccountRoleDB>
        PARSER = new com.google.protobuf.AbstractParser<AccountRoleDB>() {
      @java.lang.Override
      public AccountRoleDB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AccountRoleDB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AccountRoleDB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AccountRoleDB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Documents.AccountRoleDB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_IdFactoryDB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_IdFactoryDB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AccountRoleDB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AccountRoleDB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037ss_proto/gen/db/documents.proto\022\017com.y" +
      "orha.proto\032\031ss_proto/gen/option.proto\032$s" +
      "s_proto/gen/common/common_msg.proto\"Q\n\013I" +
      "dFactoryDB\022\013\n\003Key\030\001 \001(\t\022\016\n\006ZoneId\030\002 \001(\005\022" +
      "\016\n\006nextId\030\003 \001(\003:\025\312\265\030\021index1:Key,ZoneId\"\220" +
      "\001\n\rAccountRoleDB\022\020\n\010PlayerId\030\001 \001(\003\022\016\n\006Op" +
      "enId\030\002 \001(\t\022.\n\010roleInfo\030\003 \001(\0132\034.com.yorha" +
      ".proto.AccountRole\022\016\n\006isStar\030\004 \001(\005:\035\302\265\030\010" +
      "PlayerId\312\265\030\rindex1:OpenIdB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Option.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_IdFactoryDB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_IdFactoryDB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_IdFactoryDB_descriptor,
        new java.lang.String[] { "Key", "ZoneId", "NextId", });
    internal_static_com_yorha_proto_AccountRoleDB_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_AccountRoleDB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AccountRoleDB_descriptor,
        new java.lang.String[] { "PlayerId", "OpenId", "RoleInfo", "IsStar", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(com.yorha.proto.Option.documentId);
    registry.add(com.yorha.proto.Option.documentIndex);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    com.yorha.proto.Option.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/table/table_common.proto

package com.yorha.proto;

public final class TableCommon {
  private TableCommon() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface KVStoreValueOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KVStoreValue)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string strValue = 1;</code>
     * @return Whether the strValue field is set.
     */
    boolean hasStrValue();
    /**
     * <code>string strValue = 1;</code>
     * @return The strValue.
     */
    java.lang.String getStrValue();
    /**
     * <code>string strValue = 1;</code>
     * @return The bytes for strValue.
     */
    com.google.protobuf.ByteString
        getStrValueBytes();

    /**
     * <code>int64 longValue = 2;</code>
     * @return Whether the longValue field is set.
     */
    boolean hasLongValue();
    /**
     * <code>int64 longValue = 2;</code>
     * @return The longValue.
     */
    long getLongValue();

    /**
     * <code>int32 intValue = 3;</code>
     * @return Whether the intValue field is set.
     */
    boolean hasIntValue();
    /**
     * <code>int32 intValue = 3;</code>
     * @return The intValue.
     */
    int getIntValue();

    /**
     * <code>bytes bytesValue = 4;</code>
     * @return Whether the bytesValue field is set.
     */
    boolean hasBytesValue();
    /**
     * <code>bytes bytesValue = 4;</code>
     * @return The bytesValue.
     */
    com.google.protobuf.ByteString getBytesValue();

    public com.yorha.proto.TableCommon.KVStoreValue.ValueCase getValueCase();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KVStoreValue}
   */
  public static final class KVStoreValue extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KVStoreValue)
      KVStoreValueOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KVStoreValue.newBuilder() to construct.
    private KVStoreValue(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KVStoreValue() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KVStoreValue();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KVStoreValue(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              valueCase_ = 1;
              value_ = bs;
              break;
            }
            case 16: {
              valueCase_ = 2;
              value_ = input.readInt64();
              break;
            }
            case 24: {
              valueCase_ = 3;
              value_ = input.readInt32();
              break;
            }
            case 34: {
              valueCase_ = 4;
              value_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.TableCommon.internal_static_com_yorha_proto_KVStoreValue_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.TableCommon.internal_static_com_yorha_proto_KVStoreValue_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.TableCommon.KVStoreValue.class, com.yorha.proto.TableCommon.KVStoreValue.Builder.class);
    }

    private int bitField0_;
    private int valueCase_ = 0;
    private java.lang.Object value_;
    public enum ValueCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      STRVALUE(1),
      LONGVALUE(2),
      INTVALUE(3),
      BYTESVALUE(4),
      VALUE_NOT_SET(0);
      private final int value;
      private ValueCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static ValueCase valueOf(int value) {
        return forNumber(value);
      }

      public static ValueCase forNumber(int value) {
        switch (value) {
          case 1: return STRVALUE;
          case 2: return LONGVALUE;
          case 3: return INTVALUE;
          case 4: return BYTESVALUE;
          case 0: return VALUE_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public ValueCase
    getValueCase() {
      return ValueCase.forNumber(
          valueCase_);
    }

    public static final int STRVALUE_FIELD_NUMBER = 1;
    /**
     * <code>string strValue = 1;</code>
     * @return Whether the strValue field is set.
     */
    public boolean hasStrValue() {
      return valueCase_ == 1;
    }
    /**
     * <code>string strValue = 1;</code>
     * @return The strValue.
     */
    public java.lang.String getStrValue() {
      java.lang.Object ref = "";
      if (valueCase_ == 1) {
        ref = value_;
      }
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8() && (valueCase_ == 1)) {
          value_ = s;
        }
        return s;
      }
    }
    /**
     * <code>string strValue = 1;</code>
     * @return The bytes for strValue.
     */
    public com.google.protobuf.ByteString
        getStrValueBytes() {
      java.lang.Object ref = "";
      if (valueCase_ == 1) {
        ref = value_;
      }
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        if (valueCase_ == 1) {
          value_ = b;
        }
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LONGVALUE_FIELD_NUMBER = 2;
    /**
     * <code>int64 longValue = 2;</code>
     * @return Whether the longValue field is set.
     */
    @java.lang.Override
    public boolean hasLongValue() {
      return valueCase_ == 2;
    }
    /**
     * <code>int64 longValue = 2;</code>
     * @return The longValue.
     */
    @java.lang.Override
    public long getLongValue() {
      if (valueCase_ == 2) {
        return (java.lang.Long) value_;
      }
      return 0L;
    }

    public static final int INTVALUE_FIELD_NUMBER = 3;
    /**
     * <code>int32 intValue = 3;</code>
     * @return Whether the intValue field is set.
     */
    @java.lang.Override
    public boolean hasIntValue() {
      return valueCase_ == 3;
    }
    /**
     * <code>int32 intValue = 3;</code>
     * @return The intValue.
     */
    @java.lang.Override
    public int getIntValue() {
      if (valueCase_ == 3) {
        return (java.lang.Integer) value_;
      }
      return 0;
    }

    public static final int BYTESVALUE_FIELD_NUMBER = 4;
    /**
     * <code>bytes bytesValue = 4;</code>
     * @return Whether the bytesValue field is set.
     */
    @java.lang.Override
    public boolean hasBytesValue() {
      return valueCase_ == 4;
    }
    /**
     * <code>bytes bytesValue = 4;</code>
     * @return The bytesValue.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBytesValue() {
      if (valueCase_ == 4) {
        return (com.google.protobuf.ByteString) value_;
      }
      return com.google.protobuf.ByteString.EMPTY;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (valueCase_ == 1) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, value_);
      }
      if (valueCase_ == 2) {
        output.writeInt64(
            2, (long)((java.lang.Long) value_));
      }
      if (valueCase_ == 3) {
        output.writeInt32(
            3, (int)((java.lang.Integer) value_));
      }
      if (valueCase_ == 4) {
        output.writeBytes(
            4, (com.google.protobuf.ByteString) value_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (valueCase_ == 1) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, value_);
      }
      if (valueCase_ == 2) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(
              2, (long)((java.lang.Long) value_));
      }
      if (valueCase_ == 3) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(
              3, (int)((java.lang.Integer) value_));
      }
      if (valueCase_ == 4) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(
              4, (com.google.protobuf.ByteString) value_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.TableCommon.KVStoreValue)) {
        return super.equals(obj);
      }
      com.yorha.proto.TableCommon.KVStoreValue other = (com.yorha.proto.TableCommon.KVStoreValue) obj;

      if (!getValueCase().equals(other.getValueCase())) return false;
      switch (valueCase_) {
        case 1:
          if (!getStrValue()
              .equals(other.getStrValue())) return false;
          break;
        case 2:
          if (getLongValue()
              != other.getLongValue()) return false;
          break;
        case 3:
          if (getIntValue()
              != other.getIntValue()) return false;
          break;
        case 4:
          if (!getBytesValue()
              .equals(other.getBytesValue())) return false;
          break;
        case 0:
        default:
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      switch (valueCase_) {
        case 1:
          hash = (37 * hash) + STRVALUE_FIELD_NUMBER;
          hash = (53 * hash) + getStrValue().hashCode();
          break;
        case 2:
          hash = (37 * hash) + LONGVALUE_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getLongValue());
          break;
        case 3:
          hash = (37 * hash) + INTVALUE_FIELD_NUMBER;
          hash = (53 * hash) + getIntValue();
          break;
        case 4:
          hash = (37 * hash) + BYTESVALUE_FIELD_NUMBER;
          hash = (53 * hash) + getBytesValue().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.TableCommon.KVStoreValue parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.TableCommon.KVStoreValue prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KVStoreValue}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KVStoreValue)
        com.yorha.proto.TableCommon.KVStoreValueOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.TableCommon.internal_static_com_yorha_proto_KVStoreValue_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.TableCommon.internal_static_com_yorha_proto_KVStoreValue_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.TableCommon.KVStoreValue.class, com.yorha.proto.TableCommon.KVStoreValue.Builder.class);
      }

      // Construct using com.yorha.proto.TableCommon.KVStoreValue.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        valueCase_ = 0;
        value_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.TableCommon.internal_static_com_yorha_proto_KVStoreValue_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.TableCommon.KVStoreValue getDefaultInstanceForType() {
        return com.yorha.proto.TableCommon.KVStoreValue.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.TableCommon.KVStoreValue build() {
        com.yorha.proto.TableCommon.KVStoreValue result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.TableCommon.KVStoreValue buildPartial() {
        com.yorha.proto.TableCommon.KVStoreValue result = new com.yorha.proto.TableCommon.KVStoreValue(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (valueCase_ == 1) {
          result.value_ = value_;
        }
        if (valueCase_ == 2) {
          result.value_ = value_;
        }
        if (valueCase_ == 3) {
          result.value_ = value_;
        }
        if (valueCase_ == 4) {
          result.value_ = value_;
        }
        result.bitField0_ = to_bitField0_;
        result.valueCase_ = valueCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.TableCommon.KVStoreValue) {
          return mergeFrom((com.yorha.proto.TableCommon.KVStoreValue)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.TableCommon.KVStoreValue other) {
        if (other == com.yorha.proto.TableCommon.KVStoreValue.getDefaultInstance()) return this;
        switch (other.getValueCase()) {
          case STRVALUE: {
            valueCase_ = 1;
            value_ = other.value_;
            onChanged();
            break;
          }
          case LONGVALUE: {
            setLongValue(other.getLongValue());
            break;
          }
          case INTVALUE: {
            setIntValue(other.getIntValue());
            break;
          }
          case BYTESVALUE: {
            setBytesValue(other.getBytesValue());
            break;
          }
          case VALUE_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.TableCommon.KVStoreValue parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.TableCommon.KVStoreValue) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int valueCase_ = 0;
      private java.lang.Object value_;
      public ValueCase
          getValueCase() {
        return ValueCase.forNumber(
            valueCase_);
      }

      public Builder clearValue() {
        valueCase_ = 0;
        value_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      /**
       * <code>string strValue = 1;</code>
       * @return Whether the strValue field is set.
       */
      @java.lang.Override
      public boolean hasStrValue() {
        return valueCase_ == 1;
      }
      /**
       * <code>string strValue = 1;</code>
       * @return The strValue.
       */
      @java.lang.Override
      public java.lang.String getStrValue() {
        java.lang.Object ref = "";
        if (valueCase_ == 1) {
          ref = value_;
        }
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (valueCase_ == 1) {
            if (bs.isValidUtf8()) {
              value_ = s;
            }
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string strValue = 1;</code>
       * @return The bytes for strValue.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getStrValueBytes() {
        java.lang.Object ref = "";
        if (valueCase_ == 1) {
          ref = value_;
        }
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          if (valueCase_ == 1) {
            value_ = b;
          }
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string strValue = 1;</code>
       * @param value The strValue to set.
       * @return This builder for chaining.
       */
      public Builder setStrValue(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  valueCase_ = 1;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string strValue = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStrValue() {
        if (valueCase_ == 1) {
          valueCase_ = 0;
          value_ = null;
          onChanged();
        }
        return this;
      }
      /**
       * <code>string strValue = 1;</code>
       * @param value The bytes for strValue to set.
       * @return This builder for chaining.
       */
      public Builder setStrValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  valueCase_ = 1;
        value_ = value;
        onChanged();
        return this;
      }

      /**
       * <code>int64 longValue = 2;</code>
       * @return Whether the longValue field is set.
       */
      public boolean hasLongValue() {
        return valueCase_ == 2;
      }
      /**
       * <code>int64 longValue = 2;</code>
       * @return The longValue.
       */
      public long getLongValue() {
        if (valueCase_ == 2) {
          return (java.lang.Long) value_;
        }
        return 0L;
      }
      /**
       * <code>int64 longValue = 2;</code>
       * @param value The longValue to set.
       * @return This builder for chaining.
       */
      public Builder setLongValue(long value) {
        valueCase_ = 2;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 longValue = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLongValue() {
        if (valueCase_ == 2) {
          valueCase_ = 0;
          value_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>int32 intValue = 3;</code>
       * @return Whether the intValue field is set.
       */
      public boolean hasIntValue() {
        return valueCase_ == 3;
      }
      /**
       * <code>int32 intValue = 3;</code>
       * @return The intValue.
       */
      public int getIntValue() {
        if (valueCase_ == 3) {
          return (java.lang.Integer) value_;
        }
        return 0;
      }
      /**
       * <code>int32 intValue = 3;</code>
       * @param value The intValue to set.
       * @return This builder for chaining.
       */
      public Builder setIntValue(int value) {
        valueCase_ = 3;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 intValue = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIntValue() {
        if (valueCase_ == 3) {
          valueCase_ = 0;
          value_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>bytes bytesValue = 4;</code>
       * @return Whether the bytesValue field is set.
       */
      public boolean hasBytesValue() {
        return valueCase_ == 4;
      }
      /**
       * <code>bytes bytesValue = 4;</code>
       * @return The bytesValue.
       */
      public com.google.protobuf.ByteString getBytesValue() {
        if (valueCase_ == 4) {
          return (com.google.protobuf.ByteString) value_;
        }
        return com.google.protobuf.ByteString.EMPTY;
      }
      /**
       * <code>bytes bytesValue = 4;</code>
       * @param value The bytesValue to set.
       * @return This builder for chaining.
       */
      public Builder setBytesValue(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  valueCase_ = 4;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bytes bytesValue = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBytesValue() {
        if (valueCase_ == 4) {
          valueCase_ = 0;
          value_ = null;
          onChanged();
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KVStoreValue)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KVStoreValue)
    private static final com.yorha.proto.TableCommon.KVStoreValue DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.TableCommon.KVStoreValue();
    }

    public static com.yorha.proto.TableCommon.KVStoreValue getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KVStoreValue>
        PARSER = new com.google.protobuf.AbstractParser<KVStoreValue>() {
      @java.lang.Override
      public KVStoreValue parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KVStoreValue(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KVStoreValue> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KVStoreValue> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.TableCommon.KVStoreValue getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KVStoreValue_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KVStoreValue_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n%ss_proto/gen/table/table_common.proto\022" +
      "\017com.yorha.proto\"j\n\014KVStoreValue\022\022\n\010strV" +
      "alue\030\001 \001(\tH\000\022\023\n\tlongValue\030\002 \001(\003H\000\022\022\n\010int" +
      "Value\030\003 \001(\005H\000\022\024\n\nbytesValue\030\004 \001(\014H\000B\007\n\005v" +
      "alueB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_KVStoreValue_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_KVStoreValue_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KVStoreValue_descriptor,
        new java.lang.String[] { "StrValue", "LongValue", "IntValue", "BytesValue", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

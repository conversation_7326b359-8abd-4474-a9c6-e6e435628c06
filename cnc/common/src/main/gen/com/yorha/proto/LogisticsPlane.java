// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/logisticsPlane/logisticsPlane.proto

package com.yorha.proto;

public final class LogisticsPlane {
  private LogisticsPlane() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface LogisticsPlaneEntityOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.LogisticsPlaneEntity)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return The ownerId.
     */
    long getOwnerId();

    /**
     * <code>optional .com.yorha.proto.Move move = 2;</code>
     * @return Whether the move field is set.
     */
    boolean hasMove();
    /**
     * <code>optional .com.yorha.proto.Move move = 2;</code>
     * @return The move.
     */
    com.yorha.proto.Struct.Move getMove();
    /**
     * <code>optional .com.yorha.proto.Move move = 2;</code>
     */
    com.yorha.proto.Struct.MoveOrBuilder getMoveOrBuilder();

    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 玩家名
     * </pre>
     *
     * <code>optional string ownerName = 4;</code>
     * @return Whether the ownerName field is set.
     */
    boolean hasOwnerName();
    /**
     * <pre>
     * 玩家名
     * </pre>
     *
     * <code>optional string ownerName = 4;</code>
     * @return The ownerName.
     */
    java.lang.String getOwnerName();
    /**
     * <pre>
     * 玩家名
     * </pre>
     *
     * <code>optional string ownerName = 4;</code>
     * @return The bytes for ownerName.
     */
    com.google.protobuf.ByteString
        getOwnerNameBytes();

    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 5;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 5;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 6;</code>
     * @return Whether the briefClanName field is set.
     */
    boolean hasBriefClanName();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 6;</code>
     * @return The briefClanName.
     */
    java.lang.String getBriefClanName();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 6;</code>
     * @return The bytes for briefClanName.
     */
    com.google.protobuf.ByteString
        getBriefClanNameBytes();

    /**
     * <pre>
     * 当前物流飞机状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.LogisticsPlaneState state = 7;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <pre>
     * 当前物流飞机状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.LogisticsPlaneState state = 7;</code>
     * @return The state.
     */
    com.yorha.proto.CommonEnum.LogisticsPlaneState getState();

    /**
     * <pre>
     * 进入状态的时间戳
     * </pre>
     *
     * <code>optional int64 enterStateTs = 8;</code>
     * @return Whether the enterStateTs field is set.
     */
    boolean hasEnterStateTs();
    /**
     * <pre>
     * 进入状态的时间戳
     * </pre>
     *
     * <code>optional int64 enterStateTs = 8;</code>
     * @return The enterStateTs.
     */
    long getEnterStateTs();

    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 9;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 9;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * <pre>
   * 物流飞机，用于资源援助
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.LogisticsPlaneEntity}
   */
  public static final class LogisticsPlaneEntity extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.LogisticsPlaneEntity)
      LogisticsPlaneEntityOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LogisticsPlaneEntity.newBuilder() to construct.
    private LogisticsPlaneEntity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LogisticsPlaneEntity() {
      ownerName_ = "";
      briefClanName_ = "";
      state_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LogisticsPlaneEntity();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private LogisticsPlaneEntity(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ownerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.Struct.Move.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = move_.toBuilder();
              }
              move_ = input.readMessage(com.yorha.proto.Struct.Move.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(move_);
                move_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              templateId_ = input.readInt32();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              ownerName_ = bs;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              clanId_ = input.readInt64();
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              briefClanName_ = bs;
              break;
            }
            case 56: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.LogisticsPlaneState value = com.yorha.proto.CommonEnum.LogisticsPlaneState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(7, rawValue);
              } else {
                bitField0_ |= 0x00000040;
                state_ = rawValue;
              }
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              enterStateTs_ = input.readInt64();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.LogisticsPlane.internal_static_com_yorha_proto_LogisticsPlaneEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.LogisticsPlane.internal_static_com_yorha_proto_LogisticsPlaneEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.class, com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.Builder.class);
    }

    private int bitField0_;
    public static final int OWNERID_FIELD_NUMBER = 1;
    private long ownerId_;
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    public static final int MOVE_FIELD_NUMBER = 2;
    private com.yorha.proto.Struct.Move move_;
    /**
     * <code>optional .com.yorha.proto.Move move = 2;</code>
     * @return Whether the move field is set.
     */
    @java.lang.Override
    public boolean hasMove() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Move move = 2;</code>
     * @return The move.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Move getMove() {
      return move_ == null ? com.yorha.proto.Struct.Move.getDefaultInstance() : move_;
    }
    /**
     * <code>optional .com.yorha.proto.Move move = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.MoveOrBuilder getMoveOrBuilder() {
      return move_ == null ? com.yorha.proto.Struct.Move.getDefaultInstance() : move_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 3;
    private int templateId_;
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int OWNERNAME_FIELD_NUMBER = 4;
    private volatile java.lang.Object ownerName_;
    /**
     * <pre>
     * 玩家名
     * </pre>
     *
     * <code>optional string ownerName = 4;</code>
     * @return Whether the ownerName field is set.
     */
    @java.lang.Override
    public boolean hasOwnerName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 玩家名
     * </pre>
     *
     * <code>optional string ownerName = 4;</code>
     * @return The ownerName.
     */
    @java.lang.Override
    public java.lang.String getOwnerName() {
      java.lang.Object ref = ownerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ownerName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 玩家名
     * </pre>
     *
     * <code>optional string ownerName = 4;</code>
     * @return The bytes for ownerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOwnerNameBytes() {
      java.lang.Object ref = ownerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ownerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLANID_FIELD_NUMBER = 5;
    private long clanId_;
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 5;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 5;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int BRIEFCLANNAME_FIELD_NUMBER = 6;
    private volatile java.lang.Object briefClanName_;
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 6;</code>
     * @return Whether the briefClanName field is set.
     */
    @java.lang.Override
    public boolean hasBriefClanName() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 6;</code>
     * @return The briefClanName.
     */
    @java.lang.Override
    public java.lang.String getBriefClanName() {
      java.lang.Object ref = briefClanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          briefClanName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 6;</code>
     * @return The bytes for briefClanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBriefClanNameBytes() {
      java.lang.Object ref = briefClanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        briefClanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int STATE_FIELD_NUMBER = 7;
    private int state_;
    /**
     * <pre>
     * 当前物流飞机状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.LogisticsPlaneState state = 7;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override public boolean hasState() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 当前物流飞机状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.LogisticsPlaneState state = 7;</code>
     * @return The state.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.LogisticsPlaneState getState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.LogisticsPlaneState result = com.yorha.proto.CommonEnum.LogisticsPlaneState.valueOf(state_);
      return result == null ? com.yorha.proto.CommonEnum.LogisticsPlaneState.LPS_NONE : result;
    }

    public static final int ENTERSTATETS_FIELD_NUMBER = 8;
    private long enterStateTs_;
    /**
     * <pre>
     * 进入状态的时间戳
     * </pre>
     *
     * <code>optional int64 enterStateTs = 8;</code>
     * @return Whether the enterStateTs field is set.
     */
    @java.lang.Override
    public boolean hasEnterStateTs() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 进入状态的时间戳
     * </pre>
     *
     * <code>optional int64 enterStateTs = 8;</code>
     * @return The enterStateTs.
     */
    @java.lang.Override
    public long getEnterStateTs() {
      return enterStateTs_;
    }

    public static final int ZONEID_FIELD_NUMBER = 9;
    private int zoneId_;
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 9;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 9;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, ownerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getMove());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, templateId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, ownerName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, clanId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, briefClanName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeEnum(7, state_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt64(8, enterStateTs_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt32(9, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, ownerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getMove());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, templateId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, ownerName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, clanId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, briefClanName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(7, state_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, enterStateTs_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity)) {
        return super.equals(obj);
      }
      com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity other = (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) obj;

      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (hasMove() != other.hasMove()) return false;
      if (hasMove()) {
        if (!getMove()
            .equals(other.getMove())) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasOwnerName() != other.hasOwnerName()) return false;
      if (hasOwnerName()) {
        if (!getOwnerName()
            .equals(other.getOwnerName())) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasBriefClanName() != other.hasBriefClanName()) return false;
      if (hasBriefClanName()) {
        if (!getBriefClanName()
            .equals(other.getBriefClanName())) return false;
      }
      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (state_ != other.state_) return false;
      }
      if (hasEnterStateTs() != other.hasEnterStateTs()) return false;
      if (hasEnterStateTs()) {
        if (getEnterStateTs()
            != other.getEnterStateTs()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      if (hasMove()) {
        hash = (37 * hash) + MOVE_FIELD_NUMBER;
        hash = (53 * hash) + getMove().hashCode();
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasOwnerName()) {
        hash = (37 * hash) + OWNERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getOwnerName().hashCode();
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasBriefClanName()) {
        hash = (37 * hash) + BRIEFCLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getBriefClanName().hashCode();
      }
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + state_;
      }
      if (hasEnterStateTs()) {
        hash = (37 * hash) + ENTERSTATETS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEnterStateTs());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 物流飞机，用于资源援助
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.LogisticsPlaneEntity}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.LogisticsPlaneEntity)
        com.yorha.proto.LogisticsPlane.LogisticsPlaneEntityOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.LogisticsPlane.internal_static_com_yorha_proto_LogisticsPlaneEntity_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.LogisticsPlane.internal_static_com_yorha_proto_LogisticsPlaneEntity_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.class, com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.Builder.class);
      }

      // Construct using com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMoveFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (moveBuilder_ == null) {
          move_ = null;
        } else {
          moveBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        ownerName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        briefClanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        state_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        enterStateTs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000080);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.LogisticsPlane.internal_static_com_yorha_proto_LogisticsPlaneEntity_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity getDefaultInstanceForType() {
        return com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity build() {
        com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity buildPartial() {
        com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity result = new com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (moveBuilder_ == null) {
            result.move_ = move_;
          } else {
            result.move_ = moveBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.ownerName_ = ownerName_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.briefClanName_ = briefClanName_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.state_ = state_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.enterStateTs_ = enterStateTs_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000100;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) {
          return mergeFrom((com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity other) {
        if (other == com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity.getDefaultInstance()) return this;
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        if (other.hasMove()) {
          mergeMove(other.getMove());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasOwnerName()) {
          bitField0_ |= 0x00000008;
          ownerName_ = other.ownerName_;
          onChanged();
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasBriefClanName()) {
          bitField0_ |= 0x00000020;
          briefClanName_ = other.briefClanName_;
          onChanged();
        }
        if (other.hasState()) {
          setState(other.getState());
        }
        if (other.hasEnterStateTs()) {
          setEnterStateTs(other.getEnterStateTs());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long ownerId_ ;
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000001;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ownerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Move move_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Move, com.yorha.proto.Struct.Move.Builder, com.yorha.proto.Struct.MoveOrBuilder> moveBuilder_;
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       * @return Whether the move field is set.
       */
      public boolean hasMove() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       * @return The move.
       */
      public com.yorha.proto.Struct.Move getMove() {
        if (moveBuilder_ == null) {
          return move_ == null ? com.yorha.proto.Struct.Move.getDefaultInstance() : move_;
        } else {
          return moveBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       */
      public Builder setMove(com.yorha.proto.Struct.Move value) {
        if (moveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          move_ = value;
          onChanged();
        } else {
          moveBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       */
      public Builder setMove(
          com.yorha.proto.Struct.Move.Builder builderForValue) {
        if (moveBuilder_ == null) {
          move_ = builderForValue.build();
          onChanged();
        } else {
          moveBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       */
      public Builder mergeMove(com.yorha.proto.Struct.Move value) {
        if (moveBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              move_ != null &&
              move_ != com.yorha.proto.Struct.Move.getDefaultInstance()) {
            move_ =
              com.yorha.proto.Struct.Move.newBuilder(move_).mergeFrom(value).buildPartial();
          } else {
            move_ = value;
          }
          onChanged();
        } else {
          moveBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       */
      public Builder clearMove() {
        if (moveBuilder_ == null) {
          move_ = null;
          onChanged();
        } else {
          moveBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       */
      public com.yorha.proto.Struct.Move.Builder getMoveBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getMoveFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       */
      public com.yorha.proto.Struct.MoveOrBuilder getMoveOrBuilder() {
        if (moveBuilder_ != null) {
          return moveBuilder_.getMessageOrBuilder();
        } else {
          return move_ == null ?
              com.yorha.proto.Struct.Move.getDefaultInstance() : move_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Move move = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Move, com.yorha.proto.Struct.Move.Builder, com.yorha.proto.Struct.MoveOrBuilder> 
          getMoveFieldBuilder() {
        if (moveBuilder_ == null) {
          moveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Move, com.yorha.proto.Struct.Move.Builder, com.yorha.proto.Struct.MoveOrBuilder>(
                  getMove(),
                  getParentForChildren(),
                  isClean());
          move_ = null;
        }
        return moveBuilder_;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000004;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object ownerName_ = "";
      /**
       * <pre>
       * 玩家名
       * </pre>
       *
       * <code>optional string ownerName = 4;</code>
       * @return Whether the ownerName field is set.
       */
      public boolean hasOwnerName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 玩家名
       * </pre>
       *
       * <code>optional string ownerName = 4;</code>
       * @return The ownerName.
       */
      public java.lang.String getOwnerName() {
        java.lang.Object ref = ownerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ownerName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 玩家名
       * </pre>
       *
       * <code>optional string ownerName = 4;</code>
       * @return The bytes for ownerName.
       */
      public com.google.protobuf.ByteString
          getOwnerNameBytes() {
        java.lang.Object ref = ownerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ownerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 玩家名
       * </pre>
       *
       * <code>optional string ownerName = 4;</code>
       * @param value The ownerName to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        ownerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家名
       * </pre>
       *
       * <code>optional string ownerName = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        ownerName_ = getDefaultInstance().getOwnerName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家名
       * </pre>
       *
       * <code>optional string ownerName = 4;</code>
       * @param value The bytes for ownerName to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        ownerName_ = value;
        onChanged();
        return this;
      }

      private long clanId_ ;
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 5;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 5;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 5;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000010;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object briefClanName_ = "";
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 6;</code>
       * @return Whether the briefClanName field is set.
       */
      public boolean hasBriefClanName() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 6;</code>
       * @return The briefClanName.
       */
      public java.lang.String getBriefClanName() {
        java.lang.Object ref = briefClanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            briefClanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 6;</code>
       * @return The bytes for briefClanName.
       */
      public com.google.protobuf.ByteString
          getBriefClanNameBytes() {
        java.lang.Object ref = briefClanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          briefClanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 6;</code>
       * @param value The briefClanName to set.
       * @return This builder for chaining.
       */
      public Builder setBriefClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        briefClanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearBriefClanName() {
        bitField0_ = (bitField0_ & ~0x00000020);
        briefClanName_ = getDefaultInstance().getBriefClanName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 6;</code>
       * @param value The bytes for briefClanName to set.
       * @return This builder for chaining.
       */
      public Builder setBriefClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        briefClanName_ = value;
        onChanged();
        return this;
      }

      private int state_ = 0;
      /**
       * <pre>
       * 当前物流飞机状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.LogisticsPlaneState state = 7;</code>
       * @return Whether the state field is set.
       */
      @java.lang.Override public boolean hasState() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 当前物流飞机状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.LogisticsPlaneState state = 7;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.LogisticsPlaneState getState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.LogisticsPlaneState result = com.yorha.proto.CommonEnum.LogisticsPlaneState.valueOf(state_);
        return result == null ? com.yorha.proto.CommonEnum.LogisticsPlaneState.LPS_NONE : result;
      }
      /**
       * <pre>
       * 当前物流飞机状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.LogisticsPlaneState state = 7;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(com.yorha.proto.CommonEnum.LogisticsPlaneState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000040;
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前物流飞机状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.LogisticsPlaneState state = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        bitField0_ = (bitField0_ & ~0x00000040);
        state_ = 0;
        onChanged();
        return this;
      }

      private long enterStateTs_ ;
      /**
       * <pre>
       * 进入状态的时间戳
       * </pre>
       *
       * <code>optional int64 enterStateTs = 8;</code>
       * @return Whether the enterStateTs field is set.
       */
      @java.lang.Override
      public boolean hasEnterStateTs() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 进入状态的时间戳
       * </pre>
       *
       * <code>optional int64 enterStateTs = 8;</code>
       * @return The enterStateTs.
       */
      @java.lang.Override
      public long getEnterStateTs() {
        return enterStateTs_;
      }
      /**
       * <pre>
       * 进入状态的时间戳
       * </pre>
       *
       * <code>optional int64 enterStateTs = 8;</code>
       * @param value The enterStateTs to set.
       * @return This builder for chaining.
       */
      public Builder setEnterStateTs(long value) {
        bitField0_ |= 0x00000080;
        enterStateTs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 进入状态的时间戳
       * </pre>
       *
       * <code>optional int64 enterStateTs = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearEnterStateTs() {
        bitField0_ = (bitField0_ & ~0x00000080);
        enterStateTs_ = 0L;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 9;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 9;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 9;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000100;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.LogisticsPlaneEntity)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.LogisticsPlaneEntity)
    private static final com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity();
    }

    public static com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<LogisticsPlaneEntity>
        PARSER = new com.google.protobuf.AbstractParser<LogisticsPlaneEntity>() {
      @java.lang.Override
      public LogisticsPlaneEntity parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LogisticsPlaneEntity(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<LogisticsPlaneEntity> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LogisticsPlaneEntity> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_LogisticsPlaneEntity_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_LogisticsPlaneEntity_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n0ss_proto/gen/logisticsPlane/logisticsP" +
      "lane.proto\022\017com.yorha.proto\032%ss_proto/ge" +
      "n/common/common_enum.proto\032 ss_proto/gen" +
      "/common/struct.proto\"\365\001\n\024LogisticsPlaneE" +
      "ntity\022\017\n\007ownerId\030\001 \001(\003\022#\n\004move\030\002 \001(\0132\025.c" +
      "om.yorha.proto.Move\022\022\n\ntemplateId\030\003 \001(\005\022" +
      "\021\n\townerName\030\004 \001(\t\022\016\n\006clanId\030\005 \001(\003\022\025\n\rbr" +
      "iefClanName\030\006 \001(\t\0223\n\005state\030\007 \001(\0162$.com.y" +
      "orha.proto.LogisticsPlaneState\022\024\n\014enterS" +
      "tateTs\030\010 \001(\003\022\016\n\006zoneId\030\t \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_LogisticsPlaneEntity_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_LogisticsPlaneEntity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_LogisticsPlaneEntity_descriptor,
        new java.lang.String[] { "OwnerId", "Move", "TemplateId", "OwnerName", "ClanId", "BriefClanName", "State", "EnterStateTs", "ZoneId", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

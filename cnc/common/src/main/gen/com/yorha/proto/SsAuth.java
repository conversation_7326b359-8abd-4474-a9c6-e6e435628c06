// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/auth/ss_auth.proto

package com.yorha.proto;

public final class SsAuth {
  private SsAuth() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface AuthMsdkAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AuthMsdkAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <pre>
     * msdk token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return Whether the token field is set.
     */
    boolean hasToken();
    /**
     * <pre>
     * msdk token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return The token.
     */
    java.lang.String getToken();
    /**
     * <pre>
     * msdk token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return The bytes for token.
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    /**
     * <pre>
     * 平台id（IOS or Android or Windows）
     * </pre>
     *
     * <code>optional int32 platformId = 3;</code>
     * @return Whether the platformId field is set.
     */
    boolean hasPlatformId();
    /**
     * <pre>
     * 平台id（IOS or Android or Windows）
     * </pre>
     *
     * <code>optional int32 platformId = 3;</code>
     * @return The platformId.
     */
    int getPlatformId();

    /**
     * <pre>
     * 渠道Id
     * </pre>
     *
     * <code>optional int32 channelId = 4;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <pre>
     * 渠道Id
     * </pre>
     *
     * <code>optional int32 channelId = 4;</code>
     * @return The channelId.
     */
    int getChannelId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AuthMsdkAsk}
   */
  public static final class AuthMsdkAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AuthMsdkAsk)
      AuthMsdkAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AuthMsdkAsk.newBuilder() to construct.
    private AuthMsdkAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AuthMsdkAsk() {
      openId_ = "";
      token_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AuthMsdkAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AuthMsdkAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              openId_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              token_ = bs;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              platformId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              channelId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAuth.AuthMsdkAsk.class, com.yorha.proto.SsAuth.AuthMsdkAsk.Builder.class);
    }

    private int bitField0_;
    public static final int OPENID_FIELD_NUMBER = 1;
    private volatile java.lang.Object openId_;
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TOKEN_FIELD_NUMBER = 2;
    private volatile java.lang.Object token_;
    /**
     * <pre>
     * msdk token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return Whether the token field is set.
     */
    @java.lang.Override
    public boolean hasToken() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * msdk token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return The token.
     */
    @java.lang.Override
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * msdk token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return The bytes for token.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLATFORMID_FIELD_NUMBER = 3;
    private int platformId_;
    /**
     * <pre>
     * 平台id（IOS or Android or Windows）
     * </pre>
     *
     * <code>optional int32 platformId = 3;</code>
     * @return Whether the platformId field is set.
     */
    @java.lang.Override
    public boolean hasPlatformId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 平台id（IOS or Android or Windows）
     * </pre>
     *
     * <code>optional int32 platformId = 3;</code>
     * @return The platformId.
     */
    @java.lang.Override
    public int getPlatformId() {
      return platformId_;
    }

    public static final int CHANNELID_FIELD_NUMBER = 4;
    private int channelId_;
    /**
     * <pre>
     * 渠道Id
     * </pre>
     *
     * <code>optional int32 channelId = 4;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 渠道Id
     * </pre>
     *
     * <code>optional int32 channelId = 4;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public int getChannelId() {
      return channelId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, token_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, platformId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, channelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, token_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, platformId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, channelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAuth.AuthMsdkAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAuth.AuthMsdkAsk other = (com.yorha.proto.SsAuth.AuthMsdkAsk) obj;

      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasToken() != other.hasToken()) return false;
      if (hasToken()) {
        if (!getToken()
            .equals(other.getToken())) return false;
      }
      if (hasPlatformId() != other.hasPlatformId()) return false;
      if (hasPlatformId()) {
        if (getPlatformId()
            != other.getPlatformId()) return false;
      }
      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (getChannelId()
            != other.getChannelId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasToken()) {
        hash = (37 * hash) + TOKEN_FIELD_NUMBER;
        hash = (53 * hash) + getToken().hashCode();
      }
      if (hasPlatformId()) {
        hash = (37 * hash) + PLATFORMID_FIELD_NUMBER;
        hash = (53 * hash) + getPlatformId();
      }
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAuth.AuthMsdkAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AuthMsdkAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AuthMsdkAsk)
        com.yorha.proto.SsAuth.AuthMsdkAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAuth.AuthMsdkAsk.class, com.yorha.proto.SsAuth.AuthMsdkAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsAuth.AuthMsdkAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        platformId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        channelId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthMsdkAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsAuth.AuthMsdkAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthMsdkAsk build() {
        com.yorha.proto.SsAuth.AuthMsdkAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthMsdkAsk buildPartial() {
        com.yorha.proto.SsAuth.AuthMsdkAsk result = new com.yorha.proto.SsAuth.AuthMsdkAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.platformId_ = platformId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.channelId_ = channelId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAuth.AuthMsdkAsk) {
          return mergeFrom((com.yorha.proto.SsAuth.AuthMsdkAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAuth.AuthMsdkAsk other) {
        if (other == com.yorha.proto.SsAuth.AuthMsdkAsk.getDefaultInstance()) return this;
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000001;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasToken()) {
          bitField0_ |= 0x00000002;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasPlatformId()) {
          setPlatformId(other.getPlatformId());
        }
        if (other.hasChannelId()) {
          setChannelId(other.getChannelId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAuth.AuthMsdkAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAuth.AuthMsdkAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object openId_ = "";
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object token_ = "";
      /**
       * <pre>
       * msdk token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @return Whether the token field is set.
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * msdk token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @return The token.
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            token_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * msdk token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @return The bytes for token.
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * msdk token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @param value The token to set.
       * @return This builder for chaining.
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * msdk token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000002);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * msdk token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @param value The bytes for token to set.
       * @return This builder for chaining.
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        token_ = value;
        onChanged();
        return this;
      }

      private int platformId_ ;
      /**
       * <pre>
       * 平台id（IOS or Android or Windows）
       * </pre>
       *
       * <code>optional int32 platformId = 3;</code>
       * @return Whether the platformId field is set.
       */
      @java.lang.Override
      public boolean hasPlatformId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 平台id（IOS or Android or Windows）
       * </pre>
       *
       * <code>optional int32 platformId = 3;</code>
       * @return The platformId.
       */
      @java.lang.Override
      public int getPlatformId() {
        return platformId_;
      }
      /**
       * <pre>
       * 平台id（IOS or Android or Windows）
       * </pre>
       *
       * <code>optional int32 platformId = 3;</code>
       * @param value The platformId to set.
       * @return This builder for chaining.
       */
      public Builder setPlatformId(int value) {
        bitField0_ |= 0x00000004;
        platformId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 平台id（IOS or Android or Windows）
       * </pre>
       *
       * <code>optional int32 platformId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlatformId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        platformId_ = 0;
        onChanged();
        return this;
      }

      private int channelId_ ;
      /**
       * <pre>
       * 渠道Id
       * </pre>
       *
       * <code>optional int32 channelId = 4;</code>
       * @return Whether the channelId field is set.
       */
      @java.lang.Override
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 渠道Id
       * </pre>
       *
       * <code>optional int32 channelId = 4;</code>
       * @return The channelId.
       */
      @java.lang.Override
      public int getChannelId() {
        return channelId_;
      }
      /**
       * <pre>
       * 渠道Id
       * </pre>
       *
       * <code>optional int32 channelId = 4;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(int value) {
        bitField0_ |= 0x00000008;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 渠道Id
       * </pre>
       *
       * <code>optional int32 channelId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        channelId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AuthMsdkAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AuthMsdkAsk)
    private static final com.yorha.proto.SsAuth.AuthMsdkAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAuth.AuthMsdkAsk();
    }

    public static com.yorha.proto.SsAuth.AuthMsdkAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AuthMsdkAsk>
        PARSER = new com.google.protobuf.AbstractParser<AuthMsdkAsk>() {
      @java.lang.Override
      public AuthMsdkAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AuthMsdkAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AuthMsdkAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AuthMsdkAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAuth.AuthMsdkAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AuthMsdkAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AuthMsdkAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return Whether the result field is set.
     */
    boolean hasResult();
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return The result.
     */
    java.lang.String getResult();
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return The bytes for result.
     */
    com.google.protobuf.ByteString
        getResultBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AuthMsdkAns}
   */
  public static final class AuthMsdkAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AuthMsdkAns)
      AuthMsdkAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AuthMsdkAns.newBuilder() to construct.
    private AuthMsdkAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AuthMsdkAns() {
      result_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AuthMsdkAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AuthMsdkAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              result_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAuth.AuthMsdkAns.class, com.yorha.proto.SsAuth.AuthMsdkAns.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private volatile java.lang.Object result_;
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public java.lang.String getResult() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          result_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return The bytes for result.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getResultBytes() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        result_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, result_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, result_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAuth.AuthMsdkAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAuth.AuthMsdkAns other = (com.yorha.proto.SsAuth.AuthMsdkAns) obj;

      if (hasResult() != other.hasResult()) return false;
      if (hasResult()) {
        if (!getResult()
            .equals(other.getResult())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthMsdkAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAuth.AuthMsdkAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AuthMsdkAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AuthMsdkAns)
        com.yorha.proto.SsAuth.AuthMsdkAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAuth.AuthMsdkAns.class, com.yorha.proto.SsAuth.AuthMsdkAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsAuth.AuthMsdkAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        result_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthMsdkAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthMsdkAns getDefaultInstanceForType() {
        return com.yorha.proto.SsAuth.AuthMsdkAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthMsdkAns build() {
        com.yorha.proto.SsAuth.AuthMsdkAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthMsdkAns buildPartial() {
        com.yorha.proto.SsAuth.AuthMsdkAns result = new com.yorha.proto.SsAuth.AuthMsdkAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.result_ = result_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAuth.AuthMsdkAns) {
          return mergeFrom((com.yorha.proto.SsAuth.AuthMsdkAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAuth.AuthMsdkAns other) {
        if (other == com.yorha.proto.SsAuth.AuthMsdkAns.getDefaultInstance()) return this;
        if (other.hasResult()) {
          bitField0_ |= 0x00000001;
          result_ = other.result_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAuth.AuthMsdkAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAuth.AuthMsdkAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object result_ = "";
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @return Whether the result field is set.
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @return The result.
       */
      public java.lang.String getResult() {
        java.lang.Object ref = result_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            result_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @return The bytes for result.
       */
      public com.google.protobuf.ByteString
          getResultBytes() {
        java.lang.Object ref = result_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          result_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = getDefaultInstance().getResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @param value The bytes for result to set.
       * @return This builder for chaining.
       */
      public Builder setResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        result_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AuthMsdkAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AuthMsdkAns)
    private static final com.yorha.proto.SsAuth.AuthMsdkAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAuth.AuthMsdkAns();
    }

    public static com.yorha.proto.SsAuth.AuthMsdkAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AuthMsdkAns>
        PARSER = new com.google.protobuf.AbstractParser<AuthMsdkAns>() {
      @java.lang.Override
      public AuthMsdkAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AuthMsdkAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AuthMsdkAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AuthMsdkAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAuth.AuthMsdkAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AuthIntlAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AuthIntlAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <pre>
     * intl token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return Whether the token field is set.
     */
    boolean hasToken();
    /**
     * <pre>
     * intl token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return The token.
     */
    java.lang.String getToken();
    /**
     * <pre>
     * intl token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return The bytes for token.
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    /**
     * <pre>
     * 平台id（IOS or Android）
     * </pre>
     *
     * <code>optional int32 platformId = 3;</code>
     * @return Whether the platformId field is set.
     */
    boolean hasPlatformId();
    /**
     * <pre>
     * 平台id（IOS or Android）
     * </pre>
     *
     * <code>optional int32 platformId = 3;</code>
     * @return The platformId.
     */
    int getPlatformId();

    /**
     * <pre>
     * 渠道Id
     * </pre>
     *
     * <code>optional int32 channelId = 4;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <pre>
     * 渠道Id
     * </pre>
     *
     * <code>optional int32 channelId = 4;</code>
     * @return The channelId.
     */
    int getChannelId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AuthIntlAsk}
   */
  public static final class AuthIntlAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AuthIntlAsk)
      AuthIntlAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AuthIntlAsk.newBuilder() to construct.
    private AuthIntlAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AuthIntlAsk() {
      openId_ = "";
      token_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AuthIntlAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AuthIntlAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              openId_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              token_ = bs;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              platformId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              channelId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAuth.AuthIntlAsk.class, com.yorha.proto.SsAuth.AuthIntlAsk.Builder.class);
    }

    private int bitField0_;
    public static final int OPENID_FIELD_NUMBER = 1;
    private volatile java.lang.Object openId_;
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * player openId
     * </pre>
     *
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TOKEN_FIELD_NUMBER = 2;
    private volatile java.lang.Object token_;
    /**
     * <pre>
     * intl token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return Whether the token field is set.
     */
    @java.lang.Override
    public boolean hasToken() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * intl token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return The token.
     */
    @java.lang.Override
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * intl token
     * </pre>
     *
     * <code>optional string token = 2;</code>
     * @return The bytes for token.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLATFORMID_FIELD_NUMBER = 3;
    private int platformId_;
    /**
     * <pre>
     * 平台id（IOS or Android）
     * </pre>
     *
     * <code>optional int32 platformId = 3;</code>
     * @return Whether the platformId field is set.
     */
    @java.lang.Override
    public boolean hasPlatformId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 平台id（IOS or Android）
     * </pre>
     *
     * <code>optional int32 platformId = 3;</code>
     * @return The platformId.
     */
    @java.lang.Override
    public int getPlatformId() {
      return platformId_;
    }

    public static final int CHANNELID_FIELD_NUMBER = 4;
    private int channelId_;
    /**
     * <pre>
     * 渠道Id
     * </pre>
     *
     * <code>optional int32 channelId = 4;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 渠道Id
     * </pre>
     *
     * <code>optional int32 channelId = 4;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public int getChannelId() {
      return channelId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, token_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, platformId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, channelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, token_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, platformId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, channelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAuth.AuthIntlAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAuth.AuthIntlAsk other = (com.yorha.proto.SsAuth.AuthIntlAsk) obj;

      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasToken() != other.hasToken()) return false;
      if (hasToken()) {
        if (!getToken()
            .equals(other.getToken())) return false;
      }
      if (hasPlatformId() != other.hasPlatformId()) return false;
      if (hasPlatformId()) {
        if (getPlatformId()
            != other.getPlatformId()) return false;
      }
      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (getChannelId()
            != other.getChannelId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasToken()) {
        hash = (37 * hash) + TOKEN_FIELD_NUMBER;
        hash = (53 * hash) + getToken().hashCode();
      }
      if (hasPlatformId()) {
        hash = (37 * hash) + PLATFORMID_FIELD_NUMBER;
        hash = (53 * hash) + getPlatformId();
      }
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAuth.AuthIntlAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AuthIntlAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AuthIntlAsk)
        com.yorha.proto.SsAuth.AuthIntlAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAuth.AuthIntlAsk.class, com.yorha.proto.SsAuth.AuthIntlAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsAuth.AuthIntlAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        platformId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        channelId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthIntlAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsAuth.AuthIntlAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthIntlAsk build() {
        com.yorha.proto.SsAuth.AuthIntlAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthIntlAsk buildPartial() {
        com.yorha.proto.SsAuth.AuthIntlAsk result = new com.yorha.proto.SsAuth.AuthIntlAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.platformId_ = platformId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.channelId_ = channelId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAuth.AuthIntlAsk) {
          return mergeFrom((com.yorha.proto.SsAuth.AuthIntlAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAuth.AuthIntlAsk other) {
        if (other == com.yorha.proto.SsAuth.AuthIntlAsk.getDefaultInstance()) return this;
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000001;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasToken()) {
          bitField0_ |= 0x00000002;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasPlatformId()) {
          setPlatformId(other.getPlatformId());
        }
        if (other.hasChannelId()) {
          setChannelId(other.getChannelId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAuth.AuthIntlAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAuth.AuthIntlAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object openId_ = "";
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * player openId
       * </pre>
       *
       * <code>optional string openId = 1;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object token_ = "";
      /**
       * <pre>
       * intl token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @return Whether the token field is set.
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * intl token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @return The token.
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            token_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * intl token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @return The bytes for token.
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * intl token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @param value The token to set.
       * @return This builder for chaining.
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * intl token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000002);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * intl token
       * </pre>
       *
       * <code>optional string token = 2;</code>
       * @param value The bytes for token to set.
       * @return This builder for chaining.
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        token_ = value;
        onChanged();
        return this;
      }

      private int platformId_ ;
      /**
       * <pre>
       * 平台id（IOS or Android）
       * </pre>
       *
       * <code>optional int32 platformId = 3;</code>
       * @return Whether the platformId field is set.
       */
      @java.lang.Override
      public boolean hasPlatformId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 平台id（IOS or Android）
       * </pre>
       *
       * <code>optional int32 platformId = 3;</code>
       * @return The platformId.
       */
      @java.lang.Override
      public int getPlatformId() {
        return platformId_;
      }
      /**
       * <pre>
       * 平台id（IOS or Android）
       * </pre>
       *
       * <code>optional int32 platformId = 3;</code>
       * @param value The platformId to set.
       * @return This builder for chaining.
       */
      public Builder setPlatformId(int value) {
        bitField0_ |= 0x00000004;
        platformId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 平台id（IOS or Android）
       * </pre>
       *
       * <code>optional int32 platformId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlatformId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        platformId_ = 0;
        onChanged();
        return this;
      }

      private int channelId_ ;
      /**
       * <pre>
       * 渠道Id
       * </pre>
       *
       * <code>optional int32 channelId = 4;</code>
       * @return Whether the channelId field is set.
       */
      @java.lang.Override
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 渠道Id
       * </pre>
       *
       * <code>optional int32 channelId = 4;</code>
       * @return The channelId.
       */
      @java.lang.Override
      public int getChannelId() {
        return channelId_;
      }
      /**
       * <pre>
       * 渠道Id
       * </pre>
       *
       * <code>optional int32 channelId = 4;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(int value) {
        bitField0_ |= 0x00000008;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 渠道Id
       * </pre>
       *
       * <code>optional int32 channelId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        channelId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AuthIntlAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AuthIntlAsk)
    private static final com.yorha.proto.SsAuth.AuthIntlAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAuth.AuthIntlAsk();
    }

    public static com.yorha.proto.SsAuth.AuthIntlAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AuthIntlAsk>
        PARSER = new com.google.protobuf.AbstractParser<AuthIntlAsk>() {
      @java.lang.Override
      public AuthIntlAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AuthIntlAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AuthIntlAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AuthIntlAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAuth.AuthIntlAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AuthIntlAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AuthIntlAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return Whether the result field is set.
     */
    boolean hasResult();
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return The result.
     */
    java.lang.String getResult();
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return The bytes for result.
     */
    com.google.protobuf.ByteString
        getResultBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AuthIntlAns}
   */
  public static final class AuthIntlAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AuthIntlAns)
      AuthIntlAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AuthIntlAns.newBuilder() to construct.
    private AuthIntlAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AuthIntlAns() {
      result_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AuthIntlAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AuthIntlAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              result_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAuth.AuthIntlAns.class, com.yorha.proto.SsAuth.AuthIntlAns.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private volatile java.lang.Object result_;
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public java.lang.String getResult() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          result_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 鉴权结果（json string）
     * </pre>
     *
     * <code>optional string result = 1;</code>
     * @return The bytes for result.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getResultBytes() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        result_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, result_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, result_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAuth.AuthIntlAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAuth.AuthIntlAns other = (com.yorha.proto.SsAuth.AuthIntlAns) obj;

      if (hasResult() != other.hasResult()) return false;
      if (hasResult()) {
        if (!getResult()
            .equals(other.getResult())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAuth.AuthIntlAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAuth.AuthIntlAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AuthIntlAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AuthIntlAns)
        com.yorha.proto.SsAuth.AuthIntlAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAuth.AuthIntlAns.class, com.yorha.proto.SsAuth.AuthIntlAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsAuth.AuthIntlAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        result_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAuth.internal_static_com_yorha_proto_AuthIntlAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthIntlAns getDefaultInstanceForType() {
        return com.yorha.proto.SsAuth.AuthIntlAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthIntlAns build() {
        com.yorha.proto.SsAuth.AuthIntlAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAuth.AuthIntlAns buildPartial() {
        com.yorha.proto.SsAuth.AuthIntlAns result = new com.yorha.proto.SsAuth.AuthIntlAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.result_ = result_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAuth.AuthIntlAns) {
          return mergeFrom((com.yorha.proto.SsAuth.AuthIntlAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAuth.AuthIntlAns other) {
        if (other == com.yorha.proto.SsAuth.AuthIntlAns.getDefaultInstance()) return this;
        if (other.hasResult()) {
          bitField0_ |= 0x00000001;
          result_ = other.result_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAuth.AuthIntlAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAuth.AuthIntlAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object result_ = "";
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @return Whether the result field is set.
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @return The result.
       */
      public java.lang.String getResult() {
        java.lang.Object ref = result_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            result_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @return The bytes for result.
       */
      public com.google.protobuf.ByteString
          getResultBytes() {
        java.lang.Object ref = result_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          result_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = getDefaultInstance().getResult();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 鉴权结果（json string）
       * </pre>
       *
       * <code>optional string result = 1;</code>
       * @param value The bytes for result to set.
       * @return This builder for chaining.
       */
      public Builder setResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        result_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AuthIntlAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AuthIntlAns)
    private static final com.yorha.proto.SsAuth.AuthIntlAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAuth.AuthIntlAns();
    }

    public static com.yorha.proto.SsAuth.AuthIntlAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AuthIntlAns>
        PARSER = new com.google.protobuf.AbstractParser<AuthIntlAns>() {
      @java.lang.Override
      public AuthIntlAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AuthIntlAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AuthIntlAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AuthIntlAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAuth.AuthIntlAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AuthMsdkAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AuthMsdkAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AuthMsdkAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AuthMsdkAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AuthIntlAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AuthIntlAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AuthIntlAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AuthIntlAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037ss_proto/gen/auth/ss_auth.proto\022\017com.y" +
      "orha.proto\"S\n\013AuthMsdkAsk\022\016\n\006openId\030\001 \001(" +
      "\t\022\r\n\005token\030\002 \001(\t\022\022\n\nplatformId\030\003 \001(\005\022\021\n\t" +
      "channelId\030\004 \001(\005\"\035\n\013AuthMsdkAns\022\016\n\006result" +
      "\030\001 \001(\t\"S\n\013AuthIntlAsk\022\016\n\006openId\030\001 \001(\t\022\r\n" +
      "\005token\030\002 \001(\t\022\022\n\nplatformId\030\003 \001(\005\022\021\n\tchan" +
      "nelId\030\004 \001(\005\"\035\n\013AuthIntlAns\022\016\n\006result\030\001 \001" +
      "(\tB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_AuthMsdkAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_AuthMsdkAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AuthMsdkAsk_descriptor,
        new java.lang.String[] { "OpenId", "Token", "PlatformId", "ChannelId", });
    internal_static_com_yorha_proto_AuthMsdkAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_AuthMsdkAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AuthMsdkAns_descriptor,
        new java.lang.String[] { "Result", });
    internal_static_com_yorha_proto_AuthIntlAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_AuthIntlAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AuthIntlAsk_descriptor,
        new java.lang.String[] { "OpenId", "Token", "PlatformId", "ChannelId", });
    internal_static_com_yorha_proto_AuthIntlAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_AuthIntlAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AuthIntlAns_descriptor,
        new java.lang.String[] { "Result", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

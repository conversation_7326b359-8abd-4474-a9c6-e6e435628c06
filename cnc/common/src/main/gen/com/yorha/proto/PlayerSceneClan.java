// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_scene_clan.proto

package com.yorha.proto;

public final class PlayerSceneClan {
  private PlayerSceneClan() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_ConstructClanResBuilding_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ConstructClanResBuilding_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 要建设的军团资源中心类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <pre>
     * 要建设的军团资源中心类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.MapBuildingType getType();

    /**
     * <pre>
     * 中心点位置，用于阻挡检查
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB p = 2;</code>
     * @return Whether the p field is set.
     */
    boolean hasP();
    /**
     * <pre>
     * 中心点位置，用于阻挡检查
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB p = 2;</code>
     * @return The p.
     */
    com.yorha.proto.StructPB.PointPB getP();
    /**
     * <pre>
     * 中心点位置，用于阻挡检查
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB p = 2;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPOrBuilder();
  }
  /**
   * <pre>
   * 放置军团资源中心
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_ConstructClanResBuilding_C2S}
   */
  public static final class Player_ConstructClanResBuilding_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ConstructClanResBuilding_C2S)
      Player_ConstructClanResBuilding_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ConstructClanResBuilding_C2S.newBuilder() to construct.
    private Player_ConstructClanResBuilding_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ConstructClanResBuilding_C2S() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ConstructClanResBuilding_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ConstructClanResBuilding_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MapBuildingType value = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                type_ = rawValue;
              }
              break;
            }
            case 18: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = p_.toBuilder();
              }
              p_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(p_);
                p_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S.class, com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     * 要建设的军团资源中心类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 要建设的军团资源中心类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MapBuildingType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
    }

    public static final int P_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPB.PointPB p_;
    /**
     * <pre>
     * 中心点位置，用于阻挡检查
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB p = 2;</code>
     * @return Whether the p field is set.
     */
    @java.lang.Override
    public boolean hasP() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 中心点位置，用于阻挡检查
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB p = 2;</code>
     * @return The p.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getP() {
      return p_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p_;
    }
    /**
     * <pre>
     * 中心点位置，用于阻挡检查
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB p = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPOrBuilder() {
      return p_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getP());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getP());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S other = (com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasP() != other.hasP()) return false;
      if (hasP()) {
        if (!getP()
            .equals(other.getP())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasP()) {
        hash = (37 * hash) + P_FIELD_NUMBER;
        hash = (53 * hash) + getP().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 放置军团资源中心
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_ConstructClanResBuilding_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ConstructClanResBuilding_C2S)
        com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S.class, com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pBuilder_ == null) {
          p_ = null;
        } else {
          pBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S build() {
        com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S buildPartial() {
        com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S result = new com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (pBuilder_ == null) {
            result.p_ = p_;
          } else {
            result.p_ = pBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S other) {
        if (other == com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasP()) {
          mergeP(other.getP());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <pre>
       * 要建设的军团资源中心类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 要建设的军团资源中心类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MapBuildingType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
      }
      /**
       * <pre>
       * 要建设的军团资源中心类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.MapBuildingType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 要建设的军团资源中心类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB p_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pBuilder_;
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       * @return Whether the p field is set.
       */
      public boolean hasP() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       * @return The p.
       */
      public com.yorha.proto.StructPB.PointPB getP() {
        if (pBuilder_ == null) {
          return p_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p_;
        } else {
          return pBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       */
      public Builder setP(com.yorha.proto.StructPB.PointPB value) {
        if (pBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          p_ = value;
          onChanged();
        } else {
          pBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       */
      public Builder setP(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pBuilder_ == null) {
          p_ = builderForValue.build();
          onChanged();
        } else {
          pBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       */
      public Builder mergeP(com.yorha.proto.StructPB.PointPB value) {
        if (pBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              p_ != null &&
              p_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            p_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(p_).mergeFrom(value).buildPartial();
          } else {
            p_ = value;
          }
          onChanged();
        } else {
          pBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       */
      public Builder clearP() {
        if (pBuilder_ == null) {
          p_ = null;
          onChanged();
        } else {
          pBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPOrBuilder() {
        if (pBuilder_ != null) {
          return pBuilder_.getMessageOrBuilder();
        } else {
          return p_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p_;
        }
      }
      /**
       * <pre>
       * 中心点位置，用于阻挡检查
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB p = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPFieldBuilder() {
        if (pBuilder_ == null) {
          pBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getP(),
                  getParentForChildren(),
                  isClean());
          p_ = null;
        }
        return pBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ConstructClanResBuilding_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ConstructClanResBuilding_C2S)
    private static final com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S();
    }

    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ConstructClanResBuilding_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ConstructClanResBuilding_C2S>() {
      @java.lang.Override
      public Player_ConstructClanResBuilding_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ConstructClanResBuilding_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ConstructClanResBuilding_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ConstructClanResBuilding_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ConstructClanResBuilding_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ConstructClanResBuilding_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ConstructClanResBuilding_S2C}
   */
  public static final class Player_ConstructClanResBuilding_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ConstructClanResBuilding_S2C)
      Player_ConstructClanResBuilding_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ConstructClanResBuilding_S2C.newBuilder() to construct.
    private Player_ConstructClanResBuilding_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ConstructClanResBuilding_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ConstructClanResBuilding_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ConstructClanResBuilding_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C.class, com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C other = (com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ConstructClanResBuilding_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ConstructClanResBuilding_S2C)
        com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C.class, com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C build() {
        com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C buildPartial() {
        com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C result = new com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C other) {
        if (other == com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ConstructClanResBuilding_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ConstructClanResBuilding_S2C)
    private static final com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C();
    }

    public static com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ConstructClanResBuilding_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ConstructClanResBuilding_S2C>() {
      @java.lang.Override
      public Player_ConstructClanResBuilding_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ConstructClanResBuilding_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ConstructClanResBuilding_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ConstructClanResBuilding_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSceneClan.Player_ConstructClanResBuilding_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchClanResBuildingInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanResBuildingInfo_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标的entityId
     * </pre>
     *
     * <code>optional int64 targetId = 1;</code>
     * @return Whether the targetId field is set.
     */
    boolean hasTargetId();
    /**
     * <pre>
     * 目标的entityId
     * </pre>
     *
     * <code>optional int64 targetId = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * <pre>
   * 拉取军团资源中心信息
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_FetchClanResBuildingInfo_C2S}
   */
  public static final class Player_FetchClanResBuildingInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanResBuildingInfo_C2S)
      Player_FetchClanResBuildingInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanResBuildingInfo_C2S.newBuilder() to construct.
    private Player_FetchClanResBuildingInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanResBuildingInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanResBuildingInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanResBuildingInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              targetId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S.class, com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int TARGETID_FIELD_NUMBER = 1;
    private long targetId_;
    /**
     * <pre>
     * 目标的entityId
     * </pre>
     *
     * <code>optional int64 targetId = 1;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 目标的entityId
     * </pre>
     *
     * <code>optional int64 targetId = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, targetId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, targetId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S other = (com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S) obj;

      if (hasTargetId() != other.hasTargetId()) return false;
      if (hasTargetId()) {
        if (getTargetId()
            != other.getTargetId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTargetId()) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 拉取军团资源中心信息
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_FetchClanResBuildingInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanResBuildingInfo_C2S)
        com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S.class, com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        targetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S build() {
        com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S buildPartial() {
        com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S result = new com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S other) {
        if (other == com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S.getDefaultInstance()) return this;
        if (other.hasTargetId()) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <pre>
       * 目标的entityId
       * </pre>
       *
       * <code>optional int64 targetId = 1;</code>
       * @return Whether the targetId field is set.
       */
      @java.lang.Override
      public boolean hasTargetId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 目标的entityId
       * </pre>
       *
       * <code>optional int64 targetId = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标的entityId
       * </pre>
       *
       * <code>optional int64 targetId = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        bitField0_ |= 0x00000001;
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标的entityId
       * </pre>
       *
       * <code>optional int64 targetId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanResBuildingInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanResBuildingInfo_C2S)
    private static final com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S();
    }

    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanResBuildingInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanResBuildingInfo_C2S>() {
      @java.lang.Override
      public Player_FetchClanResBuildingInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanResBuildingInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanResBuildingInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanResBuildingInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchClanResBuildingInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanResBuildingInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 资源没被采集完的情况下，正常消失的时间戳，用于显示倒计时
     * </pre>
     *
     * <code>optional int64 disappearTsMs = 1;</code>
     * @return Whether the disappearTsMs field is set.
     */
    boolean hasDisappearTsMs();
    /**
     * <pre>
     * 资源没被采集完的情况下，正常消失的时间戳，用于显示倒计时
     * </pre>
     *
     * <code>optional int64 disappearTsMs = 1;</code>
     * @return The disappearTsMs.
     */
    long getDisappearTsMs();

    /**
     * <pre>
     * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
     * @return Whether the collectInfo field is set.
     */
    boolean hasCollectInfo();
    /**
     * <pre>
     * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
     * @return The collectInfo.
     */
    com.yorha.proto.StructCommonPB.ProgressInfoPB getCollectInfo();
    /**
     * <pre>
     * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
     */
    com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder getCollectInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanResBuildingInfo_S2C}
   */
  public static final class Player_FetchClanResBuildingInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanResBuildingInfo_S2C)
      Player_FetchClanResBuildingInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanResBuildingInfo_S2C.newBuilder() to construct.
    private Player_FetchClanResBuildingInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanResBuildingInfo_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanResBuildingInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanResBuildingInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              disappearTsMs_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = collectInfo_.toBuilder();
              }
              collectInfo_ = input.readMessage(com.yorha.proto.StructCommonPB.ProgressInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(collectInfo_);
                collectInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C.class, com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int DISAPPEARTSMS_FIELD_NUMBER = 1;
    private long disappearTsMs_;
    /**
     * <pre>
     * 资源没被采集完的情况下，正常消失的时间戳，用于显示倒计时
     * </pre>
     *
     * <code>optional int64 disappearTsMs = 1;</code>
     * @return Whether the disappearTsMs field is set.
     */
    @java.lang.Override
    public boolean hasDisappearTsMs() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 资源没被采集完的情况下，正常消失的时间戳，用于显示倒计时
     * </pre>
     *
     * <code>optional int64 disappearTsMs = 1;</code>
     * @return The disappearTsMs.
     */
    @java.lang.Override
    public long getDisappearTsMs() {
      return disappearTsMs_;
    }

    public static final int COLLECTINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.StructCommonPB.ProgressInfoPB collectInfo_;
    /**
     * <pre>
     * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
     * @return Whether the collectInfo field is set.
     */
    @java.lang.Override
    public boolean hasCollectInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
     * @return The collectInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructCommonPB.ProgressInfoPB getCollectInfo() {
      return collectInfo_ == null ? com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance() : collectInfo_;
    }
    /**
     * <pre>
     * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder getCollectInfoOrBuilder() {
      return collectInfo_ == null ? com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance() : collectInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, disappearTsMs_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getCollectInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, disappearTsMs_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCollectInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C other = (com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C) obj;

      if (hasDisappearTsMs() != other.hasDisappearTsMs()) return false;
      if (hasDisappearTsMs()) {
        if (getDisappearTsMs()
            != other.getDisappearTsMs()) return false;
      }
      if (hasCollectInfo() != other.hasCollectInfo()) return false;
      if (hasCollectInfo()) {
        if (!getCollectInfo()
            .equals(other.getCollectInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDisappearTsMs()) {
        hash = (37 * hash) + DISAPPEARTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getDisappearTsMs());
      }
      if (hasCollectInfo()) {
        hash = (37 * hash) + COLLECTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getCollectInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanResBuildingInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanResBuildingInfo_S2C)
        com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C.class, com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCollectInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        disappearTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (collectInfoBuilder_ == null) {
          collectInfo_ = null;
        } else {
          collectInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSceneClan.internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C build() {
        com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C buildPartial() {
        com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C result = new com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.disappearTsMs_ = disappearTsMs_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (collectInfoBuilder_ == null) {
            result.collectInfo_ = collectInfo_;
          } else {
            result.collectInfo_ = collectInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C other) {
        if (other == com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C.getDefaultInstance()) return this;
        if (other.hasDisappearTsMs()) {
          setDisappearTsMs(other.getDisappearTsMs());
        }
        if (other.hasCollectInfo()) {
          mergeCollectInfo(other.getCollectInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long disappearTsMs_ ;
      /**
       * <pre>
       * 资源没被采集完的情况下，正常消失的时间戳，用于显示倒计时
       * </pre>
       *
       * <code>optional int64 disappearTsMs = 1;</code>
       * @return Whether the disappearTsMs field is set.
       */
      @java.lang.Override
      public boolean hasDisappearTsMs() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 资源没被采集完的情况下，正常消失的时间戳，用于显示倒计时
       * </pre>
       *
       * <code>optional int64 disappearTsMs = 1;</code>
       * @return The disappearTsMs.
       */
      @java.lang.Override
      public long getDisappearTsMs() {
        return disappearTsMs_;
      }
      /**
       * <pre>
       * 资源没被采集完的情况下，正常消失的时间戳，用于显示倒计时
       * </pre>
       *
       * <code>optional int64 disappearTsMs = 1;</code>
       * @param value The disappearTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setDisappearTsMs(long value) {
        bitField0_ |= 0x00000001;
        disappearTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 资源没被采集完的情况下，正常消失的时间戳，用于显示倒计时
       * </pre>
       *
       * <code>optional int64 disappearTsMs = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDisappearTsMs() {
        bitField0_ = (bitField0_ & ~0x00000001);
        disappearTsMs_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructCommonPB.ProgressInfoPB collectInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommonPB.ProgressInfoPB, com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder, com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder> collectInfoBuilder_;
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       * @return Whether the collectInfo field is set.
       */
      public boolean hasCollectInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       * @return The collectInfo.
       */
      public com.yorha.proto.StructCommonPB.ProgressInfoPB getCollectInfo() {
        if (collectInfoBuilder_ == null) {
          return collectInfo_ == null ? com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance() : collectInfo_;
        } else {
          return collectInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       */
      public Builder setCollectInfo(com.yorha.proto.StructCommonPB.ProgressInfoPB value) {
        if (collectInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          collectInfo_ = value;
          onChanged();
        } else {
          collectInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       */
      public Builder setCollectInfo(
          com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder builderForValue) {
        if (collectInfoBuilder_ == null) {
          collectInfo_ = builderForValue.build();
          onChanged();
        } else {
          collectInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       */
      public Builder mergeCollectInfo(com.yorha.proto.StructCommonPB.ProgressInfoPB value) {
        if (collectInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              collectInfo_ != null &&
              collectInfo_ != com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance()) {
            collectInfo_ =
              com.yorha.proto.StructCommonPB.ProgressInfoPB.newBuilder(collectInfo_).mergeFrom(value).buildPartial();
          } else {
            collectInfo_ = value;
          }
          onChanged();
        } else {
          collectInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       */
      public Builder clearCollectInfo() {
        if (collectInfoBuilder_ == null) {
          collectInfo_ = null;
          onChanged();
        } else {
          collectInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       */
      public com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder getCollectInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCollectInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       */
      public com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder getCollectInfoOrBuilder() {
        if (collectInfoBuilder_ != null) {
          return collectInfoBuilder_.getMessageOrBuilder();
        } else {
          return collectInfo_ == null ?
              com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance() : collectInfo_;
        }
      }
      /**
       * <pre>
       * 如果玩家有军队在采集，会返回，用于显示玩家采集进度条；否则为空
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB collectInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommonPB.ProgressInfoPB, com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder, com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder> 
          getCollectInfoFieldBuilder() {
        if (collectInfoBuilder_ == null) {
          collectInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructCommonPB.ProgressInfoPB, com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder, com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder>(
                  getCollectInfo(),
                  getParentForChildren(),
                  isClean());
          collectInfo_ = null;
        }
        return collectInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanResBuildingInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanResBuildingInfo_S2C)
    private static final com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C();
    }

    public static com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanResBuildingInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanResBuildingInfo_S2C>() {
      @java.lang.Override
      public Player_FetchClanResBuildingInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanResBuildingInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanResBuildingInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanResBuildingInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/player/cs/player_scene_cl" +
      "an.proto\022\017com.yorha.proto\032\"cs_proto/gen/" +
      "common/structPB.proto\032)cs_proto/gen/comm" +
      "on/struct_commonPB.proto\032%ss_proto/gen/c" +
      "ommon/common_enum.proto\"z\n#Player_Constr" +
      "uctClanResBuilding_C2S\022.\n\004type\030\001 \001(\0162 .c" +
      "om.yorha.proto.MapBuildingType\022#\n\001p\030\002 \001(" +
      "\0132\030.com.yorha.proto.PointPB\"%\n#Player_Co" +
      "nstructClanResBuilding_S2C\"7\n#Player_Fet" +
      "chClanResBuildingInfo_C2S\022\020\n\010targetId\030\001 " +
      "\001(\003\"r\n#Player_FetchClanResBuildingInfo_S" +
      "2C\022\025\n\rdisappearTsMs\030\001 \001(\003\0224\n\013collectInfo" +
      "\030\002 \001(\0132\037.com.yorha.proto.ProgressInfoPBB" +
      "\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.StructCommonPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ConstructClanResBuilding_C2S_descriptor,
        new java.lang.String[] { "Type", "P", });
    internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ConstructClanResBuilding_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_C2S_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanResBuildingInfo_S2C_descriptor,
        new java.lang.String[] { "DisappearTsMs", "CollectInfo", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.StructCommonPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

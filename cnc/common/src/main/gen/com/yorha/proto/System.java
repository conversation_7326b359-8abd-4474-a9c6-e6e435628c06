// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/server/system.proto

package com.yorha.proto;

public final class System {
  private System() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TypedMsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.TypedMsg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    boolean hasMsgType();
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <code>optional bytes data = 2;</code>
     * @return Whether the data field is set.
     */
    boolean hasData();
    /**
     * <code>optional bytes data = 2;</code>
     * @return The data.
     */
    com.google.protobuf.ByteString getData();
  }
  /**
   * Protobuf type {@code com.yorha.proto.TypedMsg}
   */
  public static final class TypedMsg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.TypedMsg)
      TypedMsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TypedMsg.newBuilder() to construct.
    private TypedMsg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TypedMsg() {
      data_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TypedMsg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TypedMsg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              msgType_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              data_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_TypedMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_TypedMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.System.TypedMsg.class, com.yorha.proto.System.TypedMsg.Builder.class);
    }

    private int bitField0_;
    public static final int MSGTYPE_FIELD_NUMBER = 1;
    private int msgType_;
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    @java.lang.Override
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int DATA_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString data_;
    /**
     * <code>optional bytes data = 2;</code>
     * @return Whether the data field is set.
     */
    @java.lang.Override
    public boolean hasData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes data = 2;</code>
     * @return The data.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getData() {
      return data_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, data_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, data_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.System.TypedMsg)) {
        return super.equals(obj);
      }
      com.yorha.proto.System.TypedMsg other = (com.yorha.proto.System.TypedMsg) obj;

      if (hasMsgType() != other.hasMsgType()) return false;
      if (hasMsgType()) {
        if (getMsgType()
            != other.getMsgType()) return false;
      }
      if (hasData() != other.hasData()) return false;
      if (hasData()) {
        if (!getData()
            .equals(other.getData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (hasData()) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.System.TypedMsg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.TypedMsg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.TypedMsg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.TypedMsg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.System.TypedMsg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.TypedMsg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.TypedMsg)
        com.yorha.proto.System.TypedMsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_TypedMsg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_TypedMsg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.System.TypedMsg.class, com.yorha.proto.System.TypedMsg.Builder.class);
      }

      // Construct using com.yorha.proto.System.TypedMsg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        data_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_TypedMsg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.System.TypedMsg getDefaultInstanceForType() {
        return com.yorha.proto.System.TypedMsg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.System.TypedMsg build() {
        com.yorha.proto.System.TypedMsg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.System.TypedMsg buildPartial() {
        com.yorha.proto.System.TypedMsg result = new com.yorha.proto.System.TypedMsg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.msgType_ = msgType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.data_ = data_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.System.TypedMsg) {
          return mergeFrom((com.yorha.proto.System.TypedMsg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.System.TypedMsg other) {
        if (other == com.yorha.proto.System.TypedMsg.getDefaultInstance()) return this;
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        if (other.hasData()) {
          setData(other.getData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.System.TypedMsg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.System.TypedMsg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgType_ ;
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return Whether the msgType field is set.
       */
      @java.lang.Override
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000001;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString data_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes data = 2;</code>
       * @return Whether the data field is set.
       */
      @java.lang.Override
      public boolean hasData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes data = 2;</code>
       * @return The data.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getData() {
        return data_;
      }
      /**
       * <code>optional bytes data = 2;</code>
       * @param value The data to set.
       * @return This builder for chaining.
       */
      public Builder setData(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        data_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes data = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        data_ = getDefaultInstance().getData();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.TypedMsg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.TypedMsg)
    private static final com.yorha.proto.System.TypedMsg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.System.TypedMsg();
    }

    public static com.yorha.proto.System.TypedMsg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TypedMsg>
        PARSER = new com.google.protobuf.AbstractParser<TypedMsg>() {
      @java.lang.Override
      public TypedMsg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TypedMsg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TypedMsg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TypedMsg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.System.TypedMsg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface YoExceptionSnapshotOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.YoExceptionSnapshot)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
     * </pre>
     *
     * <code>optional string clazzName = 1;</code>
     * @return Whether the clazzName field is set.
     */
    boolean hasClazzName();
    /**
     * <pre>
     * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
     * </pre>
     *
     * <code>optional string clazzName = 1;</code>
     * @return The clazzName.
     */
    java.lang.String getClazzName();
    /**
     * <pre>
     * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
     * </pre>
     *
     * <code>optional string clazzName = 1;</code>
     * @return The bytes for clazzName.
     */
    com.google.protobuf.ByteString
        getClazzNameBytes();

    /**
     * <pre>
     * LogicException的code，LE是业务逻辑需要特殊处理的
     * </pre>
     *
     * <code>optional int32 errorCode = 2;</code>
     * @return Whether the errorCode field is set.
     */
    boolean hasErrorCode();
    /**
     * <pre>
     * LogicException的code，LE是业务逻辑需要特殊处理的
     * </pre>
     *
     * <code>optional int32 errorCode = 2;</code>
     * @return The errorCode.
     */
    int getErrorCode();

    /**
     * <pre>
     * Logic或者其他exception都可以借此字段附加信息
     * </pre>
     *
     * <code>optional string msg = 3;</code>
     * @return Whether the msg field is set.
     */
    boolean hasMsg();
    /**
     * <pre>
     * Logic或者其他exception都可以借此字段附加信息
     * </pre>
     *
     * <code>optional string msg = 3;</code>
     * @return The msg.
     */
    java.lang.String getMsg();
    /**
     * <pre>
     * Logic或者其他exception都可以借此字段附加信息
     * </pre>
     *
     * <code>optional string msg = 3;</code>
     * @return The bytes for msg.
     */
    com.google.protobuf.ByteString
        getMsgBytes();

    /**
     * <pre>
     * MigrateException要借助此字段传递新的zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 4;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * MigrateException要借助此字段传递新的zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 4;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.YoExceptionSnapshot}
   */
  public static final class YoExceptionSnapshot extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.YoExceptionSnapshot)
      YoExceptionSnapshotOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use YoExceptionSnapshot.newBuilder() to construct.
    private YoExceptionSnapshot(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private YoExceptionSnapshot() {
      clazzName_ = "";
      msg_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new YoExceptionSnapshot();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private YoExceptionSnapshot(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              clazzName_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              errorCode_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              msg_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_YoExceptionSnapshot_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_YoExceptionSnapshot_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.System.YoExceptionSnapshot.class, com.yorha.proto.System.YoExceptionSnapshot.Builder.class);
    }

    private int bitField0_;
    public static final int CLAZZNAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object clazzName_;
    /**
     * <pre>
     * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
     * </pre>
     *
     * <code>optional string clazzName = 1;</code>
     * @return Whether the clazzName field is set.
     */
    @java.lang.Override
    public boolean hasClazzName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
     * </pre>
     *
     * <code>optional string clazzName = 1;</code>
     * @return The clazzName.
     */
    @java.lang.Override
    public java.lang.String getClazzName() {
      java.lang.Object ref = clazzName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clazzName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
     * </pre>
     *
     * <code>optional string clazzName = 1;</code>
     * @return The bytes for clazzName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClazzNameBytes() {
      java.lang.Object ref = clazzName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clazzName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ERRORCODE_FIELD_NUMBER = 2;
    private int errorCode_;
    /**
     * <pre>
     * LogicException的code，LE是业务逻辑需要特殊处理的
     * </pre>
     *
     * <code>optional int32 errorCode = 2;</code>
     * @return Whether the errorCode field is set.
     */
    @java.lang.Override
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * LogicException的code，LE是业务逻辑需要特殊处理的
     * </pre>
     *
     * <code>optional int32 errorCode = 2;</code>
     * @return The errorCode.
     */
    @java.lang.Override
    public int getErrorCode() {
      return errorCode_;
    }

    public static final int MSG_FIELD_NUMBER = 3;
    private volatile java.lang.Object msg_;
    /**
     * <pre>
     * Logic或者其他exception都可以借此字段附加信息
     * </pre>
     *
     * <code>optional string msg = 3;</code>
     * @return Whether the msg field is set.
     */
    @java.lang.Override
    public boolean hasMsg() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * Logic或者其他exception都可以借此字段附加信息
     * </pre>
     *
     * <code>optional string msg = 3;</code>
     * @return The msg.
     */
    @java.lang.Override
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          msg_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * Logic或者其他exception都可以借此字段附加信息
     * </pre>
     *
     * <code>optional string msg = 3;</code>
     * @return The bytes for msg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ZONEID_FIELD_NUMBER = 4;
    private int zoneId_;
    /**
     * <pre>
     * MigrateException要借助此字段传递新的zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 4;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * MigrateException要借助此字段传递新的zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 4;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, clazzName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, errorCode_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, msg_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, clazzName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, errorCode_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, msg_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.System.YoExceptionSnapshot)) {
        return super.equals(obj);
      }
      com.yorha.proto.System.YoExceptionSnapshot other = (com.yorha.proto.System.YoExceptionSnapshot) obj;

      if (hasClazzName() != other.hasClazzName()) return false;
      if (hasClazzName()) {
        if (!getClazzName()
            .equals(other.getClazzName())) return false;
      }
      if (hasErrorCode() != other.hasErrorCode()) return false;
      if (hasErrorCode()) {
        if (getErrorCode()
            != other.getErrorCode()) return false;
      }
      if (hasMsg() != other.hasMsg()) return false;
      if (hasMsg()) {
        if (!getMsg()
            .equals(other.getMsg())) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClazzName()) {
        hash = (37 * hash) + CLAZZNAME_FIELD_NUMBER;
        hash = (53 * hash) + getClazzName().hashCode();
      }
      if (hasErrorCode()) {
        hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
        hash = (53 * hash) + getErrorCode();
      }
      if (hasMsg()) {
        hash = (37 * hash) + MSG_FIELD_NUMBER;
        hash = (53 * hash) + getMsg().hashCode();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoExceptionSnapshot parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.System.YoExceptionSnapshot prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.YoExceptionSnapshot}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.YoExceptionSnapshot)
        com.yorha.proto.System.YoExceptionSnapshotOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoExceptionSnapshot_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoExceptionSnapshot_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.System.YoExceptionSnapshot.class, com.yorha.proto.System.YoExceptionSnapshot.Builder.class);
      }

      // Construct using com.yorha.proto.System.YoExceptionSnapshot.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clazzName_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        msg_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoExceptionSnapshot_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.System.YoExceptionSnapshot getDefaultInstanceForType() {
        return com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.System.YoExceptionSnapshot build() {
        com.yorha.proto.System.YoExceptionSnapshot result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.System.YoExceptionSnapshot buildPartial() {
        com.yorha.proto.System.YoExceptionSnapshot result = new com.yorha.proto.System.YoExceptionSnapshot(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.clazzName_ = clazzName_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.errorCode_ = errorCode_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.msg_ = msg_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.System.YoExceptionSnapshot) {
          return mergeFrom((com.yorha.proto.System.YoExceptionSnapshot)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.System.YoExceptionSnapshot other) {
        if (other == com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance()) return this;
        if (other.hasClazzName()) {
          bitField0_ |= 0x00000001;
          clazzName_ = other.clazzName_;
          onChanged();
        }
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        if (other.hasMsg()) {
          bitField0_ |= 0x00000004;
          msg_ = other.msg_;
          onChanged();
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.System.YoExceptionSnapshot parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.System.YoExceptionSnapshot) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object clazzName_ = "";
      /**
       * <pre>
       * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
       * </pre>
       *
       * <code>optional string clazzName = 1;</code>
       * @return Whether the clazzName field is set.
       */
      public boolean hasClazzName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
       * </pre>
       *
       * <code>optional string clazzName = 1;</code>
       * @return The clazzName.
       */
      public java.lang.String getClazzName() {
        java.lang.Object ref = clazzName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clazzName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
       * </pre>
       *
       * <code>optional string clazzName = 1;</code>
       * @return The bytes for clazzName.
       */
      public com.google.protobuf.ByteString
          getClazzNameBytes() {
        java.lang.Object ref = clazzName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clazzName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
       * </pre>
       *
       * <code>optional string clazzName = 1;</code>
       * @param value The clazzName to set.
       * @return This builder for chaining.
       */
      public Builder setClazzName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        clazzName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
       * </pre>
       *
       * <code>optional string clazzName = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClazzName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clazzName_ = getDefaultInstance().getClazzName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常的class simple name，异常通常在发生端打印详细堆栈，在关注端只做简单log，着重处理
       * </pre>
       *
       * <code>optional string clazzName = 1;</code>
       * @param value The bytes for clazzName to set.
       * @return This builder for chaining.
       */
      public Builder setClazzNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        clazzName_ = value;
        onChanged();
        return this;
      }

      private int errorCode_ ;
      /**
       * <pre>
       * LogicException的code，LE是业务逻辑需要特殊处理的
       * </pre>
       *
       * <code>optional int32 errorCode = 2;</code>
       * @return Whether the errorCode field is set.
       */
      @java.lang.Override
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * LogicException的code，LE是业务逻辑需要特殊处理的
       * </pre>
       *
       * <code>optional int32 errorCode = 2;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <pre>
       * LogicException的code，LE是业务逻辑需要特殊处理的
       * </pre>
       *
       * <code>optional int32 errorCode = 2;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000002;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * LogicException的code，LE是业务逻辑需要特殊处理的
       * </pre>
       *
       * <code>optional int32 errorCode = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000002);
        errorCode_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object msg_ = "";
      /**
       * <pre>
       * Logic或者其他exception都可以借此字段附加信息
       * </pre>
       *
       * <code>optional string msg = 3;</code>
       * @return Whether the msg field is set.
       */
      public boolean hasMsg() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * Logic或者其他exception都可以借此字段附加信息
       * </pre>
       *
       * <code>optional string msg = 3;</code>
       * @return The msg.
       */
      public java.lang.String getMsg() {
        java.lang.Object ref = msg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            msg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * Logic或者其他exception都可以借此字段附加信息
       * </pre>
       *
       * <code>optional string msg = 3;</code>
       * @return The bytes for msg.
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
        java.lang.Object ref = msg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * Logic或者其他exception都可以借此字段附加信息
       * </pre>
       *
       * <code>optional string msg = 3;</code>
       * @param value The msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        msg_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Logic或者其他exception都可以借此字段附加信息
       * </pre>
       *
       * <code>optional string msg = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsg() {
        bitField0_ = (bitField0_ & ~0x00000004);
        msg_ = getDefaultInstance().getMsg();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Logic或者其他exception都可以借此字段附加信息
       * </pre>
       *
       * <code>optional string msg = 3;</code>
       * @param value The bytes for msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        msg_ = value;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * MigrateException要借助此字段传递新的zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 4;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * MigrateException要借助此字段传递新的zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 4;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * MigrateException要借助此字段传递新的zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 4;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000008;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * MigrateException要借助此字段传递新的zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.YoExceptionSnapshot)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.YoExceptionSnapshot)
    private static final com.yorha.proto.System.YoExceptionSnapshot DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.System.YoExceptionSnapshot();
    }

    public static com.yorha.proto.System.YoExceptionSnapshot getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<YoExceptionSnapshot>
        PARSER = new com.google.protobuf.AbstractParser<YoExceptionSnapshot>() {
      @java.lang.Override
      public YoExceptionSnapshot parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new YoExceptionSnapshot(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<YoExceptionSnapshot> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<YoExceptionSnapshot> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.System.YoExceptionSnapshot getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface YoActorMsgPayloadOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.YoActorMsgPayload)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
     * @return Whether the typedMsg field is set.
     */
    boolean hasTypedMsg();
    /**
     * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
     * @return The typedMsg.
     */
    com.yorha.proto.System.TypedMsg getTypedMsg();
    /**
     * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
     */
    com.yorha.proto.System.TypedMsgOrBuilder getTypedMsgOrBuilder();

    /**
     * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
     * @return Whether the exception field is set.
     */
    boolean hasException();
    /**
     * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
     * @return The exception.
     */
    com.yorha.proto.System.YoExceptionSnapshot getException();
    /**
     * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
     */
    com.yorha.proto.System.YoExceptionSnapshotOrBuilder getExceptionOrBuilder();

    public com.yorha.proto.System.YoActorMsgPayload.FieldCase getFieldCase();
  }
  /**
   * Protobuf type {@code com.yorha.proto.YoActorMsgPayload}
   */
  public static final class YoActorMsgPayload extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.YoActorMsgPayload)
      YoActorMsgPayloadOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use YoActorMsgPayload.newBuilder() to construct.
    private YoActorMsgPayload(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private YoActorMsgPayload() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new YoActorMsgPayload();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private YoActorMsgPayload(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.System.TypedMsg.Builder subBuilder = null;
              if (fieldCase_ == 1) {
                subBuilder = ((com.yorha.proto.System.TypedMsg) field_).toBuilder();
              }
              field_ =
                  input.readMessage(com.yorha.proto.System.TypedMsg.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.System.TypedMsg) field_);
                field_ = subBuilder.buildPartial();
              }
              fieldCase_ = 1;
              break;
            }
            case 18: {
              com.yorha.proto.System.YoExceptionSnapshot.Builder subBuilder = null;
              if (fieldCase_ == 2) {
                subBuilder = ((com.yorha.proto.System.YoExceptionSnapshot) field_).toBuilder();
              }
              field_ =
                  input.readMessage(com.yorha.proto.System.YoExceptionSnapshot.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom((com.yorha.proto.System.YoExceptionSnapshot) field_);
                field_ = subBuilder.buildPartial();
              }
              fieldCase_ = 2;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgPayload_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgPayload_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.System.YoActorMsgPayload.class, com.yorha.proto.System.YoActorMsgPayload.Builder.class);
    }

    private int bitField0_;
    private int fieldCase_ = 0;
    private java.lang.Object field_;
    public enum FieldCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      TYPEDMSG(1),
      EXCEPTION(2),
      FIELD_NOT_SET(0);
      private final int value;
      private FieldCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static FieldCase valueOf(int value) {
        return forNumber(value);
      }

      public static FieldCase forNumber(int value) {
        switch (value) {
          case 1: return TYPEDMSG;
          case 2: return EXCEPTION;
          case 0: return FIELD_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public FieldCase
    getFieldCase() {
      return FieldCase.forNumber(
          fieldCase_);
    }

    public static final int TYPEDMSG_FIELD_NUMBER = 1;
    /**
     * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
     * @return Whether the typedMsg field is set.
     */
    @java.lang.Override
    public boolean hasTypedMsg() {
      return fieldCase_ == 1;
    }
    /**
     * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
     * @return The typedMsg.
     */
    @java.lang.Override
    public com.yorha.proto.System.TypedMsg getTypedMsg() {
      if (fieldCase_ == 1) {
         return (com.yorha.proto.System.TypedMsg) field_;
      }
      return com.yorha.proto.System.TypedMsg.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.System.TypedMsgOrBuilder getTypedMsgOrBuilder() {
      if (fieldCase_ == 1) {
         return (com.yorha.proto.System.TypedMsg) field_;
      }
      return com.yorha.proto.System.TypedMsg.getDefaultInstance();
    }

    public static final int EXCEPTION_FIELD_NUMBER = 2;
    /**
     * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
     * @return Whether the exception field is set.
     */
    @java.lang.Override
    public boolean hasException() {
      return fieldCase_ == 2;
    }
    /**
     * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
     * @return The exception.
     */
    @java.lang.Override
    public com.yorha.proto.System.YoExceptionSnapshot getException() {
      if (fieldCase_ == 2) {
         return (com.yorha.proto.System.YoExceptionSnapshot) field_;
      }
      return com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance();
    }
    /**
     * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.System.YoExceptionSnapshotOrBuilder getExceptionOrBuilder() {
      if (fieldCase_ == 2) {
         return (com.yorha.proto.System.YoExceptionSnapshot) field_;
      }
      return com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (fieldCase_ == 1) {
        output.writeMessage(1, (com.yorha.proto.System.TypedMsg) field_);
      }
      if (fieldCase_ == 2) {
        output.writeMessage(2, (com.yorha.proto.System.YoExceptionSnapshot) field_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (fieldCase_ == 1) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, (com.yorha.proto.System.TypedMsg) field_);
      }
      if (fieldCase_ == 2) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, (com.yorha.proto.System.YoExceptionSnapshot) field_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.System.YoActorMsgPayload)) {
        return super.equals(obj);
      }
      com.yorha.proto.System.YoActorMsgPayload other = (com.yorha.proto.System.YoActorMsgPayload) obj;

      if (!getFieldCase().equals(other.getFieldCase())) return false;
      switch (fieldCase_) {
        case 1:
          if (!getTypedMsg()
              .equals(other.getTypedMsg())) return false;
          break;
        case 2:
          if (!getException()
              .equals(other.getException())) return false;
          break;
        case 0:
        default:
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      switch (fieldCase_) {
        case 1:
          hash = (37 * hash) + TYPEDMSG_FIELD_NUMBER;
          hash = (53 * hash) + getTypedMsg().hashCode();
          break;
        case 2:
          hash = (37 * hash) + EXCEPTION_FIELD_NUMBER;
          hash = (53 * hash) + getException().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoActorMsgPayload parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.System.YoActorMsgPayload prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.YoActorMsgPayload}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.YoActorMsgPayload)
        com.yorha.proto.System.YoActorMsgPayloadOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgPayload_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgPayload_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.System.YoActorMsgPayload.class, com.yorha.proto.System.YoActorMsgPayload.Builder.class);
      }

      // Construct using com.yorha.proto.System.YoActorMsgPayload.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fieldCase_ = 0;
        field_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgPayload_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.System.YoActorMsgPayload getDefaultInstanceForType() {
        return com.yorha.proto.System.YoActorMsgPayload.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.System.YoActorMsgPayload build() {
        com.yorha.proto.System.YoActorMsgPayload result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.System.YoActorMsgPayload buildPartial() {
        com.yorha.proto.System.YoActorMsgPayload result = new com.yorha.proto.System.YoActorMsgPayload(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (fieldCase_ == 1) {
          if (typedMsgBuilder_ == null) {
            result.field_ = field_;
          } else {
            result.field_ = typedMsgBuilder_.build();
          }
        }
        if (fieldCase_ == 2) {
          if (exceptionBuilder_ == null) {
            result.field_ = field_;
          } else {
            result.field_ = exceptionBuilder_.build();
          }
        }
        result.bitField0_ = to_bitField0_;
        result.fieldCase_ = fieldCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.System.YoActorMsgPayload) {
          return mergeFrom((com.yorha.proto.System.YoActorMsgPayload)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.System.YoActorMsgPayload other) {
        if (other == com.yorha.proto.System.YoActorMsgPayload.getDefaultInstance()) return this;
        switch (other.getFieldCase()) {
          case TYPEDMSG: {
            mergeTypedMsg(other.getTypedMsg());
            break;
          }
          case EXCEPTION: {
            mergeException(other.getException());
            break;
          }
          case FIELD_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.System.YoActorMsgPayload parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.System.YoActorMsgPayload) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int fieldCase_ = 0;
      private java.lang.Object field_;
      public FieldCase
          getFieldCase() {
        return FieldCase.forNumber(
            fieldCase_);
      }

      public Builder clearField() {
        fieldCase_ = 0;
        field_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.System.TypedMsg, com.yorha.proto.System.TypedMsg.Builder, com.yorha.proto.System.TypedMsgOrBuilder> typedMsgBuilder_;
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       * @return Whether the typedMsg field is set.
       */
      @java.lang.Override
      public boolean hasTypedMsg() {
        return fieldCase_ == 1;
      }
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       * @return The typedMsg.
       */
      @java.lang.Override
      public com.yorha.proto.System.TypedMsg getTypedMsg() {
        if (typedMsgBuilder_ == null) {
          if (fieldCase_ == 1) {
            return (com.yorha.proto.System.TypedMsg) field_;
          }
          return com.yorha.proto.System.TypedMsg.getDefaultInstance();
        } else {
          if (fieldCase_ == 1) {
            return typedMsgBuilder_.getMessage();
          }
          return com.yorha.proto.System.TypedMsg.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       */
      public Builder setTypedMsg(com.yorha.proto.System.TypedMsg value) {
        if (typedMsgBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          field_ = value;
          onChanged();
        } else {
          typedMsgBuilder_.setMessage(value);
        }
        fieldCase_ = 1;
        return this;
      }
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       */
      public Builder setTypedMsg(
          com.yorha.proto.System.TypedMsg.Builder builderForValue) {
        if (typedMsgBuilder_ == null) {
          field_ = builderForValue.build();
          onChanged();
        } else {
          typedMsgBuilder_.setMessage(builderForValue.build());
        }
        fieldCase_ = 1;
        return this;
      }
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       */
      public Builder mergeTypedMsg(com.yorha.proto.System.TypedMsg value) {
        if (typedMsgBuilder_ == null) {
          if (fieldCase_ == 1 &&
              field_ != com.yorha.proto.System.TypedMsg.getDefaultInstance()) {
            field_ = com.yorha.proto.System.TypedMsg.newBuilder((com.yorha.proto.System.TypedMsg) field_)
                .mergeFrom(value).buildPartial();
          } else {
            field_ = value;
          }
          onChanged();
        } else {
          if (fieldCase_ == 1) {
            typedMsgBuilder_.mergeFrom(value);
          }
          typedMsgBuilder_.setMessage(value);
        }
        fieldCase_ = 1;
        return this;
      }
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       */
      public Builder clearTypedMsg() {
        if (typedMsgBuilder_ == null) {
          if (fieldCase_ == 1) {
            fieldCase_ = 0;
            field_ = null;
            onChanged();
          }
        } else {
          if (fieldCase_ == 1) {
            fieldCase_ = 0;
            field_ = null;
          }
          typedMsgBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       */
      public com.yorha.proto.System.TypedMsg.Builder getTypedMsgBuilder() {
        return getTypedMsgFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       */
      @java.lang.Override
      public com.yorha.proto.System.TypedMsgOrBuilder getTypedMsgOrBuilder() {
        if ((fieldCase_ == 1) && (typedMsgBuilder_ != null)) {
          return typedMsgBuilder_.getMessageOrBuilder();
        } else {
          if (fieldCase_ == 1) {
            return (com.yorha.proto.System.TypedMsg) field_;
          }
          return com.yorha.proto.System.TypedMsg.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.TypedMsg typedMsg = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.System.TypedMsg, com.yorha.proto.System.TypedMsg.Builder, com.yorha.proto.System.TypedMsgOrBuilder> 
          getTypedMsgFieldBuilder() {
        if (typedMsgBuilder_ == null) {
          if (!(fieldCase_ == 1)) {
            field_ = com.yorha.proto.System.TypedMsg.getDefaultInstance();
          }
          typedMsgBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.System.TypedMsg, com.yorha.proto.System.TypedMsg.Builder, com.yorha.proto.System.TypedMsgOrBuilder>(
                  (com.yorha.proto.System.TypedMsg) field_,
                  getParentForChildren(),
                  isClean());
          field_ = null;
        }
        fieldCase_ = 1;
        onChanged();;
        return typedMsgBuilder_;
      }

      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.System.YoExceptionSnapshot, com.yorha.proto.System.YoExceptionSnapshot.Builder, com.yorha.proto.System.YoExceptionSnapshotOrBuilder> exceptionBuilder_;
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       * @return Whether the exception field is set.
       */
      @java.lang.Override
      public boolean hasException() {
        return fieldCase_ == 2;
      }
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       * @return The exception.
       */
      @java.lang.Override
      public com.yorha.proto.System.YoExceptionSnapshot getException() {
        if (exceptionBuilder_ == null) {
          if (fieldCase_ == 2) {
            return (com.yorha.proto.System.YoExceptionSnapshot) field_;
          }
          return com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance();
        } else {
          if (fieldCase_ == 2) {
            return exceptionBuilder_.getMessage();
          }
          return com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       */
      public Builder setException(com.yorha.proto.System.YoExceptionSnapshot value) {
        if (exceptionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          field_ = value;
          onChanged();
        } else {
          exceptionBuilder_.setMessage(value);
        }
        fieldCase_ = 2;
        return this;
      }
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       */
      public Builder setException(
          com.yorha.proto.System.YoExceptionSnapshot.Builder builderForValue) {
        if (exceptionBuilder_ == null) {
          field_ = builderForValue.build();
          onChanged();
        } else {
          exceptionBuilder_.setMessage(builderForValue.build());
        }
        fieldCase_ = 2;
        return this;
      }
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       */
      public Builder mergeException(com.yorha.proto.System.YoExceptionSnapshot value) {
        if (exceptionBuilder_ == null) {
          if (fieldCase_ == 2 &&
              field_ != com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance()) {
            field_ = com.yorha.proto.System.YoExceptionSnapshot.newBuilder((com.yorha.proto.System.YoExceptionSnapshot) field_)
                .mergeFrom(value).buildPartial();
          } else {
            field_ = value;
          }
          onChanged();
        } else {
          if (fieldCase_ == 2) {
            exceptionBuilder_.mergeFrom(value);
          }
          exceptionBuilder_.setMessage(value);
        }
        fieldCase_ = 2;
        return this;
      }
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       */
      public Builder clearException() {
        if (exceptionBuilder_ == null) {
          if (fieldCase_ == 2) {
            fieldCase_ = 0;
            field_ = null;
            onChanged();
          }
        } else {
          if (fieldCase_ == 2) {
            fieldCase_ = 0;
            field_ = null;
          }
          exceptionBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       */
      public com.yorha.proto.System.YoExceptionSnapshot.Builder getExceptionBuilder() {
        return getExceptionFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       */
      @java.lang.Override
      public com.yorha.proto.System.YoExceptionSnapshotOrBuilder getExceptionOrBuilder() {
        if ((fieldCase_ == 2) && (exceptionBuilder_ != null)) {
          return exceptionBuilder_.getMessageOrBuilder();
        } else {
          if (fieldCase_ == 2) {
            return (com.yorha.proto.System.YoExceptionSnapshot) field_;
          }
          return com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance();
        }
      }
      /**
       * <code>.com.yorha.proto.YoExceptionSnapshot exception = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.System.YoExceptionSnapshot, com.yorha.proto.System.YoExceptionSnapshot.Builder, com.yorha.proto.System.YoExceptionSnapshotOrBuilder> 
          getExceptionFieldBuilder() {
        if (exceptionBuilder_ == null) {
          if (!(fieldCase_ == 2)) {
            field_ = com.yorha.proto.System.YoExceptionSnapshot.getDefaultInstance();
          }
          exceptionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.System.YoExceptionSnapshot, com.yorha.proto.System.YoExceptionSnapshot.Builder, com.yorha.proto.System.YoExceptionSnapshotOrBuilder>(
                  (com.yorha.proto.System.YoExceptionSnapshot) field_,
                  getParentForChildren(),
                  isClean());
          field_ = null;
        }
        fieldCase_ = 2;
        onChanged();;
        return exceptionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.YoActorMsgPayload)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.YoActorMsgPayload)
    private static final com.yorha.proto.System.YoActorMsgPayload DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.System.YoActorMsgPayload();
    }

    public static com.yorha.proto.System.YoActorMsgPayload getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<YoActorMsgPayload>
        PARSER = new com.google.protobuf.AbstractParser<YoActorMsgPayload>() {
      @java.lang.Override
      public YoActorMsgPayload parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new YoActorMsgPayload(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<YoActorMsgPayload> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<YoActorMsgPayload> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.System.YoActorMsgPayload getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface YoActorMsgEnvelopeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.YoActorMsgEnvelope)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 接收者ref
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
     * @return Whether the sender field is set.
     */
    boolean hasSender();
    /**
     * <pre>
     * 接收者ref
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
     * @return The sender.
     */
    com.yorha.proto.CommonMsg.ActorRefData getSender();
    /**
     * <pre>
     * 接收者ref
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
     */
    com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSenderOrBuilder();

    /**
     * <pre>
     * 单播接收者，注意，此字段和单receiver不能同时存在。
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
     * @return Whether the receiver field is set.
     */
    boolean hasReceiver();
    /**
     * <pre>
     * 单播接收者，注意，此字段和单receiver不能同时存在。
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
     * @return The receiver.
     */
    com.yorha.proto.CommonMsg.ActorRefData getReceiver();
    /**
     * <pre>
     * 单播接收者，注意，此字段和单receiver不能同时存在。
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
     */
    com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getReceiverOrBuilder();

    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    java.util.List<com.yorha.proto.CommonMsg.ActorRefData> 
        getReceiverListList();
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    com.yorha.proto.CommonMsg.ActorRefData getReceiverList(int index);
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    int getReceiverListCount();
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    java.util.List<? extends com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
        getReceiverListOrBuilderList();
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getReceiverListOrBuilder(
        int index);

    /**
     * <pre>
     * 信封的唯一标识
     * </pre>
     *
     * <code>optional int64 msgSeqId = 4;</code>
     * @return Whether the msgSeqId field is set.
     */
    boolean hasMsgSeqId();
    /**
     * <pre>
     * 信封的唯一标识
     * </pre>
     *
     * <code>optional int64 msgSeqId = 4;</code>
     * @return The msgSeqId.
     */
    long getMsgSeqId();

    /**
     * <pre>
     * 标记位，位掩码
     * </pre>
     *
     * <code>optional int32 tag = 5;</code>
     * @return Whether the tag field is set.
     */
    boolean hasTag();
    /**
     * <pre>
     * 标记位，位掩码
     * </pre>
     *
     * <code>optional int32 tag = 5;</code>
     * @return The tag.
     */
    int getTag();

    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
     * @return Whether the payload field is set.
     */
    boolean hasPayload();
    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
     * @return The payload.
     */
    com.yorha.proto.System.YoActorMsgPayload getPayload();
    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
     */
    com.yorha.proto.System.YoActorMsgPayloadOrBuilder getPayloadOrBuilder();

    /**
     * <pre>
     * 链路跟踪
     * </pre>
     *
     * <code>optional bytes jaegerCarrier = 7;</code>
     * @return Whether the jaegerCarrier field is set.
     */
    boolean hasJaegerCarrier();
    /**
     * <pre>
     * 链路跟踪
     * </pre>
     *
     * <code>optional bytes jaegerCarrier = 7;</code>
     * @return The jaegerCarrier.
     */
    com.google.protobuf.ByteString getJaegerCarrier();

    /**
     * <pre>
     * 发送时间戳
     * </pre>
     *
     * <code>optional int64 sendTsMs = 8;</code>
     * @return Whether the sendTsMs field is set.
     */
    boolean hasSendTsMs();
    /**
     * <pre>
     * 发送时间戳
     * </pre>
     *
     * <code>optional int64 sendTsMs = 8;</code>
     * @return The sendTsMs.
     */
    long getSendTsMs();

    /**
     * <pre>
     * 信封 ask stage id，仅在andAsk、thenAsk这些场景使用; 当信封不符合上述场景，此数值不存在。
     * </pre>
     *
     * <code>optional int64 askStageId = 9;</code>
     * @return Whether the askStageId field is set.
     */
    boolean hasAskStageId();
    /**
     * <pre>
     * 信封 ask stage id，仅在andAsk、thenAsk这些场景使用; 当信封不符合上述场景，此数值不存在。
     * </pre>
     *
     * <code>optional int64 askStageId = 9;</code>
     * @return The askStageId.
     */
    long getAskStageId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.YoActorMsgEnvelope}
   */
  public static final class YoActorMsgEnvelope extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.YoActorMsgEnvelope)
      YoActorMsgEnvelopeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use YoActorMsgEnvelope.newBuilder() to construct.
    private YoActorMsgEnvelope(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private YoActorMsgEnvelope() {
      receiverList_ = java.util.Collections.emptyList();
      jaegerCarrier_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new YoActorMsgEnvelope();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private YoActorMsgEnvelope(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ActorRefData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = sender_.toBuilder();
              }
              sender_ = input.readMessage(com.yorha.proto.CommonMsg.ActorRefData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sender_);
                sender_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.ActorRefData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = receiver_.toBuilder();
              }
              receiver_ = input.readMessage(com.yorha.proto.CommonMsg.ActorRefData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(receiver_);
                receiver_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                receiverList_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ActorRefData>();
                mutable_bitField0_ |= 0x00000004;
              }
              receiverList_.add(
                  input.readMessage(com.yorha.proto.CommonMsg.ActorRefData.PARSER, extensionRegistry));
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              msgSeqId_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              tag_ = input.readInt32();
              break;
            }
            case 50: {
              com.yorha.proto.System.YoActorMsgPayload.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = payload_.toBuilder();
              }
              payload_ = input.readMessage(com.yorha.proto.System.YoActorMsgPayload.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(payload_);
                payload_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 58: {
              bitField0_ |= 0x00000020;
              jaegerCarrier_ = input.readBytes();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000040;
              sendTsMs_ = input.readInt64();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000080;
              askStageId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          receiverList_ = java.util.Collections.unmodifiableList(receiverList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgEnvelope_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgEnvelope_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.System.YoActorMsgEnvelope.class, com.yorha.proto.System.YoActorMsgEnvelope.Builder.class);
    }

    private int bitField0_;
    public static final int SENDER_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ActorRefData sender_;
    /**
     * <pre>
     * 接收者ref
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
     * @return Whether the sender field is set.
     */
    @java.lang.Override
    public boolean hasSender() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 接收者ref
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
     * @return The sender.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefData getSender() {
      return sender_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sender_;
    }
    /**
     * <pre>
     * 接收者ref
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSenderOrBuilder() {
      return sender_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sender_;
    }

    public static final int RECEIVER_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.ActorRefData receiver_;
    /**
     * <pre>
     * 单播接收者，注意，此字段和单receiver不能同时存在。
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
     * @return Whether the receiver field is set.
     */
    @java.lang.Override
    public boolean hasReceiver() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 单播接收者，注意，此字段和单receiver不能同时存在。
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
     * @return The receiver.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefData getReceiver() {
      return receiver_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : receiver_;
    }
    /**
     * <pre>
     * 单播接收者，注意，此字段和单receiver不能同时存在。
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getReceiverOrBuilder() {
      return receiver_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : receiver_;
    }

    public static final int RECEIVERLIST_FIELD_NUMBER = 3;
    private java.util.List<com.yorha.proto.CommonMsg.ActorRefData> receiverList_;
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.CommonMsg.ActorRefData> getReceiverListList() {
      return receiverList_;
    }
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
        getReceiverListOrBuilderList() {
      return receiverList_;
    }
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    @java.lang.Override
    public int getReceiverListCount() {
      return receiverList_.size();
    }
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefData getReceiverList(int index) {
      return receiverList_.get(index);
    }
    /**
     * <pre>
     * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getReceiverListOrBuilder(
        int index) {
      return receiverList_.get(index);
    }

    public static final int MSGSEQID_FIELD_NUMBER = 4;
    private long msgSeqId_;
    /**
     * <pre>
     * 信封的唯一标识
     * </pre>
     *
     * <code>optional int64 msgSeqId = 4;</code>
     * @return Whether the msgSeqId field is set.
     */
    @java.lang.Override
    public boolean hasMsgSeqId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 信封的唯一标识
     * </pre>
     *
     * <code>optional int64 msgSeqId = 4;</code>
     * @return The msgSeqId.
     */
    @java.lang.Override
    public long getMsgSeqId() {
      return msgSeqId_;
    }

    public static final int TAG_FIELD_NUMBER = 5;
    private int tag_;
    /**
     * <pre>
     * 标记位，位掩码
     * </pre>
     *
     * <code>optional int32 tag = 5;</code>
     * @return Whether the tag field is set.
     */
    @java.lang.Override
    public boolean hasTag() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 标记位，位掩码
     * </pre>
     *
     * <code>optional int32 tag = 5;</code>
     * @return The tag.
     */
    @java.lang.Override
    public int getTag() {
      return tag_;
    }

    public static final int PAYLOAD_FIELD_NUMBER = 6;
    private com.yorha.proto.System.YoActorMsgPayload payload_;
    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
     * @return Whether the payload field is set.
     */
    @java.lang.Override
    public boolean hasPayload() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
     * @return The payload.
     */
    @java.lang.Override
    public com.yorha.proto.System.YoActorMsgPayload getPayload() {
      return payload_ == null ? com.yorha.proto.System.YoActorMsgPayload.getDefaultInstance() : payload_;
    }
    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
     */
    @java.lang.Override
    public com.yorha.proto.System.YoActorMsgPayloadOrBuilder getPayloadOrBuilder() {
      return payload_ == null ? com.yorha.proto.System.YoActorMsgPayload.getDefaultInstance() : payload_;
    }

    public static final int JAEGERCARRIER_FIELD_NUMBER = 7;
    private com.google.protobuf.ByteString jaegerCarrier_;
    /**
     * <pre>
     * 链路跟踪
     * </pre>
     *
     * <code>optional bytes jaegerCarrier = 7;</code>
     * @return Whether the jaegerCarrier field is set.
     */
    @java.lang.Override
    public boolean hasJaegerCarrier() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 链路跟踪
     * </pre>
     *
     * <code>optional bytes jaegerCarrier = 7;</code>
     * @return The jaegerCarrier.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getJaegerCarrier() {
      return jaegerCarrier_;
    }

    public static final int SENDTSMS_FIELD_NUMBER = 8;
    private long sendTsMs_;
    /**
     * <pre>
     * 发送时间戳
     * </pre>
     *
     * <code>optional int64 sendTsMs = 8;</code>
     * @return Whether the sendTsMs field is set.
     */
    @java.lang.Override
    public boolean hasSendTsMs() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 发送时间戳
     * </pre>
     *
     * <code>optional int64 sendTsMs = 8;</code>
     * @return The sendTsMs.
     */
    @java.lang.Override
    public long getSendTsMs() {
      return sendTsMs_;
    }

    public static final int ASKSTAGEID_FIELD_NUMBER = 9;
    private long askStageId_;
    /**
     * <pre>
     * 信封 ask stage id，仅在andAsk、thenAsk这些场景使用; 当信封不符合上述场景，此数值不存在。
     * </pre>
     *
     * <code>optional int64 askStageId = 9;</code>
     * @return Whether the askStageId field is set.
     */
    @java.lang.Override
    public boolean hasAskStageId() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 信封 ask stage id，仅在andAsk、thenAsk这些场景使用; 当信封不符合上述场景，此数值不存在。
     * </pre>
     *
     * <code>optional int64 askStageId = 9;</code>
     * @return The askStageId.
     */
    @java.lang.Override
    public long getAskStageId() {
      return askStageId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getSender());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getReceiver());
      }
      for (int i = 0; i < receiverList_.size(); i++) {
        output.writeMessage(3, receiverList_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(4, msgSeqId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(5, tag_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(6, getPayload());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(7, jaegerCarrier_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt64(8, sendTsMs_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt64(9, askStageId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getSender());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getReceiver());
      }
      for (int i = 0; i < receiverList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, receiverList_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, msgSeqId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, tag_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getPayload());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, jaegerCarrier_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, sendTsMs_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, askStageId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.System.YoActorMsgEnvelope)) {
        return super.equals(obj);
      }
      com.yorha.proto.System.YoActorMsgEnvelope other = (com.yorha.proto.System.YoActorMsgEnvelope) obj;

      if (hasSender() != other.hasSender()) return false;
      if (hasSender()) {
        if (!getSender()
            .equals(other.getSender())) return false;
      }
      if (hasReceiver() != other.hasReceiver()) return false;
      if (hasReceiver()) {
        if (!getReceiver()
            .equals(other.getReceiver())) return false;
      }
      if (!getReceiverListList()
          .equals(other.getReceiverListList())) return false;
      if (hasMsgSeqId() != other.hasMsgSeqId()) return false;
      if (hasMsgSeqId()) {
        if (getMsgSeqId()
            != other.getMsgSeqId()) return false;
      }
      if (hasTag() != other.hasTag()) return false;
      if (hasTag()) {
        if (getTag()
            != other.getTag()) return false;
      }
      if (hasPayload() != other.hasPayload()) return false;
      if (hasPayload()) {
        if (!getPayload()
            .equals(other.getPayload())) return false;
      }
      if (hasJaegerCarrier() != other.hasJaegerCarrier()) return false;
      if (hasJaegerCarrier()) {
        if (!getJaegerCarrier()
            .equals(other.getJaegerCarrier())) return false;
      }
      if (hasSendTsMs() != other.hasSendTsMs()) return false;
      if (hasSendTsMs()) {
        if (getSendTsMs()
            != other.getSendTsMs()) return false;
      }
      if (hasAskStageId() != other.hasAskStageId()) return false;
      if (hasAskStageId()) {
        if (getAskStageId()
            != other.getAskStageId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSender()) {
        hash = (37 * hash) + SENDER_FIELD_NUMBER;
        hash = (53 * hash) + getSender().hashCode();
      }
      if (hasReceiver()) {
        hash = (37 * hash) + RECEIVER_FIELD_NUMBER;
        hash = (53 * hash) + getReceiver().hashCode();
      }
      if (getReceiverListCount() > 0) {
        hash = (37 * hash) + RECEIVERLIST_FIELD_NUMBER;
        hash = (53 * hash) + getReceiverListList().hashCode();
      }
      if (hasMsgSeqId()) {
        hash = (37 * hash) + MSGSEQID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMsgSeqId());
      }
      if (hasTag()) {
        hash = (37 * hash) + TAG_FIELD_NUMBER;
        hash = (53 * hash) + getTag();
      }
      if (hasPayload()) {
        hash = (37 * hash) + PAYLOAD_FIELD_NUMBER;
        hash = (53 * hash) + getPayload().hashCode();
      }
      if (hasJaegerCarrier()) {
        hash = (37 * hash) + JAEGERCARRIER_FIELD_NUMBER;
        hash = (53 * hash) + getJaegerCarrier().hashCode();
      }
      if (hasSendTsMs()) {
        hash = (37 * hash) + SENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSendTsMs());
      }
      if (hasAskStageId()) {
        hash = (37 * hash) + ASKSTAGEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getAskStageId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.YoActorMsgEnvelope parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.System.YoActorMsgEnvelope prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.YoActorMsgEnvelope}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.YoActorMsgEnvelope)
        com.yorha.proto.System.YoActorMsgEnvelopeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgEnvelope_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgEnvelope_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.System.YoActorMsgEnvelope.class, com.yorha.proto.System.YoActorMsgEnvelope.Builder.class);
      }

      // Construct using com.yorha.proto.System.YoActorMsgEnvelope.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSenderFieldBuilder();
          getReceiverFieldBuilder();
          getReceiverListFieldBuilder();
          getPayloadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (senderBuilder_ == null) {
          sender_ = null;
        } else {
          senderBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (receiverBuilder_ == null) {
          receiver_ = null;
        } else {
          receiverBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (receiverListBuilder_ == null) {
          receiverList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          receiverListBuilder_.clear();
        }
        msgSeqId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        tag_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        if (payloadBuilder_ == null) {
          payload_ = null;
        } else {
          payloadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        jaegerCarrier_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        sendTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000080);
        askStageId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_YoActorMsgEnvelope_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.System.YoActorMsgEnvelope getDefaultInstanceForType() {
        return com.yorha.proto.System.YoActorMsgEnvelope.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.System.YoActorMsgEnvelope build() {
        com.yorha.proto.System.YoActorMsgEnvelope result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.System.YoActorMsgEnvelope buildPartial() {
        com.yorha.proto.System.YoActorMsgEnvelope result = new com.yorha.proto.System.YoActorMsgEnvelope(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (senderBuilder_ == null) {
            result.sender_ = sender_;
          } else {
            result.sender_ = senderBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (receiverBuilder_ == null) {
            result.receiver_ = receiver_;
          } else {
            result.receiver_ = receiverBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (receiverListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            receiverList_ = java.util.Collections.unmodifiableList(receiverList_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.receiverList_ = receiverList_;
        } else {
          result.receiverList_ = receiverListBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.msgSeqId_ = msgSeqId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.tag_ = tag_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          if (payloadBuilder_ == null) {
            result.payload_ = payload_;
          } else {
            result.payload_ = payloadBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.jaegerCarrier_ = jaegerCarrier_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.sendTsMs_ = sendTsMs_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.askStageId_ = askStageId_;
          to_bitField0_ |= 0x00000080;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.System.YoActorMsgEnvelope) {
          return mergeFrom((com.yorha.proto.System.YoActorMsgEnvelope)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.System.YoActorMsgEnvelope other) {
        if (other == com.yorha.proto.System.YoActorMsgEnvelope.getDefaultInstance()) return this;
        if (other.hasSender()) {
          mergeSender(other.getSender());
        }
        if (other.hasReceiver()) {
          mergeReceiver(other.getReceiver());
        }
        if (receiverListBuilder_ == null) {
          if (!other.receiverList_.isEmpty()) {
            if (receiverList_.isEmpty()) {
              receiverList_ = other.receiverList_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureReceiverListIsMutable();
              receiverList_.addAll(other.receiverList_);
            }
            onChanged();
          }
        } else {
          if (!other.receiverList_.isEmpty()) {
            if (receiverListBuilder_.isEmpty()) {
              receiverListBuilder_.dispose();
              receiverListBuilder_ = null;
              receiverList_ = other.receiverList_;
              bitField0_ = (bitField0_ & ~0x00000004);
              receiverListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getReceiverListFieldBuilder() : null;
            } else {
              receiverListBuilder_.addAllMessages(other.receiverList_);
            }
          }
        }
        if (other.hasMsgSeqId()) {
          setMsgSeqId(other.getMsgSeqId());
        }
        if (other.hasTag()) {
          setTag(other.getTag());
        }
        if (other.hasPayload()) {
          mergePayload(other.getPayload());
        }
        if (other.hasJaegerCarrier()) {
          setJaegerCarrier(other.getJaegerCarrier());
        }
        if (other.hasSendTsMs()) {
          setSendTsMs(other.getSendTsMs());
        }
        if (other.hasAskStageId()) {
          setAskStageId(other.getAskStageId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.System.YoActorMsgEnvelope parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.System.YoActorMsgEnvelope) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ActorRefData sender_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> senderBuilder_;
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       * @return Whether the sender field is set.
       */
      public boolean hasSender() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       * @return The sender.
       */
      public com.yorha.proto.CommonMsg.ActorRefData getSender() {
        if (senderBuilder_ == null) {
          return sender_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sender_;
        } else {
          return senderBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       */
      public Builder setSender(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (senderBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sender_ = value;
          onChanged();
        } else {
          senderBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       */
      public Builder setSender(
          com.yorha.proto.CommonMsg.ActorRefData.Builder builderForValue) {
        if (senderBuilder_ == null) {
          sender_ = builderForValue.build();
          onChanged();
        } else {
          senderBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       */
      public Builder mergeSender(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (senderBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              sender_ != null &&
              sender_ != com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance()) {
            sender_ =
              com.yorha.proto.CommonMsg.ActorRefData.newBuilder(sender_).mergeFrom(value).buildPartial();
          } else {
            sender_ = value;
          }
          onChanged();
        } else {
          senderBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       */
      public Builder clearSender() {
        if (senderBuilder_ == null) {
          sender_ = null;
          onChanged();
        } else {
          senderBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData.Builder getSenderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSenderFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSenderOrBuilder() {
        if (senderBuilder_ != null) {
          return senderBuilder_.getMessageOrBuilder();
        } else {
          return sender_ == null ?
              com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sender_;
        }
      }
      /**
       * <pre>
       * 接收者ref
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sender = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
          getSenderFieldBuilder() {
        if (senderBuilder_ == null) {
          senderBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder>(
                  getSender(),
                  getParentForChildren(),
                  isClean());
          sender_ = null;
        }
        return senderBuilder_;
      }

      private com.yorha.proto.CommonMsg.ActorRefData receiver_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> receiverBuilder_;
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       * @return Whether the receiver field is set.
       */
      public boolean hasReceiver() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       * @return The receiver.
       */
      public com.yorha.proto.CommonMsg.ActorRefData getReceiver() {
        if (receiverBuilder_ == null) {
          return receiver_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : receiver_;
        } else {
          return receiverBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       */
      public Builder setReceiver(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (receiverBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          receiver_ = value;
          onChanged();
        } else {
          receiverBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       */
      public Builder setReceiver(
          com.yorha.proto.CommonMsg.ActorRefData.Builder builderForValue) {
        if (receiverBuilder_ == null) {
          receiver_ = builderForValue.build();
          onChanged();
        } else {
          receiverBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       */
      public Builder mergeReceiver(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (receiverBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              receiver_ != null &&
              receiver_ != com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance()) {
            receiver_ =
              com.yorha.proto.CommonMsg.ActorRefData.newBuilder(receiver_).mergeFrom(value).buildPartial();
          } else {
            receiver_ = value;
          }
          onChanged();
        } else {
          receiverBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       */
      public Builder clearReceiver() {
        if (receiverBuilder_ == null) {
          receiver_ = null;
          onChanged();
        } else {
          receiverBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData.Builder getReceiverBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getReceiverFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getReceiverOrBuilder() {
        if (receiverBuilder_ != null) {
          return receiverBuilder_.getMessageOrBuilder();
        } else {
          return receiver_ == null ?
              com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : receiver_;
        }
      }
      /**
       * <pre>
       * 单播接收者，注意，此字段和单receiver不能同时存在。
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData receiver = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
          getReceiverFieldBuilder() {
        if (receiverBuilder_ == null) {
          receiverBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder>(
                  getReceiver(),
                  getParentForChildren(),
                  isClean());
          receiver_ = null;
        }
        return receiverBuilder_;
      }

      private java.util.List<com.yorha.proto.CommonMsg.ActorRefData> receiverList_ =
        java.util.Collections.emptyList();
      private void ensureReceiverListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          receiverList_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ActorRefData>(receiverList_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> receiverListBuilder_;

      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ActorRefData> getReceiverListList() {
        if (receiverListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(receiverList_);
        } else {
          return receiverListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public int getReceiverListCount() {
        if (receiverListBuilder_ == null) {
          return receiverList_.size();
        } else {
          return receiverListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData getReceiverList(int index) {
        if (receiverListBuilder_ == null) {
          return receiverList_.get(index);
        } else {
          return receiverListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder setReceiverList(
          int index, com.yorha.proto.CommonMsg.ActorRefData value) {
        if (receiverListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureReceiverListIsMutable();
          receiverList_.set(index, value);
          onChanged();
        } else {
          receiverListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder setReceiverList(
          int index, com.yorha.proto.CommonMsg.ActorRefData.Builder builderForValue) {
        if (receiverListBuilder_ == null) {
          ensureReceiverListIsMutable();
          receiverList_.set(index, builderForValue.build());
          onChanged();
        } else {
          receiverListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder addReceiverList(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (receiverListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureReceiverListIsMutable();
          receiverList_.add(value);
          onChanged();
        } else {
          receiverListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder addReceiverList(
          int index, com.yorha.proto.CommonMsg.ActorRefData value) {
        if (receiverListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureReceiverListIsMutable();
          receiverList_.add(index, value);
          onChanged();
        } else {
          receiverListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder addReceiverList(
          com.yorha.proto.CommonMsg.ActorRefData.Builder builderForValue) {
        if (receiverListBuilder_ == null) {
          ensureReceiverListIsMutable();
          receiverList_.add(builderForValue.build());
          onChanged();
        } else {
          receiverListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder addReceiverList(
          int index, com.yorha.proto.CommonMsg.ActorRefData.Builder builderForValue) {
        if (receiverListBuilder_ == null) {
          ensureReceiverListIsMutable();
          receiverList_.add(index, builderForValue.build());
          onChanged();
        } else {
          receiverListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder addAllReceiverList(
          java.lang.Iterable<? extends com.yorha.proto.CommonMsg.ActorRefData> values) {
        if (receiverListBuilder_ == null) {
          ensureReceiverListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, receiverList_);
          onChanged();
        } else {
          receiverListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder clearReceiverList() {
        if (receiverListBuilder_ == null) {
          receiverList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          receiverListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public Builder removeReceiverList(int index) {
        if (receiverListBuilder_ == null) {
          ensureReceiverListIsMutable();
          receiverList_.remove(index);
          onChanged();
        } else {
          receiverListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData.Builder getReceiverListBuilder(
          int index) {
        return getReceiverListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getReceiverListOrBuilder(
          int index) {
        if (receiverListBuilder_ == null) {
          return receiverList_.get(index);  } else {
          return receiverListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public java.util.List<? extends com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
           getReceiverListOrBuilderList() {
        if (receiverListBuilder_ != null) {
          return receiverListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(receiverList_);
        }
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData.Builder addReceiverListBuilder() {
        return getReceiverListFieldBuilder().addBuilder(
            com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance());
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData.Builder addReceiverListBuilder(
          int index) {
        return getReceiverListFieldBuilder().addBuilder(
            index, com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance());
      }
      /**
       * <pre>
       * 组播的接收者列表，注意，此字段和单receiverList不能同时存在。
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ActorRefData receiverList = 3;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ActorRefData.Builder> 
           getReceiverListBuilderList() {
        return getReceiverListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
          getReceiverListFieldBuilder() {
        if (receiverListBuilder_ == null) {
          receiverListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder>(
                  receiverList_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          receiverList_ = null;
        }
        return receiverListBuilder_;
      }

      private long msgSeqId_ ;
      /**
       * <pre>
       * 信封的唯一标识
       * </pre>
       *
       * <code>optional int64 msgSeqId = 4;</code>
       * @return Whether the msgSeqId field is set.
       */
      @java.lang.Override
      public boolean hasMsgSeqId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 信封的唯一标识
       * </pre>
       *
       * <code>optional int64 msgSeqId = 4;</code>
       * @return The msgSeqId.
       */
      @java.lang.Override
      public long getMsgSeqId() {
        return msgSeqId_;
      }
      /**
       * <pre>
       * 信封的唯一标识
       * </pre>
       *
       * <code>optional int64 msgSeqId = 4;</code>
       * @param value The msgSeqId to set.
       * @return This builder for chaining.
       */
      public Builder setMsgSeqId(long value) {
        bitField0_ |= 0x00000008;
        msgSeqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 信封的唯一标识
       * </pre>
       *
       * <code>optional int64 msgSeqId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgSeqId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        msgSeqId_ = 0L;
        onChanged();
        return this;
      }

      private int tag_ ;
      /**
       * <pre>
       * 标记位，位掩码
       * </pre>
       *
       * <code>optional int32 tag = 5;</code>
       * @return Whether the tag field is set.
       */
      @java.lang.Override
      public boolean hasTag() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 标记位，位掩码
       * </pre>
       *
       * <code>optional int32 tag = 5;</code>
       * @return The tag.
       */
      @java.lang.Override
      public int getTag() {
        return tag_;
      }
      /**
       * <pre>
       * 标记位，位掩码
       * </pre>
       *
       * <code>optional int32 tag = 5;</code>
       * @param value The tag to set.
       * @return This builder for chaining.
       */
      public Builder setTag(int value) {
        bitField0_ |= 0x00000010;
        tag_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记位，位掩码
       * </pre>
       *
       * <code>optional int32 tag = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTag() {
        bitField0_ = (bitField0_ & ~0x00000010);
        tag_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.System.YoActorMsgPayload payload_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.System.YoActorMsgPayload, com.yorha.proto.System.YoActorMsgPayload.Builder, com.yorha.proto.System.YoActorMsgPayloadOrBuilder> payloadBuilder_;
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       * @return Whether the payload field is set.
       */
      public boolean hasPayload() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       * @return The payload.
       */
      public com.yorha.proto.System.YoActorMsgPayload getPayload() {
        if (payloadBuilder_ == null) {
          return payload_ == null ? com.yorha.proto.System.YoActorMsgPayload.getDefaultInstance() : payload_;
        } else {
          return payloadBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       */
      public Builder setPayload(com.yorha.proto.System.YoActorMsgPayload value) {
        if (payloadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payload_ = value;
          onChanged();
        } else {
          payloadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       */
      public Builder setPayload(
          com.yorha.proto.System.YoActorMsgPayload.Builder builderForValue) {
        if (payloadBuilder_ == null) {
          payload_ = builderForValue.build();
          onChanged();
        } else {
          payloadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       */
      public Builder mergePayload(com.yorha.proto.System.YoActorMsgPayload value) {
        if (payloadBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
              payload_ != null &&
              payload_ != com.yorha.proto.System.YoActorMsgPayload.getDefaultInstance()) {
            payload_ =
              com.yorha.proto.System.YoActorMsgPayload.newBuilder(payload_).mergeFrom(value).buildPartial();
          } else {
            payload_ = value;
          }
          onChanged();
        } else {
          payloadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       */
      public Builder clearPayload() {
        if (payloadBuilder_ == null) {
          payload_ = null;
          onChanged();
        } else {
          payloadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       */
      public com.yorha.proto.System.YoActorMsgPayload.Builder getPayloadBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getPayloadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       */
      public com.yorha.proto.System.YoActorMsgPayloadOrBuilder getPayloadOrBuilder() {
        if (payloadBuilder_ != null) {
          return payloadBuilder_.getMessageOrBuilder();
        } else {
          return payload_ == null ?
              com.yorha.proto.System.YoActorMsgPayload.getDefaultInstance() : payload_;
        }
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgPayload payload = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.System.YoActorMsgPayload, com.yorha.proto.System.YoActorMsgPayload.Builder, com.yorha.proto.System.YoActorMsgPayloadOrBuilder> 
          getPayloadFieldBuilder() {
        if (payloadBuilder_ == null) {
          payloadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.System.YoActorMsgPayload, com.yorha.proto.System.YoActorMsgPayload.Builder, com.yorha.proto.System.YoActorMsgPayloadOrBuilder>(
                  getPayload(),
                  getParentForChildren(),
                  isClean());
          payload_ = null;
        }
        return payloadBuilder_;
      }

      private com.google.protobuf.ByteString jaegerCarrier_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * 链路跟踪
       * </pre>
       *
       * <code>optional bytes jaegerCarrier = 7;</code>
       * @return Whether the jaegerCarrier field is set.
       */
      @java.lang.Override
      public boolean hasJaegerCarrier() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 链路跟踪
       * </pre>
       *
       * <code>optional bytes jaegerCarrier = 7;</code>
       * @return The jaegerCarrier.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getJaegerCarrier() {
        return jaegerCarrier_;
      }
      /**
       * <pre>
       * 链路跟踪
       * </pre>
       *
       * <code>optional bytes jaegerCarrier = 7;</code>
       * @param value The jaegerCarrier to set.
       * @return This builder for chaining.
       */
      public Builder setJaegerCarrier(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        jaegerCarrier_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 链路跟踪
       * </pre>
       *
       * <code>optional bytes jaegerCarrier = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearJaegerCarrier() {
        bitField0_ = (bitField0_ & ~0x00000040);
        jaegerCarrier_ = getDefaultInstance().getJaegerCarrier();
        onChanged();
        return this;
      }

      private long sendTsMs_ ;
      /**
       * <pre>
       * 发送时间戳
       * </pre>
       *
       * <code>optional int64 sendTsMs = 8;</code>
       * @return Whether the sendTsMs field is set.
       */
      @java.lang.Override
      public boolean hasSendTsMs() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 发送时间戳
       * </pre>
       *
       * <code>optional int64 sendTsMs = 8;</code>
       * @return The sendTsMs.
       */
      @java.lang.Override
      public long getSendTsMs() {
        return sendTsMs_;
      }
      /**
       * <pre>
       * 发送时间戳
       * </pre>
       *
       * <code>optional int64 sendTsMs = 8;</code>
       * @param value The sendTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setSendTsMs(long value) {
        bitField0_ |= 0x00000080;
        sendTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发送时间戳
       * </pre>
       *
       * <code>optional int64 sendTsMs = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearSendTsMs() {
        bitField0_ = (bitField0_ & ~0x00000080);
        sendTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long askStageId_ ;
      /**
       * <pre>
       * 信封 ask stage id，仅在andAsk、thenAsk这些场景使用; 当信封不符合上述场景，此数值不存在。
       * </pre>
       *
       * <code>optional int64 askStageId = 9;</code>
       * @return Whether the askStageId field is set.
       */
      @java.lang.Override
      public boolean hasAskStageId() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 信封 ask stage id，仅在andAsk、thenAsk这些场景使用; 当信封不符合上述场景，此数值不存在。
       * </pre>
       *
       * <code>optional int64 askStageId = 9;</code>
       * @return The askStageId.
       */
      @java.lang.Override
      public long getAskStageId() {
        return askStageId_;
      }
      /**
       * <pre>
       * 信封 ask stage id，仅在andAsk、thenAsk这些场景使用; 当信封不符合上述场景，此数值不存在。
       * </pre>
       *
       * <code>optional int64 askStageId = 9;</code>
       * @param value The askStageId to set.
       * @return This builder for chaining.
       */
      public Builder setAskStageId(long value) {
        bitField0_ |= 0x00000100;
        askStageId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 信封 ask stage id，仅在andAsk、thenAsk这些场景使用; 当信封不符合上述场景，此数值不存在。
       * </pre>
       *
       * <code>optional int64 askStageId = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearAskStageId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        askStageId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.YoActorMsgEnvelope)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.YoActorMsgEnvelope)
    private static final com.yorha.proto.System.YoActorMsgEnvelope DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.System.YoActorMsgEnvelope();
    }

    public static com.yorha.proto.System.YoActorMsgEnvelope getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<YoActorMsgEnvelope>
        PARSER = new com.google.protobuf.AbstractParser<YoActorMsgEnvelope>() {
      @java.lang.Override
      public YoActorMsgEnvelope parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new YoActorMsgEnvelope(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<YoActorMsgEnvelope> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<YoActorMsgEnvelope> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.System.YoActorMsgEnvelope getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OffLineRpcOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OffLineRpc)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * RPC消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
     * @return Whether the msg field is set.
     */
    boolean hasMsg();
    /**
     * <pre>
     * RPC消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
     * @return The msg.
     */
    com.yorha.proto.System.YoActorMsgEnvelope getMsg();
    /**
     * <pre>
     * RPC消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
     */
    com.yorha.proto.System.YoActorMsgEnvelopeOrBuilder getMsgOrBuilder();

    /**
     * <pre>
     * 过期时间，过期时间后的rpc不处理
     * </pre>
     *
     * <code>optional int64 expireTsMs = 2;</code>
     * @return Whether the expireTsMs field is set.
     */
    boolean hasExpireTsMs();
    /**
     * <pre>
     * 过期时间，过期时间后的rpc不处理
     * </pre>
     *
     * <code>optional int64 expireTsMs = 2;</code>
     * @return The expireTsMs.
     */
    long getExpireTsMs();
  }
  /**
   * <pre>
   * 离线RPC
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.OffLineRpc}
   */
  public static final class OffLineRpc extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OffLineRpc)
      OffLineRpcOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OffLineRpc.newBuilder() to construct.
    private OffLineRpc(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OffLineRpc() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OffLineRpc();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OffLineRpc(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.System.YoActorMsgEnvelope.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = msg_.toBuilder();
              }
              msg_ = input.readMessage(com.yorha.proto.System.YoActorMsgEnvelope.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(msg_);
                msg_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              expireTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_OffLineRpc_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.System.internal_static_com_yorha_proto_OffLineRpc_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.System.OffLineRpc.class, com.yorha.proto.System.OffLineRpc.Builder.class);
    }

    private int bitField0_;
    public static final int MSG_FIELD_NUMBER = 1;
    private com.yorha.proto.System.YoActorMsgEnvelope msg_;
    /**
     * <pre>
     * RPC消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
     * @return Whether the msg field is set.
     */
    @java.lang.Override
    public boolean hasMsg() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * RPC消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
     * @return The msg.
     */
    @java.lang.Override
    public com.yorha.proto.System.YoActorMsgEnvelope getMsg() {
      return msg_ == null ? com.yorha.proto.System.YoActorMsgEnvelope.getDefaultInstance() : msg_;
    }
    /**
     * <pre>
     * RPC消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.System.YoActorMsgEnvelopeOrBuilder getMsgOrBuilder() {
      return msg_ == null ? com.yorha.proto.System.YoActorMsgEnvelope.getDefaultInstance() : msg_;
    }

    public static final int EXPIRETSMS_FIELD_NUMBER = 2;
    private long expireTsMs_;
    /**
     * <pre>
     * 过期时间，过期时间后的rpc不处理
     * </pre>
     *
     * <code>optional int64 expireTsMs = 2;</code>
     * @return Whether the expireTsMs field is set.
     */
    @java.lang.Override
    public boolean hasExpireTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 过期时间，过期时间后的rpc不处理
     * </pre>
     *
     * <code>optional int64 expireTsMs = 2;</code>
     * @return The expireTsMs.
     */
    @java.lang.Override
    public long getExpireTsMs() {
      return expireTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getMsg());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, expireTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getMsg());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, expireTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.System.OffLineRpc)) {
        return super.equals(obj);
      }
      com.yorha.proto.System.OffLineRpc other = (com.yorha.proto.System.OffLineRpc) obj;

      if (hasMsg() != other.hasMsg()) return false;
      if (hasMsg()) {
        if (!getMsg()
            .equals(other.getMsg())) return false;
      }
      if (hasExpireTsMs() != other.hasExpireTsMs()) return false;
      if (hasExpireTsMs()) {
        if (getExpireTsMs()
            != other.getExpireTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsg()) {
        hash = (37 * hash) + MSG_FIELD_NUMBER;
        hash = (53 * hash) + getMsg().hashCode();
      }
      if (hasExpireTsMs()) {
        hash = (37 * hash) + EXPIRETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getExpireTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.System.OffLineRpc parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.OffLineRpc parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.OffLineRpc parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.System.OffLineRpc parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.System.OffLineRpc prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 离线RPC
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.OffLineRpc}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OffLineRpc)
        com.yorha.proto.System.OffLineRpcOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_OffLineRpc_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_OffLineRpc_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.System.OffLineRpc.class, com.yorha.proto.System.OffLineRpc.Builder.class);
      }

      // Construct using com.yorha.proto.System.OffLineRpc.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMsgFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (msgBuilder_ == null) {
          msg_ = null;
        } else {
          msgBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        expireTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.System.internal_static_com_yorha_proto_OffLineRpc_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.System.OffLineRpc getDefaultInstanceForType() {
        return com.yorha.proto.System.OffLineRpc.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.System.OffLineRpc build() {
        com.yorha.proto.System.OffLineRpc result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.System.OffLineRpc buildPartial() {
        com.yorha.proto.System.OffLineRpc result = new com.yorha.proto.System.OffLineRpc(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (msgBuilder_ == null) {
            result.msg_ = msg_;
          } else {
            result.msg_ = msgBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.expireTsMs_ = expireTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.System.OffLineRpc) {
          return mergeFrom((com.yorha.proto.System.OffLineRpc)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.System.OffLineRpc other) {
        if (other == com.yorha.proto.System.OffLineRpc.getDefaultInstance()) return this;
        if (other.hasMsg()) {
          mergeMsg(other.getMsg());
        }
        if (other.hasExpireTsMs()) {
          setExpireTsMs(other.getExpireTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.System.OffLineRpc parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.System.OffLineRpc) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.System.YoActorMsgEnvelope msg_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.System.YoActorMsgEnvelope, com.yorha.proto.System.YoActorMsgEnvelope.Builder, com.yorha.proto.System.YoActorMsgEnvelopeOrBuilder> msgBuilder_;
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       * @return Whether the msg field is set.
       */
      public boolean hasMsg() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       * @return The msg.
       */
      public com.yorha.proto.System.YoActorMsgEnvelope getMsg() {
        if (msgBuilder_ == null) {
          return msg_ == null ? com.yorha.proto.System.YoActorMsgEnvelope.getDefaultInstance() : msg_;
        } else {
          return msgBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       */
      public Builder setMsg(com.yorha.proto.System.YoActorMsgEnvelope value) {
        if (msgBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          msg_ = value;
          onChanged();
        } else {
          msgBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       */
      public Builder setMsg(
          com.yorha.proto.System.YoActorMsgEnvelope.Builder builderForValue) {
        if (msgBuilder_ == null) {
          msg_ = builderForValue.build();
          onChanged();
        } else {
          msgBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       */
      public Builder mergeMsg(com.yorha.proto.System.YoActorMsgEnvelope value) {
        if (msgBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              msg_ != null &&
              msg_ != com.yorha.proto.System.YoActorMsgEnvelope.getDefaultInstance()) {
            msg_ =
              com.yorha.proto.System.YoActorMsgEnvelope.newBuilder(msg_).mergeFrom(value).buildPartial();
          } else {
            msg_ = value;
          }
          onChanged();
        } else {
          msgBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       */
      public Builder clearMsg() {
        if (msgBuilder_ == null) {
          msg_ = null;
          onChanged();
        } else {
          msgBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       */
      public com.yorha.proto.System.YoActorMsgEnvelope.Builder getMsgBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getMsgFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       */
      public com.yorha.proto.System.YoActorMsgEnvelopeOrBuilder getMsgOrBuilder() {
        if (msgBuilder_ != null) {
          return msgBuilder_.getMessageOrBuilder();
        } else {
          return msg_ == null ?
              com.yorha.proto.System.YoActorMsgEnvelope.getDefaultInstance() : msg_;
        }
      }
      /**
       * <pre>
       * RPC消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoActorMsgEnvelope msg = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.System.YoActorMsgEnvelope, com.yorha.proto.System.YoActorMsgEnvelope.Builder, com.yorha.proto.System.YoActorMsgEnvelopeOrBuilder> 
          getMsgFieldBuilder() {
        if (msgBuilder_ == null) {
          msgBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.System.YoActorMsgEnvelope, com.yorha.proto.System.YoActorMsgEnvelope.Builder, com.yorha.proto.System.YoActorMsgEnvelopeOrBuilder>(
                  getMsg(),
                  getParentForChildren(),
                  isClean());
          msg_ = null;
        }
        return msgBuilder_;
      }

      private long expireTsMs_ ;
      /**
       * <pre>
       * 过期时间，过期时间后的rpc不处理
       * </pre>
       *
       * <code>optional int64 expireTsMs = 2;</code>
       * @return Whether the expireTsMs field is set.
       */
      @java.lang.Override
      public boolean hasExpireTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 过期时间，过期时间后的rpc不处理
       * </pre>
       *
       * <code>optional int64 expireTsMs = 2;</code>
       * @return The expireTsMs.
       */
      @java.lang.Override
      public long getExpireTsMs() {
        return expireTsMs_;
      }
      /**
       * <pre>
       * 过期时间，过期时间后的rpc不处理
       * </pre>
       *
       * <code>optional int64 expireTsMs = 2;</code>
       * @param value The expireTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setExpireTsMs(long value) {
        bitField0_ |= 0x00000002;
        expireTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 过期时间，过期时间后的rpc不处理
       * </pre>
       *
       * <code>optional int64 expireTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearExpireTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        expireTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OffLineRpc)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OffLineRpc)
    private static final com.yorha.proto.System.OffLineRpc DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.System.OffLineRpc();
    }

    public static com.yorha.proto.System.OffLineRpc getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OffLineRpc>
        PARSER = new com.google.protobuf.AbstractParser<OffLineRpc>() {
      @java.lang.Override
      public OffLineRpc parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OffLineRpc(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OffLineRpc> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OffLineRpc> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.System.OffLineRpc getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_TypedMsg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_TypedMsg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_YoExceptionSnapshot_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_YoExceptionSnapshot_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_YoActorMsgPayload_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_YoActorMsgPayload_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_YoActorMsgEnvelope_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_YoActorMsgEnvelope_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OffLineRpc_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OffLineRpc_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n ss_proto/gen/server/system.proto\022\017com." +
      "yorha.proto\032$ss_proto/gen/common/common_" +
      "msg.proto\")\n\010TypedMsg\022\017\n\007msgType\030\001 \001(\005\022\014" +
      "\n\004data\030\002 \001(\014\"X\n\023YoExceptionSnapshot\022\021\n\tc" +
      "lazzName\030\001 \001(\t\022\021\n\terrorCode\030\002 \001(\005\022\013\n\003msg" +
      "\030\003 \001(\t\022\016\n\006zoneId\030\004 \001(\005\"\206\001\n\021YoActorMsgPay" +
      "load\022-\n\010typedMsg\030\001 \001(\0132\031.com.yorha.proto" +
      ".TypedMsgH\000\0229\n\texception\030\002 \001(\0132$.com.yor" +
      "ha.proto.YoExceptionSnapshotH\000B\007\n\005field\"" +
      "\272\002\n\022YoActorMsgEnvelope\022-\n\006sender\030\001 \001(\0132\035" +
      ".com.yorha.proto.ActorRefData\022/\n\010receive" +
      "r\030\002 \001(\0132\035.com.yorha.proto.ActorRefData\0223" +
      "\n\014receiverList\030\003 \003(\0132\035.com.yorha.proto.A" +
      "ctorRefData\022\020\n\010msgSeqId\030\004 \001(\003\022\013\n\003tag\030\005 \001" +
      "(\005\0223\n\007payload\030\006 \001(\0132\".com.yorha.proto.Yo" +
      "ActorMsgPayload\022\025\n\rjaegerCarrier\030\007 \001(\014\022\020" +
      "\n\010sendTsMs\030\010 \001(\003\022\022\n\naskStageId\030\t \001(\003\"R\n\n" +
      "OffLineRpc\0220\n\003msg\030\001 \001(\0132#.com.yorha.prot" +
      "o.YoActorMsgEnvelope\022\022\n\nexpireTsMs\030\002 \001(\003" +
      "B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_TypedMsg_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_TypedMsg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_TypedMsg_descriptor,
        new java.lang.String[] { "MsgType", "Data", });
    internal_static_com_yorha_proto_YoExceptionSnapshot_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_YoExceptionSnapshot_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_YoExceptionSnapshot_descriptor,
        new java.lang.String[] { "ClazzName", "ErrorCode", "Msg", "ZoneId", });
    internal_static_com_yorha_proto_YoActorMsgPayload_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_YoActorMsgPayload_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_YoActorMsgPayload_descriptor,
        new java.lang.String[] { "TypedMsg", "Exception", "Field", });
    internal_static_com_yorha_proto_YoActorMsgEnvelope_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_YoActorMsgEnvelope_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_YoActorMsgEnvelope_descriptor,
        new java.lang.String[] { "Sender", "Receiver", "ReceiverList", "MsgSeqId", "Tag", "Payload", "JaegerCarrier", "SendTsMs", "AskStageId", });
    internal_static_com_yorha_proto_OffLineRpc_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_OffLineRpc_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OffLineRpc_descriptor,
        new java.lang.String[] { "Msg", "ExpireTsMs", });
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

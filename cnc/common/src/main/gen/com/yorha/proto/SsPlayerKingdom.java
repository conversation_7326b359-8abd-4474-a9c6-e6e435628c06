// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/ss/ss_player_kingdom.proto

package com.yorha.proto;

public final class SsPlayerKingdom {
  private SsPlayerKingdom() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface KingdomOfficeChangeCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingdomOfficeChangeCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 旧的职位id
     * </pre>
     *
     * <code>optional int32 oldOfficeId = 1;</code>
     * @return Whether the oldOfficeId field is set.
     */
    boolean hasOldOfficeId();
    /**
     * <pre>
     * 旧的职位id
     * </pre>
     *
     * <code>optional int32 oldOfficeId = 1;</code>
     * @return The oldOfficeId.
     */
    int getOldOfficeId();

    /**
     * <pre>
     * 新的职位id
     * </pre>
     *
     * <code>optional int32 newOfficeId = 2;</code>
     * @return Whether the newOfficeId field is set.
     */
    boolean hasNewOfficeId();
    /**
     * <pre>
     * 新的职位id
     * </pre>
     *
     * <code>optional int32 newOfficeId = 2;</code>
     * @return The newOfficeId.
     */
    int getNewOfficeId();

    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return Whether the clanSimpleName field is set.
     */
    boolean hasClanSimpleName();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return The clanSimpleName.
     */
    java.lang.String getClanSimpleName();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return The bytes for clanSimpleName.
     */
    com.google.protobuf.ByteString
        getClanSimpleNameBytes();

    /**
     * <pre>
     * 国王任期
     * </pre>
     *
     * <code>optional int32 kingNum = 4;</code>
     * @return Whether the kingNum field is set.
     */
    boolean hasKingNum();
    /**
     * <pre>
     * 国王任期
     * </pre>
     *
     * <code>optional int32 kingNum = 4;</code>
     * @return The kingNum.
     */
    int getKingNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingdomOfficeChangeCmd}
   */
  public static final class KingdomOfficeChangeCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingdomOfficeChangeCmd)
      KingdomOfficeChangeCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingdomOfficeChangeCmd.newBuilder() to construct.
    private KingdomOfficeChangeCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingdomOfficeChangeCmd() {
      clanSimpleName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingdomOfficeChangeCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingdomOfficeChangeCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              oldOfficeId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              newOfficeId_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              clanSimpleName_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              kingNum_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomOfficeChangeCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomOfficeChangeCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd.class, com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd.Builder.class);
    }

    private int bitField0_;
    public static final int OLDOFFICEID_FIELD_NUMBER = 1;
    private int oldOfficeId_;
    /**
     * <pre>
     * 旧的职位id
     * </pre>
     *
     * <code>optional int32 oldOfficeId = 1;</code>
     * @return Whether the oldOfficeId field is set.
     */
    @java.lang.Override
    public boolean hasOldOfficeId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 旧的职位id
     * </pre>
     *
     * <code>optional int32 oldOfficeId = 1;</code>
     * @return The oldOfficeId.
     */
    @java.lang.Override
    public int getOldOfficeId() {
      return oldOfficeId_;
    }

    public static final int NEWOFFICEID_FIELD_NUMBER = 2;
    private int newOfficeId_;
    /**
     * <pre>
     * 新的职位id
     * </pre>
     *
     * <code>optional int32 newOfficeId = 2;</code>
     * @return Whether the newOfficeId field is set.
     */
    @java.lang.Override
    public boolean hasNewOfficeId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 新的职位id
     * </pre>
     *
     * <code>optional int32 newOfficeId = 2;</code>
     * @return The newOfficeId.
     */
    @java.lang.Override
    public int getNewOfficeId() {
      return newOfficeId_;
    }

    public static final int CLANSIMPLENAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object clanSimpleName_;
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return Whether the clanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasClanSimpleName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return The clanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getClanSimpleName() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return The bytes for clanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSimpleNameBytes() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int KINGNUM_FIELD_NUMBER = 4;
    private int kingNum_;
    /**
     * <pre>
     * 国王任期
     * </pre>
     *
     * <code>optional int32 kingNum = 4;</code>
     * @return Whether the kingNum field is set.
     */
    @java.lang.Override
    public boolean hasKingNum() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 国王任期
     * </pre>
     *
     * <code>optional int32 kingNum = 4;</code>
     * @return The kingNum.
     */
    @java.lang.Override
    public int getKingNum() {
      return kingNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, oldOfficeId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, newOfficeId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, clanSimpleName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, kingNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, oldOfficeId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, newOfficeId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, clanSimpleName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, kingNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd other = (com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd) obj;

      if (hasOldOfficeId() != other.hasOldOfficeId()) return false;
      if (hasOldOfficeId()) {
        if (getOldOfficeId()
            != other.getOldOfficeId()) return false;
      }
      if (hasNewOfficeId() != other.hasNewOfficeId()) return false;
      if (hasNewOfficeId()) {
        if (getNewOfficeId()
            != other.getNewOfficeId()) return false;
      }
      if (hasClanSimpleName() != other.hasClanSimpleName()) return false;
      if (hasClanSimpleName()) {
        if (!getClanSimpleName()
            .equals(other.getClanSimpleName())) return false;
      }
      if (hasKingNum() != other.hasKingNum()) return false;
      if (hasKingNum()) {
        if (getKingNum()
            != other.getKingNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOldOfficeId()) {
        hash = (37 * hash) + OLDOFFICEID_FIELD_NUMBER;
        hash = (53 * hash) + getOldOfficeId();
      }
      if (hasNewOfficeId()) {
        hash = (37 * hash) + NEWOFFICEID_FIELD_NUMBER;
        hash = (53 * hash) + getNewOfficeId();
      }
      if (hasClanSimpleName()) {
        hash = (37 * hash) + CLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSimpleName().hashCode();
      }
      if (hasKingNum()) {
        hash = (37 * hash) + KINGNUM_FIELD_NUMBER;
        hash = (53 * hash) + getKingNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingdomOfficeChangeCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingdomOfficeChangeCmd)
        com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomOfficeChangeCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomOfficeChangeCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd.class, com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        oldOfficeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        newOfficeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        clanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        kingNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomOfficeChangeCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd build() {
        com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd buildPartial() {
        com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd result = new com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.oldOfficeId_ = oldOfficeId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.newOfficeId_ = newOfficeId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.clanSimpleName_ = clanSimpleName_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.kingNum_ = kingNum_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd other) {
        if (other == com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd.getDefaultInstance()) return this;
        if (other.hasOldOfficeId()) {
          setOldOfficeId(other.getOldOfficeId());
        }
        if (other.hasNewOfficeId()) {
          setNewOfficeId(other.getNewOfficeId());
        }
        if (other.hasClanSimpleName()) {
          bitField0_ |= 0x00000004;
          clanSimpleName_ = other.clanSimpleName_;
          onChanged();
        }
        if (other.hasKingNum()) {
          setKingNum(other.getKingNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int oldOfficeId_ ;
      /**
       * <pre>
       * 旧的职位id
       * </pre>
       *
       * <code>optional int32 oldOfficeId = 1;</code>
       * @return Whether the oldOfficeId field is set.
       */
      @java.lang.Override
      public boolean hasOldOfficeId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 旧的职位id
       * </pre>
       *
       * <code>optional int32 oldOfficeId = 1;</code>
       * @return The oldOfficeId.
       */
      @java.lang.Override
      public int getOldOfficeId() {
        return oldOfficeId_;
      }
      /**
       * <pre>
       * 旧的职位id
       * </pre>
       *
       * <code>optional int32 oldOfficeId = 1;</code>
       * @param value The oldOfficeId to set.
       * @return This builder for chaining.
       */
      public Builder setOldOfficeId(int value) {
        bitField0_ |= 0x00000001;
        oldOfficeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 旧的职位id
       * </pre>
       *
       * <code>optional int32 oldOfficeId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOldOfficeId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        oldOfficeId_ = 0;
        onChanged();
        return this;
      }

      private int newOfficeId_ ;
      /**
       * <pre>
       * 新的职位id
       * </pre>
       *
       * <code>optional int32 newOfficeId = 2;</code>
       * @return Whether the newOfficeId field is set.
       */
      @java.lang.Override
      public boolean hasNewOfficeId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 新的职位id
       * </pre>
       *
       * <code>optional int32 newOfficeId = 2;</code>
       * @return The newOfficeId.
       */
      @java.lang.Override
      public int getNewOfficeId() {
        return newOfficeId_;
      }
      /**
       * <pre>
       * 新的职位id
       * </pre>
       *
       * <code>optional int32 newOfficeId = 2;</code>
       * @param value The newOfficeId to set.
       * @return This builder for chaining.
       */
      public Builder setNewOfficeId(int value) {
        bitField0_ |= 0x00000002;
        newOfficeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新的职位id
       * </pre>
       *
       * <code>optional int32 newOfficeId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewOfficeId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        newOfficeId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object clanSimpleName_ = "";
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @return Whether the clanSimpleName field is set.
       */
      public boolean hasClanSimpleName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @return The clanSimpleName.
       */
      public java.lang.String getClanSimpleName() {
        java.lang.Object ref = clanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @return The bytes for clanSimpleName.
       */
      public com.google.protobuf.ByteString
          getClanSimpleNameBytes() {
        java.lang.Object ref = clanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @param value The clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clanSimpleName_ = getDefaultInstance().getClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @param value The bytes for clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }

      private int kingNum_ ;
      /**
       * <pre>
       * 国王任期
       * </pre>
       *
       * <code>optional int32 kingNum = 4;</code>
       * @return Whether the kingNum field is set.
       */
      @java.lang.Override
      public boolean hasKingNum() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 国王任期
       * </pre>
       *
       * <code>optional int32 kingNum = 4;</code>
       * @return The kingNum.
       */
      @java.lang.Override
      public int getKingNum() {
        return kingNum_;
      }
      /**
       * <pre>
       * 国王任期
       * </pre>
       *
       * <code>optional int32 kingNum = 4;</code>
       * @param value The kingNum to set.
       * @return This builder for chaining.
       */
      public Builder setKingNum(int value) {
        bitField0_ |= 0x00000008;
        kingNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 国王任期
       * </pre>
       *
       * <code>optional int32 kingNum = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingNum() {
        bitField0_ = (bitField0_ & ~0x00000008);
        kingNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingdomOfficeChangeCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingdomOfficeChangeCmd)
    private static final com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd();
    }

    public static com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingdomOfficeChangeCmd>
        PARSER = new com.google.protobuf.AbstractParser<KingdomOfficeChangeCmd>() {
      @java.lang.Override
      public KingdomOfficeChangeCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingdomOfficeChangeCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingdomOfficeChangeCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingdomOfficeChangeCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerKingdom.KingdomOfficeChangeCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingdomSettleGainTaxCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingdomSettleGainTaxCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */
    int getResourceCount();
    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */
    boolean containsResource(
        int key);
    /**
     * Use {@link #getResourceMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Long>
    getResource();
    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Long>
    getResourceMap();
    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */

    long getResourceOrDefault(
        int key,
        long defaultValue);
    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */

    long getResourceOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingdomSettleGainTaxCmd}
   */
  public static final class KingdomSettleGainTaxCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingdomSettleGainTaxCmd)
      KingdomSettleGainTaxCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingdomSettleGainTaxCmd.newBuilder() to construct.
    private KingdomSettleGainTaxCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingdomSettleGainTaxCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingdomSettleGainTaxCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingdomSettleGainTaxCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                resource_ = com.google.protobuf.MapField.newMapField(
                    ResourceDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
              resource__ = input.readMessage(
                  ResourceDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              resource_.getMutableMap().put(
                  resource__.getKey(), resource__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetResource();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd.class, com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd.Builder.class);
    }

    public static final int RESOURCE_FIELD_NUMBER = 2;
    private static final class ResourceDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Long>newDefaultInstance(
                  com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_ResourceEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Long> resource_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
    internalGetResource() {
      if (resource_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ResourceDefaultEntryHolder.defaultEntry);
      }
      return resource_;
    }

    public int getResourceCount() {
      return internalGetResource().getMap().size();
    }
    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */

    @java.lang.Override
    public boolean containsResource(
        int key) {
      
      return internalGetResource().getMap().containsKey(key);
    }
    /**
     * Use {@link #getResourceMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Long> getResource() {
      return getResourceMap();
    }
    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Long> getResourceMap() {
      return internalGetResource().getMap();
    }
    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */
    @java.lang.Override

    public long getResourceOrDefault(
        int key,
        long defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetResource().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 赋税技能获得的资源
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; resource = 2;</code>
     */
    @java.lang.Override

    public long getResourceOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetResource().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetResource(),
          ResourceDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Long> entry
           : internalGetResource().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
        resource__ = ResourceDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, resource__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd other = (com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd) obj;

      if (!internalGetResource().equals(
          other.internalGetResource())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetResource().getMap().isEmpty()) {
        hash = (37 * hash) + RESOURCE_FIELD_NUMBER;
        hash = (53 * hash) + internalGetResource().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingdomSettleGainTaxCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingdomSettleGainTaxCmd)
        com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetResource();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableResource();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd.class, com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableResource().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerKingdom.internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd build() {
        com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd buildPartial() {
        com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd result = new com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd(this);
        int from_bitField0_ = bitField0_;
        result.resource_ = internalGetResource();
        result.resource_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd other) {
        if (other == com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd.getDefaultInstance()) return this;
        internalGetMutableResource().mergeFrom(
            other.internalGetResource());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Long> resource_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetResource() {
        if (resource_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ResourceDefaultEntryHolder.defaultEntry);
        }
        return resource_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetMutableResource() {
        onChanged();;
        if (resource_ == null) {
          resource_ = com.google.protobuf.MapField.newMapField(
              ResourceDefaultEntryHolder.defaultEntry);
        }
        if (!resource_.isMutable()) {
          resource_ = resource_.copy();
        }
        return resource_;
      }

      public int getResourceCount() {
        return internalGetResource().getMap().size();
      }
      /**
       * <pre>
       * 赋税技能获得的资源
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; resource = 2;</code>
       */

      @java.lang.Override
      public boolean containsResource(
          int key) {
        
        return internalGetResource().getMap().containsKey(key);
      }
      /**
       * Use {@link #getResourceMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long> getResource() {
        return getResourceMap();
      }
      /**
       * <pre>
       * 赋税技能获得的资源
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; resource = 2;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Long> getResourceMap() {
        return internalGetResource().getMap();
      }
      /**
       * <pre>
       * 赋税技能获得的资源
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; resource = 2;</code>
       */
      @java.lang.Override

      public long getResourceOrDefault(
          int key,
          long defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetResource().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 赋税技能获得的资源
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; resource = 2;</code>
       */
      @java.lang.Override

      public long getResourceOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetResource().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearResource() {
        internalGetMutableResource().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 赋税技能获得的资源
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; resource = 2;</code>
       */

      public Builder removeResource(
          int key) {
        
        internalGetMutableResource().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long>
      getMutableResource() {
        return internalGetMutableResource().getMutableMap();
      }
      /**
       * <pre>
       * 赋税技能获得的资源
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; resource = 2;</code>
       */
      public Builder putResource(
          int key,
          long value) {
        
        
        internalGetMutableResource().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 赋税技能获得的资源
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; resource = 2;</code>
       */

      public Builder putAllResource(
          java.util.Map<java.lang.Integer, java.lang.Long> values) {
        internalGetMutableResource().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingdomSettleGainTaxCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingdomSettleGainTaxCmd)
    private static final com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd();
    }

    public static com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingdomSettleGainTaxCmd>
        PARSER = new com.google.protobuf.AbstractParser<KingdomSettleGainTaxCmd>() {
      @java.lang.Override
      public KingdomSettleGainTaxCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingdomSettleGainTaxCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingdomSettleGainTaxCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingdomSettleGainTaxCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerKingdom.KingdomSettleGainTaxCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingdomOfficeChangeCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingdomOfficeChangeCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_ResourceEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_ResourceEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/player/ss/ss_player_kingd" +
      "om.proto\022\017com.yorha.proto\"k\n\026KingdomOffi" +
      "ceChangeCmd\022\023\n\013oldOfficeId\030\001 \001(\005\022\023\n\013newO" +
      "fficeId\030\002 \001(\005\022\026\n\016clanSimpleName\030\003 \001(\t\022\017\n" +
      "\007kingNum\030\004 \001(\005\"\224\001\n\027KingdomSettleGainTaxC" +
      "md\022H\n\010resource\030\002 \003(\01326.com.yorha.proto.K" +
      "ingdomSettleGainTaxCmd.ResourceEntry\032/\n\r" +
      "ResourceEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(" +
      "\003:\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_KingdomOfficeChangeCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_KingdomOfficeChangeCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingdomOfficeChangeCmd_descriptor,
        new java.lang.String[] { "OldOfficeId", "NewOfficeId", "ClanSimpleName", "KingNum", });
    internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_descriptor,
        new java.lang.String[] { "Resource", });
    internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_ResourceEntry_descriptor =
      internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_ResourceEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingdomSettleGainTaxCmd_ResourceEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/spyPlane/spyPlanePB.proto

package com.yorha.proto;

public final class SpyPlanePB {
  private SpyPlanePB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SpyPlaneEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SpyPlaneEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return The ownerId.
     */
    long getOwnerId();

    /**
     * <code>optional .com.yorha.proto.MovePB move = 2;</code>
     * @return Whether the move field is set.
     */
    boolean hasMove();
    /**
     * <code>optional .com.yorha.proto.MovePB move = 2;</code>
     * @return The move.
     */
    com.yorha.proto.StructPB.MovePB getMove();
    /**
     * <code>optional .com.yorha.proto.MovePB move = 2;</code>
     */
    com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder();

    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 剩余需要探索的迷雾id
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
     * @return Whether the exploreGridId field is set.
     */
    boolean hasExploreGridId();
    /**
     * <pre>
     * 剩余需要探索的迷雾id
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
     * @return The exploreGridId.
     */
    com.yorha.proto.StructPB.PointListPB getExploreGridId();
    /**
     * <pre>
     * 剩余需要探索的迷雾id
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
     */
    com.yorha.proto.StructPB.PointListPBOrBuilder getExploreGridIdOrBuilder();

    /**
     * <pre>
     * 行为
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyPlaneActionType action = 5;</code>
     * @return Whether the action field is set.
     */
    boolean hasAction();
    /**
     * <pre>
     * 行为
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyPlaneActionType action = 5;</code>
     * @return The action.
     */
    com.yorha.proto.CommonEnum.SpyPlaneActionType getAction();

    /**
     * <pre>
     * 侦察机状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyPlaneState state = 6;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <pre>
     * 侦察机状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyPlaneState state = 6;</code>
     * @return The state.
     */
    com.yorha.proto.CommonEnum.SpyPlaneState getState();

    /**
     * <pre>
     * 拥有者名字
     * </pre>
     *
     * <code>optional string ownerName = 7;</code>
     * @return Whether the ownerName field is set.
     */
    boolean hasOwnerName();
    /**
     * <pre>
     * 拥有者名字
     * </pre>
     *
     * <code>optional string ownerName = 7;</code>
     * @return The ownerName.
     */
    java.lang.String getOwnerName();
    /**
     * <pre>
     * 拥有者名字
     * </pre>
     *
     * <code>optional string ownerName = 7;</code>
     * @return The bytes for ownerName.
     */
    com.google.protobuf.ByteString
        getOwnerNameBytes();

    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 8;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 8;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 9;</code>
     * @return Whether the briefClanName field is set.
     */
    boolean hasBriefClanName();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 9;</code>
     * @return The briefClanName.
     */
    java.lang.String getBriefClanName();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 9;</code>
     * @return The bytes for briefClanName.
     */
    com.google.protobuf.ByteString
        getBriefClanNameBytes();

    /**
     * <pre>
     * 玩家上每一架飞机的唯一标识
     * </pre>
     *
     * <code>optional int64 playerPlaneId = 10;</code>
     * @return Whether the playerPlaneId field is set.
     */
    boolean hasPlayerPlaneId();
    /**
     * <pre>
     * 玩家上每一架飞机的唯一标识
     * </pre>
     *
     * <code>optional int64 playerPlaneId = 10;</code>
     * @return The playerPlaneId.
     */
    long getPlayerPlaneId();

    /**
     * <code>optional bool finishNeedReturn = 11;</code>
     * @return Whether the finishNeedReturn field is set.
     */
    boolean hasFinishNeedReturn();
    /**
     * <code>optional bool finishNeedReturn = 11;</code>
     * @return The finishNeedReturn.
     */
    boolean getFinishNeedReturn();

    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 12;</code>
     * @return Whether the targetId field is set.
     */
    boolean hasTargetId();
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 12;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <pre>
     * 侦查类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyType spyType = 15;</code>
     * @return Whether the spyType field is set.
     */
    boolean hasSpyType();
    /**
     * <pre>
     * 侦查类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyType spyType = 15;</code>
     * @return The spyType.
     */
    com.yorha.proto.CommonEnum.SpyType getSpyType();

    /**
     * <code>optional int64 targetPlayerId = 16;</code>
     * @return Whether the targetPlayerId field is set.
     */
    boolean hasTargetPlayerId();
    /**
     * <code>optional int64 targetPlayerId = 16;</code>
     * @return The targetPlayerId.
     */
    long getTargetPlayerId();

    /**
     * <pre>
     * 本次需要探索迷雾总数
     * </pre>
     *
     * <code>optional int32 totalExploreGridSize = 17;</code>
     * @return Whether the totalExploreGridSize field is set.
     */
    boolean hasTotalExploreGridSize();
    /**
     * <pre>
     * 本次需要探索迷雾总数
     * </pre>
     *
     * <code>optional int32 totalExploreGridSize = 17;</code>
     * @return The totalExploreGridSize.
     */
    int getTotalExploreGridSize();

    /**
     * <pre>
     * 驻扎开始时间戳
     * </pre>
     *
     * <code>optional int64 stayStartStamp = 18;</code>
     * @return Whether the stayStartStamp field is set.
     */
    boolean hasStayStartStamp();
    /**
     * <pre>
     * 驻扎开始时间戳
     * </pre>
     *
     * <code>optional int64 stayStartStamp = 18;</code>
     * @return The stayStartStamp.
     */
    long getStayStartStamp();

    /**
     * <pre>
     * 调查开始时间戳
     * </pre>
     *
     * <code>optional int64 surveyS = 20;</code>
     * @return Whether the surveyS field is set.
     */
    boolean hasSurveyS();
    /**
     * <pre>
     * 调查开始时间戳
     * </pre>
     *
     * <code>optional int64 surveyS = 20;</code>
     * @return The surveyS.
     */
    long getSurveyS();

    /**
     * <pre>
     * 调查结束时间戳
     * </pre>
     *
     * <code>optional int64 surveyE = 21;</code>
     * @return Whether the surveyE field is set.
     */
    boolean hasSurveyE();
    /**
     * <pre>
     * 调查结束时间戳
     * </pre>
     *
     * <code>optional int64 surveyE = 21;</code>
     * @return The surveyE.
     */
    long getSurveyE();

    /**
     * <pre>
     * 目标配置id
     * </pre>
     *
     * <code>optional int64 targetConfigId = 22;</code>
     * @return Whether the targetConfigId field is set.
     */
    boolean hasTargetConfigId();
    /**
     * <pre>
     * 目标配置id
     * </pre>
     *
     * <code>optional int64 targetConfigId = 22;</code>
     * @return The targetConfigId.
     */
    long getTargetConfigId();

    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 23;</code>
     * @return Whether the camp field is set.
     */
    boolean hasCamp();
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 23;</code>
     * @return The camp.
     */
    com.yorha.proto.CommonEnum.Camp getCamp();

    /**
     * <pre>
     * 所属玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 24;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 所属玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 24;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SpyPlaneEntityPB}
   */
  public static final class SpyPlaneEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SpyPlaneEntityPB)
      SpyPlaneEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SpyPlaneEntityPB.newBuilder() to construct.
    private SpyPlaneEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SpyPlaneEntityPB() {
      action_ = 0;
      state_ = 0;
      ownerName_ = "";
      briefClanName_ = "";
      spyType_ = 0;
      camp_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SpyPlaneEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SpyPlaneEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ownerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructPB.MovePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = move_.toBuilder();
              }
              move_ = input.readMessage(com.yorha.proto.StructPB.MovePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(move_);
                move_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              templateId_ = input.readInt32();
              break;
            }
            case 34: {
              com.yorha.proto.StructPB.PointListPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = exploreGridId_.toBuilder();
              }
              exploreGridId_ = input.readMessage(com.yorha.proto.StructPB.PointListPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(exploreGridId_);
                exploreGridId_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 40: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SpyPlaneActionType value = com.yorha.proto.CommonEnum.SpyPlaneActionType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(5, rawValue);
              } else {
                bitField0_ |= 0x00000010;
                action_ = rawValue;
              }
              break;
            }
            case 48: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SpyPlaneState value = com.yorha.proto.CommonEnum.SpyPlaneState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(6, rawValue);
              } else {
                bitField0_ |= 0x00000020;
                state_ = rawValue;
              }
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              ownerName_ = bs;
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              clanId_ = input.readInt64();
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              briefClanName_ = bs;
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              playerPlaneId_ = input.readInt64();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              finishNeedReturn_ = input.readBool();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              targetId_ = input.readInt64();
              break;
            }
            case 120: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SpyType value = com.yorha.proto.CommonEnum.SpyType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(15, rawValue);
              } else {
                bitField0_ |= 0x00001000;
                spyType_ = rawValue;
              }
              break;
            }
            case 128: {
              bitField0_ |= 0x00002000;
              targetPlayerId_ = input.readInt64();
              break;
            }
            case 136: {
              bitField0_ |= 0x00004000;
              totalExploreGridSize_ = input.readInt32();
              break;
            }
            case 144: {
              bitField0_ |= 0x00008000;
              stayStartStamp_ = input.readInt64();
              break;
            }
            case 160: {
              bitField0_ |= 0x00010000;
              surveyS_ = input.readInt64();
              break;
            }
            case 168: {
              bitField0_ |= 0x00020000;
              surveyE_ = input.readInt64();
              break;
            }
            case 176: {
              bitField0_ |= 0x00040000;
              targetConfigId_ = input.readInt64();
              break;
            }
            case 184: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Camp value = com.yorha.proto.CommonEnum.Camp.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(23, rawValue);
              } else {
                bitField0_ |= 0x00080000;
                camp_ = rawValue;
              }
              break;
            }
            case 192: {
              bitField0_ |= 0x00100000;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SpyPlanePB.internal_static_com_yorha_proto_SpyPlaneEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SpyPlanePB.internal_static_com_yorha_proto_SpyPlaneEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.class, com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int OWNERID_FIELD_NUMBER = 1;
    private long ownerId_;
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    public static final int MOVE_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPB.MovePB move_;
    /**
     * <code>optional .com.yorha.proto.MovePB move = 2;</code>
     * @return Whether the move field is set.
     */
    @java.lang.Override
    public boolean hasMove() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MovePB move = 2;</code>
     * @return The move.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.MovePB getMove() {
      return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
    }
    /**
     * <code>optional .com.yorha.proto.MovePB move = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder() {
      return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 3;
    private int templateId_;
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int EXPLOREGRIDID_FIELD_NUMBER = 4;
    private com.yorha.proto.StructPB.PointListPB exploreGridId_;
    /**
     * <pre>
     * 剩余需要探索的迷雾id
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
     * @return Whether the exploreGridId field is set.
     */
    @java.lang.Override
    public boolean hasExploreGridId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 剩余需要探索的迷雾id
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
     * @return The exploreGridId.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointListPB getExploreGridId() {
      return exploreGridId_ == null ? com.yorha.proto.StructPB.PointListPB.getDefaultInstance() : exploreGridId_;
    }
    /**
     * <pre>
     * 剩余需要探索的迷雾id
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointListPBOrBuilder getExploreGridIdOrBuilder() {
      return exploreGridId_ == null ? com.yorha.proto.StructPB.PointListPB.getDefaultInstance() : exploreGridId_;
    }

    public static final int ACTION_FIELD_NUMBER = 5;
    private int action_;
    /**
     * <pre>
     * 行为
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyPlaneActionType action = 5;</code>
     * @return Whether the action field is set.
     */
    @java.lang.Override public boolean hasAction() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 行为
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyPlaneActionType action = 5;</code>
     * @return The action.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SpyPlaneActionType getAction() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SpyPlaneActionType result = com.yorha.proto.CommonEnum.SpyPlaneActionType.valueOf(action_);
      return result == null ? com.yorha.proto.CommonEnum.SpyPlaneActionType.SPAT_NONE : result;
    }

    public static final int STATE_FIELD_NUMBER = 6;
    private int state_;
    /**
     * <pre>
     * 侦察机状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyPlaneState state = 6;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override public boolean hasState() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 侦察机状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyPlaneState state = 6;</code>
     * @return The state.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SpyPlaneState getState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SpyPlaneState result = com.yorha.proto.CommonEnum.SpyPlaneState.valueOf(state_);
      return result == null ? com.yorha.proto.CommonEnum.SpyPlaneState.SPS_NONE : result;
    }

    public static final int OWNERNAME_FIELD_NUMBER = 7;
    private volatile java.lang.Object ownerName_;
    /**
     * <pre>
     * 拥有者名字
     * </pre>
     *
     * <code>optional string ownerName = 7;</code>
     * @return Whether the ownerName field is set.
     */
    @java.lang.Override
    public boolean hasOwnerName() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 拥有者名字
     * </pre>
     *
     * <code>optional string ownerName = 7;</code>
     * @return The ownerName.
     */
    @java.lang.Override
    public java.lang.String getOwnerName() {
      java.lang.Object ref = ownerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ownerName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 拥有者名字
     * </pre>
     *
     * <code>optional string ownerName = 7;</code>
     * @return The bytes for ownerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOwnerNameBytes() {
      java.lang.Object ref = ownerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ownerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLANID_FIELD_NUMBER = 8;
    private long clanId_;
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 8;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 8;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int BRIEFCLANNAME_FIELD_NUMBER = 9;
    private volatile java.lang.Object briefClanName_;
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 9;</code>
     * @return Whether the briefClanName field is set.
     */
    @java.lang.Override
    public boolean hasBriefClanName() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 9;</code>
     * @return The briefClanName.
     */
    @java.lang.Override
    public java.lang.String getBriefClanName() {
      java.lang.Object ref = briefClanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          briefClanName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string briefClanName = 9;</code>
     * @return The bytes for briefClanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBriefClanNameBytes() {
      java.lang.Object ref = briefClanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        briefClanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLAYERPLANEID_FIELD_NUMBER = 10;
    private long playerPlaneId_;
    /**
     * <pre>
     * 玩家上每一架飞机的唯一标识
     * </pre>
     *
     * <code>optional int64 playerPlaneId = 10;</code>
     * @return Whether the playerPlaneId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerPlaneId() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 玩家上每一架飞机的唯一标识
     * </pre>
     *
     * <code>optional int64 playerPlaneId = 10;</code>
     * @return The playerPlaneId.
     */
    @java.lang.Override
    public long getPlayerPlaneId() {
      return playerPlaneId_;
    }

    public static final int FINISHNEEDRETURN_FIELD_NUMBER = 11;
    private boolean finishNeedReturn_;
    /**
     * <code>optional bool finishNeedReturn = 11;</code>
     * @return Whether the finishNeedReturn field is set.
     */
    @java.lang.Override
    public boolean hasFinishNeedReturn() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bool finishNeedReturn = 11;</code>
     * @return The finishNeedReturn.
     */
    @java.lang.Override
    public boolean getFinishNeedReturn() {
      return finishNeedReturn_;
    }

    public static final int TARGETID_FIELD_NUMBER = 12;
    private long targetId_;
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 12;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 12;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int SPYTYPE_FIELD_NUMBER = 15;
    private int spyType_;
    /**
     * <pre>
     * 侦查类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyType spyType = 15;</code>
     * @return Whether the spyType field is set.
     */
    @java.lang.Override public boolean hasSpyType() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 侦查类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpyType spyType = 15;</code>
     * @return The spyType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SpyType getSpyType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SpyType result = com.yorha.proto.CommonEnum.SpyType.valueOf(spyType_);
      return result == null ? com.yorha.proto.CommonEnum.SpyType.SPYTYPE_NONE : result;
    }

    public static final int TARGETPLAYERID_FIELD_NUMBER = 16;
    private long targetPlayerId_;
    /**
     * <code>optional int64 targetPlayerId = 16;</code>
     * @return Whether the targetPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasTargetPlayerId() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional int64 targetPlayerId = 16;</code>
     * @return The targetPlayerId.
     */
    @java.lang.Override
    public long getTargetPlayerId() {
      return targetPlayerId_;
    }

    public static final int TOTALEXPLOREGRIDSIZE_FIELD_NUMBER = 17;
    private int totalExploreGridSize_;
    /**
     * <pre>
     * 本次需要探索迷雾总数
     * </pre>
     *
     * <code>optional int32 totalExploreGridSize = 17;</code>
     * @return Whether the totalExploreGridSize field is set.
     */
    @java.lang.Override
    public boolean hasTotalExploreGridSize() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 本次需要探索迷雾总数
     * </pre>
     *
     * <code>optional int32 totalExploreGridSize = 17;</code>
     * @return The totalExploreGridSize.
     */
    @java.lang.Override
    public int getTotalExploreGridSize() {
      return totalExploreGridSize_;
    }

    public static final int STAYSTARTSTAMP_FIELD_NUMBER = 18;
    private long stayStartStamp_;
    /**
     * <pre>
     * 驻扎开始时间戳
     * </pre>
     *
     * <code>optional int64 stayStartStamp = 18;</code>
     * @return Whether the stayStartStamp field is set.
     */
    @java.lang.Override
    public boolean hasStayStartStamp() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 驻扎开始时间戳
     * </pre>
     *
     * <code>optional int64 stayStartStamp = 18;</code>
     * @return The stayStartStamp.
     */
    @java.lang.Override
    public long getStayStartStamp() {
      return stayStartStamp_;
    }

    public static final int SURVEYS_FIELD_NUMBER = 20;
    private long surveyS_;
    /**
     * <pre>
     * 调查开始时间戳
     * </pre>
     *
     * <code>optional int64 surveyS = 20;</code>
     * @return Whether the surveyS field is set.
     */
    @java.lang.Override
    public boolean hasSurveyS() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * 调查开始时间戳
     * </pre>
     *
     * <code>optional int64 surveyS = 20;</code>
     * @return The surveyS.
     */
    @java.lang.Override
    public long getSurveyS() {
      return surveyS_;
    }

    public static final int SURVEYE_FIELD_NUMBER = 21;
    private long surveyE_;
    /**
     * <pre>
     * 调查结束时间戳
     * </pre>
     *
     * <code>optional int64 surveyE = 21;</code>
     * @return Whether the surveyE field is set.
     */
    @java.lang.Override
    public boolean hasSurveyE() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * 调查结束时间戳
     * </pre>
     *
     * <code>optional int64 surveyE = 21;</code>
     * @return The surveyE.
     */
    @java.lang.Override
    public long getSurveyE() {
      return surveyE_;
    }

    public static final int TARGETCONFIGID_FIELD_NUMBER = 22;
    private long targetConfigId_;
    /**
     * <pre>
     * 目标配置id
     * </pre>
     *
     * <code>optional int64 targetConfigId = 22;</code>
     * @return Whether the targetConfigId field is set.
     */
    @java.lang.Override
    public boolean hasTargetConfigId() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * 目标配置id
     * </pre>
     *
     * <code>optional int64 targetConfigId = 22;</code>
     * @return The targetConfigId.
     */
    @java.lang.Override
    public long getTargetConfigId() {
      return targetConfigId_;
    }

    public static final int CAMP_FIELD_NUMBER = 23;
    private int camp_;
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 23;</code>
     * @return Whether the camp field is set.
     */
    @java.lang.Override public boolean hasCamp() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 23;</code>
     * @return The camp.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Camp getCamp() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
      return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
    }

    public static final int ZONEID_FIELD_NUMBER = 24;
    private int zoneId_;
    /**
     * <pre>
     * 所属玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 24;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 所属玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 24;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, ownerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getMove());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, templateId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getExploreGridId());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeEnum(5, action_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeEnum(6, state_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, ownerName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt64(8, clanId_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, briefClanName_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt64(10, playerPlaneId_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeBool(11, finishNeedReturn_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeInt64(12, targetId_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeEnum(15, spyType_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeInt64(16, targetPlayerId_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeInt32(17, totalExploreGridSize_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeInt64(18, stayStartStamp_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeInt64(20, surveyS_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeInt64(21, surveyE_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeInt64(22, targetConfigId_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeEnum(23, camp_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeInt32(24, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, ownerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getMove());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, templateId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getExploreGridId());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(5, action_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(6, state_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, ownerName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, clanId_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, briefClanName_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, playerPlaneId_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(11, finishNeedReturn_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(12, targetId_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(15, spyType_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(16, targetPlayerId_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(17, totalExploreGridSize_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(18, stayStartStamp_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(20, surveyS_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(21, surveyE_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(22, targetConfigId_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(23, camp_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(24, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB other = (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) obj;

      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (hasMove() != other.hasMove()) return false;
      if (hasMove()) {
        if (!getMove()
            .equals(other.getMove())) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasExploreGridId() != other.hasExploreGridId()) return false;
      if (hasExploreGridId()) {
        if (!getExploreGridId()
            .equals(other.getExploreGridId())) return false;
      }
      if (hasAction() != other.hasAction()) return false;
      if (hasAction()) {
        if (action_ != other.action_) return false;
      }
      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (state_ != other.state_) return false;
      }
      if (hasOwnerName() != other.hasOwnerName()) return false;
      if (hasOwnerName()) {
        if (!getOwnerName()
            .equals(other.getOwnerName())) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasBriefClanName() != other.hasBriefClanName()) return false;
      if (hasBriefClanName()) {
        if (!getBriefClanName()
            .equals(other.getBriefClanName())) return false;
      }
      if (hasPlayerPlaneId() != other.hasPlayerPlaneId()) return false;
      if (hasPlayerPlaneId()) {
        if (getPlayerPlaneId()
            != other.getPlayerPlaneId()) return false;
      }
      if (hasFinishNeedReturn() != other.hasFinishNeedReturn()) return false;
      if (hasFinishNeedReturn()) {
        if (getFinishNeedReturn()
            != other.getFinishNeedReturn()) return false;
      }
      if (hasTargetId() != other.hasTargetId()) return false;
      if (hasTargetId()) {
        if (getTargetId()
            != other.getTargetId()) return false;
      }
      if (hasSpyType() != other.hasSpyType()) return false;
      if (hasSpyType()) {
        if (spyType_ != other.spyType_) return false;
      }
      if (hasTargetPlayerId() != other.hasTargetPlayerId()) return false;
      if (hasTargetPlayerId()) {
        if (getTargetPlayerId()
            != other.getTargetPlayerId()) return false;
      }
      if (hasTotalExploreGridSize() != other.hasTotalExploreGridSize()) return false;
      if (hasTotalExploreGridSize()) {
        if (getTotalExploreGridSize()
            != other.getTotalExploreGridSize()) return false;
      }
      if (hasStayStartStamp() != other.hasStayStartStamp()) return false;
      if (hasStayStartStamp()) {
        if (getStayStartStamp()
            != other.getStayStartStamp()) return false;
      }
      if (hasSurveyS() != other.hasSurveyS()) return false;
      if (hasSurveyS()) {
        if (getSurveyS()
            != other.getSurveyS()) return false;
      }
      if (hasSurveyE() != other.hasSurveyE()) return false;
      if (hasSurveyE()) {
        if (getSurveyE()
            != other.getSurveyE()) return false;
      }
      if (hasTargetConfigId() != other.hasTargetConfigId()) return false;
      if (hasTargetConfigId()) {
        if (getTargetConfigId()
            != other.getTargetConfigId()) return false;
      }
      if (hasCamp() != other.hasCamp()) return false;
      if (hasCamp()) {
        if (camp_ != other.camp_) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      if (hasMove()) {
        hash = (37 * hash) + MOVE_FIELD_NUMBER;
        hash = (53 * hash) + getMove().hashCode();
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasExploreGridId()) {
        hash = (37 * hash) + EXPLOREGRIDID_FIELD_NUMBER;
        hash = (53 * hash) + getExploreGridId().hashCode();
      }
      if (hasAction()) {
        hash = (37 * hash) + ACTION_FIELD_NUMBER;
        hash = (53 * hash) + action_;
      }
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + state_;
      }
      if (hasOwnerName()) {
        hash = (37 * hash) + OWNERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getOwnerName().hashCode();
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasBriefClanName()) {
        hash = (37 * hash) + BRIEFCLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getBriefClanName().hashCode();
      }
      if (hasPlayerPlaneId()) {
        hash = (37 * hash) + PLAYERPLANEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerPlaneId());
      }
      if (hasFinishNeedReturn()) {
        hash = (37 * hash) + FINISHNEEDRETURN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getFinishNeedReturn());
      }
      if (hasTargetId()) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetId());
      }
      if (hasSpyType()) {
        hash = (37 * hash) + SPYTYPE_FIELD_NUMBER;
        hash = (53 * hash) + spyType_;
      }
      if (hasTargetPlayerId()) {
        hash = (37 * hash) + TARGETPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetPlayerId());
      }
      if (hasTotalExploreGridSize()) {
        hash = (37 * hash) + TOTALEXPLOREGRIDSIZE_FIELD_NUMBER;
        hash = (53 * hash) + getTotalExploreGridSize();
      }
      if (hasStayStartStamp()) {
        hash = (37 * hash) + STAYSTARTSTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStayStartStamp());
      }
      if (hasSurveyS()) {
        hash = (37 * hash) + SURVEYS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSurveyS());
      }
      if (hasSurveyE()) {
        hash = (37 * hash) + SURVEYE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSurveyE());
      }
      if (hasTargetConfigId()) {
        hash = (37 * hash) + TARGETCONFIGID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetConfigId());
      }
      if (hasCamp()) {
        hash = (37 * hash) + CAMP_FIELD_NUMBER;
        hash = (53 * hash) + camp_;
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SpyPlaneEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SpyPlaneEntityPB)
        com.yorha.proto.SpyPlanePB.SpyPlaneEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SpyPlanePB.internal_static_com_yorha_proto_SpyPlaneEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SpyPlanePB.internal_static_com_yorha_proto_SpyPlaneEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.class, com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMoveFieldBuilder();
          getExploreGridIdFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (moveBuilder_ == null) {
          move_ = null;
        } else {
          moveBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (exploreGridIdBuilder_ == null) {
          exploreGridId_ = null;
        } else {
          exploreGridIdBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        action_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        state_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        ownerName_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000080);
        briefClanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        playerPlaneId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        finishNeedReturn_ = false;
        bitField0_ = (bitField0_ & ~0x00000400);
        targetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000800);
        spyType_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        targetPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00002000);
        totalExploreGridSize_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        stayStartStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00008000);
        surveyS_ = 0L;
        bitField0_ = (bitField0_ & ~0x00010000);
        surveyE_ = 0L;
        bitField0_ = (bitField0_ & ~0x00020000);
        targetConfigId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00040000);
        camp_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00100000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SpyPlanePB.internal_static_com_yorha_proto_SpyPlaneEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB build() {
        com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB buildPartial() {
        com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB result = new com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (moveBuilder_ == null) {
            result.move_ = move_;
          } else {
            result.move_ = moveBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (exploreGridIdBuilder_ == null) {
            result.exploreGridId_ = exploreGridId_;
          } else {
            result.exploreGridId_ = exploreGridIdBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.action_ = action_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.state_ = state_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.ownerName_ = ownerName_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          to_bitField0_ |= 0x00000100;
        }
        result.briefClanName_ = briefClanName_;
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.playerPlaneId_ = playerPlaneId_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.finishNeedReturn_ = finishNeedReturn_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.targetId_ = targetId_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          to_bitField0_ |= 0x00001000;
        }
        result.spyType_ = spyType_;
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.targetPlayerId_ = targetPlayerId_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.totalExploreGridSize_ = totalExploreGridSize_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.stayStartStamp_ = stayStartStamp_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.surveyS_ = surveyS_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.surveyE_ = surveyE_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.targetConfigId_ = targetConfigId_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          to_bitField0_ |= 0x00080000;
        }
        result.camp_ = camp_;
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00100000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) {
          return mergeFrom((com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB other) {
        if (other == com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB.getDefaultInstance()) return this;
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        if (other.hasMove()) {
          mergeMove(other.getMove());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasExploreGridId()) {
          mergeExploreGridId(other.getExploreGridId());
        }
        if (other.hasAction()) {
          setAction(other.getAction());
        }
        if (other.hasState()) {
          setState(other.getState());
        }
        if (other.hasOwnerName()) {
          bitField0_ |= 0x00000040;
          ownerName_ = other.ownerName_;
          onChanged();
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasBriefClanName()) {
          bitField0_ |= 0x00000100;
          briefClanName_ = other.briefClanName_;
          onChanged();
        }
        if (other.hasPlayerPlaneId()) {
          setPlayerPlaneId(other.getPlayerPlaneId());
        }
        if (other.hasFinishNeedReturn()) {
          setFinishNeedReturn(other.getFinishNeedReturn());
        }
        if (other.hasTargetId()) {
          setTargetId(other.getTargetId());
        }
        if (other.hasSpyType()) {
          setSpyType(other.getSpyType());
        }
        if (other.hasTargetPlayerId()) {
          setTargetPlayerId(other.getTargetPlayerId());
        }
        if (other.hasTotalExploreGridSize()) {
          setTotalExploreGridSize(other.getTotalExploreGridSize());
        }
        if (other.hasStayStartStamp()) {
          setStayStartStamp(other.getStayStartStamp());
        }
        if (other.hasSurveyS()) {
          setSurveyS(other.getSurveyS());
        }
        if (other.hasSurveyE()) {
          setSurveyE(other.getSurveyE());
        }
        if (other.hasTargetConfigId()) {
          setTargetConfigId(other.getTargetConfigId());
        }
        if (other.hasCamp()) {
          setCamp(other.getCamp());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long ownerId_ ;
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000001;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ownerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.MovePB move_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder> moveBuilder_;
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       * @return Whether the move field is set.
       */
      public boolean hasMove() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       * @return The move.
       */
      public com.yorha.proto.StructPB.MovePB getMove() {
        if (moveBuilder_ == null) {
          return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
        } else {
          return moveBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       */
      public Builder setMove(com.yorha.proto.StructPB.MovePB value) {
        if (moveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          move_ = value;
          onChanged();
        } else {
          moveBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       */
      public Builder setMove(
          com.yorha.proto.StructPB.MovePB.Builder builderForValue) {
        if (moveBuilder_ == null) {
          move_ = builderForValue.build();
          onChanged();
        } else {
          moveBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       */
      public Builder mergeMove(com.yorha.proto.StructPB.MovePB value) {
        if (moveBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              move_ != null &&
              move_ != com.yorha.proto.StructPB.MovePB.getDefaultInstance()) {
            move_ =
              com.yorha.proto.StructPB.MovePB.newBuilder(move_).mergeFrom(value).buildPartial();
          } else {
            move_ = value;
          }
          onChanged();
        } else {
          moveBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       */
      public Builder clearMove() {
        if (moveBuilder_ == null) {
          move_ = null;
          onChanged();
        } else {
          moveBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       */
      public com.yorha.proto.StructPB.MovePB.Builder getMoveBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getMoveFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       */
      public com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder() {
        if (moveBuilder_ != null) {
          return moveBuilder_.getMessageOrBuilder();
        } else {
          return move_ == null ?
              com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder> 
          getMoveFieldBuilder() {
        if (moveBuilder_ == null) {
          moveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder>(
                  getMove(),
                  getParentForChildren(),
                  isClean());
          move_ = null;
        }
        return moveBuilder_;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000004;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointListPB exploreGridId_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointListPB, com.yorha.proto.StructPB.PointListPB.Builder, com.yorha.proto.StructPB.PointListPBOrBuilder> exploreGridIdBuilder_;
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       * @return Whether the exploreGridId field is set.
       */
      public boolean hasExploreGridId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       * @return The exploreGridId.
       */
      public com.yorha.proto.StructPB.PointListPB getExploreGridId() {
        if (exploreGridIdBuilder_ == null) {
          return exploreGridId_ == null ? com.yorha.proto.StructPB.PointListPB.getDefaultInstance() : exploreGridId_;
        } else {
          return exploreGridIdBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       */
      public Builder setExploreGridId(com.yorha.proto.StructPB.PointListPB value) {
        if (exploreGridIdBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          exploreGridId_ = value;
          onChanged();
        } else {
          exploreGridIdBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       */
      public Builder setExploreGridId(
          com.yorha.proto.StructPB.PointListPB.Builder builderForValue) {
        if (exploreGridIdBuilder_ == null) {
          exploreGridId_ = builderForValue.build();
          onChanged();
        } else {
          exploreGridIdBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       */
      public Builder mergeExploreGridId(com.yorha.proto.StructPB.PointListPB value) {
        if (exploreGridIdBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              exploreGridId_ != null &&
              exploreGridId_ != com.yorha.proto.StructPB.PointListPB.getDefaultInstance()) {
            exploreGridId_ =
              com.yorha.proto.StructPB.PointListPB.newBuilder(exploreGridId_).mergeFrom(value).buildPartial();
          } else {
            exploreGridId_ = value;
          }
          onChanged();
        } else {
          exploreGridIdBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       */
      public Builder clearExploreGridId() {
        if (exploreGridIdBuilder_ == null) {
          exploreGridId_ = null;
          onChanged();
        } else {
          exploreGridIdBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       */
      public com.yorha.proto.StructPB.PointListPB.Builder getExploreGridIdBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getExploreGridIdFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       */
      public com.yorha.proto.StructPB.PointListPBOrBuilder getExploreGridIdOrBuilder() {
        if (exploreGridIdBuilder_ != null) {
          return exploreGridIdBuilder_.getMessageOrBuilder();
        } else {
          return exploreGridId_ == null ?
              com.yorha.proto.StructPB.PointListPB.getDefaultInstance() : exploreGridId_;
        }
      }
      /**
       * <pre>
       * 剩余需要探索的迷雾id
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointListPB exploreGridId = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointListPB, com.yorha.proto.StructPB.PointListPB.Builder, com.yorha.proto.StructPB.PointListPBOrBuilder> 
          getExploreGridIdFieldBuilder() {
        if (exploreGridIdBuilder_ == null) {
          exploreGridIdBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointListPB, com.yorha.proto.StructPB.PointListPB.Builder, com.yorha.proto.StructPB.PointListPBOrBuilder>(
                  getExploreGridId(),
                  getParentForChildren(),
                  isClean());
          exploreGridId_ = null;
        }
        return exploreGridIdBuilder_;
      }

      private int action_ = 0;
      /**
       * <pre>
       * 行为
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyPlaneActionType action = 5;</code>
       * @return Whether the action field is set.
       */
      @java.lang.Override public boolean hasAction() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 行为
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyPlaneActionType action = 5;</code>
       * @return The action.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SpyPlaneActionType getAction() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SpyPlaneActionType result = com.yorha.proto.CommonEnum.SpyPlaneActionType.valueOf(action_);
        return result == null ? com.yorha.proto.CommonEnum.SpyPlaneActionType.SPAT_NONE : result;
      }
      /**
       * <pre>
       * 行为
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyPlaneActionType action = 5;</code>
       * @param value The action to set.
       * @return This builder for chaining.
       */
      public Builder setAction(com.yorha.proto.CommonEnum.SpyPlaneActionType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000010;
        action_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 行为
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyPlaneActionType action = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearAction() {
        bitField0_ = (bitField0_ & ~0x00000010);
        action_ = 0;
        onChanged();
        return this;
      }

      private int state_ = 0;
      /**
       * <pre>
       * 侦察机状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyPlaneState state = 6;</code>
       * @return Whether the state field is set.
       */
      @java.lang.Override public boolean hasState() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 侦察机状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyPlaneState state = 6;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SpyPlaneState getState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SpyPlaneState result = com.yorha.proto.CommonEnum.SpyPlaneState.valueOf(state_);
        return result == null ? com.yorha.proto.CommonEnum.SpyPlaneState.SPS_NONE : result;
      }
      /**
       * <pre>
       * 侦察机状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyPlaneState state = 6;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(com.yorha.proto.CommonEnum.SpyPlaneState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000020;
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 侦察机状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyPlaneState state = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        bitField0_ = (bitField0_ & ~0x00000020);
        state_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object ownerName_ = "";
      /**
       * <pre>
       * 拥有者名字
       * </pre>
       *
       * <code>optional string ownerName = 7;</code>
       * @return Whether the ownerName field is set.
       */
      public boolean hasOwnerName() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 拥有者名字
       * </pre>
       *
       * <code>optional string ownerName = 7;</code>
       * @return The ownerName.
       */
      public java.lang.String getOwnerName() {
        java.lang.Object ref = ownerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ownerName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 拥有者名字
       * </pre>
       *
       * <code>optional string ownerName = 7;</code>
       * @return The bytes for ownerName.
       */
      public com.google.protobuf.ByteString
          getOwnerNameBytes() {
        java.lang.Object ref = ownerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ownerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 拥有者名字
       * </pre>
       *
       * <code>optional string ownerName = 7;</code>
       * @param value The ownerName to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        ownerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 拥有者名字
       * </pre>
       *
       * <code>optional string ownerName = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerName() {
        bitField0_ = (bitField0_ & ~0x00000040);
        ownerName_ = getDefaultInstance().getOwnerName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 拥有者名字
       * </pre>
       *
       * <code>optional string ownerName = 7;</code>
       * @param value The bytes for ownerName to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        ownerName_ = value;
        onChanged();
        return this;
      }

      private long clanId_ ;
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 8;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 8;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 8;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000080;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000080);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object briefClanName_ = "";
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 9;</code>
       * @return Whether the briefClanName field is set.
       */
      public boolean hasBriefClanName() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 9;</code>
       * @return The briefClanName.
       */
      public java.lang.String getBriefClanName() {
        java.lang.Object ref = briefClanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            briefClanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 9;</code>
       * @return The bytes for briefClanName.
       */
      public com.google.protobuf.ByteString
          getBriefClanNameBytes() {
        java.lang.Object ref = briefClanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          briefClanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 9;</code>
       * @param value The briefClanName to set.
       * @return This builder for chaining.
       */
      public Builder setBriefClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        briefClanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearBriefClanName() {
        bitField0_ = (bitField0_ & ~0x00000100);
        briefClanName_ = getDefaultInstance().getBriefClanName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string briefClanName = 9;</code>
       * @param value The bytes for briefClanName to set.
       * @return This builder for chaining.
       */
      public Builder setBriefClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        briefClanName_ = value;
        onChanged();
        return this;
      }

      private long playerPlaneId_ ;
      /**
       * <pre>
       * 玩家上每一架飞机的唯一标识
       * </pre>
       *
       * <code>optional int64 playerPlaneId = 10;</code>
       * @return Whether the playerPlaneId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerPlaneId() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 玩家上每一架飞机的唯一标识
       * </pre>
       *
       * <code>optional int64 playerPlaneId = 10;</code>
       * @return The playerPlaneId.
       */
      @java.lang.Override
      public long getPlayerPlaneId() {
        return playerPlaneId_;
      }
      /**
       * <pre>
       * 玩家上每一架飞机的唯一标识
       * </pre>
       *
       * <code>optional int64 playerPlaneId = 10;</code>
       * @param value The playerPlaneId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerPlaneId(long value) {
        bitField0_ |= 0x00000200;
        playerPlaneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家上每一架飞机的唯一标识
       * </pre>
       *
       * <code>optional int64 playerPlaneId = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerPlaneId() {
        bitField0_ = (bitField0_ & ~0x00000200);
        playerPlaneId_ = 0L;
        onChanged();
        return this;
      }

      private boolean finishNeedReturn_ ;
      /**
       * <code>optional bool finishNeedReturn = 11;</code>
       * @return Whether the finishNeedReturn field is set.
       */
      @java.lang.Override
      public boolean hasFinishNeedReturn() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bool finishNeedReturn = 11;</code>
       * @return The finishNeedReturn.
       */
      @java.lang.Override
      public boolean getFinishNeedReturn() {
        return finishNeedReturn_;
      }
      /**
       * <code>optional bool finishNeedReturn = 11;</code>
       * @param value The finishNeedReturn to set.
       * @return This builder for chaining.
       */
      public Builder setFinishNeedReturn(boolean value) {
        bitField0_ |= 0x00000400;
        finishNeedReturn_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool finishNeedReturn = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearFinishNeedReturn() {
        bitField0_ = (bitField0_ & ~0x00000400);
        finishNeedReturn_ = false;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 12;</code>
       * @return Whether the targetId field is set.
       */
      @java.lang.Override
      public boolean hasTargetId() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 12;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 12;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        bitField0_ |= 0x00000800;
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000800);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private int spyType_ = 0;
      /**
       * <pre>
       * 侦查类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyType spyType = 15;</code>
       * @return Whether the spyType field is set.
       */
      @java.lang.Override public boolean hasSpyType() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 侦查类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyType spyType = 15;</code>
       * @return The spyType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SpyType getSpyType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SpyType result = com.yorha.proto.CommonEnum.SpyType.valueOf(spyType_);
        return result == null ? com.yorha.proto.CommonEnum.SpyType.SPYTYPE_NONE : result;
      }
      /**
       * <pre>
       * 侦查类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyType spyType = 15;</code>
       * @param value The spyType to set.
       * @return This builder for chaining.
       */
      public Builder setSpyType(com.yorha.proto.CommonEnum.SpyType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00001000;
        spyType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 侦查类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpyType spyType = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearSpyType() {
        bitField0_ = (bitField0_ & ~0x00001000);
        spyType_ = 0;
        onChanged();
        return this;
      }

      private long targetPlayerId_ ;
      /**
       * <code>optional int64 targetPlayerId = 16;</code>
       * @return Whether the targetPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasTargetPlayerId() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional int64 targetPlayerId = 16;</code>
       * @return The targetPlayerId.
       */
      @java.lang.Override
      public long getTargetPlayerId() {
        return targetPlayerId_;
      }
      /**
       * <code>optional int64 targetPlayerId = 16;</code>
       * @param value The targetPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetPlayerId(long value) {
        bitField0_ |= 0x00002000;
        targetPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 targetPlayerId = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetPlayerId() {
        bitField0_ = (bitField0_ & ~0x00002000);
        targetPlayerId_ = 0L;
        onChanged();
        return this;
      }

      private int totalExploreGridSize_ ;
      /**
       * <pre>
       * 本次需要探索迷雾总数
       * </pre>
       *
       * <code>optional int32 totalExploreGridSize = 17;</code>
       * @return Whether the totalExploreGridSize field is set.
       */
      @java.lang.Override
      public boolean hasTotalExploreGridSize() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 本次需要探索迷雾总数
       * </pre>
       *
       * <code>optional int32 totalExploreGridSize = 17;</code>
       * @return The totalExploreGridSize.
       */
      @java.lang.Override
      public int getTotalExploreGridSize() {
        return totalExploreGridSize_;
      }
      /**
       * <pre>
       * 本次需要探索迷雾总数
       * </pre>
       *
       * <code>optional int32 totalExploreGridSize = 17;</code>
       * @param value The totalExploreGridSize to set.
       * @return This builder for chaining.
       */
      public Builder setTotalExploreGridSize(int value) {
        bitField0_ |= 0x00004000;
        totalExploreGridSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次需要探索迷雾总数
       * </pre>
       *
       * <code>optional int32 totalExploreGridSize = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalExploreGridSize() {
        bitField0_ = (bitField0_ & ~0x00004000);
        totalExploreGridSize_ = 0;
        onChanged();
        return this;
      }

      private long stayStartStamp_ ;
      /**
       * <pre>
       * 驻扎开始时间戳
       * </pre>
       *
       * <code>optional int64 stayStartStamp = 18;</code>
       * @return Whether the stayStartStamp field is set.
       */
      @java.lang.Override
      public boolean hasStayStartStamp() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 驻扎开始时间戳
       * </pre>
       *
       * <code>optional int64 stayStartStamp = 18;</code>
       * @return The stayStartStamp.
       */
      @java.lang.Override
      public long getStayStartStamp() {
        return stayStartStamp_;
      }
      /**
       * <pre>
       * 驻扎开始时间戳
       * </pre>
       *
       * <code>optional int64 stayStartStamp = 18;</code>
       * @param value The stayStartStamp to set.
       * @return This builder for chaining.
       */
      public Builder setStayStartStamp(long value) {
        bitField0_ |= 0x00008000;
        stayStartStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 驻扎开始时间戳
       * </pre>
       *
       * <code>optional int64 stayStartStamp = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearStayStartStamp() {
        bitField0_ = (bitField0_ & ~0x00008000);
        stayStartStamp_ = 0L;
        onChanged();
        return this;
      }

      private long surveyS_ ;
      /**
       * <pre>
       * 调查开始时间戳
       * </pre>
       *
       * <code>optional int64 surveyS = 20;</code>
       * @return Whether the surveyS field is set.
       */
      @java.lang.Override
      public boolean hasSurveyS() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 调查开始时间戳
       * </pre>
       *
       * <code>optional int64 surveyS = 20;</code>
       * @return The surveyS.
       */
      @java.lang.Override
      public long getSurveyS() {
        return surveyS_;
      }
      /**
       * <pre>
       * 调查开始时间戳
       * </pre>
       *
       * <code>optional int64 surveyS = 20;</code>
       * @param value The surveyS to set.
       * @return This builder for chaining.
       */
      public Builder setSurveyS(long value) {
        bitField0_ |= 0x00010000;
        surveyS_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 调查开始时间戳
       * </pre>
       *
       * <code>optional int64 surveyS = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearSurveyS() {
        bitField0_ = (bitField0_ & ~0x00010000);
        surveyS_ = 0L;
        onChanged();
        return this;
      }

      private long surveyE_ ;
      /**
       * <pre>
       * 调查结束时间戳
       * </pre>
       *
       * <code>optional int64 surveyE = 21;</code>
       * @return Whether the surveyE field is set.
       */
      @java.lang.Override
      public boolean hasSurveyE() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 调查结束时间戳
       * </pre>
       *
       * <code>optional int64 surveyE = 21;</code>
       * @return The surveyE.
       */
      @java.lang.Override
      public long getSurveyE() {
        return surveyE_;
      }
      /**
       * <pre>
       * 调查结束时间戳
       * </pre>
       *
       * <code>optional int64 surveyE = 21;</code>
       * @param value The surveyE to set.
       * @return This builder for chaining.
       */
      public Builder setSurveyE(long value) {
        bitField0_ |= 0x00020000;
        surveyE_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 调查结束时间戳
       * </pre>
       *
       * <code>optional int64 surveyE = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearSurveyE() {
        bitField0_ = (bitField0_ & ~0x00020000);
        surveyE_ = 0L;
        onChanged();
        return this;
      }

      private long targetConfigId_ ;
      /**
       * <pre>
       * 目标配置id
       * </pre>
       *
       * <code>optional int64 targetConfigId = 22;</code>
       * @return Whether the targetConfigId field is set.
       */
      @java.lang.Override
      public boolean hasTargetConfigId() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * 目标配置id
       * </pre>
       *
       * <code>optional int64 targetConfigId = 22;</code>
       * @return The targetConfigId.
       */
      @java.lang.Override
      public long getTargetConfigId() {
        return targetConfigId_;
      }
      /**
       * <pre>
       * 目标配置id
       * </pre>
       *
       * <code>optional int64 targetConfigId = 22;</code>
       * @param value The targetConfigId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetConfigId(long value) {
        bitField0_ |= 0x00040000;
        targetConfigId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标配置id
       * </pre>
       *
       * <code>optional int64 targetConfigId = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetConfigId() {
        bitField0_ = (bitField0_ & ~0x00040000);
        targetConfigId_ = 0L;
        onChanged();
        return this;
      }

      private int camp_ = 0;
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 23;</code>
       * @return Whether the camp field is set.
       */
      @java.lang.Override public boolean hasCamp() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 23;</code>
       * @return The camp.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Camp getCamp() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
        return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 23;</code>
       * @param value The camp to set.
       * @return This builder for chaining.
       */
      public Builder setCamp(com.yorha.proto.CommonEnum.Camp value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00080000;
        camp_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearCamp() {
        bitField0_ = (bitField0_ & ~0x00080000);
        camp_ = 0;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 所属玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 24;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 所属玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 24;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 所属玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 24;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00100000;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00100000);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SpyPlaneEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SpyPlaneEntityPB)
    private static final com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB();
    }

    public static com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SpyPlaneEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<SpyPlaneEntityPB>() {
      @java.lang.Override
      public SpyPlaneEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SpyPlaneEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SpyPlaneEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SpyPlaneEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SpyPlaneEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SpyPlaneEntityPB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&cs_proto/gen/spyPlane/spyPlanePB.proto" +
      "\022\017com.yorha.proto\032\"cs_proto/gen/common/s" +
      "tructPB.proto\032%ss_proto/gen/common/commo" +
      "n_enum.proto\"\334\004\n\020SpyPlaneEntityPB\022\017\n\007own" +
      "erId\030\001 \001(\003\022%\n\004move\030\002 \001(\0132\027.com.yorha.pro" +
      "to.MovePB\022\022\n\ntemplateId\030\003 \001(\005\0223\n\rexplore" +
      "GridId\030\004 \001(\0132\034.com.yorha.proto.PointList" +
      "PB\0223\n\006action\030\005 \001(\0162#.com.yorha.proto.Spy" +
      "PlaneActionType\022-\n\005state\030\006 \001(\0162\036.com.yor" +
      "ha.proto.SpyPlaneState\022\021\n\townerName\030\007 \001(" +
      "\t\022\016\n\006clanId\030\010 \001(\003\022\025\n\rbriefClanName\030\t \001(\t" +
      "\022\025\n\rplayerPlaneId\030\n \001(\003\022\030\n\020finishNeedRet" +
      "urn\030\013 \001(\010\022\020\n\010targetId\030\014 \001(\003\022)\n\007spyType\030\017" +
      " \001(\0162\030.com.yorha.proto.SpyType\022\026\n\016target" +
      "PlayerId\030\020 \001(\003\022\034\n\024totalExploreGridSize\030\021" +
      " \001(\005\022\026\n\016stayStartStamp\030\022 \001(\003\022\017\n\007surveyS\030" +
      "\024 \001(\003\022\017\n\007surveyE\030\025 \001(\003\022\026\n\016targetConfigId" +
      "\030\026 \001(\003\022#\n\004camp\030\027 \001(\0162\025.com.yorha.proto.C" +
      "amp\022\016\n\006zoneId\030\030 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_SpyPlaneEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SpyPlaneEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SpyPlaneEntityPB_descriptor,
        new java.lang.String[] { "OwnerId", "Move", "TemplateId", "ExploreGridId", "Action", "State", "OwnerName", "ClanId", "BriefClanName", "PlayerPlaneId", "FinishNeedReturn", "TargetId", "SpyType", "TargetPlayerId", "TotalExploreGridSize", "StayStartStamp", "SurveyS", "SurveyE", "TargetConfigId", "Camp", "ZoneId", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

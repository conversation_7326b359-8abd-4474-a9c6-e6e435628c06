// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/groupChat/ss_groupChat.proto

package com.yorha.proto;

public final class SsGroupChat {
  private SsGroupChat() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CreateChatAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateChatAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 私聊或群聊的群主id
     * </pre>
     *
     * <code>optional int64 chatOwner = 1;</code>
     * @return Whether the chatOwner field is set.
     */
    boolean hasChatOwner();
    /**
     * <pre>
     * 私聊或群聊的群主id
     * </pre>
     *
     * <code>optional int64 chatOwner = 1;</code>
     * @return The chatOwner.
     */
    long getChatOwner();

    /**
     * <pre>
     * 聊天成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
     * @return Whether the chatMembers field is set.
     */
    boolean hasChatMembers();
    /**
     * <pre>
     * 聊天成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
     * @return The chatMembers.
     */
    com.yorha.proto.CommonMsg.ChatChannelMember getChatMembers();
    /**
     * <pre>
     * 聊天成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
     */
    com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getChatMembersOrBuilder();

    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string name = 3;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string name = 3;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string name = 3;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateChatAsk}
   */
  public static final class CreateChatAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateChatAsk)
      CreateChatAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateChatAsk.newBuilder() to construct.
    private CreateChatAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateChatAsk() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateChatAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateChatAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              chatOwner_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.ChatChannelMember.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = chatMembers_.toBuilder();
              }
              chatMembers_ = input.readMessage(com.yorha.proto.CommonMsg.ChatChannelMember.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatMembers_);
                chatMembers_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              name_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.CreateChatAsk.class, com.yorha.proto.SsGroupChat.CreateChatAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHATOWNER_FIELD_NUMBER = 1;
    private long chatOwner_;
    /**
     * <pre>
     * 私聊或群聊的群主id
     * </pre>
     *
     * <code>optional int64 chatOwner = 1;</code>
     * @return Whether the chatOwner field is set.
     */
    @java.lang.Override
    public boolean hasChatOwner() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 私聊或群聊的群主id
     * </pre>
     *
     * <code>optional int64 chatOwner = 1;</code>
     * @return The chatOwner.
     */
    @java.lang.Override
    public long getChatOwner() {
      return chatOwner_;
    }

    public static final int CHATMEMBERS_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.ChatChannelMember chatMembers_;
    /**
     * <pre>
     * 聊天成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
     * @return Whether the chatMembers field is set.
     */
    @java.lang.Override
    public boolean hasChatMembers() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 聊天成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
     * @return The chatMembers.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatChannelMember getChatMembers() {
      return chatMembers_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : chatMembers_;
    }
    /**
     * <pre>
     * 聊天成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getChatMembersOrBuilder() {
      return chatMembers_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : chatMembers_;
    }

    public static final int NAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string name = 3;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string name = 3;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string name = 3;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, chatOwner_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getChatMembers());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, chatOwner_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getChatMembers());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.CreateChatAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.CreateChatAsk other = (com.yorha.proto.SsGroupChat.CreateChatAsk) obj;

      if (hasChatOwner() != other.hasChatOwner()) return false;
      if (hasChatOwner()) {
        if (getChatOwner()
            != other.getChatOwner()) return false;
      }
      if (hasChatMembers() != other.hasChatMembers()) return false;
      if (hasChatMembers()) {
        if (!getChatMembers()
            .equals(other.getChatMembers())) return false;
      }
      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatOwner()) {
        hash = (37 * hash) + CHATOWNER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getChatOwner());
      }
      if (hasChatMembers()) {
        hash = (37 * hash) + CHATMEMBERS_FIELD_NUMBER;
        hash = (53 * hash) + getChatMembers().hashCode();
      }
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.CreateChatAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateChatAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateChatAsk)
        com.yorha.proto.SsGroupChat.CreateChatAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.CreateChatAsk.class, com.yorha.proto.SsGroupChat.CreateChatAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.CreateChatAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMembersFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chatOwner_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (chatMembersBuilder_ == null) {
          chatMembers_ = null;
        } else {
          chatMembersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.CreateChatAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.CreateChatAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.CreateChatAsk build() {
        com.yorha.proto.SsGroupChat.CreateChatAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.CreateChatAsk buildPartial() {
        com.yorha.proto.SsGroupChat.CreateChatAsk result = new com.yorha.proto.SsGroupChat.CreateChatAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chatOwner_ = chatOwner_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (chatMembersBuilder_ == null) {
            result.chatMembers_ = chatMembers_;
          } else {
            result.chatMembers_ = chatMembersBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.name_ = name_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.CreateChatAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.CreateChatAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.CreateChatAsk other) {
        if (other == com.yorha.proto.SsGroupChat.CreateChatAsk.getDefaultInstance()) return this;
        if (other.hasChatOwner()) {
          setChatOwner(other.getChatOwner());
        }
        if (other.hasChatMembers()) {
          mergeChatMembers(other.getChatMembers());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000004;
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.CreateChatAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.CreateChatAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long chatOwner_ ;
      /**
       * <pre>
       * 私聊或群聊的群主id
       * </pre>
       *
       * <code>optional int64 chatOwner = 1;</code>
       * @return Whether the chatOwner field is set.
       */
      @java.lang.Override
      public boolean hasChatOwner() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 私聊或群聊的群主id
       * </pre>
       *
       * <code>optional int64 chatOwner = 1;</code>
       * @return The chatOwner.
       */
      @java.lang.Override
      public long getChatOwner() {
        return chatOwner_;
      }
      /**
       * <pre>
       * 私聊或群聊的群主id
       * </pre>
       *
       * <code>optional int64 chatOwner = 1;</code>
       * @param value The chatOwner to set.
       * @return This builder for chaining.
       */
      public Builder setChatOwner(long value) {
        bitField0_ |= 0x00000001;
        chatOwner_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 私聊或群聊的群主id
       * </pre>
       *
       * <code>optional int64 chatOwner = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChatOwner() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chatOwner_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.ChatChannelMember chatMembers_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder> chatMembersBuilder_;
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       * @return Whether the chatMembers field is set.
       */
      public boolean hasChatMembers() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       * @return The chatMembers.
       */
      public com.yorha.proto.CommonMsg.ChatChannelMember getChatMembers() {
        if (chatMembersBuilder_ == null) {
          return chatMembers_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : chatMembers_;
        } else {
          return chatMembersBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       */
      public Builder setChatMembers(com.yorha.proto.CommonMsg.ChatChannelMember value) {
        if (chatMembersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatMembers_ = value;
          onChanged();
        } else {
          chatMembersBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       */
      public Builder setChatMembers(
          com.yorha.proto.CommonMsg.ChatChannelMember.Builder builderForValue) {
        if (chatMembersBuilder_ == null) {
          chatMembers_ = builderForValue.build();
          onChanged();
        } else {
          chatMembersBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       */
      public Builder mergeChatMembers(com.yorha.proto.CommonMsg.ChatChannelMember value) {
        if (chatMembersBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              chatMembers_ != null &&
              chatMembers_ != com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance()) {
            chatMembers_ =
              com.yorha.proto.CommonMsg.ChatChannelMember.newBuilder(chatMembers_).mergeFrom(value).buildPartial();
          } else {
            chatMembers_ = value;
          }
          onChanged();
        } else {
          chatMembersBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       */
      public Builder clearChatMembers() {
        if (chatMembersBuilder_ == null) {
          chatMembers_ = null;
          onChanged();
        } else {
          chatMembersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatChannelMember.Builder getChatMembersBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getChatMembersFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getChatMembersOrBuilder() {
        if (chatMembersBuilder_ != null) {
          return chatMembersBuilder_.getMessageOrBuilder();
        } else {
          return chatMembers_ == null ?
              com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : chatMembers_;
        }
      }
      /**
       * <pre>
       * 聊天成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember chatMembers = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder> 
          getChatMembersFieldBuilder() {
        if (chatMembersBuilder_ == null) {
          chatMembersBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder>(
                  getChatMembers(),
                  getParentForChildren(),
                  isClean());
          chatMembers_ = null;
        }
        return chatMembersBuilder_;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string name = 3;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string name = 3;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string name = 3;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string name = 3;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string name = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string name = 3;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateChatAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateChatAsk)
    private static final com.yorha.proto.SsGroupChat.CreateChatAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.CreateChatAsk();
    }

    public static com.yorha.proto.SsGroupChat.CreateChatAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateChatAsk>
        PARSER = new com.google.protobuf.AbstractParser<CreateChatAsk>() {
      @java.lang.Override
      public CreateChatAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateChatAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateChatAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateChatAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.CreateChatAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreateChatAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateChatAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 failedPlayer = 1;</code>
     * @return A list containing the failedPlayer.
     */
    java.util.List<java.lang.Long> getFailedPlayerList();
    /**
     * <code>repeated int64 failedPlayer = 1;</code>
     * @return The count of failedPlayer.
     */
    int getFailedPlayerCount();
    /**
     * <code>repeated int64 failedPlayer = 1;</code>
     * @param index The index of the element to return.
     * @return The failedPlayer at the given index.
     */
    long getFailedPlayer(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateChatAns}
   */
  public static final class CreateChatAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateChatAns)
      CreateChatAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateChatAns.newBuilder() to construct.
    private CreateChatAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateChatAns() {
      failedPlayer_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateChatAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateChatAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                failedPlayer_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              failedPlayer_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                failedPlayer_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                failedPlayer_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          failedPlayer_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.CreateChatAns.class, com.yorha.proto.SsGroupChat.CreateChatAns.Builder.class);
    }

    public static final int FAILEDPLAYER_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList failedPlayer_;
    /**
     * <code>repeated int64 failedPlayer = 1;</code>
     * @return A list containing the failedPlayer.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getFailedPlayerList() {
      return failedPlayer_;
    }
    /**
     * <code>repeated int64 failedPlayer = 1;</code>
     * @return The count of failedPlayer.
     */
    public int getFailedPlayerCount() {
      return failedPlayer_.size();
    }
    /**
     * <code>repeated int64 failedPlayer = 1;</code>
     * @param index The index of the element to return.
     * @return The failedPlayer at the given index.
     */
    public long getFailedPlayer(int index) {
      return failedPlayer_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < failedPlayer_.size(); i++) {
        output.writeInt64(1, failedPlayer_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < failedPlayer_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(failedPlayer_.getLong(i));
        }
        size += dataSize;
        size += 1 * getFailedPlayerList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.CreateChatAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.CreateChatAns other = (com.yorha.proto.SsGroupChat.CreateChatAns) obj;

      if (!getFailedPlayerList()
          .equals(other.getFailedPlayerList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getFailedPlayerCount() > 0) {
        hash = (37 * hash) + FAILEDPLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getFailedPlayerList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.CreateChatAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.CreateChatAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateChatAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateChatAns)
        com.yorha.proto.SsGroupChat.CreateChatAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.CreateChatAns.class, com.yorha.proto.SsGroupChat.CreateChatAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.CreateChatAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        failedPlayer_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_CreateChatAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.CreateChatAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.CreateChatAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.CreateChatAns build() {
        com.yorha.proto.SsGroupChat.CreateChatAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.CreateChatAns buildPartial() {
        com.yorha.proto.SsGroupChat.CreateChatAns result = new com.yorha.proto.SsGroupChat.CreateChatAns(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          failedPlayer_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.failedPlayer_ = failedPlayer_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.CreateChatAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.CreateChatAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.CreateChatAns other) {
        if (other == com.yorha.proto.SsGroupChat.CreateChatAns.getDefaultInstance()) return this;
        if (!other.failedPlayer_.isEmpty()) {
          if (failedPlayer_.isEmpty()) {
            failedPlayer_ = other.failedPlayer_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureFailedPlayerIsMutable();
            failedPlayer_.addAll(other.failedPlayer_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.CreateChatAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.CreateChatAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList failedPlayer_ = emptyLongList();
      private void ensureFailedPlayerIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          failedPlayer_ = mutableCopy(failedPlayer_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 failedPlayer = 1;</code>
       * @return A list containing the failedPlayer.
       */
      public java.util.List<java.lang.Long>
          getFailedPlayerList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(failedPlayer_) : failedPlayer_;
      }
      /**
       * <code>repeated int64 failedPlayer = 1;</code>
       * @return The count of failedPlayer.
       */
      public int getFailedPlayerCount() {
        return failedPlayer_.size();
      }
      /**
       * <code>repeated int64 failedPlayer = 1;</code>
       * @param index The index of the element to return.
       * @return The failedPlayer at the given index.
       */
      public long getFailedPlayer(int index) {
        return failedPlayer_.getLong(index);
      }
      /**
       * <code>repeated int64 failedPlayer = 1;</code>
       * @param index The index to set the value at.
       * @param value The failedPlayer to set.
       * @return This builder for chaining.
       */
      public Builder setFailedPlayer(
          int index, long value) {
        ensureFailedPlayerIsMutable();
        failedPlayer_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 failedPlayer = 1;</code>
       * @param value The failedPlayer to add.
       * @return This builder for chaining.
       */
      public Builder addFailedPlayer(long value) {
        ensureFailedPlayerIsMutable();
        failedPlayer_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 failedPlayer = 1;</code>
       * @param values The failedPlayer to add.
       * @return This builder for chaining.
       */
      public Builder addAllFailedPlayer(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureFailedPlayerIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, failedPlayer_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 failedPlayer = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFailedPlayer() {
        failedPlayer_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateChatAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateChatAns)
    private static final com.yorha.proto.SsGroupChat.CreateChatAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.CreateChatAns();
    }

    public static com.yorha.proto.SsGroupChat.CreateChatAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateChatAns>
        PARSER = new com.google.protobuf.AbstractParser<CreateChatAns>() {
      @java.lang.Override
      public CreateChatAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateChatAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateChatAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateChatAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.CreateChatAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendChatMessageAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendChatMessageAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return Whether the chatMessage field is set.
     */
    boolean hasChatMessage();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return The chatMessage.
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMessage();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder();

    /**
     * <pre>
     * 私聊带上
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 私聊带上
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendChatMessageAsk}
   */
  public static final class SendChatMessageAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendChatMessageAsk)
      SendChatMessageAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendChatMessageAsk.newBuilder() to construct.
    private SendChatMessageAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendChatMessageAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendChatMessageAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendChatMessageAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ChatMessage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = chatMessage_.toBuilder();
              }
              chatMessage_ = input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatMessage_);
                chatMessage_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.SendChatMessageAsk.class, com.yorha.proto.SsGroupChat.SendChatMessageAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHATMESSAGE_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return Whether the chatMessage field is set.
     */
    @java.lang.Override
    public boolean hasChatMessage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return The chatMessage.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }

    public static final int ZONEID_FIELD_NUMBER = 2;
    private int zoneId_;
    /**
     * <pre>
     * 私聊带上
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 私聊带上
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getChatMessage());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getChatMessage());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.SendChatMessageAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.SendChatMessageAsk other = (com.yorha.proto.SsGroupChat.SendChatMessageAsk) obj;

      if (hasChatMessage() != other.hasChatMessage()) return false;
      if (hasChatMessage()) {
        if (!getChatMessage()
            .equals(other.getChatMessage())) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatMessage()) {
        hash = (37 * hash) + CHATMESSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getChatMessage().hashCode();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.SendChatMessageAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendChatMessageAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendChatMessageAsk)
        com.yorha.proto.SsGroupChat.SendChatMessageAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.SendChatMessageAsk.class, com.yorha.proto.SsGroupChat.SendChatMessageAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.SendChatMessageAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMessageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.SendChatMessageAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.SendChatMessageAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.SendChatMessageAsk build() {
        com.yorha.proto.SsGroupChat.SendChatMessageAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.SendChatMessageAsk buildPartial() {
        com.yorha.proto.SsGroupChat.SendChatMessageAsk result = new com.yorha.proto.SsGroupChat.SendChatMessageAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (chatMessageBuilder_ == null) {
            result.chatMessage_ = chatMessage_;
          } else {
            result.chatMessage_ = chatMessageBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.SendChatMessageAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.SendChatMessageAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.SendChatMessageAsk other) {
        if (other == com.yorha.proto.SsGroupChat.SendChatMessageAsk.getDefaultInstance()) return this;
        if (other.hasChatMessage()) {
          mergeChatMessage(other.getChatMessage());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.SendChatMessageAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.SendChatMessageAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMessageBuilder_;
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       * @return Whether the chatMessage field is set.
       */
      public boolean hasChatMessage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       * @return The chatMessage.
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
        if (chatMessageBuilder_ == null) {
          return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        } else {
          return chatMessageBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder setChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatMessage_ = value;
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder setChatMessage(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = builderForValue.build();
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder mergeChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              chatMessage_ != null &&
              chatMessage_ != com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance()) {
            chatMessage_ =
              com.yorha.proto.CommonMsg.ChatMessage.newBuilder(chatMessage_).mergeFrom(value).buildPartial();
          } else {
            chatMessage_ = value;
          }
          onChanged();
        } else {
          chatMessageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder clearChatMessage() {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
          onChanged();
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMessageBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatMessageFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
        if (chatMessageBuilder_ != null) {
          return chatMessageBuilder_.getMessageOrBuilder();
        } else {
          return chatMessage_ == null ?
              com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMessageFieldBuilder() {
        if (chatMessageBuilder_ == null) {
          chatMessageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  getChatMessage(),
                  getParentForChildren(),
                  isClean());
          chatMessage_ = null;
        }
        return chatMessageBuilder_;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 私聊带上
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 私聊带上
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 私聊带上
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000002;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 私聊带上
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendChatMessageAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendChatMessageAsk)
    private static final com.yorha.proto.SsGroupChat.SendChatMessageAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.SendChatMessageAsk();
    }

    public static com.yorha.proto.SsGroupChat.SendChatMessageAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendChatMessageAsk>
        PARSER = new com.google.protobuf.AbstractParser<SendChatMessageAsk>() {
      @java.lang.Override
      public SendChatMessageAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendChatMessageAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendChatMessageAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendChatMessageAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.SendChatMessageAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendChatMessageAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendChatMessageAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return Whether the messageId field is set.
     */
    boolean hasMessageId();
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return The messageId.
     */
    long getMessageId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendChatMessageAns}
   */
  public static final class SendChatMessageAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendChatMessageAns)
      SendChatMessageAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendChatMessageAns.newBuilder() to construct.
    private SendChatMessageAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendChatMessageAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendChatMessageAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendChatMessageAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              messageId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.SendChatMessageAns.class, com.yorha.proto.SsGroupChat.SendChatMessageAns.Builder.class);
    }

    private int bitField0_;
    public static final int MESSAGEID_FIELD_NUMBER = 1;
    private long messageId_;
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return Whether the messageId field is set.
     */
    @java.lang.Override
    public boolean hasMessageId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return The messageId.
     */
    @java.lang.Override
    public long getMessageId() {
      return messageId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, messageId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, messageId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.SendChatMessageAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.SendChatMessageAns other = (com.yorha.proto.SsGroupChat.SendChatMessageAns) obj;

      if (hasMessageId() != other.hasMessageId()) return false;
      if (hasMessageId()) {
        if (getMessageId()
            != other.getMessageId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMessageId()) {
        hash = (37 * hash) + MESSAGEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMessageId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.SendChatMessageAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.SendChatMessageAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendChatMessageAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendChatMessageAns)
        com.yorha.proto.SsGroupChat.SendChatMessageAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.SendChatMessageAns.class, com.yorha.proto.SsGroupChat.SendChatMessageAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.SendChatMessageAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        messageId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_SendChatMessageAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.SendChatMessageAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.SendChatMessageAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.SendChatMessageAns build() {
        com.yorha.proto.SsGroupChat.SendChatMessageAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.SendChatMessageAns buildPartial() {
        com.yorha.proto.SsGroupChat.SendChatMessageAns result = new com.yorha.proto.SsGroupChat.SendChatMessageAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.messageId_ = messageId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.SendChatMessageAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.SendChatMessageAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.SendChatMessageAns other) {
        if (other == com.yorha.proto.SsGroupChat.SendChatMessageAns.getDefaultInstance()) return this;
        if (other.hasMessageId()) {
          setMessageId(other.getMessageId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.SendChatMessageAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.SendChatMessageAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long messageId_ ;
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return Whether the messageId field is set.
       */
      @java.lang.Override
      public boolean hasMessageId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return The messageId.
       */
      @java.lang.Override
      public long getMessageId() {
        return messageId_;
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @param value The messageId to set.
       * @return This builder for chaining.
       */
      public Builder setMessageId(long value) {
        bitField0_ |= 0x00000001;
        messageId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMessageId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        messageId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendChatMessageAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendChatMessageAns)
    private static final com.yorha.proto.SsGroupChat.SendChatMessageAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.SendChatMessageAns();
    }

    public static com.yorha.proto.SsGroupChat.SendChatMessageAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendChatMessageAns>
        PARSER = new com.google.protobuf.AbstractParser<SendChatMessageAns>() {
      @java.lang.Override
      public SendChatMessageAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendChatMessageAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendChatMessageAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendChatMessageAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.SendChatMessageAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryChatMessageAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryChatMessageAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 fromId = 1;</code>
     * @return Whether the fromId field is set.
     */
    boolean hasFromId();
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return The fromId.
     */
    long getFromId();

    /**
     * <code>optional int64 toId = 2;</code>
     * @return Whether the toId field is set.
     */
    boolean hasToId();
    /**
     * <code>optional int64 toId = 2;</code>
     * @return The toId.
     */
    long getToId();

    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return A list containing the shieldList.
     */
    java.util.List<java.lang.Long> getShieldListList();
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return The count of shieldList.
     */
    int getShieldListCount();
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @param index The index of the element to return.
     * @return The shieldList at the given index.
     */
    long getShieldList(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryChatMessageAsk}
   */
  public static final class QueryChatMessageAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryChatMessageAsk)
      QueryChatMessageAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryChatMessageAsk.newBuilder() to construct.
    private QueryChatMessageAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryChatMessageAsk() {
      shieldList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryChatMessageAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryChatMessageAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              fromId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toId_ = input.readInt64();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                shieldList_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              shieldList_.addLong(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                shieldList_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                shieldList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          shieldList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.QueryChatMessageAsk.class, com.yorha.proto.SsGroupChat.QueryChatMessageAsk.Builder.class);
    }

    private int bitField0_;
    public static final int FROMID_FIELD_NUMBER = 1;
    private long fromId_;
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return Whether the fromId field is set.
     */
    @java.lang.Override
    public boolean hasFromId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return The fromId.
     */
    @java.lang.Override
    public long getFromId() {
      return fromId_;
    }

    public static final int TOID_FIELD_NUMBER = 2;
    private long toId_;
    /**
     * <code>optional int64 toId = 2;</code>
     * @return Whether the toId field is set.
     */
    @java.lang.Override
    public boolean hasToId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 toId = 2;</code>
     * @return The toId.
     */
    @java.lang.Override
    public long getToId() {
      return toId_;
    }

    public static final int SHIELDLIST_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.LongList shieldList_;
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return A list containing the shieldList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getShieldListList() {
      return shieldList_;
    }
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return The count of shieldList.
     */
    public int getShieldListCount() {
      return shieldList_.size();
    }
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @param index The index of the element to return.
     * @return The shieldList at the given index.
     */
    public long getShieldList(int index) {
      return shieldList_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, fromId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toId_);
      }
      for (int i = 0; i < shieldList_.size(); i++) {
        output.writeInt64(3, shieldList_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, fromId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < shieldList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(shieldList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getShieldListList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.QueryChatMessageAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.QueryChatMessageAsk other = (com.yorha.proto.SsGroupChat.QueryChatMessageAsk) obj;

      if (hasFromId() != other.hasFromId()) return false;
      if (hasFromId()) {
        if (getFromId()
            != other.getFromId()) return false;
      }
      if (hasToId() != other.hasToId()) return false;
      if (hasToId()) {
        if (getToId()
            != other.getToId()) return false;
      }
      if (!getShieldListList()
          .equals(other.getShieldListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasFromId()) {
        hash = (37 * hash) + FROMID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getFromId());
      }
      if (hasToId()) {
        hash = (37 * hash) + TOID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToId());
      }
      if (getShieldListCount() > 0) {
        hash = (37 * hash) + SHIELDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getShieldListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.QueryChatMessageAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryChatMessageAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryChatMessageAsk)
        com.yorha.proto.SsGroupChat.QueryChatMessageAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.QueryChatMessageAsk.class, com.yorha.proto.SsGroupChat.QueryChatMessageAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.QueryChatMessageAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fromId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        toId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        shieldList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.QueryChatMessageAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.QueryChatMessageAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.QueryChatMessageAsk build() {
        com.yorha.proto.SsGroupChat.QueryChatMessageAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.QueryChatMessageAsk buildPartial() {
        com.yorha.proto.SsGroupChat.QueryChatMessageAsk result = new com.yorha.proto.SsGroupChat.QueryChatMessageAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.fromId_ = fromId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toId_ = toId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          shieldList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.shieldList_ = shieldList_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.QueryChatMessageAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.QueryChatMessageAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.QueryChatMessageAsk other) {
        if (other == com.yorha.proto.SsGroupChat.QueryChatMessageAsk.getDefaultInstance()) return this;
        if (other.hasFromId()) {
          setFromId(other.getFromId());
        }
        if (other.hasToId()) {
          setToId(other.getToId());
        }
        if (!other.shieldList_.isEmpty()) {
          if (shieldList_.isEmpty()) {
            shieldList_ = other.shieldList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureShieldListIsMutable();
            shieldList_.addAll(other.shieldList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.QueryChatMessageAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.QueryChatMessageAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long fromId_ ;
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return Whether the fromId field is set.
       */
      @java.lang.Override
      public boolean hasFromId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return The fromId.
       */
      @java.lang.Override
      public long getFromId() {
        return fromId_;
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @param value The fromId to set.
       * @return This builder for chaining.
       */
      public Builder setFromId(long value) {
        bitField0_ |= 0x00000001;
        fromId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        fromId_ = 0L;
        onChanged();
        return this;
      }

      private long toId_ ;
      /**
       * <code>optional int64 toId = 2;</code>
       * @return Whether the toId field is set.
       */
      @java.lang.Override
      public boolean hasToId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @return The toId.
       */
      @java.lang.Override
      public long getToId() {
        return toId_;
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @param value The toId to set.
       * @return This builder for chaining.
       */
      public Builder setToId(long value) {
        bitField0_ |= 0x00000002;
        toId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList shieldList_ = emptyLongList();
      private void ensureShieldListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          shieldList_ = mutableCopy(shieldList_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return A list containing the shieldList.
       */
      public java.util.List<java.lang.Long>
          getShieldListList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(shieldList_) : shieldList_;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return The count of shieldList.
       */
      public int getShieldListCount() {
        return shieldList_.size();
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param index The index of the element to return.
       * @return The shieldList at the given index.
       */
      public long getShieldList(int index) {
        return shieldList_.getLong(index);
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param index The index to set the value at.
       * @param value The shieldList to set.
       * @return This builder for chaining.
       */
      public Builder setShieldList(
          int index, long value) {
        ensureShieldListIsMutable();
        shieldList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param value The shieldList to add.
       * @return This builder for chaining.
       */
      public Builder addShieldList(long value) {
        ensureShieldListIsMutable();
        shieldList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param values The shieldList to add.
       * @return This builder for chaining.
       */
      public Builder addAllShieldList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureShieldListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, shieldList_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearShieldList() {
        shieldList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryChatMessageAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryChatMessageAsk)
    private static final com.yorha.proto.SsGroupChat.QueryChatMessageAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.QueryChatMessageAsk();
    }

    public static com.yorha.proto.SsGroupChat.QueryChatMessageAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryChatMessageAsk>
        PARSER = new com.google.protobuf.AbstractParser<QueryChatMessageAsk>() {
      @java.lang.Override
      public QueryChatMessageAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryChatMessageAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryChatMessageAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryChatMessageAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.QueryChatMessageAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryChatMessageAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryChatMessageAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    java.util.List<com.yorha.proto.CommonMsg.ChatMessage> 
        getChatMsgsList();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index);
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    int getChatMsgsCount();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
        getChatMsgsOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryChatMessageAns}
   */
  public static final class QueryChatMessageAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryChatMessageAns)
      QueryChatMessageAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryChatMessageAns.newBuilder() to construct.
    private QueryChatMessageAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryChatMessageAns() {
      chatMsgs_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryChatMessageAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryChatMessageAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                chatMsgs_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ChatMessage>();
                mutable_bitField0_ |= 0x00000001;
              }
              chatMsgs_.add(
                  input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          chatMsgs_ = java.util.Collections.unmodifiableList(chatMsgs_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.QueryChatMessageAns.class, com.yorha.proto.SsGroupChat.QueryChatMessageAns.Builder.class);
    }

    public static final int CHATMSGS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.CommonMsg.ChatMessage> chatMsgs_;
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.CommonMsg.ChatMessage> getChatMsgsList() {
      return chatMsgs_;
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
        getChatMsgsOrBuilderList() {
      return chatMsgs_;
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public int getChatMsgsCount() {
      return chatMsgs_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index) {
      return chatMsgs_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
        int index) {
      return chatMsgs_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < chatMsgs_.size(); i++) {
        output.writeMessage(1, chatMsgs_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < chatMsgs_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, chatMsgs_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.QueryChatMessageAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.QueryChatMessageAns other = (com.yorha.proto.SsGroupChat.QueryChatMessageAns) obj;

      if (!getChatMsgsList()
          .equals(other.getChatMsgsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChatMsgsCount() > 0) {
        hash = (37 * hash) + CHATMSGS_FIELD_NUMBER;
        hash = (53 * hash) + getChatMsgsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.QueryChatMessageAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryChatMessageAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryChatMessageAns)
        com.yorha.proto.SsGroupChat.QueryChatMessageAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.QueryChatMessageAns.class, com.yorha.proto.SsGroupChat.QueryChatMessageAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.QueryChatMessageAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMsgsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatMsgsBuilder_ == null) {
          chatMsgs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          chatMsgsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_QueryChatMessageAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.QueryChatMessageAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.QueryChatMessageAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.QueryChatMessageAns build() {
        com.yorha.proto.SsGroupChat.QueryChatMessageAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.QueryChatMessageAns buildPartial() {
        com.yorha.proto.SsGroupChat.QueryChatMessageAns result = new com.yorha.proto.SsGroupChat.QueryChatMessageAns(this);
        int from_bitField0_ = bitField0_;
        if (chatMsgsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            chatMsgs_ = java.util.Collections.unmodifiableList(chatMsgs_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.chatMsgs_ = chatMsgs_;
        } else {
          result.chatMsgs_ = chatMsgsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.QueryChatMessageAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.QueryChatMessageAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.QueryChatMessageAns other) {
        if (other == com.yorha.proto.SsGroupChat.QueryChatMessageAns.getDefaultInstance()) return this;
        if (chatMsgsBuilder_ == null) {
          if (!other.chatMsgs_.isEmpty()) {
            if (chatMsgs_.isEmpty()) {
              chatMsgs_ = other.chatMsgs_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureChatMsgsIsMutable();
              chatMsgs_.addAll(other.chatMsgs_);
            }
            onChanged();
          }
        } else {
          if (!other.chatMsgs_.isEmpty()) {
            if (chatMsgsBuilder_.isEmpty()) {
              chatMsgsBuilder_.dispose();
              chatMsgsBuilder_ = null;
              chatMsgs_ = other.chatMsgs_;
              bitField0_ = (bitField0_ & ~0x00000001);
              chatMsgsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChatMsgsFieldBuilder() : null;
            } else {
              chatMsgsBuilder_.addAllMessages(other.chatMsgs_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.QueryChatMessageAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.QueryChatMessageAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.CommonMsg.ChatMessage> chatMsgs_ =
        java.util.Collections.emptyList();
      private void ensureChatMsgsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          chatMsgs_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ChatMessage>(chatMsgs_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMsgsBuilder_;

      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ChatMessage> getChatMsgsList() {
        if (chatMsgsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(chatMsgs_);
        } else {
          return chatMsgsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public int getChatMsgsCount() {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.size();
        } else {
          return chatMsgsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index) {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.get(index);
        } else {
          return chatMsgsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder setChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.set(index, value);
          onChanged();
        } else {
          chatMsgsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder setChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.set(index, builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.add(value);
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.add(index, value);
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.add(builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.add(index, builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addAllChatMsgs(
          java.lang.Iterable<? extends com.yorha.proto.CommonMsg.ChatMessage> values) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, chatMsgs_);
          onChanged();
        } else {
          chatMsgsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder clearChatMsgs() {
        if (chatMsgsBuilder_ == null) {
          chatMsgs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          chatMsgsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder removeChatMsgs(int index) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.remove(index);
          onChanged();
        } else {
          chatMsgsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMsgsBuilder(
          int index) {
        return getChatMsgsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
          int index) {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.get(index);  } else {
          return chatMsgsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
           getChatMsgsOrBuilderList() {
        if (chatMsgsBuilder_ != null) {
          return chatMsgsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(chatMsgs_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder addChatMsgsBuilder() {
        return getChatMsgsFieldBuilder().addBuilder(
            com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder addChatMsgsBuilder(
          int index) {
        return getChatMsgsFieldBuilder().addBuilder(
            index, com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ChatMessage.Builder> 
           getChatMsgsBuilderList() {
        return getChatMsgsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMsgsFieldBuilder() {
        if (chatMsgsBuilder_ == null) {
          chatMsgsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  chatMsgs_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          chatMsgs_ = null;
        }
        return chatMsgsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryChatMessageAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryChatMessageAns)
    private static final com.yorha.proto.SsGroupChat.QueryChatMessageAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.QueryChatMessageAns();
    }

    public static com.yorha.proto.SsGroupChat.QueryChatMessageAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryChatMessageAns>
        PARSER = new com.google.protobuf.AbstractParser<QueryChatMessageAns>() {
      @java.lang.Override
      public QueryChatMessageAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryChatMessageAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryChatMessageAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryChatMessageAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.QueryChatMessageAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchChatMemberAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchChatMemberAsk)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchChatMemberAsk}
   */
  public static final class FetchChatMemberAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchChatMemberAsk)
      FetchChatMemberAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchChatMemberAsk.newBuilder() to construct.
    private FetchChatMemberAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchChatMemberAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchChatMemberAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchChatMemberAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.FetchChatMemberAsk.class, com.yorha.proto.SsGroupChat.FetchChatMemberAsk.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.FetchChatMemberAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.FetchChatMemberAsk other = (com.yorha.proto.SsGroupChat.FetchChatMemberAsk) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.FetchChatMemberAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchChatMemberAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchChatMemberAsk)
        com.yorha.proto.SsGroupChat.FetchChatMemberAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.FetchChatMemberAsk.class, com.yorha.proto.SsGroupChat.FetchChatMemberAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.FetchChatMemberAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.FetchChatMemberAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.FetchChatMemberAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.FetchChatMemberAsk build() {
        com.yorha.proto.SsGroupChat.FetchChatMemberAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.FetchChatMemberAsk buildPartial() {
        com.yorha.proto.SsGroupChat.FetchChatMemberAsk result = new com.yorha.proto.SsGroupChat.FetchChatMemberAsk(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.FetchChatMemberAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.FetchChatMemberAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.FetchChatMemberAsk other) {
        if (other == com.yorha.proto.SsGroupChat.FetchChatMemberAsk.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.FetchChatMemberAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.FetchChatMemberAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchChatMemberAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchChatMemberAsk)
    private static final com.yorha.proto.SsGroupChat.FetchChatMemberAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.FetchChatMemberAsk();
    }

    public static com.yorha.proto.SsGroupChat.FetchChatMemberAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchChatMemberAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchChatMemberAsk>() {
      @java.lang.Override
      public FetchChatMemberAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchChatMemberAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchChatMemberAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchChatMemberAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.FetchChatMemberAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchChatMemberAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchChatMemberAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 群聊描述信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
     * @return Whether the description field is set.
     */
    boolean hasDescription();
    /**
     * <pre>
     * 群聊描述信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
     * @return The description.
     */
    com.yorha.proto.CommonMsg.ChatDescriptionInfo getDescription();
    /**
     * <pre>
     * 群聊描述信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatDescriptionInfoOrBuilder getDescriptionOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchChatMemberAns}
   */
  public static final class FetchChatMemberAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchChatMemberAns)
      FetchChatMemberAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchChatMemberAns.newBuilder() to construct.
    private FetchChatMemberAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchChatMemberAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchChatMemberAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchChatMemberAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ChatDescriptionInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = description_.toBuilder();
              }
              description_ = input.readMessage(com.yorha.proto.CommonMsg.ChatDescriptionInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(description_);
                description_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.FetchChatMemberAns.class, com.yorha.proto.SsGroupChat.FetchChatMemberAns.Builder.class);
    }

    private int bitField0_;
    public static final int DESCRIPTION_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ChatDescriptionInfo description_;
    /**
     * <pre>
     * 群聊描述信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
     * @return Whether the description field is set.
     */
    @java.lang.Override
    public boolean hasDescription() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 群聊描述信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
     * @return The description.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatDescriptionInfo getDescription() {
      return description_ == null ? com.yorha.proto.CommonMsg.ChatDescriptionInfo.getDefaultInstance() : description_;
    }
    /**
     * <pre>
     * 群聊描述信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatDescriptionInfoOrBuilder getDescriptionOrBuilder() {
      return description_ == null ? com.yorha.proto.CommonMsg.ChatDescriptionInfo.getDefaultInstance() : description_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getDescription());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDescription());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.FetchChatMemberAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.FetchChatMemberAns other = (com.yorha.proto.SsGroupChat.FetchChatMemberAns) obj;

      if (hasDescription() != other.hasDescription()) return false;
      if (hasDescription()) {
        if (!getDescription()
            .equals(other.getDescription())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDescription()) {
        hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
        hash = (53 * hash) + getDescription().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.FetchChatMemberAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchChatMemberAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchChatMemberAns)
        com.yorha.proto.SsGroupChat.FetchChatMemberAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.FetchChatMemberAns.class, com.yorha.proto.SsGroupChat.FetchChatMemberAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.FetchChatMemberAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDescriptionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (descriptionBuilder_ == null) {
          description_ = null;
        } else {
          descriptionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_FetchChatMemberAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.FetchChatMemberAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.FetchChatMemberAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.FetchChatMemberAns build() {
        com.yorha.proto.SsGroupChat.FetchChatMemberAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.FetchChatMemberAns buildPartial() {
        com.yorha.proto.SsGroupChat.FetchChatMemberAns result = new com.yorha.proto.SsGroupChat.FetchChatMemberAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (descriptionBuilder_ == null) {
            result.description_ = description_;
          } else {
            result.description_ = descriptionBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.FetchChatMemberAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.FetchChatMemberAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.FetchChatMemberAns other) {
        if (other == com.yorha.proto.SsGroupChat.FetchChatMemberAns.getDefaultInstance()) return this;
        if (other.hasDescription()) {
          mergeDescription(other.getDescription());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.FetchChatMemberAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.FetchChatMemberAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatDescriptionInfo description_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatDescriptionInfo, com.yorha.proto.CommonMsg.ChatDescriptionInfo.Builder, com.yorha.proto.CommonMsg.ChatDescriptionInfoOrBuilder> descriptionBuilder_;
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       * @return Whether the description field is set.
       */
      public boolean hasDescription() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       * @return The description.
       */
      public com.yorha.proto.CommonMsg.ChatDescriptionInfo getDescription() {
        if (descriptionBuilder_ == null) {
          return description_ == null ? com.yorha.proto.CommonMsg.ChatDescriptionInfo.getDefaultInstance() : description_;
        } else {
          return descriptionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       */
      public Builder setDescription(com.yorha.proto.CommonMsg.ChatDescriptionInfo value) {
        if (descriptionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          description_ = value;
          onChanged();
        } else {
          descriptionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       */
      public Builder setDescription(
          com.yorha.proto.CommonMsg.ChatDescriptionInfo.Builder builderForValue) {
        if (descriptionBuilder_ == null) {
          description_ = builderForValue.build();
          onChanged();
        } else {
          descriptionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       */
      public Builder mergeDescription(com.yorha.proto.CommonMsg.ChatDescriptionInfo value) {
        if (descriptionBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              description_ != null &&
              description_ != com.yorha.proto.CommonMsg.ChatDescriptionInfo.getDefaultInstance()) {
            description_ =
              com.yorha.proto.CommonMsg.ChatDescriptionInfo.newBuilder(description_).mergeFrom(value).buildPartial();
          } else {
            description_ = value;
          }
          onChanged();
        } else {
          descriptionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       */
      public Builder clearDescription() {
        if (descriptionBuilder_ == null) {
          description_ = null;
          onChanged();
        } else {
          descriptionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatDescriptionInfo.Builder getDescriptionBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDescriptionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatDescriptionInfoOrBuilder getDescriptionOrBuilder() {
        if (descriptionBuilder_ != null) {
          return descriptionBuilder_.getMessageOrBuilder();
        } else {
          return description_ == null ?
              com.yorha.proto.CommonMsg.ChatDescriptionInfo.getDefaultInstance() : description_;
        }
      }
      /**
       * <pre>
       * 群聊描述信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatDescriptionInfo description = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatDescriptionInfo, com.yorha.proto.CommonMsg.ChatDescriptionInfo.Builder, com.yorha.proto.CommonMsg.ChatDescriptionInfoOrBuilder> 
          getDescriptionFieldBuilder() {
        if (descriptionBuilder_ == null) {
          descriptionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatDescriptionInfo, com.yorha.proto.CommonMsg.ChatDescriptionInfo.Builder, com.yorha.proto.CommonMsg.ChatDescriptionInfoOrBuilder>(
                  getDescription(),
                  getParentForChildren(),
                  isClean());
          description_ = null;
        }
        return descriptionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchChatMemberAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchChatMemberAns)
    private static final com.yorha.proto.SsGroupChat.FetchChatMemberAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.FetchChatMemberAns();
    }

    public static com.yorha.proto.SsGroupChat.FetchChatMemberAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchChatMemberAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchChatMemberAns>() {
      @java.lang.Override
      public FetchChatMemberAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchChatMemberAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchChatMemberAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchChatMemberAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.FetchChatMemberAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JudgeMemberAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JudgeMemberAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.JudgeMemberAsk}
   */
  public static final class JudgeMemberAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JudgeMemberAsk)
      JudgeMemberAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JudgeMemberAsk.newBuilder() to construct.
    private JudgeMemberAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JudgeMemberAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JudgeMemberAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JudgeMemberAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.JudgeMemberAsk.class, com.yorha.proto.SsGroupChat.JudgeMemberAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.JudgeMemberAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.JudgeMemberAsk other = (com.yorha.proto.SsGroupChat.JudgeMemberAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.JudgeMemberAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JudgeMemberAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JudgeMemberAsk)
        com.yorha.proto.SsGroupChat.JudgeMemberAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.JudgeMemberAsk.class, com.yorha.proto.SsGroupChat.JudgeMemberAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.JudgeMemberAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.JudgeMemberAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.JudgeMemberAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.JudgeMemberAsk build() {
        com.yorha.proto.SsGroupChat.JudgeMemberAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.JudgeMemberAsk buildPartial() {
        com.yorha.proto.SsGroupChat.JudgeMemberAsk result = new com.yorha.proto.SsGroupChat.JudgeMemberAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.JudgeMemberAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.JudgeMemberAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.JudgeMemberAsk other) {
        if (other == com.yorha.proto.SsGroupChat.JudgeMemberAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.JudgeMemberAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.JudgeMemberAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JudgeMemberAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JudgeMemberAsk)
    private static final com.yorha.proto.SsGroupChat.JudgeMemberAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.JudgeMemberAsk();
    }

    public static com.yorha.proto.SsGroupChat.JudgeMemberAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JudgeMemberAsk>
        PARSER = new com.google.protobuf.AbstractParser<JudgeMemberAsk>() {
      @java.lang.Override
      public JudgeMemberAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JudgeMemberAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JudgeMemberAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JudgeMemberAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.JudgeMemberAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JudgeMemberAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JudgeMemberAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否在群聊
     * </pre>
     *
     * <code>optional bool isIn = 1;</code>
     * @return Whether the isIn field is set.
     */
    boolean hasIsIn();
    /**
     * <pre>
     * 是否在群聊
     * </pre>
     *
     * <code>optional bool isIn = 1;</code>
     * @return The isIn.
     */
    boolean getIsIn();

    /**
     * <pre>
     * 群主
     * </pre>
     *
     * <code>optional int64 owner = 2;</code>
     * @return Whether the owner field is set.
     */
    boolean hasOwner();
    /**
     * <pre>
     * 群主
     * </pre>
     *
     * <code>optional int64 owner = 2;</code>
     * @return The owner.
     */
    long getOwner();

    /**
     * <pre>
     * 群聊版本号
     * </pre>
     *
     * <code>optional int32 version = 3;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <pre>
     * 群聊版本号
     * </pre>
     *
     * <code>optional int32 version = 3;</code>
     * @return The version.
     */
    int getVersion();

    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string chatName = 4;</code>
     * @return Whether the chatName field is set.
     */
    boolean hasChatName();
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string chatName = 4;</code>
     * @return The chatName.
     */
    java.lang.String getChatName();
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string chatName = 4;</code>
     * @return The bytes for chatName.
     */
    com.google.protobuf.ByteString
        getChatNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.JudgeMemberAns}
   */
  public static final class JudgeMemberAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JudgeMemberAns)
      JudgeMemberAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JudgeMemberAns.newBuilder() to construct.
    private JudgeMemberAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JudgeMemberAns() {
      chatName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JudgeMemberAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JudgeMemberAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isIn_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              owner_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              version_ = input.readInt32();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              chatName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.JudgeMemberAns.class, com.yorha.proto.SsGroupChat.JudgeMemberAns.Builder.class);
    }

    private int bitField0_;
    public static final int ISIN_FIELD_NUMBER = 1;
    private boolean isIn_;
    /**
     * <pre>
     * 是否在群聊
     * </pre>
     *
     * <code>optional bool isIn = 1;</code>
     * @return Whether the isIn field is set.
     */
    @java.lang.Override
    public boolean hasIsIn() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 是否在群聊
     * </pre>
     *
     * <code>optional bool isIn = 1;</code>
     * @return The isIn.
     */
    @java.lang.Override
    public boolean getIsIn() {
      return isIn_;
    }

    public static final int OWNER_FIELD_NUMBER = 2;
    private long owner_;
    /**
     * <pre>
     * 群主
     * </pre>
     *
     * <code>optional int64 owner = 2;</code>
     * @return Whether the owner field is set.
     */
    @java.lang.Override
    public boolean hasOwner() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 群主
     * </pre>
     *
     * <code>optional int64 owner = 2;</code>
     * @return The owner.
     */
    @java.lang.Override
    public long getOwner() {
      return owner_;
    }

    public static final int VERSION_FIELD_NUMBER = 3;
    private int version_;
    /**
     * <pre>
     * 群聊版本号
     * </pre>
     *
     * <code>optional int32 version = 3;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 群聊版本号
     * </pre>
     *
     * <code>optional int32 version = 3;</code>
     * @return The version.
     */
    @java.lang.Override
    public int getVersion() {
      return version_;
    }

    public static final int CHATNAME_FIELD_NUMBER = 4;
    private volatile java.lang.Object chatName_;
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string chatName = 4;</code>
     * @return Whether the chatName field is set.
     */
    @java.lang.Override
    public boolean hasChatName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string chatName = 4;</code>
     * @return The chatName.
     */
    @java.lang.Override
    public java.lang.String getChatName() {
      java.lang.Object ref = chatName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          chatName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string chatName = 4;</code>
     * @return The bytes for chatName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChatNameBytes() {
      java.lang.Object ref = chatName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        chatName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isIn_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, owner_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, version_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, chatName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isIn_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, owner_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, version_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, chatName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.JudgeMemberAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.JudgeMemberAns other = (com.yorha.proto.SsGroupChat.JudgeMemberAns) obj;

      if (hasIsIn() != other.hasIsIn()) return false;
      if (hasIsIn()) {
        if (getIsIn()
            != other.getIsIn()) return false;
      }
      if (hasOwner() != other.hasOwner()) return false;
      if (hasOwner()) {
        if (getOwner()
            != other.getOwner()) return false;
      }
      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (hasChatName() != other.hasChatName()) return false;
      if (hasChatName()) {
        if (!getChatName()
            .equals(other.getChatName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsIn()) {
        hash = (37 * hash) + ISIN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsIn());
      }
      if (hasOwner()) {
        hash = (37 * hash) + OWNER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwner());
      }
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getVersion();
      }
      if (hasChatName()) {
        hash = (37 * hash) + CHATNAME_FIELD_NUMBER;
        hash = (53 * hash) + getChatName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.JudgeMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.JudgeMemberAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JudgeMemberAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JudgeMemberAns)
        com.yorha.proto.SsGroupChat.JudgeMemberAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.JudgeMemberAns.class, com.yorha.proto.SsGroupChat.JudgeMemberAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.JudgeMemberAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isIn_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        owner_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        version_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        chatName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_JudgeMemberAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.JudgeMemberAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.JudgeMemberAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.JudgeMemberAns build() {
        com.yorha.proto.SsGroupChat.JudgeMemberAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.JudgeMemberAns buildPartial() {
        com.yorha.proto.SsGroupChat.JudgeMemberAns result = new com.yorha.proto.SsGroupChat.JudgeMemberAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isIn_ = isIn_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.owner_ = owner_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.chatName_ = chatName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.JudgeMemberAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.JudgeMemberAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.JudgeMemberAns other) {
        if (other == com.yorha.proto.SsGroupChat.JudgeMemberAns.getDefaultInstance()) return this;
        if (other.hasIsIn()) {
          setIsIn(other.getIsIn());
        }
        if (other.hasOwner()) {
          setOwner(other.getOwner());
        }
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        if (other.hasChatName()) {
          bitField0_ |= 0x00000008;
          chatName_ = other.chatName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.JudgeMemberAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.JudgeMemberAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isIn_ ;
      /**
       * <pre>
       * 是否在群聊
       * </pre>
       *
       * <code>optional bool isIn = 1;</code>
       * @return Whether the isIn field is set.
       */
      @java.lang.Override
      public boolean hasIsIn() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 是否在群聊
       * </pre>
       *
       * <code>optional bool isIn = 1;</code>
       * @return The isIn.
       */
      @java.lang.Override
      public boolean getIsIn() {
        return isIn_;
      }
      /**
       * <pre>
       * 是否在群聊
       * </pre>
       *
       * <code>optional bool isIn = 1;</code>
       * @param value The isIn to set.
       * @return This builder for chaining.
       */
      public Builder setIsIn(boolean value) {
        bitField0_ |= 0x00000001;
        isIn_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否在群聊
       * </pre>
       *
       * <code>optional bool isIn = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsIn() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isIn_ = false;
        onChanged();
        return this;
      }

      private long owner_ ;
      /**
       * <pre>
       * 群主
       * </pre>
       *
       * <code>optional int64 owner = 2;</code>
       * @return Whether the owner field is set.
       */
      @java.lang.Override
      public boolean hasOwner() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 群主
       * </pre>
       *
       * <code>optional int64 owner = 2;</code>
       * @return The owner.
       */
      @java.lang.Override
      public long getOwner() {
        return owner_;
      }
      /**
       * <pre>
       * 群主
       * </pre>
       *
       * <code>optional int64 owner = 2;</code>
       * @param value The owner to set.
       * @return This builder for chaining.
       */
      public Builder setOwner(long value) {
        bitField0_ |= 0x00000002;
        owner_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群主
       * </pre>
       *
       * <code>optional int64 owner = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwner() {
        bitField0_ = (bitField0_ & ~0x00000002);
        owner_ = 0L;
        onChanged();
        return this;
      }

      private int version_ ;
      /**
       * <pre>
       * 群聊版本号
       * </pre>
       *
       * <code>optional int32 version = 3;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 群聊版本号
       * </pre>
       *
       * <code>optional int32 version = 3;</code>
       * @return The version.
       */
      @java.lang.Override
      public int getVersion() {
        return version_;
      }
      /**
       * <pre>
       * 群聊版本号
       * </pre>
       *
       * <code>optional int32 version = 3;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(int value) {
        bitField0_ |= 0x00000004;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊版本号
       * </pre>
       *
       * <code>optional int32 version = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000004);
        version_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object chatName_ = "";
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string chatName = 4;</code>
       * @return Whether the chatName field is set.
       */
      public boolean hasChatName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string chatName = 4;</code>
       * @return The chatName.
       */
      public java.lang.String getChatName() {
        java.lang.Object ref = chatName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            chatName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string chatName = 4;</code>
       * @return The bytes for chatName.
       */
      public com.google.protobuf.ByteString
          getChatNameBytes() {
        java.lang.Object ref = chatName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          chatName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string chatName = 4;</code>
       * @param value The chatName to set.
       * @return This builder for chaining.
       */
      public Builder setChatName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        chatName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string chatName = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearChatName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        chatName_ = getDefaultInstance().getChatName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string chatName = 4;</code>
       * @param value The bytes for chatName to set.
       * @return This builder for chaining.
       */
      public Builder setChatNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        chatName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JudgeMemberAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JudgeMemberAns)
    private static final com.yorha.proto.SsGroupChat.JudgeMemberAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.JudgeMemberAns();
    }

    public static com.yorha.proto.SsGroupChat.JudgeMemberAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JudgeMemberAns>
        PARSER = new com.google.protobuf.AbstractParser<JudgeMemberAns>() {
      @java.lang.Override
      public JudgeMemberAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JudgeMemberAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JudgeMemberAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JudgeMemberAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.JudgeMemberAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface InviteNewMemberAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.InviteNewMemberAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 邀请人id
     * </pre>
     *
     * <code>optional int64 inviter = 1;</code>
     * @return Whether the inviter field is set.
     */
    boolean hasInviter();
    /**
     * <pre>
     * 邀请人id
     * </pre>
     *
     * <code>optional int64 inviter = 1;</code>
     * @return The inviter.
     */
    long getInviter();

    /**
     * <pre>
     * 群聊成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
     * @return Whether the members field is set.
     */
    boolean hasMembers();
    /**
     * <pre>
     * 群聊成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
     * @return The members.
     */
    com.yorha.proto.CommonMsg.ChatChannelMember getMembers();
    /**
     * <pre>
     * 群聊成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
     */
    com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getMembersOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.InviteNewMemberAsk}
   */
  public static final class InviteNewMemberAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.InviteNewMemberAsk)
      InviteNewMemberAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use InviteNewMemberAsk.newBuilder() to construct.
    private InviteNewMemberAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private InviteNewMemberAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new InviteNewMemberAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private InviteNewMemberAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              inviter_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.ChatChannelMember.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = members_.toBuilder();
              }
              members_ = input.readMessage(com.yorha.proto.CommonMsg.ChatChannelMember.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(members_);
                members_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.InviteNewMemberAsk.class, com.yorha.proto.SsGroupChat.InviteNewMemberAsk.Builder.class);
    }

    private int bitField0_;
    public static final int INVITER_FIELD_NUMBER = 1;
    private long inviter_;
    /**
     * <pre>
     * 邀请人id
     * </pre>
     *
     * <code>optional int64 inviter = 1;</code>
     * @return Whether the inviter field is set.
     */
    @java.lang.Override
    public boolean hasInviter() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 邀请人id
     * </pre>
     *
     * <code>optional int64 inviter = 1;</code>
     * @return The inviter.
     */
    @java.lang.Override
    public long getInviter() {
      return inviter_;
    }

    public static final int MEMBERS_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.ChatChannelMember members_;
    /**
     * <pre>
     * 群聊成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
     * @return Whether the members field is set.
     */
    @java.lang.Override
    public boolean hasMembers() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 群聊成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
     * @return The members.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatChannelMember getMembers() {
      return members_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : members_;
    }
    /**
     * <pre>
     * 群聊成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getMembersOrBuilder() {
      return members_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : members_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, inviter_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getMembers());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, inviter_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getMembers());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.InviteNewMemberAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.InviteNewMemberAsk other = (com.yorha.proto.SsGroupChat.InviteNewMemberAsk) obj;

      if (hasInviter() != other.hasInviter()) return false;
      if (hasInviter()) {
        if (getInviter()
            != other.getInviter()) return false;
      }
      if (hasMembers() != other.hasMembers()) return false;
      if (hasMembers()) {
        if (!getMembers()
            .equals(other.getMembers())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInviter()) {
        hash = (37 * hash) + INVITER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getInviter());
      }
      if (hasMembers()) {
        hash = (37 * hash) + MEMBERS_FIELD_NUMBER;
        hash = (53 * hash) + getMembers().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.InviteNewMemberAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.InviteNewMemberAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.InviteNewMemberAsk)
        com.yorha.proto.SsGroupChat.InviteNewMemberAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.InviteNewMemberAsk.class, com.yorha.proto.SsGroupChat.InviteNewMemberAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.InviteNewMemberAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMembersFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        inviter_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (membersBuilder_ == null) {
          members_ = null;
        } else {
          membersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.InviteNewMemberAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.InviteNewMemberAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.InviteNewMemberAsk build() {
        com.yorha.proto.SsGroupChat.InviteNewMemberAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.InviteNewMemberAsk buildPartial() {
        com.yorha.proto.SsGroupChat.InviteNewMemberAsk result = new com.yorha.proto.SsGroupChat.InviteNewMemberAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.inviter_ = inviter_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (membersBuilder_ == null) {
            result.members_ = members_;
          } else {
            result.members_ = membersBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.InviteNewMemberAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.InviteNewMemberAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.InviteNewMemberAsk other) {
        if (other == com.yorha.proto.SsGroupChat.InviteNewMemberAsk.getDefaultInstance()) return this;
        if (other.hasInviter()) {
          setInviter(other.getInviter());
        }
        if (other.hasMembers()) {
          mergeMembers(other.getMembers());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.InviteNewMemberAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.InviteNewMemberAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long inviter_ ;
      /**
       * <pre>
       * 邀请人id
       * </pre>
       *
       * <code>optional int64 inviter = 1;</code>
       * @return Whether the inviter field is set.
       */
      @java.lang.Override
      public boolean hasInviter() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 邀请人id
       * </pre>
       *
       * <code>optional int64 inviter = 1;</code>
       * @return The inviter.
       */
      @java.lang.Override
      public long getInviter() {
        return inviter_;
      }
      /**
       * <pre>
       * 邀请人id
       * </pre>
       *
       * <code>optional int64 inviter = 1;</code>
       * @param value The inviter to set.
       * @return This builder for chaining.
       */
      public Builder setInviter(long value) {
        bitField0_ |= 0x00000001;
        inviter_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邀请人id
       * </pre>
       *
       * <code>optional int64 inviter = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInviter() {
        bitField0_ = (bitField0_ & ~0x00000001);
        inviter_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.ChatChannelMember members_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder> membersBuilder_;
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       * @return Whether the members field is set.
       */
      public boolean hasMembers() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       * @return The members.
       */
      public com.yorha.proto.CommonMsg.ChatChannelMember getMembers() {
        if (membersBuilder_ == null) {
          return members_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : members_;
        } else {
          return membersBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       */
      public Builder setMembers(com.yorha.proto.CommonMsg.ChatChannelMember value) {
        if (membersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          members_ = value;
          onChanged();
        } else {
          membersBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       */
      public Builder setMembers(
          com.yorha.proto.CommonMsg.ChatChannelMember.Builder builderForValue) {
        if (membersBuilder_ == null) {
          members_ = builderForValue.build();
          onChanged();
        } else {
          membersBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       */
      public Builder mergeMembers(com.yorha.proto.CommonMsg.ChatChannelMember value) {
        if (membersBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              members_ != null &&
              members_ != com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance()) {
            members_ =
              com.yorha.proto.CommonMsg.ChatChannelMember.newBuilder(members_).mergeFrom(value).buildPartial();
          } else {
            members_ = value;
          }
          onChanged();
        } else {
          membersBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       */
      public Builder clearMembers() {
        if (membersBuilder_ == null) {
          members_ = null;
          onChanged();
        } else {
          membersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatChannelMember.Builder getMembersBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getMembersFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getMembersOrBuilder() {
        if (membersBuilder_ != null) {
          return membersBuilder_.getMessageOrBuilder();
        } else {
          return members_ == null ?
              com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : members_;
        }
      }
      /**
       * <pre>
       * 群聊成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder> 
          getMembersFieldBuilder() {
        if (membersBuilder_ == null) {
          membersBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder>(
                  getMembers(),
                  getParentForChildren(),
                  isClean());
          members_ = null;
        }
        return membersBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.InviteNewMemberAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.InviteNewMemberAsk)
    private static final com.yorha.proto.SsGroupChat.InviteNewMemberAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.InviteNewMemberAsk();
    }

    public static com.yorha.proto.SsGroupChat.InviteNewMemberAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<InviteNewMemberAsk>
        PARSER = new com.google.protobuf.AbstractParser<InviteNewMemberAsk>() {
      @java.lang.Override
      public InviteNewMemberAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new InviteNewMemberAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<InviteNewMemberAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<InviteNewMemberAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.InviteNewMemberAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface InviteNewMemberAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.InviteNewMemberAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 新加入的成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
     * @return Whether the members field is set.
     */
    boolean hasMembers();
    /**
     * <pre>
     * 新加入的成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
     * @return The members.
     */
    com.yorha.proto.CommonMsg.ChatChannelMember getMembers();
    /**
     * <pre>
     * 新加入的成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getMembersOrBuilder();

    /**
     * <pre>
     * 最新的群聊版本
     * </pre>
     *
     * <code>optional int32 newVersion = 2;</code>
     * @return Whether the newVersion field is set.
     */
    boolean hasNewVersion();
    /**
     * <pre>
     * 最新的群聊版本
     * </pre>
     *
     * <code>optional int32 newVersion = 2;</code>
     * @return The newVersion.
     */
    int getNewVersion();

    /**
     * <pre>
     * 失败的成员
     * </pre>
     *
     * <code>repeated int64 failedPlayer = 3;</code>
     * @return A list containing the failedPlayer.
     */
    java.util.List<java.lang.Long> getFailedPlayerList();
    /**
     * <pre>
     * 失败的成员
     * </pre>
     *
     * <code>repeated int64 failedPlayer = 3;</code>
     * @return The count of failedPlayer.
     */
    int getFailedPlayerCount();
    /**
     * <pre>
     * 失败的成员
     * </pre>
     *
     * <code>repeated int64 failedPlayer = 3;</code>
     * @param index The index of the element to return.
     * @return The failedPlayer at the given index.
     */
    long getFailedPlayer(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.InviteNewMemberAns}
   */
  public static final class InviteNewMemberAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.InviteNewMemberAns)
      InviteNewMemberAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use InviteNewMemberAns.newBuilder() to construct.
    private InviteNewMemberAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private InviteNewMemberAns() {
      failedPlayer_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new InviteNewMemberAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private InviteNewMemberAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ChatChannelMember.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = members_.toBuilder();
              }
              members_ = input.readMessage(com.yorha.proto.CommonMsg.ChatChannelMember.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(members_);
                members_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              newVersion_ = input.readInt32();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                failedPlayer_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              failedPlayer_.addLong(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                failedPlayer_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                failedPlayer_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          failedPlayer_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.InviteNewMemberAns.class, com.yorha.proto.SsGroupChat.InviteNewMemberAns.Builder.class);
    }

    private int bitField0_;
    public static final int MEMBERS_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ChatChannelMember members_;
    /**
     * <pre>
     * 新加入的成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
     * @return Whether the members field is set.
     */
    @java.lang.Override
    public boolean hasMembers() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 新加入的成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
     * @return The members.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatChannelMember getMembers() {
      return members_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : members_;
    }
    /**
     * <pre>
     * 新加入的成员
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getMembersOrBuilder() {
      return members_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : members_;
    }

    public static final int NEWVERSION_FIELD_NUMBER = 2;
    private int newVersion_;
    /**
     * <pre>
     * 最新的群聊版本
     * </pre>
     *
     * <code>optional int32 newVersion = 2;</code>
     * @return Whether the newVersion field is set.
     */
    @java.lang.Override
    public boolean hasNewVersion() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 最新的群聊版本
     * </pre>
     *
     * <code>optional int32 newVersion = 2;</code>
     * @return The newVersion.
     */
    @java.lang.Override
    public int getNewVersion() {
      return newVersion_;
    }

    public static final int FAILEDPLAYER_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.LongList failedPlayer_;
    /**
     * <pre>
     * 失败的成员
     * </pre>
     *
     * <code>repeated int64 failedPlayer = 3;</code>
     * @return A list containing the failedPlayer.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getFailedPlayerList() {
      return failedPlayer_;
    }
    /**
     * <pre>
     * 失败的成员
     * </pre>
     *
     * <code>repeated int64 failedPlayer = 3;</code>
     * @return The count of failedPlayer.
     */
    public int getFailedPlayerCount() {
      return failedPlayer_.size();
    }
    /**
     * <pre>
     * 失败的成员
     * </pre>
     *
     * <code>repeated int64 failedPlayer = 3;</code>
     * @param index The index of the element to return.
     * @return The failedPlayer at the given index.
     */
    public long getFailedPlayer(int index) {
      return failedPlayer_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getMembers());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, newVersion_);
      }
      for (int i = 0; i < failedPlayer_.size(); i++) {
        output.writeInt64(3, failedPlayer_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getMembers());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, newVersion_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < failedPlayer_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(failedPlayer_.getLong(i));
        }
        size += dataSize;
        size += 1 * getFailedPlayerList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.InviteNewMemberAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.InviteNewMemberAns other = (com.yorha.proto.SsGroupChat.InviteNewMemberAns) obj;

      if (hasMembers() != other.hasMembers()) return false;
      if (hasMembers()) {
        if (!getMembers()
            .equals(other.getMembers())) return false;
      }
      if (hasNewVersion() != other.hasNewVersion()) return false;
      if (hasNewVersion()) {
        if (getNewVersion()
            != other.getNewVersion()) return false;
      }
      if (!getFailedPlayerList()
          .equals(other.getFailedPlayerList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMembers()) {
        hash = (37 * hash) + MEMBERS_FIELD_NUMBER;
        hash = (53 * hash) + getMembers().hashCode();
      }
      if (hasNewVersion()) {
        hash = (37 * hash) + NEWVERSION_FIELD_NUMBER;
        hash = (53 * hash) + getNewVersion();
      }
      if (getFailedPlayerCount() > 0) {
        hash = (37 * hash) + FAILEDPLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getFailedPlayerList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.InviteNewMemberAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.InviteNewMemberAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.InviteNewMemberAns)
        com.yorha.proto.SsGroupChat.InviteNewMemberAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.InviteNewMemberAns.class, com.yorha.proto.SsGroupChat.InviteNewMemberAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.InviteNewMemberAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMembersFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (membersBuilder_ == null) {
          members_ = null;
        } else {
          membersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        newVersion_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        failedPlayer_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_InviteNewMemberAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.InviteNewMemberAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.InviteNewMemberAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.InviteNewMemberAns build() {
        com.yorha.proto.SsGroupChat.InviteNewMemberAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.InviteNewMemberAns buildPartial() {
        com.yorha.proto.SsGroupChat.InviteNewMemberAns result = new com.yorha.proto.SsGroupChat.InviteNewMemberAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (membersBuilder_ == null) {
            result.members_ = members_;
          } else {
            result.members_ = membersBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.newVersion_ = newVersion_;
          to_bitField0_ |= 0x00000002;
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          failedPlayer_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.failedPlayer_ = failedPlayer_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.InviteNewMemberAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.InviteNewMemberAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.InviteNewMemberAns other) {
        if (other == com.yorha.proto.SsGroupChat.InviteNewMemberAns.getDefaultInstance()) return this;
        if (other.hasMembers()) {
          mergeMembers(other.getMembers());
        }
        if (other.hasNewVersion()) {
          setNewVersion(other.getNewVersion());
        }
        if (!other.failedPlayer_.isEmpty()) {
          if (failedPlayer_.isEmpty()) {
            failedPlayer_ = other.failedPlayer_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureFailedPlayerIsMutable();
            failedPlayer_.addAll(other.failedPlayer_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.InviteNewMemberAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.InviteNewMemberAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatChannelMember members_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder> membersBuilder_;
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       * @return Whether the members field is set.
       */
      public boolean hasMembers() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       * @return The members.
       */
      public com.yorha.proto.CommonMsg.ChatChannelMember getMembers() {
        if (membersBuilder_ == null) {
          return members_ == null ? com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : members_;
        } else {
          return membersBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       */
      public Builder setMembers(com.yorha.proto.CommonMsg.ChatChannelMember value) {
        if (membersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          members_ = value;
          onChanged();
        } else {
          membersBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       */
      public Builder setMembers(
          com.yorha.proto.CommonMsg.ChatChannelMember.Builder builderForValue) {
        if (membersBuilder_ == null) {
          members_ = builderForValue.build();
          onChanged();
        } else {
          membersBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       */
      public Builder mergeMembers(com.yorha.proto.CommonMsg.ChatChannelMember value) {
        if (membersBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              members_ != null &&
              members_ != com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance()) {
            members_ =
              com.yorha.proto.CommonMsg.ChatChannelMember.newBuilder(members_).mergeFrom(value).buildPartial();
          } else {
            members_ = value;
          }
          onChanged();
        } else {
          membersBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       */
      public Builder clearMembers() {
        if (membersBuilder_ == null) {
          members_ = null;
          onChanged();
        } else {
          membersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatChannelMember.Builder getMembersBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getMembersFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder getMembersOrBuilder() {
        if (membersBuilder_ != null) {
          return membersBuilder_.getMessageOrBuilder();
        } else {
          return members_ == null ?
              com.yorha.proto.CommonMsg.ChatChannelMember.getDefaultInstance() : members_;
        }
      }
      /**
       * <pre>
       * 新加入的成员
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatChannelMember members = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder> 
          getMembersFieldBuilder() {
        if (membersBuilder_ == null) {
          membersBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatChannelMember, com.yorha.proto.CommonMsg.ChatChannelMember.Builder, com.yorha.proto.CommonMsg.ChatChannelMemberOrBuilder>(
                  getMembers(),
                  getParentForChildren(),
                  isClean());
          members_ = null;
        }
        return membersBuilder_;
      }

      private int newVersion_ ;
      /**
       * <pre>
       * 最新的群聊版本
       * </pre>
       *
       * <code>optional int32 newVersion = 2;</code>
       * @return Whether the newVersion field is set.
       */
      @java.lang.Override
      public boolean hasNewVersion() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 最新的群聊版本
       * </pre>
       *
       * <code>optional int32 newVersion = 2;</code>
       * @return The newVersion.
       */
      @java.lang.Override
      public int getNewVersion() {
        return newVersion_;
      }
      /**
       * <pre>
       * 最新的群聊版本
       * </pre>
       *
       * <code>optional int32 newVersion = 2;</code>
       * @param value The newVersion to set.
       * @return This builder for chaining.
       */
      public Builder setNewVersion(int value) {
        bitField0_ |= 0x00000002;
        newVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最新的群聊版本
       * </pre>
       *
       * <code>optional int32 newVersion = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewVersion() {
        bitField0_ = (bitField0_ & ~0x00000002);
        newVersion_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList failedPlayer_ = emptyLongList();
      private void ensureFailedPlayerIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          failedPlayer_ = mutableCopy(failedPlayer_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <pre>
       * 失败的成员
       * </pre>
       *
       * <code>repeated int64 failedPlayer = 3;</code>
       * @return A list containing the failedPlayer.
       */
      public java.util.List<java.lang.Long>
          getFailedPlayerList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(failedPlayer_) : failedPlayer_;
      }
      /**
       * <pre>
       * 失败的成员
       * </pre>
       *
       * <code>repeated int64 failedPlayer = 3;</code>
       * @return The count of failedPlayer.
       */
      public int getFailedPlayerCount() {
        return failedPlayer_.size();
      }
      /**
       * <pre>
       * 失败的成员
       * </pre>
       *
       * <code>repeated int64 failedPlayer = 3;</code>
       * @param index The index of the element to return.
       * @return The failedPlayer at the given index.
       */
      public long getFailedPlayer(int index) {
        return failedPlayer_.getLong(index);
      }
      /**
       * <pre>
       * 失败的成员
       * </pre>
       *
       * <code>repeated int64 failedPlayer = 3;</code>
       * @param index The index to set the value at.
       * @param value The failedPlayer to set.
       * @return This builder for chaining.
       */
      public Builder setFailedPlayer(
          int index, long value) {
        ensureFailedPlayerIsMutable();
        failedPlayer_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 失败的成员
       * </pre>
       *
       * <code>repeated int64 failedPlayer = 3;</code>
       * @param value The failedPlayer to add.
       * @return This builder for chaining.
       */
      public Builder addFailedPlayer(long value) {
        ensureFailedPlayerIsMutable();
        failedPlayer_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 失败的成员
       * </pre>
       *
       * <code>repeated int64 failedPlayer = 3;</code>
       * @param values The failedPlayer to add.
       * @return This builder for chaining.
       */
      public Builder addAllFailedPlayer(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureFailedPlayerIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, failedPlayer_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 失败的成员
       * </pre>
       *
       * <code>repeated int64 failedPlayer = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFailedPlayer() {
        failedPlayer_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.InviteNewMemberAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.InviteNewMemberAns)
    private static final com.yorha.proto.SsGroupChat.InviteNewMemberAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.InviteNewMemberAns();
    }

    public static com.yorha.proto.SsGroupChat.InviteNewMemberAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<InviteNewMemberAns>
        PARSER = new com.google.protobuf.AbstractParser<InviteNewMemberAns>() {
      @java.lang.Override
      public InviteNewMemberAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new InviteNewMemberAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<InviteNewMemberAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<InviteNewMemberAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.InviteNewMemberAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RemoveGroupMemberAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RemoveGroupMemberAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 被删除人id
     * </pre>
     *
     * <code>repeated int64 memberIds = 1;</code>
     * @return A list containing the memberIds.
     */
    java.util.List<java.lang.Long> getMemberIdsList();
    /**
     * <pre>
     * 被删除人id
     * </pre>
     *
     * <code>repeated int64 memberIds = 1;</code>
     * @return The count of memberIds.
     */
    int getMemberIdsCount();
    /**
     * <pre>
     * 被删除人id
     * </pre>
     *
     * <code>repeated int64 memberIds = 1;</code>
     * @param index The index of the element to return.
     * @return The memberIds at the given index.
     */
    long getMemberIds(int index);

    /**
     * <pre>
     * 删除人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return Whether the operator field is set.
     */
    boolean hasOperator();
    /**
     * <pre>
     * 删除人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return The operator.
     */
    long getOperator();

    /**
     * <pre>
     * 是否是主动退群
     * </pre>
     *
     * <code>optional bool quitGroup = 3;</code>
     * @return Whether the quitGroup field is set.
     */
    boolean hasQuitGroup();
    /**
     * <pre>
     * 是否是主动退群
     * </pre>
     *
     * <code>optional bool quitGroup = 3;</code>
     * @return The quitGroup.
     */
    boolean getQuitGroup();

    /**
     * <pre>
     * 主动退群的成员名字
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <pre>
     * 主动退群的成员名字
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     * 主动退群的成员名字
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RemoveGroupMemberAsk}
   */
  public static final class RemoveGroupMemberAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RemoveGroupMemberAsk)
      RemoveGroupMemberAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RemoveGroupMemberAsk.newBuilder() to construct.
    private RemoveGroupMemberAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RemoveGroupMemberAsk() {
      memberIds_ = emptyLongList();
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RemoveGroupMemberAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RemoveGroupMemberAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                memberIds_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              memberIds_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                memberIds_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                memberIds_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              operator_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              quitGroup_ = input.readBool();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              name_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          memberIds_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk.class, com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk.Builder.class);
    }

    private int bitField0_;
    public static final int MEMBERIDS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList memberIds_;
    /**
     * <pre>
     * 被删除人id
     * </pre>
     *
     * <code>repeated int64 memberIds = 1;</code>
     * @return A list containing the memberIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getMemberIdsList() {
      return memberIds_;
    }
    /**
     * <pre>
     * 被删除人id
     * </pre>
     *
     * <code>repeated int64 memberIds = 1;</code>
     * @return The count of memberIds.
     */
    public int getMemberIdsCount() {
      return memberIds_.size();
    }
    /**
     * <pre>
     * 被删除人id
     * </pre>
     *
     * <code>repeated int64 memberIds = 1;</code>
     * @param index The index of the element to return.
     * @return The memberIds at the given index.
     */
    public long getMemberIds(int index) {
      return memberIds_.getLong(index);
    }

    public static final int OPERATOR_FIELD_NUMBER = 2;
    private long operator_;
    /**
     * <pre>
     * 删除人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return Whether the operator field is set.
     */
    @java.lang.Override
    public boolean hasOperator() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 删除人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return The operator.
     */
    @java.lang.Override
    public long getOperator() {
      return operator_;
    }

    public static final int QUITGROUP_FIELD_NUMBER = 3;
    private boolean quitGroup_;
    /**
     * <pre>
     * 是否是主动退群
     * </pre>
     *
     * <code>optional bool quitGroup = 3;</code>
     * @return Whether the quitGroup field is set.
     */
    @java.lang.Override
    public boolean hasQuitGroup() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 是否是主动退群
     * </pre>
     *
     * <code>optional bool quitGroup = 3;</code>
     * @return The quitGroup.
     */
    @java.lang.Override
    public boolean getQuitGroup() {
      return quitGroup_;
    }

    public static final int NAME_FIELD_NUMBER = 4;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     * 主动退群的成员名字
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 主动退群的成员名字
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 主动退群的成员名字
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < memberIds_.size(); i++) {
        output.writeInt64(1, memberIds_.getLong(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(2, operator_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(3, quitGroup_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < memberIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(memberIds_.getLong(i));
        }
        size += dataSize;
        size += 1 * getMemberIdsList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, operator_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, quitGroup_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk other = (com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk) obj;

      if (!getMemberIdsList()
          .equals(other.getMemberIdsList())) return false;
      if (hasOperator() != other.hasOperator()) return false;
      if (hasOperator()) {
        if (getOperator()
            != other.getOperator()) return false;
      }
      if (hasQuitGroup() != other.hasQuitGroup()) return false;
      if (hasQuitGroup()) {
        if (getQuitGroup()
            != other.getQuitGroup()) return false;
      }
      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getMemberIdsCount() > 0) {
        hash = (37 * hash) + MEMBERIDS_FIELD_NUMBER;
        hash = (53 * hash) + getMemberIdsList().hashCode();
      }
      if (hasOperator()) {
        hash = (37 * hash) + OPERATOR_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOperator());
      }
      if (hasQuitGroup()) {
        hash = (37 * hash) + QUITGROUP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getQuitGroup());
      }
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RemoveGroupMemberAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RemoveGroupMemberAsk)
        com.yorha.proto.SsGroupChat.RemoveGroupMemberAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk.class, com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        memberIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        operator_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        quitGroup_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk build() {
        com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk buildPartial() {
        com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk result = new com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          memberIds_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.memberIds_ = memberIds_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.operator_ = operator_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.quitGroup_ = quitGroup_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.name_ = name_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk other) {
        if (other == com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk.getDefaultInstance()) return this;
        if (!other.memberIds_.isEmpty()) {
          if (memberIds_.isEmpty()) {
            memberIds_ = other.memberIds_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureMemberIdsIsMutable();
            memberIds_.addAll(other.memberIds_);
          }
          onChanged();
        }
        if (other.hasOperator()) {
          setOperator(other.getOperator());
        }
        if (other.hasQuitGroup()) {
          setQuitGroup(other.getQuitGroup());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000008;
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList memberIds_ = emptyLongList();
      private void ensureMemberIdsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          memberIds_ = mutableCopy(memberIds_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       * 被删除人id
       * </pre>
       *
       * <code>repeated int64 memberIds = 1;</code>
       * @return A list containing the memberIds.
       */
      public java.util.List<java.lang.Long>
          getMemberIdsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(memberIds_) : memberIds_;
      }
      /**
       * <pre>
       * 被删除人id
       * </pre>
       *
       * <code>repeated int64 memberIds = 1;</code>
       * @return The count of memberIds.
       */
      public int getMemberIdsCount() {
        return memberIds_.size();
      }
      /**
       * <pre>
       * 被删除人id
       * </pre>
       *
       * <code>repeated int64 memberIds = 1;</code>
       * @param index The index of the element to return.
       * @return The memberIds at the given index.
       */
      public long getMemberIds(int index) {
        return memberIds_.getLong(index);
      }
      /**
       * <pre>
       * 被删除人id
       * </pre>
       *
       * <code>repeated int64 memberIds = 1;</code>
       * @param index The index to set the value at.
       * @param value The memberIds to set.
       * @return This builder for chaining.
       */
      public Builder setMemberIds(
          int index, long value) {
        ensureMemberIdsIsMutable();
        memberIds_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被删除人id
       * </pre>
       *
       * <code>repeated int64 memberIds = 1;</code>
       * @param value The memberIds to add.
       * @return This builder for chaining.
       */
      public Builder addMemberIds(long value) {
        ensureMemberIdsIsMutable();
        memberIds_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被删除人id
       * </pre>
       *
       * <code>repeated int64 memberIds = 1;</code>
       * @param values The memberIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllMemberIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureMemberIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, memberIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被删除人id
       * </pre>
       *
       * <code>repeated int64 memberIds = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMemberIds() {
        memberIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private long operator_ ;
      /**
       * <pre>
       * 删除人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return Whether the operator field is set.
       */
      @java.lang.Override
      public boolean hasOperator() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 删除人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return The operator.
       */
      @java.lang.Override
      public long getOperator() {
        return operator_;
      }
      /**
       * <pre>
       * 删除人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @param value The operator to set.
       * @return This builder for chaining.
       */
      public Builder setOperator(long value) {
        bitField0_ |= 0x00000002;
        operator_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 删除人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperator() {
        bitField0_ = (bitField0_ & ~0x00000002);
        operator_ = 0L;
        onChanged();
        return this;
      }

      private boolean quitGroup_ ;
      /**
       * <pre>
       * 是否是主动退群
       * </pre>
       *
       * <code>optional bool quitGroup = 3;</code>
       * @return Whether the quitGroup field is set.
       */
      @java.lang.Override
      public boolean hasQuitGroup() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 是否是主动退群
       * </pre>
       *
       * <code>optional bool quitGroup = 3;</code>
       * @return The quitGroup.
       */
      @java.lang.Override
      public boolean getQuitGroup() {
        return quitGroup_;
      }
      /**
       * <pre>
       * 是否是主动退群
       * </pre>
       *
       * <code>optional bool quitGroup = 3;</code>
       * @param value The quitGroup to set.
       * @return This builder for chaining.
       */
      public Builder setQuitGroup(boolean value) {
        bitField0_ |= 0x00000004;
        quitGroup_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否是主动退群
       * </pre>
       *
       * <code>optional bool quitGroup = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuitGroup() {
        bitField0_ = (bitField0_ & ~0x00000004);
        quitGroup_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       * 主动退群的成员名字
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 主动退群的成员名字
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 主动退群的成员名字
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 主动退群的成员名字
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 主动退群的成员名字
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 主动退群的成员名字
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RemoveGroupMemberAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RemoveGroupMemberAsk)
    private static final com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk();
    }

    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RemoveGroupMemberAsk>
        PARSER = new com.google.protobuf.AbstractParser<RemoveGroupMemberAsk>() {
      @java.lang.Override
      public RemoveGroupMemberAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RemoveGroupMemberAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RemoveGroupMemberAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RemoveGroupMemberAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.RemoveGroupMemberAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RemoveGroupMemberAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RemoveGroupMemberAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否删除聊天
     * </pre>
     *
     * <code>optional bool deleteGroup = 1;</code>
     * @return Whether the deleteGroup field is set.
     */
    boolean hasDeleteGroup();
    /**
     * <pre>
     * 是否删除聊天
     * </pre>
     *
     * <code>optional bool deleteGroup = 1;</code>
     * @return The deleteGroup.
     */
    boolean getDeleteGroup();

    /**
     * <pre>
     * 新群主id
     * </pre>
     *
     * <code>optional int64 groupOwner = 2;</code>
     * @return Whether the groupOwner field is set.
     */
    boolean hasGroupOwner();
    /**
     * <pre>
     * 新群主id
     * </pre>
     *
     * <code>optional int64 groupOwner = 2;</code>
     * @return The groupOwner.
     */
    long getGroupOwner();

    /**
     * <pre>
     * 新群版本
     * </pre>
     *
     * <code>optional int32 groupVersion = 3;</code>
     * @return Whether the groupVersion field is set.
     */
    boolean hasGroupVersion();
    /**
     * <pre>
     * 新群版本
     * </pre>
     *
     * <code>optional int32 groupVersion = 3;</code>
     * @return The groupVersion.
     */
    int getGroupVersion();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RemoveGroupMemberAns}
   */
  public static final class RemoveGroupMemberAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RemoveGroupMemberAns)
      RemoveGroupMemberAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RemoveGroupMemberAns.newBuilder() to construct.
    private RemoveGroupMemberAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RemoveGroupMemberAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RemoveGroupMemberAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RemoveGroupMemberAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              deleteGroup_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              groupOwner_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              groupVersion_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.RemoveGroupMemberAns.class, com.yorha.proto.SsGroupChat.RemoveGroupMemberAns.Builder.class);
    }

    private int bitField0_;
    public static final int DELETEGROUP_FIELD_NUMBER = 1;
    private boolean deleteGroup_;
    /**
     * <pre>
     * 是否删除聊天
     * </pre>
     *
     * <code>optional bool deleteGroup = 1;</code>
     * @return Whether the deleteGroup field is set.
     */
    @java.lang.Override
    public boolean hasDeleteGroup() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 是否删除聊天
     * </pre>
     *
     * <code>optional bool deleteGroup = 1;</code>
     * @return The deleteGroup.
     */
    @java.lang.Override
    public boolean getDeleteGroup() {
      return deleteGroup_;
    }

    public static final int GROUPOWNER_FIELD_NUMBER = 2;
    private long groupOwner_;
    /**
     * <pre>
     * 新群主id
     * </pre>
     *
     * <code>optional int64 groupOwner = 2;</code>
     * @return Whether the groupOwner field is set.
     */
    @java.lang.Override
    public boolean hasGroupOwner() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 新群主id
     * </pre>
     *
     * <code>optional int64 groupOwner = 2;</code>
     * @return The groupOwner.
     */
    @java.lang.Override
    public long getGroupOwner() {
      return groupOwner_;
    }

    public static final int GROUPVERSION_FIELD_NUMBER = 3;
    private int groupVersion_;
    /**
     * <pre>
     * 新群版本
     * </pre>
     *
     * <code>optional int32 groupVersion = 3;</code>
     * @return Whether the groupVersion field is set.
     */
    @java.lang.Override
    public boolean hasGroupVersion() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 新群版本
     * </pre>
     *
     * <code>optional int32 groupVersion = 3;</code>
     * @return The groupVersion.
     */
    @java.lang.Override
    public int getGroupVersion() {
      return groupVersion_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, deleteGroup_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, groupOwner_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, groupVersion_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, deleteGroup_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, groupOwner_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, groupVersion_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.RemoveGroupMemberAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.RemoveGroupMemberAns other = (com.yorha.proto.SsGroupChat.RemoveGroupMemberAns) obj;

      if (hasDeleteGroup() != other.hasDeleteGroup()) return false;
      if (hasDeleteGroup()) {
        if (getDeleteGroup()
            != other.getDeleteGroup()) return false;
      }
      if (hasGroupOwner() != other.hasGroupOwner()) return false;
      if (hasGroupOwner()) {
        if (getGroupOwner()
            != other.getGroupOwner()) return false;
      }
      if (hasGroupVersion() != other.hasGroupVersion()) return false;
      if (hasGroupVersion()) {
        if (getGroupVersion()
            != other.getGroupVersion()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDeleteGroup()) {
        hash = (37 * hash) + DELETEGROUP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getDeleteGroup());
      }
      if (hasGroupOwner()) {
        hash = (37 * hash) + GROUPOWNER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getGroupOwner());
      }
      if (hasGroupVersion()) {
        hash = (37 * hash) + GROUPVERSION_FIELD_NUMBER;
        hash = (53 * hash) + getGroupVersion();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.RemoveGroupMemberAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RemoveGroupMemberAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RemoveGroupMemberAns)
        com.yorha.proto.SsGroupChat.RemoveGroupMemberAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.RemoveGroupMemberAns.class, com.yorha.proto.SsGroupChat.RemoveGroupMemberAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.RemoveGroupMemberAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        deleteGroup_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        groupOwner_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        groupVersion_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_RemoveGroupMemberAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.RemoveGroupMemberAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.RemoveGroupMemberAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.RemoveGroupMemberAns build() {
        com.yorha.proto.SsGroupChat.RemoveGroupMemberAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.RemoveGroupMemberAns buildPartial() {
        com.yorha.proto.SsGroupChat.RemoveGroupMemberAns result = new com.yorha.proto.SsGroupChat.RemoveGroupMemberAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.deleteGroup_ = deleteGroup_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.groupOwner_ = groupOwner_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.groupVersion_ = groupVersion_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.RemoveGroupMemberAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.RemoveGroupMemberAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.RemoveGroupMemberAns other) {
        if (other == com.yorha.proto.SsGroupChat.RemoveGroupMemberAns.getDefaultInstance()) return this;
        if (other.hasDeleteGroup()) {
          setDeleteGroup(other.getDeleteGroup());
        }
        if (other.hasGroupOwner()) {
          setGroupOwner(other.getGroupOwner());
        }
        if (other.hasGroupVersion()) {
          setGroupVersion(other.getGroupVersion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.RemoveGroupMemberAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.RemoveGroupMemberAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean deleteGroup_ ;
      /**
       * <pre>
       * 是否删除聊天
       * </pre>
       *
       * <code>optional bool deleteGroup = 1;</code>
       * @return Whether the deleteGroup field is set.
       */
      @java.lang.Override
      public boolean hasDeleteGroup() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 是否删除聊天
       * </pre>
       *
       * <code>optional bool deleteGroup = 1;</code>
       * @return The deleteGroup.
       */
      @java.lang.Override
      public boolean getDeleteGroup() {
        return deleteGroup_;
      }
      /**
       * <pre>
       * 是否删除聊天
       * </pre>
       *
       * <code>optional bool deleteGroup = 1;</code>
       * @param value The deleteGroup to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteGroup(boolean value) {
        bitField0_ |= 0x00000001;
        deleteGroup_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否删除聊天
       * </pre>
       *
       * <code>optional bool deleteGroup = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteGroup() {
        bitField0_ = (bitField0_ & ~0x00000001);
        deleteGroup_ = false;
        onChanged();
        return this;
      }

      private long groupOwner_ ;
      /**
       * <pre>
       * 新群主id
       * </pre>
       *
       * <code>optional int64 groupOwner = 2;</code>
       * @return Whether the groupOwner field is set.
       */
      @java.lang.Override
      public boolean hasGroupOwner() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 新群主id
       * </pre>
       *
       * <code>optional int64 groupOwner = 2;</code>
       * @return The groupOwner.
       */
      @java.lang.Override
      public long getGroupOwner() {
        return groupOwner_;
      }
      /**
       * <pre>
       * 新群主id
       * </pre>
       *
       * <code>optional int64 groupOwner = 2;</code>
       * @param value The groupOwner to set.
       * @return This builder for chaining.
       */
      public Builder setGroupOwner(long value) {
        bitField0_ |= 0x00000002;
        groupOwner_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新群主id
       * </pre>
       *
       * <code>optional int64 groupOwner = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupOwner() {
        bitField0_ = (bitField0_ & ~0x00000002);
        groupOwner_ = 0L;
        onChanged();
        return this;
      }

      private int groupVersion_ ;
      /**
       * <pre>
       * 新群版本
       * </pre>
       *
       * <code>optional int32 groupVersion = 3;</code>
       * @return Whether the groupVersion field is set.
       */
      @java.lang.Override
      public boolean hasGroupVersion() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 新群版本
       * </pre>
       *
       * <code>optional int32 groupVersion = 3;</code>
       * @return The groupVersion.
       */
      @java.lang.Override
      public int getGroupVersion() {
        return groupVersion_;
      }
      /**
       * <pre>
       * 新群版本
       * </pre>
       *
       * <code>optional int32 groupVersion = 3;</code>
       * @param value The groupVersion to set.
       * @return This builder for chaining.
       */
      public Builder setGroupVersion(int value) {
        bitField0_ |= 0x00000004;
        groupVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新群版本
       * </pre>
       *
       * <code>optional int32 groupVersion = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupVersion() {
        bitField0_ = (bitField0_ & ~0x00000004);
        groupVersion_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RemoveGroupMemberAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RemoveGroupMemberAns)
    private static final com.yorha.proto.SsGroupChat.RemoveGroupMemberAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.RemoveGroupMemberAns();
    }

    public static com.yorha.proto.SsGroupChat.RemoveGroupMemberAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RemoveGroupMemberAns>
        PARSER = new com.google.protobuf.AbstractParser<RemoveGroupMemberAns>() {
      @java.lang.Override
      public RemoveGroupMemberAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RemoveGroupMemberAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RemoveGroupMemberAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RemoveGroupMemberAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.RemoveGroupMemberAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TransferGroupOwnerAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.TransferGroupOwnerAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 newOwner = 1;</code>
     * @return Whether the newOwner field is set.
     */
    boolean hasNewOwner();
    /**
     * <code>optional int64 newOwner = 1;</code>
     * @return The newOwner.
     */
    long getNewOwner();

    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return Whether the operator field is set.
     */
    boolean hasOperator();
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return The operator.
     */
    long getOperator();
  }
  /**
   * Protobuf type {@code com.yorha.proto.TransferGroupOwnerAsk}
   */
  public static final class TransferGroupOwnerAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.TransferGroupOwnerAsk)
      TransferGroupOwnerAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TransferGroupOwnerAsk.newBuilder() to construct.
    private TransferGroupOwnerAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TransferGroupOwnerAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TransferGroupOwnerAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TransferGroupOwnerAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              newOwner_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              operator_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk.class, com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk.Builder.class);
    }

    private int bitField0_;
    public static final int NEWOWNER_FIELD_NUMBER = 1;
    private long newOwner_;
    /**
     * <code>optional int64 newOwner = 1;</code>
     * @return Whether the newOwner field is set.
     */
    @java.lang.Override
    public boolean hasNewOwner() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 newOwner = 1;</code>
     * @return The newOwner.
     */
    @java.lang.Override
    public long getNewOwner() {
      return newOwner_;
    }

    public static final int OPERATOR_FIELD_NUMBER = 2;
    private long operator_;
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return Whether the operator field is set.
     */
    @java.lang.Override
    public boolean hasOperator() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return The operator.
     */
    @java.lang.Override
    public long getOperator() {
      return operator_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, newOwner_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, operator_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, newOwner_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, operator_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk other = (com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk) obj;

      if (hasNewOwner() != other.hasNewOwner()) return false;
      if (hasNewOwner()) {
        if (getNewOwner()
            != other.getNewOwner()) return false;
      }
      if (hasOperator() != other.hasOperator()) return false;
      if (hasOperator()) {
        if (getOperator()
            != other.getOperator()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNewOwner()) {
        hash = (37 * hash) + NEWOWNER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getNewOwner());
      }
      if (hasOperator()) {
        hash = (37 * hash) + OPERATOR_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOperator());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.TransferGroupOwnerAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.TransferGroupOwnerAsk)
        com.yorha.proto.SsGroupChat.TransferGroupOwnerAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk.class, com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        newOwner_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        operator_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk build() {
        com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk buildPartial() {
        com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk result = new com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.newOwner_ = newOwner_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.operator_ = operator_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk other) {
        if (other == com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk.getDefaultInstance()) return this;
        if (other.hasNewOwner()) {
          setNewOwner(other.getNewOwner());
        }
        if (other.hasOperator()) {
          setOperator(other.getOperator());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long newOwner_ ;
      /**
       * <code>optional int64 newOwner = 1;</code>
       * @return Whether the newOwner field is set.
       */
      @java.lang.Override
      public boolean hasNewOwner() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 newOwner = 1;</code>
       * @return The newOwner.
       */
      @java.lang.Override
      public long getNewOwner() {
        return newOwner_;
      }
      /**
       * <code>optional int64 newOwner = 1;</code>
       * @param value The newOwner to set.
       * @return This builder for chaining.
       */
      public Builder setNewOwner(long value) {
        bitField0_ |= 0x00000001;
        newOwner_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 newOwner = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewOwner() {
        bitField0_ = (bitField0_ & ~0x00000001);
        newOwner_ = 0L;
        onChanged();
        return this;
      }

      private long operator_ ;
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return Whether the operator field is set.
       */
      @java.lang.Override
      public boolean hasOperator() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return The operator.
       */
      @java.lang.Override
      public long getOperator() {
        return operator_;
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @param value The operator to set.
       * @return This builder for chaining.
       */
      public Builder setOperator(long value) {
        bitField0_ |= 0x00000002;
        operator_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperator() {
        bitField0_ = (bitField0_ & ~0x00000002);
        operator_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.TransferGroupOwnerAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.TransferGroupOwnerAsk)
    private static final com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk();
    }

    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TransferGroupOwnerAsk>
        PARSER = new com.google.protobuf.AbstractParser<TransferGroupOwnerAsk>() {
      @java.lang.Override
      public TransferGroupOwnerAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TransferGroupOwnerAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TransferGroupOwnerAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TransferGroupOwnerAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.TransferGroupOwnerAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TransferGroupOwnerAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.TransferGroupOwnerAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.TransferGroupOwnerAns}
   */
  public static final class TransferGroupOwnerAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.TransferGroupOwnerAns)
      TransferGroupOwnerAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TransferGroupOwnerAns.newBuilder() to construct.
    private TransferGroupOwnerAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TransferGroupOwnerAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TransferGroupOwnerAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TransferGroupOwnerAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.TransferGroupOwnerAns.class, com.yorha.proto.SsGroupChat.TransferGroupOwnerAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.TransferGroupOwnerAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.TransferGroupOwnerAns other = (com.yorha.proto.SsGroupChat.TransferGroupOwnerAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.TransferGroupOwnerAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.TransferGroupOwnerAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.TransferGroupOwnerAns)
        com.yorha.proto.SsGroupChat.TransferGroupOwnerAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.TransferGroupOwnerAns.class, com.yorha.proto.SsGroupChat.TransferGroupOwnerAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.TransferGroupOwnerAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_TransferGroupOwnerAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.TransferGroupOwnerAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.TransferGroupOwnerAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.TransferGroupOwnerAns build() {
        com.yorha.proto.SsGroupChat.TransferGroupOwnerAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.TransferGroupOwnerAns buildPartial() {
        com.yorha.proto.SsGroupChat.TransferGroupOwnerAns result = new com.yorha.proto.SsGroupChat.TransferGroupOwnerAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.TransferGroupOwnerAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.TransferGroupOwnerAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.TransferGroupOwnerAns other) {
        if (other == com.yorha.proto.SsGroupChat.TransferGroupOwnerAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.TransferGroupOwnerAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.TransferGroupOwnerAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.TransferGroupOwnerAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.TransferGroupOwnerAns)
    private static final com.yorha.proto.SsGroupChat.TransferGroupOwnerAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.TransferGroupOwnerAns();
    }

    public static com.yorha.proto.SsGroupChat.TransferGroupOwnerAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TransferGroupOwnerAns>
        PARSER = new com.google.protobuf.AbstractParser<TransferGroupOwnerAns>() {
      @java.lang.Override
      public TransferGroupOwnerAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TransferGroupOwnerAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TransferGroupOwnerAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TransferGroupOwnerAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.TransferGroupOwnerAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DismissChatGroupAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DismissChatGroupAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return Whether the chatGroupId field is set.
     */
    boolean hasChatGroupId();
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return The chatGroupId.
     */
    java.lang.String getChatGroupId();
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return The bytes for chatGroupId.
     */
    com.google.protobuf.ByteString
        getChatGroupIdBytes();

    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return Whether the operator field is set.
     */
    boolean hasOperator();
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return The operator.
     */
    long getOperator();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DismissChatGroupAsk}
   */
  public static final class DismissChatGroupAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DismissChatGroupAsk)
      DismissChatGroupAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DismissChatGroupAsk.newBuilder() to construct.
    private DismissChatGroupAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DismissChatGroupAsk() {
      chatGroupId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DismissChatGroupAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DismissChatGroupAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              chatGroupId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              operator_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.DismissChatGroupAsk.class, com.yorha.proto.SsGroupChat.DismissChatGroupAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHATGROUPID_FIELD_NUMBER = 1;
    private volatile java.lang.Object chatGroupId_;
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return Whether the chatGroupId field is set.
     */
    @java.lang.Override
    public boolean hasChatGroupId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return The chatGroupId.
     */
    @java.lang.Override
    public java.lang.String getChatGroupId() {
      java.lang.Object ref = chatGroupId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          chatGroupId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return The bytes for chatGroupId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChatGroupIdBytes() {
      java.lang.Object ref = chatGroupId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        chatGroupId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OPERATOR_FIELD_NUMBER = 2;
    private long operator_;
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return Whether the operator field is set.
     */
    @java.lang.Override
    public boolean hasOperator() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 2;</code>
     * @return The operator.
     */
    @java.lang.Override
    public long getOperator() {
      return operator_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, chatGroupId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, operator_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, chatGroupId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, operator_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.DismissChatGroupAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.DismissChatGroupAsk other = (com.yorha.proto.SsGroupChat.DismissChatGroupAsk) obj;

      if (hasChatGroupId() != other.hasChatGroupId()) return false;
      if (hasChatGroupId()) {
        if (!getChatGroupId()
            .equals(other.getChatGroupId())) return false;
      }
      if (hasOperator() != other.hasOperator()) return false;
      if (hasOperator()) {
        if (getOperator()
            != other.getOperator()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatGroupId()) {
        hash = (37 * hash) + CHATGROUPID_FIELD_NUMBER;
        hash = (53 * hash) + getChatGroupId().hashCode();
      }
      if (hasOperator()) {
        hash = (37 * hash) + OPERATOR_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOperator());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.DismissChatGroupAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DismissChatGroupAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DismissChatGroupAsk)
        com.yorha.proto.SsGroupChat.DismissChatGroupAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.DismissChatGroupAsk.class, com.yorha.proto.SsGroupChat.DismissChatGroupAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.DismissChatGroupAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chatGroupId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        operator_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.DismissChatGroupAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.DismissChatGroupAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.DismissChatGroupAsk build() {
        com.yorha.proto.SsGroupChat.DismissChatGroupAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.DismissChatGroupAsk buildPartial() {
        com.yorha.proto.SsGroupChat.DismissChatGroupAsk result = new com.yorha.proto.SsGroupChat.DismissChatGroupAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.chatGroupId_ = chatGroupId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.operator_ = operator_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.DismissChatGroupAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.DismissChatGroupAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.DismissChatGroupAsk other) {
        if (other == com.yorha.proto.SsGroupChat.DismissChatGroupAsk.getDefaultInstance()) return this;
        if (other.hasChatGroupId()) {
          bitField0_ |= 0x00000001;
          chatGroupId_ = other.chatGroupId_;
          onChanged();
        }
        if (other.hasOperator()) {
          setOperator(other.getOperator());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.DismissChatGroupAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.DismissChatGroupAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object chatGroupId_ = "";
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @return Whether the chatGroupId field is set.
       */
      public boolean hasChatGroupId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @return The chatGroupId.
       */
      public java.lang.String getChatGroupId() {
        java.lang.Object ref = chatGroupId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            chatGroupId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @return The bytes for chatGroupId.
       */
      public com.google.protobuf.ByteString
          getChatGroupIdBytes() {
        java.lang.Object ref = chatGroupId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          chatGroupId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @param value The chatGroupId to set.
       * @return This builder for chaining.
       */
      public Builder setChatGroupId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        chatGroupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChatGroupId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chatGroupId_ = getDefaultInstance().getChatGroupId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @param value The bytes for chatGroupId to set.
       * @return This builder for chaining.
       */
      public Builder setChatGroupIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        chatGroupId_ = value;
        onChanged();
        return this;
      }

      private long operator_ ;
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return Whether the operator field is set.
       */
      @java.lang.Override
      public boolean hasOperator() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return The operator.
       */
      @java.lang.Override
      public long getOperator() {
        return operator_;
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @param value The operator to set.
       * @return This builder for chaining.
       */
      public Builder setOperator(long value) {
        bitField0_ |= 0x00000002;
        operator_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperator() {
        bitField0_ = (bitField0_ & ~0x00000002);
        operator_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DismissChatGroupAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DismissChatGroupAsk)
    private static final com.yorha.proto.SsGroupChat.DismissChatGroupAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.DismissChatGroupAsk();
    }

    public static com.yorha.proto.SsGroupChat.DismissChatGroupAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DismissChatGroupAsk>
        PARSER = new com.google.protobuf.AbstractParser<DismissChatGroupAsk>() {
      @java.lang.Override
      public DismissChatGroupAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DismissChatGroupAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DismissChatGroupAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DismissChatGroupAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.DismissChatGroupAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DismissChatGroupAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DismissChatGroupAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.DismissChatGroupAns}
   */
  public static final class DismissChatGroupAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DismissChatGroupAns)
      DismissChatGroupAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DismissChatGroupAns.newBuilder() to construct.
    private DismissChatGroupAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DismissChatGroupAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DismissChatGroupAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DismissChatGroupAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.DismissChatGroupAns.class, com.yorha.proto.SsGroupChat.DismissChatGroupAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.DismissChatGroupAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.DismissChatGroupAns other = (com.yorha.proto.SsGroupChat.DismissChatGroupAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.DismissChatGroupAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DismissChatGroupAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DismissChatGroupAns)
        com.yorha.proto.SsGroupChat.DismissChatGroupAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.DismissChatGroupAns.class, com.yorha.proto.SsGroupChat.DismissChatGroupAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.DismissChatGroupAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_DismissChatGroupAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.DismissChatGroupAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.DismissChatGroupAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.DismissChatGroupAns build() {
        com.yorha.proto.SsGroupChat.DismissChatGroupAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.DismissChatGroupAns buildPartial() {
        com.yorha.proto.SsGroupChat.DismissChatGroupAns result = new com.yorha.proto.SsGroupChat.DismissChatGroupAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.DismissChatGroupAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.DismissChatGroupAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.DismissChatGroupAns other) {
        if (other == com.yorha.proto.SsGroupChat.DismissChatGroupAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.DismissChatGroupAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.DismissChatGroupAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DismissChatGroupAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DismissChatGroupAns)
    private static final com.yorha.proto.SsGroupChat.DismissChatGroupAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.DismissChatGroupAns();
    }

    public static com.yorha.proto.SsGroupChat.DismissChatGroupAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DismissChatGroupAns>
        PARSER = new com.google.protobuf.AbstractParser<DismissChatGroupAns>() {
      @java.lang.Override
      public DismissChatGroupAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DismissChatGroupAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DismissChatGroupAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DismissChatGroupAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.DismissChatGroupAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ModifyGroupNameAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ModifyGroupNameAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return Whether the chatGroupId field is set.
     */
    boolean hasChatGroupId();
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return The chatGroupId.
     */
    java.lang.String getChatGroupId();
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return The bytes for chatGroupId.
     */
    com.google.protobuf.ByteString
        getChatGroupIdBytes();

    /**
     * <code>optional string newName = 2;</code>
     * @return Whether the newName field is set.
     */
    boolean hasNewName();
    /**
     * <code>optional string newName = 2;</code>
     * @return The newName.
     */
    java.lang.String getNewName();
    /**
     * <code>optional string newName = 2;</code>
     * @return The bytes for newName.
     */
    com.google.protobuf.ByteString
        getNewNameBytes();

    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 3;</code>
     * @return Whether the operator field is set.
     */
    boolean hasOperator();
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 3;</code>
     * @return The operator.
     */
    long getOperator();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ModifyGroupNameAsk}
   */
  public static final class ModifyGroupNameAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ModifyGroupNameAsk)
      ModifyGroupNameAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ModifyGroupNameAsk.newBuilder() to construct.
    private ModifyGroupNameAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ModifyGroupNameAsk() {
      chatGroupId_ = "";
      newName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ModifyGroupNameAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ModifyGroupNameAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              chatGroupId_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              newName_ = bs;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              operator_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.ModifyGroupNameAsk.class, com.yorha.proto.SsGroupChat.ModifyGroupNameAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHATGROUPID_FIELD_NUMBER = 1;
    private volatile java.lang.Object chatGroupId_;
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return Whether the chatGroupId field is set.
     */
    @java.lang.Override
    public boolean hasChatGroupId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return The chatGroupId.
     */
    @java.lang.Override
    public java.lang.String getChatGroupId() {
      java.lang.Object ref = chatGroupId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          chatGroupId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string chatGroupId = 1;</code>
     * @return The bytes for chatGroupId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChatGroupIdBytes() {
      java.lang.Object ref = chatGroupId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        chatGroupId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NEWNAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object newName_;
    /**
     * <code>optional string newName = 2;</code>
     * @return Whether the newName field is set.
     */
    @java.lang.Override
    public boolean hasNewName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string newName = 2;</code>
     * @return The newName.
     */
    @java.lang.Override
    public java.lang.String getNewName() {
      java.lang.Object ref = newName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          newName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string newName = 2;</code>
     * @return The bytes for newName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNewNameBytes() {
      java.lang.Object ref = newName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        newName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OPERATOR_FIELD_NUMBER = 3;
    private long operator_;
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 3;</code>
     * @return Whether the operator field is set.
     */
    @java.lang.Override
    public boolean hasOperator() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 操作人
     * </pre>
     *
     * <code>optional int64 operator = 3;</code>
     * @return The operator.
     */
    @java.lang.Override
    public long getOperator() {
      return operator_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, chatGroupId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, newName_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, operator_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, chatGroupId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, newName_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, operator_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.ModifyGroupNameAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.ModifyGroupNameAsk other = (com.yorha.proto.SsGroupChat.ModifyGroupNameAsk) obj;

      if (hasChatGroupId() != other.hasChatGroupId()) return false;
      if (hasChatGroupId()) {
        if (!getChatGroupId()
            .equals(other.getChatGroupId())) return false;
      }
      if (hasNewName() != other.hasNewName()) return false;
      if (hasNewName()) {
        if (!getNewName()
            .equals(other.getNewName())) return false;
      }
      if (hasOperator() != other.hasOperator()) return false;
      if (hasOperator()) {
        if (getOperator()
            != other.getOperator()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatGroupId()) {
        hash = (37 * hash) + CHATGROUPID_FIELD_NUMBER;
        hash = (53 * hash) + getChatGroupId().hashCode();
      }
      if (hasNewName()) {
        hash = (37 * hash) + NEWNAME_FIELD_NUMBER;
        hash = (53 * hash) + getNewName().hashCode();
      }
      if (hasOperator()) {
        hash = (37 * hash) + OPERATOR_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOperator());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.ModifyGroupNameAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ModifyGroupNameAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ModifyGroupNameAsk)
        com.yorha.proto.SsGroupChat.ModifyGroupNameAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.ModifyGroupNameAsk.class, com.yorha.proto.SsGroupChat.ModifyGroupNameAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.ModifyGroupNameAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chatGroupId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        newName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        operator_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.ModifyGroupNameAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.ModifyGroupNameAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.ModifyGroupNameAsk build() {
        com.yorha.proto.SsGroupChat.ModifyGroupNameAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.ModifyGroupNameAsk buildPartial() {
        com.yorha.proto.SsGroupChat.ModifyGroupNameAsk result = new com.yorha.proto.SsGroupChat.ModifyGroupNameAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.chatGroupId_ = chatGroupId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.newName_ = newName_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.operator_ = operator_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.ModifyGroupNameAsk) {
          return mergeFrom((com.yorha.proto.SsGroupChat.ModifyGroupNameAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.ModifyGroupNameAsk other) {
        if (other == com.yorha.proto.SsGroupChat.ModifyGroupNameAsk.getDefaultInstance()) return this;
        if (other.hasChatGroupId()) {
          bitField0_ |= 0x00000001;
          chatGroupId_ = other.chatGroupId_;
          onChanged();
        }
        if (other.hasNewName()) {
          bitField0_ |= 0x00000002;
          newName_ = other.newName_;
          onChanged();
        }
        if (other.hasOperator()) {
          setOperator(other.getOperator());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.ModifyGroupNameAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.ModifyGroupNameAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object chatGroupId_ = "";
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @return Whether the chatGroupId field is set.
       */
      public boolean hasChatGroupId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @return The chatGroupId.
       */
      public java.lang.String getChatGroupId() {
        java.lang.Object ref = chatGroupId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            chatGroupId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @return The bytes for chatGroupId.
       */
      public com.google.protobuf.ByteString
          getChatGroupIdBytes() {
        java.lang.Object ref = chatGroupId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          chatGroupId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @param value The chatGroupId to set.
       * @return This builder for chaining.
       */
      public Builder setChatGroupId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        chatGroupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChatGroupId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chatGroupId_ = getDefaultInstance().getChatGroupId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string chatGroupId = 1;</code>
       * @param value The bytes for chatGroupId to set.
       * @return This builder for chaining.
       */
      public Builder setChatGroupIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        chatGroupId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object newName_ = "";
      /**
       * <code>optional string newName = 2;</code>
       * @return Whether the newName field is set.
       */
      public boolean hasNewName() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string newName = 2;</code>
       * @return The newName.
       */
      public java.lang.String getNewName() {
        java.lang.Object ref = newName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            newName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string newName = 2;</code>
       * @return The bytes for newName.
       */
      public com.google.protobuf.ByteString
          getNewNameBytes() {
        java.lang.Object ref = newName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          newName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string newName = 2;</code>
       * @param value The newName to set.
       * @return This builder for chaining.
       */
      public Builder setNewName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        newName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string newName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        newName_ = getDefaultInstance().getNewName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string newName = 2;</code>
       * @param value The bytes for newName to set.
       * @return This builder for chaining.
       */
      public Builder setNewNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        newName_ = value;
        onChanged();
        return this;
      }

      private long operator_ ;
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 3;</code>
       * @return Whether the operator field is set.
       */
      @java.lang.Override
      public boolean hasOperator() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 3;</code>
       * @return The operator.
       */
      @java.lang.Override
      public long getOperator() {
        return operator_;
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 3;</code>
       * @param value The operator to set.
       * @return This builder for chaining.
       */
      public Builder setOperator(long value) {
        bitField0_ |= 0x00000004;
        operator_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作人
       * </pre>
       *
       * <code>optional int64 operator = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperator() {
        bitField0_ = (bitField0_ & ~0x00000004);
        operator_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ModifyGroupNameAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ModifyGroupNameAsk)
    private static final com.yorha.proto.SsGroupChat.ModifyGroupNameAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.ModifyGroupNameAsk();
    }

    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ModifyGroupNameAsk>
        PARSER = new com.google.protobuf.AbstractParser<ModifyGroupNameAsk>() {
      @java.lang.Override
      public ModifyGroupNameAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ModifyGroupNameAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ModifyGroupNameAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ModifyGroupNameAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.ModifyGroupNameAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ModifyGroupNameAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ModifyGroupNameAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.ModifyGroupNameAns}
   */
  public static final class ModifyGroupNameAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ModifyGroupNameAns)
      ModifyGroupNameAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ModifyGroupNameAns.newBuilder() to construct.
    private ModifyGroupNameAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ModifyGroupNameAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ModifyGroupNameAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ModifyGroupNameAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGroupChat.ModifyGroupNameAns.class, com.yorha.proto.SsGroupChat.ModifyGroupNameAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGroupChat.ModifyGroupNameAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGroupChat.ModifyGroupNameAns other = (com.yorha.proto.SsGroupChat.ModifyGroupNameAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGroupChat.ModifyGroupNameAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ModifyGroupNameAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ModifyGroupNameAns)
        com.yorha.proto.SsGroupChat.ModifyGroupNameAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGroupChat.ModifyGroupNameAns.class, com.yorha.proto.SsGroupChat.ModifyGroupNameAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsGroupChat.ModifyGroupNameAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGroupChat.internal_static_com_yorha_proto_ModifyGroupNameAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.ModifyGroupNameAns getDefaultInstanceForType() {
        return com.yorha.proto.SsGroupChat.ModifyGroupNameAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.ModifyGroupNameAns build() {
        com.yorha.proto.SsGroupChat.ModifyGroupNameAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGroupChat.ModifyGroupNameAns buildPartial() {
        com.yorha.proto.SsGroupChat.ModifyGroupNameAns result = new com.yorha.proto.SsGroupChat.ModifyGroupNameAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGroupChat.ModifyGroupNameAns) {
          return mergeFrom((com.yorha.proto.SsGroupChat.ModifyGroupNameAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGroupChat.ModifyGroupNameAns other) {
        if (other == com.yorha.proto.SsGroupChat.ModifyGroupNameAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGroupChat.ModifyGroupNameAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGroupChat.ModifyGroupNameAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ModifyGroupNameAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ModifyGroupNameAns)
    private static final com.yorha.proto.SsGroupChat.ModifyGroupNameAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGroupChat.ModifyGroupNameAns();
    }

    public static com.yorha.proto.SsGroupChat.ModifyGroupNameAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ModifyGroupNameAns>
        PARSER = new com.google.protobuf.AbstractParser<ModifyGroupNameAns>() {
      @java.lang.Override
      public ModifyGroupNameAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ModifyGroupNameAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ModifyGroupNameAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ModifyGroupNameAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGroupChat.ModifyGroupNameAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateChatAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateChatAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateChatAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateChatAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendChatMessageAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendChatMessageAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendChatMessageAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendChatMessageAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryChatMessageAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryChatMessageAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryChatMessageAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryChatMessageAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchChatMemberAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchChatMemberAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchChatMemberAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchChatMemberAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JudgeMemberAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JudgeMemberAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JudgeMemberAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JudgeMemberAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_InviteNewMemberAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_InviteNewMemberAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_InviteNewMemberAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_InviteNewMemberAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RemoveGroupMemberAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RemoveGroupMemberAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RemoveGroupMemberAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RemoveGroupMemberAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_TransferGroupOwnerAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_TransferGroupOwnerAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_TransferGroupOwnerAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_TransferGroupOwnerAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DismissChatGroupAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DismissChatGroupAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DismissChatGroupAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DismissChatGroupAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ModifyGroupNameAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ModifyGroupNameAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ModifyGroupNameAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ModifyGroupNameAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)ss_proto/gen/groupChat/ss_groupChat.pr" +
      "oto\022\017com.yorha.proto\032$ss_proto/gen/commo" +
      "n/common_msg.proto\"i\n\rCreateChatAsk\022\021\n\tc" +
      "hatOwner\030\001 \001(\003\0227\n\013chatMembers\030\002 \001(\0132\".co" +
      "m.yorha.proto.ChatChannelMember\022\014\n\004name\030" +
      "\003 \001(\t\"%\n\rCreateChatAns\022\024\n\014failedPlayer\030\001" +
      " \003(\003\"W\n\022SendChatMessageAsk\0221\n\013chatMessag" +
      "e\030\001 \001(\0132\034.com.yorha.proto.ChatMessage\022\016\n" +
      "\006zoneId\030\002 \001(\005\"\'\n\022SendChatMessageAns\022\021\n\tm" +
      "essageId\030\001 \001(\003\"G\n\023QueryChatMessageAsk\022\016\n" +
      "\006fromId\030\001 \001(\003\022\014\n\004toId\030\002 \001(\003\022\022\n\nshieldLis" +
      "t\030\003 \003(\003\"E\n\023QueryChatMessageAns\022.\n\010chatMs" +
      "gs\030\001 \003(\0132\034.com.yorha.proto.ChatMessage\"\024" +
      "\n\022FetchChatMemberAsk\"O\n\022FetchChatMemberA" +
      "ns\0229\n\013description\030\001 \001(\0132$.com.yorha.prot" +
      "o.ChatDescriptionInfo\"\"\n\016JudgeMemberAsk\022" +
      "\020\n\010playerId\030\001 \001(\003\"P\n\016JudgeMemberAns\022\014\n\004i" +
      "sIn\030\001 \001(\010\022\r\n\005owner\030\002 \001(\003\022\017\n\007version\030\003 \001(" +
      "\005\022\020\n\010chatName\030\004 \001(\t\"Z\n\022InviteNewMemberAs" +
      "k\022\017\n\007inviter\030\001 \001(\003\0223\n\007members\030\002 \001(\0132\".co" +
      "m.yorha.proto.ChatChannelMember\"s\n\022Invit" +
      "eNewMemberAns\0223\n\007members\030\001 \001(\0132\".com.yor" +
      "ha.proto.ChatChannelMember\022\022\n\nnewVersion" +
      "\030\002 \001(\005\022\024\n\014failedPlayer\030\003 \003(\003\"\\\n\024RemoveGr" +
      "oupMemberAsk\022\021\n\tmemberIds\030\001 \003(\003\022\020\n\010opera" +
      "tor\030\002 \001(\003\022\021\n\tquitGroup\030\003 \001(\010\022\014\n\004name\030\004 \001" +
      "(\t\"U\n\024RemoveGroupMemberAns\022\023\n\013deleteGrou" +
      "p\030\001 \001(\010\022\022\n\ngroupOwner\030\002 \001(\003\022\024\n\014groupVers" +
      "ion\030\003 \001(\005\";\n\025TransferGroupOwnerAsk\022\020\n\010ne" +
      "wOwner\030\001 \001(\003\022\020\n\010operator\030\002 \001(\003\"\027\n\025Transf" +
      "erGroupOwnerAns\"<\n\023DismissChatGroupAsk\022\023" +
      "\n\013chatGroupId\030\001 \001(\t\022\020\n\010operator\030\002 \001(\003\"\025\n" +
      "\023DismissChatGroupAns\"L\n\022ModifyGroupNameA" +
      "sk\022\023\n\013chatGroupId\030\001 \001(\t\022\017\n\007newName\030\002 \001(\t" +
      "\022\020\n\010operator\030\003 \001(\003\"\024\n\022ModifyGroupNameAns" +
      "B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_CreateChatAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_CreateChatAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateChatAsk_descriptor,
        new java.lang.String[] { "ChatOwner", "ChatMembers", "Name", });
    internal_static_com_yorha_proto_CreateChatAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_CreateChatAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateChatAns_descriptor,
        new java.lang.String[] { "FailedPlayer", });
    internal_static_com_yorha_proto_SendChatMessageAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SendChatMessageAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendChatMessageAsk_descriptor,
        new java.lang.String[] { "ChatMessage", "ZoneId", });
    internal_static_com_yorha_proto_SendChatMessageAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_SendChatMessageAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendChatMessageAns_descriptor,
        new java.lang.String[] { "MessageId", });
    internal_static_com_yorha_proto_QueryChatMessageAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_QueryChatMessageAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryChatMessageAsk_descriptor,
        new java.lang.String[] { "FromId", "ToId", "ShieldList", });
    internal_static_com_yorha_proto_QueryChatMessageAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_QueryChatMessageAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryChatMessageAns_descriptor,
        new java.lang.String[] { "ChatMsgs", });
    internal_static_com_yorha_proto_FetchChatMemberAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_FetchChatMemberAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchChatMemberAsk_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_FetchChatMemberAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_FetchChatMemberAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchChatMemberAns_descriptor,
        new java.lang.String[] { "Description", });
    internal_static_com_yorha_proto_JudgeMemberAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_JudgeMemberAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JudgeMemberAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_JudgeMemberAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_JudgeMemberAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JudgeMemberAns_descriptor,
        new java.lang.String[] { "IsIn", "Owner", "Version", "ChatName", });
    internal_static_com_yorha_proto_InviteNewMemberAsk_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_InviteNewMemberAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_InviteNewMemberAsk_descriptor,
        new java.lang.String[] { "Inviter", "Members", });
    internal_static_com_yorha_proto_InviteNewMemberAns_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_InviteNewMemberAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_InviteNewMemberAns_descriptor,
        new java.lang.String[] { "Members", "NewVersion", "FailedPlayer", });
    internal_static_com_yorha_proto_RemoveGroupMemberAsk_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_RemoveGroupMemberAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RemoveGroupMemberAsk_descriptor,
        new java.lang.String[] { "MemberIds", "Operator", "QuitGroup", "Name", });
    internal_static_com_yorha_proto_RemoveGroupMemberAns_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_RemoveGroupMemberAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RemoveGroupMemberAns_descriptor,
        new java.lang.String[] { "DeleteGroup", "GroupOwner", "GroupVersion", });
    internal_static_com_yorha_proto_TransferGroupOwnerAsk_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_TransferGroupOwnerAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_TransferGroupOwnerAsk_descriptor,
        new java.lang.String[] { "NewOwner", "Operator", });
    internal_static_com_yorha_proto_TransferGroupOwnerAns_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_yorha_proto_TransferGroupOwnerAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_TransferGroupOwnerAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_DismissChatGroupAsk_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_com_yorha_proto_DismissChatGroupAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DismissChatGroupAsk_descriptor,
        new java.lang.String[] { "ChatGroupId", "Operator", });
    internal_static_com_yorha_proto_DismissChatGroupAns_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_com_yorha_proto_DismissChatGroupAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DismissChatGroupAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_ModifyGroupNameAsk_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_com_yorha_proto_ModifyGroupNameAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ModifyGroupNameAsk_descriptor,
        new java.lang.String[] { "ChatGroupId", "NewName", "Operator", });
    internal_static_com_yorha_proto_ModifyGroupNameAns_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_com_yorha_proto_ModifyGroupNameAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ModifyGroupNameAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

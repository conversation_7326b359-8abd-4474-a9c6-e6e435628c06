// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/playerCard/ss_playerCard.proto

package com.yorha.proto;

public final class SsPlayerCard {
  private SsPlayerCard() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QueryPlayerCardAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryPlayerCardAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryPlayerCardAsk}
   */
  public static final class QueryPlayerCardAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryPlayerCardAsk)
      QueryPlayerCardAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryPlayerCardAsk.newBuilder() to construct.
    private QueryPlayerCardAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryPlayerCardAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryPlayerCardAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryPlayerCardAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk.class, com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk other = (com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryPlayerCardAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryPlayerCardAsk)
        com.yorha.proto.SsPlayerCard.QueryPlayerCardAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk.class, com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk build() {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk buildPartial() {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk result = new com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk other) {
        if (other == com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryPlayerCardAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryPlayerCardAsk)
    private static final com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk();
    }

    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryPlayerCardAsk>
        PARSER = new com.google.protobuf.AbstractParser<QueryPlayerCardAsk>() {
      @java.lang.Override
      public QueryPlayerCardAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryPlayerCardAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryPlayerCardAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryPlayerCardAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.QueryPlayerCardAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryPlayerCardAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryPlayerCardAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     * @return Whether the cardInfo field is set.
     */
    boolean hasCardInfo();
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     * @return The cardInfo.
     */
    com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfo();
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     */
    com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryPlayerCardAns}
   */
  public static final class QueryPlayerCardAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryPlayerCardAns)
      QueryPlayerCardAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryPlayerCardAns.newBuilder() to construct.
    private QueryPlayerCardAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryPlayerCardAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryPlayerCardAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryPlayerCardAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.PlayerCardInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = cardInfo_.toBuilder();
              }
              cardInfo_ = input.readMessage(com.yorha.proto.StructPB.PlayerCardInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardInfo_);
                cardInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.QueryPlayerCardAns.class, com.yorha.proto.SsPlayerCard.QueryPlayerCardAns.Builder.class);
    }

    private int bitField0_;
    public static final int CARDINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.PlayerCardInfoPB cardInfo_;
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     * @return Whether the cardInfo field is set.
     */
    @java.lang.Override
    public boolean hasCardInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     * @return The cardInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfo() {
      return cardInfo_ == null ? com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance() : cardInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardInfoOrBuilder() {
      return cardInfo_ == null ? com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance() : cardInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCardInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCardInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.QueryPlayerCardAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.QueryPlayerCardAns other = (com.yorha.proto.SsPlayerCard.QueryPlayerCardAns) obj;

      if (hasCardInfo() != other.hasCardInfo()) return false;
      if (hasCardInfo()) {
        if (!getCardInfo()
            .equals(other.getCardInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCardInfo()) {
        hash = (37 * hash) + CARDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getCardInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.QueryPlayerCardAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryPlayerCardAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryPlayerCardAns)
        com.yorha.proto.SsPlayerCard.QueryPlayerCardAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.QueryPlayerCardAns.class, com.yorha.proto.SsPlayerCard.QueryPlayerCardAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.QueryPlayerCardAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (cardInfoBuilder_ == null) {
          cardInfo_ = null;
        } else {
          cardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.QueryPlayerCardAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardAns build() {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardAns buildPartial() {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardAns result = new com.yorha.proto.SsPlayerCard.QueryPlayerCardAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (cardInfoBuilder_ == null) {
            result.cardInfo_ = cardInfo_;
          } else {
            result.cardInfo_ = cardInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.QueryPlayerCardAns) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.QueryPlayerCardAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.QueryPlayerCardAns other) {
        if (other == com.yorha.proto.SsPlayerCard.QueryPlayerCardAns.getDefaultInstance()) return this;
        if (other.hasCardInfo()) {
          mergeCardInfo(other.getCardInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.QueryPlayerCardAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.PlayerCardInfoPB cardInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> cardInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       * @return Whether the cardInfo field is set.
       */
      public boolean hasCardInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       * @return The cardInfo.
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfo() {
        if (cardInfoBuilder_ == null) {
          return cardInfo_ == null ? com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance() : cardInfo_;
        } else {
          return cardInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public Builder setCardInfo(com.yorha.proto.StructPB.PlayerCardInfoPB value) {
        if (cardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardInfo_ = value;
          onChanged();
        } else {
          cardInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public Builder setCardInfo(
          com.yorha.proto.StructPB.PlayerCardInfoPB.Builder builderForValue) {
        if (cardInfoBuilder_ == null) {
          cardInfo_ = builderForValue.build();
          onChanged();
        } else {
          cardInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public Builder mergeCardInfo(com.yorha.proto.StructPB.PlayerCardInfoPB value) {
        if (cardInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              cardInfo_ != null &&
              cardInfo_ != com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance()) {
            cardInfo_ =
              com.yorha.proto.StructPB.PlayerCardInfoPB.newBuilder(cardInfo_).mergeFrom(value).buildPartial();
          } else {
            cardInfo_ = value;
          }
          onChanged();
        } else {
          cardInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public Builder clearCardInfo() {
        if (cardInfoBuilder_ == null) {
          cardInfo_ = null;
          onChanged();
        } else {
          cardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPB.Builder getCardInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCardInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardInfoOrBuilder() {
        if (cardInfoBuilder_ != null) {
          return cardInfoBuilder_.getMessageOrBuilder();
        } else {
          return cardInfo_ == null ?
              com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance() : cardInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> 
          getCardInfoFieldBuilder() {
        if (cardInfoBuilder_ == null) {
          cardInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder>(
                  getCardInfo(),
                  getParentForChildren(),
                  isClean());
          cardInfo_ = null;
        }
        return cardInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryPlayerCardAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryPlayerCardAns)
    private static final com.yorha.proto.SsPlayerCard.QueryPlayerCardAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.QueryPlayerCardAns();
    }

    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryPlayerCardAns>
        PARSER = new com.google.protobuf.AbstractParser<QueryPlayerCardAns>() {
      @java.lang.Override
      public QueryPlayerCardAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryPlayerCardAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryPlayerCardAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryPlayerCardAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.QueryPlayerCardAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryPlayerCardAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryPlayerCardAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 players = 1;</code>
     * @return A list containing the players.
     */
    java.util.List<java.lang.Long> getPlayersList();
    /**
     * <code>repeated int64 players = 1;</code>
     * @return The count of players.
     */
    int getPlayersCount();
    /**
     * <code>repeated int64 players = 1;</code>
     * @param index The index of the element to return.
     * @return The players at the given index.
     */
    long getPlayers(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryPlayerCardAsk}
   */
  public static final class BatchQueryPlayerCardAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryPlayerCardAsk)
      BatchQueryPlayerCardAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryPlayerCardAsk.newBuilder() to construct.
    private BatchQueryPlayerCardAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryPlayerCardAsk() {
      players_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryPlayerCardAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryPlayerCardAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                players_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              players_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                players_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                players_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          players_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk.Builder.class);
    }

    public static final int PLAYERS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList players_;
    /**
     * <code>repeated int64 players = 1;</code>
     * @return A list containing the players.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getPlayersList() {
      return players_;
    }
    /**
     * <code>repeated int64 players = 1;</code>
     * @return The count of players.
     */
    public int getPlayersCount() {
      return players_.size();
    }
    /**
     * <code>repeated int64 players = 1;</code>
     * @param index The index of the element to return.
     * @return The players at the given index.
     */
    public long getPlayers(int index) {
      return players_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < players_.size(); i++) {
        output.writeInt64(1, players_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < players_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(players_.getLong(i));
        }
        size += dataSize;
        size += 1 * getPlayersList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk other = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk) obj;

      if (!getPlayersList()
          .equals(other.getPlayersList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPlayersCount() > 0) {
        hash = (37 * hash) + PLAYERS_FIELD_NUMBER;
        hash = (53 * hash) + getPlayersList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryPlayerCardAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryPlayerCardAsk)
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        players_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk build() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk buildPartial() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk result = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          players_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.players_ = players_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk other) {
        if (other == com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk.getDefaultInstance()) return this;
        if (!other.players_.isEmpty()) {
          if (players_.isEmpty()) {
            players_ = other.players_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePlayersIsMutable();
            players_.addAll(other.players_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList players_ = emptyLongList();
      private void ensurePlayersIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          players_ = mutableCopy(players_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 players = 1;</code>
       * @return A list containing the players.
       */
      public java.util.List<java.lang.Long>
          getPlayersList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(players_) : players_;
      }
      /**
       * <code>repeated int64 players = 1;</code>
       * @return The count of players.
       */
      public int getPlayersCount() {
        return players_.size();
      }
      /**
       * <code>repeated int64 players = 1;</code>
       * @param index The index of the element to return.
       * @return The players at the given index.
       */
      public long getPlayers(int index) {
        return players_.getLong(index);
      }
      /**
       * <code>repeated int64 players = 1;</code>
       * @param index The index to set the value at.
       * @param value The players to set.
       * @return This builder for chaining.
       */
      public Builder setPlayers(
          int index, long value) {
        ensurePlayersIsMutable();
        players_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 players = 1;</code>
       * @param value The players to add.
       * @return This builder for chaining.
       */
      public Builder addPlayers(long value) {
        ensurePlayersIsMutable();
        players_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 players = 1;</code>
       * @param values The players to add.
       * @return This builder for chaining.
       */
      public Builder addAllPlayers(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensurePlayersIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, players_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 players = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayers() {
        players_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryPlayerCardAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryPlayerCardAsk)
    private static final com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk();
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryPlayerCardAsk>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryPlayerCardAsk>() {
      @java.lang.Override
      public BatchQueryPlayerCardAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryPlayerCardAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryPlayerCardAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryPlayerCardAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryPlayerCardAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryPlayerCardAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */
    int getCardInfosCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */
    boolean containsCardInfos(
        long key);
    /**
     * Use {@link #getCardInfosMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>
    getCardInfos();
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>
    getCardInfosMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */

    com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfosOrDefault(
        long key,
        com.yorha.proto.StructPB.PlayerCardInfoPB defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */

    com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfosOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryPlayerCardAns}
   */
  public static final class BatchQueryPlayerCardAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryPlayerCardAns)
      BatchQueryPlayerCardAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryPlayerCardAns.newBuilder() to construct.
    private BatchQueryPlayerCardAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryPlayerCardAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryPlayerCardAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryPlayerCardAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                cardInfos_ = com.google.protobuf.MapField.newMapField(
                    CardInfosDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>
              cardInfos__ = input.readMessage(
                  CardInfosDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              cardInfos_.getMutableMap().put(
                  cardInfos__.getKey(), cardInfos__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetCardInfos();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns.Builder.class);
    }

    public static final int CARDINFOS_FIELD_NUMBER = 1;
    private static final class CardInfosDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>newDefaultInstance(
                  com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAns_CardInfosEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> cardInfos_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>
    internalGetCardInfos() {
      if (cardInfos_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            CardInfosDefaultEntryHolder.defaultEntry);
      }
      return cardInfos_;
    }

    public int getCardInfosCount() {
      return internalGetCardInfos().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */

    @java.lang.Override
    public boolean containsCardInfos(
        long key) {
      
      return internalGetCardInfos().getMap().containsKey(key);
    }
    /**
     * Use {@link #getCardInfosMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> getCardInfos() {
      return getCardInfosMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> getCardInfosMap() {
      return internalGetCardInfos().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfosOrDefault(
        long key,
        com.yorha.proto.StructPB.PlayerCardInfoPB defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> map =
          internalGetCardInfos().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfosOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> map =
          internalGetCardInfos().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetCardInfos(),
          CardInfosDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> entry
           : internalGetCardInfos().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>
        cardInfos__ = CardInfosDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, cardInfos__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns other = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns) obj;

      if (!internalGetCardInfos().equals(
          other.internalGetCardInfos())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetCardInfos().getMap().isEmpty()) {
        hash = (37 * hash) + CARDINFOS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetCardInfos().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryPlayerCardAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryPlayerCardAns)
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetCardInfos();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableCardInfos();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableCardInfos().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns build() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns buildPartial() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns result = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns(this);
        int from_bitField0_ = bitField0_;
        result.cardInfos_ = internalGetCardInfos();
        result.cardInfos_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns other) {
        if (other == com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns.getDefaultInstance()) return this;
        internalGetMutableCardInfos().mergeFrom(
            other.internalGetCardInfos());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> cardInfos_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>
      internalGetCardInfos() {
        if (cardInfos_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              CardInfosDefaultEntryHolder.defaultEntry);
        }
        return cardInfos_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>
      internalGetMutableCardInfos() {
        onChanged();;
        if (cardInfos_ == null) {
          cardInfos_ = com.google.protobuf.MapField.newMapField(
              CardInfosDefaultEntryHolder.defaultEntry);
        }
        if (!cardInfos_.isMutable()) {
          cardInfos_ = cardInfos_.copy();
        }
        return cardInfos_;
      }

      public int getCardInfosCount() {
        return internalGetCardInfos().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
       */

      @java.lang.Override
      public boolean containsCardInfos(
          long key) {
        
        return internalGetCardInfos().getMap().containsKey(key);
      }
      /**
       * Use {@link #getCardInfosMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> getCardInfos() {
        return getCardInfosMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> getCardInfosMap() {
        return internalGetCardInfos().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfosOrDefault(
          long key,
          com.yorha.proto.StructPB.PlayerCardInfoPB defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> map =
            internalGetCardInfos().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfosOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> map =
            internalGetCardInfos().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearCardInfos() {
        internalGetMutableCardInfos().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
       */

      public Builder removeCardInfos(
          long key) {
        
        internalGetMutableCardInfos().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB>
      getMutableCardInfos() {
        return internalGetMutableCardInfos().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
       */
      public Builder putCardInfos(
          long key,
          com.yorha.proto.StructPB.PlayerCardInfoPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableCardInfos().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardInfoPB&gt; cardInfos = 1;</code>
       */

      public Builder putAllCardInfos(
          java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardInfoPB> values) {
        internalGetMutableCardInfos().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryPlayerCardAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryPlayerCardAns)
    private static final com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns();
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryPlayerCardAns>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryPlayerCardAns>() {
      @java.lang.Override
      public BatchQueryPlayerCardAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryPlayerCardAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryPlayerCardAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryPlayerCardAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryPlayerCardHeadAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryPlayerCardHeadAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryPlayerCardHeadAsk}
   */
  public static final class QueryPlayerCardHeadAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryPlayerCardHeadAsk)
      QueryPlayerCardHeadAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryPlayerCardHeadAsk.newBuilder() to construct.
    private QueryPlayerCardHeadAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryPlayerCardHeadAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryPlayerCardHeadAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryPlayerCardHeadAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk.class, com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk other = (com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryPlayerCardHeadAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryPlayerCardHeadAsk)
        com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk.class, com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk build() {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk buildPartial() {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk result = new com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk other) {
        if (other == com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryPlayerCardHeadAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryPlayerCardHeadAsk)
    private static final com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk();
    }

    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryPlayerCardHeadAsk>
        PARSER = new com.google.protobuf.AbstractParser<QueryPlayerCardHeadAsk>() {
      @java.lang.Override
      public QueryPlayerCardHeadAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryPlayerCardHeadAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryPlayerCardHeadAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryPlayerCardHeadAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryPlayerCardHeadAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryPlayerCardHeadAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
     * @return Whether the cardHead field is set.
     */
    boolean hasCardHead();
    /**
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
     * @return The cardHead.
     */
    com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead();
    /**
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
     */
    com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryPlayerCardHeadAns}
   */
  public static final class QueryPlayerCardHeadAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryPlayerCardHeadAns)
      QueryPlayerCardHeadAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryPlayerCardHeadAns.newBuilder() to construct.
    private QueryPlayerCardHeadAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryPlayerCardHeadAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryPlayerCardHeadAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryPlayerCardHeadAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.PlayerCardHeadPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = cardHead_.toBuilder();
              }
              cardHead_ = input.readMessage(com.yorha.proto.StructPB.PlayerCardHeadPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardHead_);
                cardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns.class, com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns.Builder.class);
    }

    private int bitField0_;
    public static final int CARDHEAD_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.PlayerCardHeadPB cardHead_;
    /**
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
     * @return Whether the cardHead field is set.
     */
    @java.lang.Override
    public boolean hasCardHead() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
     * @return The cardHead.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead() {
      return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder() {
      return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCardHead());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCardHead());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns other = (com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns) obj;

      if (hasCardHead() != other.hasCardHead()) return false;
      if (hasCardHead()) {
        if (!getCardHead()
            .equals(other.getCardHead())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCardHead()) {
        hash = (37 * hash) + CARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getCardHead().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryPlayerCardHeadAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryPlayerCardHeadAns)
        com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns.class, com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_QueryPlayerCardHeadAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns build() {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns buildPartial() {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns result = new com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (cardHeadBuilder_ == null) {
            result.cardHead_ = cardHead_;
          } else {
            result.cardHead_ = cardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns other) {
        if (other == com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns.getDefaultInstance()) return this;
        if (other.hasCardHead()) {
          mergeCardHead(other.getCardHead());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.PlayerCardHeadPB cardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> cardHeadBuilder_;
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       * @return Whether the cardHead field is set.
       */
      public boolean hasCardHead() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       * @return The cardHead.
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead() {
        if (cardHeadBuilder_ == null) {
          return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
        } else {
          return cardHeadBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       */
      public Builder setCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (cardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardHead_ = value;
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       */
      public Builder setCardHead(
          com.yorha.proto.StructPB.PlayerCardHeadPB.Builder builderForValue) {
        if (cardHeadBuilder_ == null) {
          cardHead_ = builderForValue.build();
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       */
      public Builder mergeCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (cardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              cardHead_ != null &&
              cardHead_ != com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance()) {
            cardHead_ =
              com.yorha.proto.StructPB.PlayerCardHeadPB.newBuilder(cardHead_).mergeFrom(value).buildPartial();
          } else {
            cardHead_ = value;
          }
          onChanged();
        } else {
          cardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       */
      public Builder clearCardHead() {
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
          onChanged();
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB.Builder getCardHeadBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder() {
        if (cardHeadBuilder_ != null) {
          return cardHeadBuilder_.getMessageOrBuilder();
        } else {
          return cardHead_ == null ?
              com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> 
          getCardHeadFieldBuilder() {
        if (cardHeadBuilder_ == null) {
          cardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder>(
                  getCardHead(),
                  getParentForChildren(),
                  isClean());
          cardHead_ = null;
        }
        return cardHeadBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryPlayerCardHeadAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryPlayerCardHeadAns)
    private static final com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns();
    }

    public static com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryPlayerCardHeadAns>
        PARSER = new com.google.protobuf.AbstractParser<QueryPlayerCardHeadAns>() {
      @java.lang.Override
      public QueryPlayerCardHeadAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryPlayerCardHeadAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryPlayerCardHeadAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryPlayerCardHeadAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.QueryPlayerCardHeadAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryPlayerCardHeadAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryPlayerCardHeadAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @return A list containing the playerIds.
     */
    java.util.List<java.lang.Long> getPlayerIdsList();
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @return The count of playerIds.
     */
    int getPlayerIdsCount();
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @param index The index of the element to return.
     * @return The playerIds at the given index.
     */
    long getPlayerIds(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryPlayerCardHeadAsk}
   */
  public static final class BatchQueryPlayerCardHeadAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryPlayerCardHeadAsk)
      BatchQueryPlayerCardHeadAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryPlayerCardHeadAsk.newBuilder() to construct.
    private BatchQueryPlayerCardHeadAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryPlayerCardHeadAsk() {
      playerIds_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryPlayerCardHeadAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryPlayerCardHeadAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                playerIds_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              playerIds_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                playerIds_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                playerIds_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          playerIds_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk.Builder.class);
    }

    public static final int PLAYERIDS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList playerIds_;
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @return A list containing the playerIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getPlayerIdsList() {
      return playerIds_;
    }
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @return The count of playerIds.
     */
    public int getPlayerIdsCount() {
      return playerIds_.size();
    }
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @param index The index of the element to return.
     * @return The playerIds at the given index.
     */
    public long getPlayerIds(int index) {
      return playerIds_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < playerIds_.size(); i++) {
        output.writeInt64(1, playerIds_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < playerIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(playerIds_.getLong(i));
        }
        size += dataSize;
        size += 1 * getPlayerIdsList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk other = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk) obj;

      if (!getPlayerIdsList()
          .equals(other.getPlayerIdsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPlayerIdsCount() > 0) {
        hash = (37 * hash) + PLAYERIDS_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryPlayerCardHeadAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryPlayerCardHeadAsk)
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk build() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk buildPartial() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk result = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          playerIds_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.playerIds_ = playerIds_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk other) {
        if (other == com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk.getDefaultInstance()) return this;
        if (!other.playerIds_.isEmpty()) {
          if (playerIds_.isEmpty()) {
            playerIds_ = other.playerIds_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePlayerIdsIsMutable();
            playerIds_.addAll(other.playerIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList playerIds_ = emptyLongList();
      private void ensurePlayerIdsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          playerIds_ = mutableCopy(playerIds_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @return A list containing the playerIds.
       */
      public java.util.List<java.lang.Long>
          getPlayerIdsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(playerIds_) : playerIds_;
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @return The count of playerIds.
       */
      public int getPlayerIdsCount() {
        return playerIds_.size();
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @param index The index of the element to return.
       * @return The playerIds at the given index.
       */
      public long getPlayerIds(int index) {
        return playerIds_.getLong(index);
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @param index The index to set the value at.
       * @param value The playerIds to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerIds(
          int index, long value) {
        ensurePlayerIdsIsMutable();
        playerIds_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @param value The playerIds to add.
       * @return This builder for chaining.
       */
      public Builder addPlayerIds(long value) {
        ensurePlayerIdsIsMutable();
        playerIds_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @param values The playerIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllPlayerIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensurePlayerIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerIds_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerIds() {
        playerIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryPlayerCardHeadAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryPlayerCardHeadAsk)
    private static final com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk();
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryPlayerCardHeadAsk>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryPlayerCardHeadAsk>() {
      @java.lang.Override
      public BatchQueryPlayerCardHeadAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryPlayerCardHeadAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryPlayerCardHeadAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryPlayerCardHeadAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryPlayerCardHeadAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryPlayerCardHeadAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */
    int getCardHeadListCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */
    boolean containsCardHeadList(
        long key);
    /**
     * Use {@link #getCardHeadListMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>
    getCardHeadList();
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>
    getCardHeadListMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */

    com.yorha.proto.StructPB.PlayerCardHeadPB getCardHeadListOrDefault(
        long key,
        com.yorha.proto.StructPB.PlayerCardHeadPB defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */

    com.yorha.proto.StructPB.PlayerCardHeadPB getCardHeadListOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryPlayerCardHeadAns}
   */
  public static final class BatchQueryPlayerCardHeadAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryPlayerCardHeadAns)
      BatchQueryPlayerCardHeadAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryPlayerCardHeadAns.newBuilder() to construct.
    private BatchQueryPlayerCardHeadAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryPlayerCardHeadAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryPlayerCardHeadAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryPlayerCardHeadAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                cardHeadList_ = com.google.protobuf.MapField.newMapField(
                    CardHeadListDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>
              cardHeadList__ = input.readMessage(
                  CardHeadListDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              cardHeadList_.getMutableMap().put(
                  cardHeadList__.getKey(), cardHeadList__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetCardHeadList();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns.Builder.class);
    }

    public static final int CARDHEADLIST_FIELD_NUMBER = 1;
    private static final class CardHeadListDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>newDefaultInstance(
                  com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_CardHeadListEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> cardHeadList_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>
    internalGetCardHeadList() {
      if (cardHeadList_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            CardHeadListDefaultEntryHolder.defaultEntry);
      }
      return cardHeadList_;
    }

    public int getCardHeadListCount() {
      return internalGetCardHeadList().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */

    @java.lang.Override
    public boolean containsCardHeadList(
        long key) {
      
      return internalGetCardHeadList().getMap().containsKey(key);
    }
    /**
     * Use {@link #getCardHeadListMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> getCardHeadList() {
      return getCardHeadListMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> getCardHeadListMap() {
      return internalGetCardHeadList().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHeadListOrDefault(
        long key,
        com.yorha.proto.StructPB.PlayerCardHeadPB defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> map =
          internalGetCardHeadList().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHeadListOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> map =
          internalGetCardHeadList().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetCardHeadList(),
          CardHeadListDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> entry
           : internalGetCardHeadList().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>
        cardHeadList__ = CardHeadListDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, cardHeadList__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns other = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns) obj;

      if (!internalGetCardHeadList().equals(
          other.internalGetCardHeadList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetCardHeadList().getMap().isEmpty()) {
        hash = (37 * hash) + CARDHEADLIST_FIELD_NUMBER;
        hash = (53 * hash) + internalGetCardHeadList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryPlayerCardHeadAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryPlayerCardHeadAns)
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetCardHeadList();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableCardHeadList();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableCardHeadList().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns build() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns buildPartial() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns result = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns(this);
        int from_bitField0_ = bitField0_;
        result.cardHeadList_ = internalGetCardHeadList();
        result.cardHeadList_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns other) {
        if (other == com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns.getDefaultInstance()) return this;
        internalGetMutableCardHeadList().mergeFrom(
            other.internalGetCardHeadList());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> cardHeadList_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>
      internalGetCardHeadList() {
        if (cardHeadList_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              CardHeadListDefaultEntryHolder.defaultEntry);
        }
        return cardHeadList_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>
      internalGetMutableCardHeadList() {
        onChanged();;
        if (cardHeadList_ == null) {
          cardHeadList_ = com.google.protobuf.MapField.newMapField(
              CardHeadListDefaultEntryHolder.defaultEntry);
        }
        if (!cardHeadList_.isMutable()) {
          cardHeadList_ = cardHeadList_.copy();
        }
        return cardHeadList_;
      }

      public int getCardHeadListCount() {
        return internalGetCardHeadList().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
       */

      @java.lang.Override
      public boolean containsCardHeadList(
          long key) {
        
        return internalGetCardHeadList().getMap().containsKey(key);
      }
      /**
       * Use {@link #getCardHeadListMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> getCardHeadList() {
        return getCardHeadListMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> getCardHeadListMap() {
        return internalGetCardHeadList().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHeadListOrDefault(
          long key,
          com.yorha.proto.StructPB.PlayerCardHeadPB defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> map =
            internalGetCardHeadList().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHeadListOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> map =
            internalGetCardHeadList().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearCardHeadList() {
        internalGetMutableCardHeadList().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
       */

      public Builder removeCardHeadList(
          long key) {
        
        internalGetMutableCardHeadList().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB>
      getMutableCardHeadList() {
        return internalGetMutableCardHeadList().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
       */
      public Builder putCardHeadList(
          long key,
          com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableCardHeadList().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.PlayerCardHeadPB&gt; cardHeadList = 1;</code>
       */

      public Builder putAllCardHeadList(
          java.util.Map<java.lang.Long, com.yorha.proto.StructPB.PlayerCardHeadPB> values) {
        internalGetMutableCardHeadList().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryPlayerCardHeadAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryPlayerCardHeadAns)
    private static final com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns();
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryPlayerCardHeadAns>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryPlayerCardHeadAns>() {
      @java.lang.Override
      public BatchQueryPlayerCardHeadAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryPlayerCardHeadAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryPlayerCardHeadAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryPlayerCardHeadAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.BatchQueryPlayerCardHeadAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UpdatePlayerCardCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.UpdatePlayerCardCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     * @return Whether the cardInfo field is set.
     */
    boolean hasCardInfo();
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     * @return The cardInfo.
     */
    com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfo();
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     */
    com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.UpdatePlayerCardCmd}
   */
  public static final class UpdatePlayerCardCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.UpdatePlayerCardCmd)
      UpdatePlayerCardCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpdatePlayerCardCmd.newBuilder() to construct.
    private UpdatePlayerCardCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpdatePlayerCardCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpdatePlayerCardCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UpdatePlayerCardCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.PlayerCardInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = cardInfo_.toBuilder();
              }
              cardInfo_ = input.readMessage(com.yorha.proto.StructPB.PlayerCardInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardInfo_);
                cardInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_UpdatePlayerCardCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_UpdatePlayerCardCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd.class, com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CARDINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.PlayerCardInfoPB cardInfo_;
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     * @return Whether the cardInfo field is set.
     */
    @java.lang.Override
    public boolean hasCardInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     * @return The cardInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfo() {
      return cardInfo_ == null ? com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance() : cardInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardInfoOrBuilder() {
      return cardInfo_ == null ? com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance() : cardInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCardInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCardInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd other = (com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd) obj;

      if (hasCardInfo() != other.hasCardInfo()) return false;
      if (hasCardInfo()) {
        if (!getCardInfo()
            .equals(other.getCardInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCardInfo()) {
        hash = (37 * hash) + CARDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getCardInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.UpdatePlayerCardCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.UpdatePlayerCardCmd)
        com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_UpdatePlayerCardCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_UpdatePlayerCardCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd.class, com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (cardInfoBuilder_ == null) {
          cardInfo_ = null;
        } else {
          cardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_UpdatePlayerCardCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd build() {
        com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd buildPartial() {
        com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd result = new com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (cardInfoBuilder_ == null) {
            result.cardInfo_ = cardInfo_;
          } else {
            result.cardInfo_ = cardInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd other) {
        if (other == com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd.getDefaultInstance()) return this;
        if (other.hasCardInfo()) {
          mergeCardInfo(other.getCardInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.PlayerCardInfoPB cardInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> cardInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       * @return Whether the cardInfo field is set.
       */
      public boolean hasCardInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       * @return The cardInfo.
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPB getCardInfo() {
        if (cardInfoBuilder_ == null) {
          return cardInfo_ == null ? com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance() : cardInfo_;
        } else {
          return cardInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public Builder setCardInfo(com.yorha.proto.StructPB.PlayerCardInfoPB value) {
        if (cardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardInfo_ = value;
          onChanged();
        } else {
          cardInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public Builder setCardInfo(
          com.yorha.proto.StructPB.PlayerCardInfoPB.Builder builderForValue) {
        if (cardInfoBuilder_ == null) {
          cardInfo_ = builderForValue.build();
          onChanged();
        } else {
          cardInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public Builder mergeCardInfo(com.yorha.proto.StructPB.PlayerCardInfoPB value) {
        if (cardInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              cardInfo_ != null &&
              cardInfo_ != com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance()) {
            cardInfo_ =
              com.yorha.proto.StructPB.PlayerCardInfoPB.newBuilder(cardInfo_).mergeFrom(value).buildPartial();
          } else {
            cardInfo_ = value;
          }
          onChanged();
        } else {
          cardInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public Builder clearCardInfo() {
        if (cardInfoBuilder_ == null) {
          cardInfo_ = null;
          onChanged();
        } else {
          cardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPB.Builder getCardInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCardInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardInfoOrBuilder() {
        if (cardInfoBuilder_ != null) {
          return cardInfoBuilder_.getMessageOrBuilder();
        } else {
          return cardInfo_ == null ?
              com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance() : cardInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardInfoPB cardInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> 
          getCardInfoFieldBuilder() {
        if (cardInfoBuilder_ == null) {
          cardInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder>(
                  getCardInfo(),
                  getParentForChildren(),
                  isClean());
          cardInfo_ = null;
        }
        return cardInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.UpdatePlayerCardCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.UpdatePlayerCardCmd)
    private static final com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd();
    }

    public static com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<UpdatePlayerCardCmd>
        PARSER = new com.google.protobuf.AbstractParser<UpdatePlayerCardCmd>() {
      @java.lang.Override
      public UpdatePlayerCardCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UpdatePlayerCardCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UpdatePlayerCardCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpdatePlayerCardCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.UpdatePlayerCardCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryPlayerZoneIdAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryPlayerZoneIdAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @return A list containing the playerIds.
     */
    java.util.List<java.lang.Long> getPlayerIdsList();
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @return The count of playerIds.
     */
    int getPlayerIdsCount();
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @param index The index of the element to return.
     * @return The playerIds at the given index.
     */
    long getPlayerIds(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryPlayerZoneIdAsk}
   */
  public static final class BatchQueryPlayerZoneIdAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryPlayerZoneIdAsk)
      BatchQueryPlayerZoneIdAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryPlayerZoneIdAsk.newBuilder() to construct.
    private BatchQueryPlayerZoneIdAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryPlayerZoneIdAsk() {
      playerIds_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryPlayerZoneIdAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryPlayerZoneIdAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                playerIds_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              playerIds_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                playerIds_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                playerIds_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          playerIds_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk.Builder.class);
    }

    public static final int PLAYERIDS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList playerIds_;
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @return A list containing the playerIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getPlayerIdsList() {
      return playerIds_;
    }
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @return The count of playerIds.
     */
    public int getPlayerIdsCount() {
      return playerIds_.size();
    }
    /**
     * <code>repeated int64 playerIds = 1;</code>
     * @param index The index of the element to return.
     * @return The playerIds at the given index.
     */
    public long getPlayerIds(int index) {
      return playerIds_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < playerIds_.size(); i++) {
        output.writeInt64(1, playerIds_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < playerIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(playerIds_.getLong(i));
        }
        size += dataSize;
        size += 1 * getPlayerIdsList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk other = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk) obj;

      if (!getPlayerIdsList()
          .equals(other.getPlayerIdsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPlayerIdsCount() > 0) {
        hash = (37 * hash) + PLAYERIDS_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryPlayerZoneIdAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryPlayerZoneIdAsk)
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk build() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk buildPartial() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk result = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          playerIds_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.playerIds_ = playerIds_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk other) {
        if (other == com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk.getDefaultInstance()) return this;
        if (!other.playerIds_.isEmpty()) {
          if (playerIds_.isEmpty()) {
            playerIds_ = other.playerIds_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePlayerIdsIsMutable();
            playerIds_.addAll(other.playerIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList playerIds_ = emptyLongList();
      private void ensurePlayerIdsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          playerIds_ = mutableCopy(playerIds_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @return A list containing the playerIds.
       */
      public java.util.List<java.lang.Long>
          getPlayerIdsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(playerIds_) : playerIds_;
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @return The count of playerIds.
       */
      public int getPlayerIdsCount() {
        return playerIds_.size();
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @param index The index of the element to return.
       * @return The playerIds at the given index.
       */
      public long getPlayerIds(int index) {
        return playerIds_.getLong(index);
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @param index The index to set the value at.
       * @param value The playerIds to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerIds(
          int index, long value) {
        ensurePlayerIdsIsMutable();
        playerIds_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @param value The playerIds to add.
       * @return This builder for chaining.
       */
      public Builder addPlayerIds(long value) {
        ensurePlayerIdsIsMutable();
        playerIds_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @param values The playerIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllPlayerIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensurePlayerIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerIds_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerIds = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerIds() {
        playerIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryPlayerZoneIdAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryPlayerZoneIdAsk)
    private static final com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk();
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryPlayerZoneIdAsk>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryPlayerZoneIdAsk>() {
      @java.lang.Override
      public BatchQueryPlayerZoneIdAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryPlayerZoneIdAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryPlayerZoneIdAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryPlayerZoneIdAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryPlayerZoneIdAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryPlayerZoneIdAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */
    int getPlayerZoneIdsCount();
    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */
    boolean containsPlayerZoneIds(
        long key);
    /**
     * Use {@link #getPlayerZoneIdsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, java.lang.Integer>
    getPlayerZoneIds();
    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */
    java.util.Map<java.lang.Long, java.lang.Integer>
    getPlayerZoneIdsMap();
    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */

    int getPlayerZoneIdsOrDefault(
        long key,
        int defaultValue);
    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */

    int getPlayerZoneIdsOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryPlayerZoneIdAns}
   */
  public static final class BatchQueryPlayerZoneIdAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryPlayerZoneIdAns)
      BatchQueryPlayerZoneIdAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryPlayerZoneIdAns.newBuilder() to construct.
    private BatchQueryPlayerZoneIdAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryPlayerZoneIdAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryPlayerZoneIdAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryPlayerZoneIdAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                playerZoneIds_ = com.google.protobuf.MapField.newMapField(
                    PlayerZoneIdsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, java.lang.Integer>
              playerZoneIds__ = input.readMessage(
                  PlayerZoneIdsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              playerZoneIds_.getMutableMap().put(
                  playerZoneIds__.getKey(), playerZoneIds__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetPlayerZoneIds();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns.Builder.class);
    }

    public static final int PLAYERZONEIDS_FIELD_NUMBER = 1;
    private static final class PlayerZoneIdsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_PlayerZoneIdsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Long, java.lang.Integer> playerZoneIds_;
    private com.google.protobuf.MapField<java.lang.Long, java.lang.Integer>
    internalGetPlayerZoneIds() {
      if (playerZoneIds_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            PlayerZoneIdsDefaultEntryHolder.defaultEntry);
      }
      return playerZoneIds_;
    }

    public int getPlayerZoneIdsCount() {
      return internalGetPlayerZoneIds().getMap().size();
    }
    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */

    @java.lang.Override
    public boolean containsPlayerZoneIds(
        long key) {
      
      return internalGetPlayerZoneIds().getMap().containsKey(key);
    }
    /**
     * Use {@link #getPlayerZoneIdsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, java.lang.Integer> getPlayerZoneIds() {
      return getPlayerZoneIdsMap();
    }
    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, java.lang.Integer> getPlayerZoneIdsMap() {
      return internalGetPlayerZoneIds().getMap();
    }
    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */
    @java.lang.Override

    public int getPlayerZoneIdsOrDefault(
        long key,
        int defaultValue) {
      
      java.util.Map<java.lang.Long, java.lang.Integer> map =
          internalGetPlayerZoneIds().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * key=playerId, val=zoneId
     * </pre>
     *
     * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
     */
    @java.lang.Override

    public int getPlayerZoneIdsOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, java.lang.Integer> map =
          internalGetPlayerZoneIds().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetPlayerZoneIds(),
          PlayerZoneIdsDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, java.lang.Integer> entry
           : internalGetPlayerZoneIds().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, java.lang.Integer>
        playerZoneIds__ = PlayerZoneIdsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, playerZoneIds__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns other = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns) obj;

      if (!internalGetPlayerZoneIds().equals(
          other.internalGetPlayerZoneIds())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetPlayerZoneIds().getMap().isEmpty()) {
        hash = (37 * hash) + PLAYERZONEIDS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetPlayerZoneIds().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryPlayerZoneIdAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryPlayerZoneIdAns)
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetPlayerZoneIds();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutablePlayerZoneIds();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns.class, com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutablePlayerZoneIds().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerCard.internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns build() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns buildPartial() {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns result = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns(this);
        int from_bitField0_ = bitField0_;
        result.playerZoneIds_ = internalGetPlayerZoneIds();
        result.playerZoneIds_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns) {
          return mergeFrom((com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns other) {
        if (other == com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns.getDefaultInstance()) return this;
        internalGetMutablePlayerZoneIds().mergeFrom(
            other.internalGetPlayerZoneIds());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, java.lang.Integer> playerZoneIds_;
      private com.google.protobuf.MapField<java.lang.Long, java.lang.Integer>
      internalGetPlayerZoneIds() {
        if (playerZoneIds_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              PlayerZoneIdsDefaultEntryHolder.defaultEntry);
        }
        return playerZoneIds_;
      }
      private com.google.protobuf.MapField<java.lang.Long, java.lang.Integer>
      internalGetMutablePlayerZoneIds() {
        onChanged();;
        if (playerZoneIds_ == null) {
          playerZoneIds_ = com.google.protobuf.MapField.newMapField(
              PlayerZoneIdsDefaultEntryHolder.defaultEntry);
        }
        if (!playerZoneIds_.isMutable()) {
          playerZoneIds_ = playerZoneIds_.copy();
        }
        return playerZoneIds_;
      }

      public int getPlayerZoneIdsCount() {
        return internalGetPlayerZoneIds().getMap().size();
      }
      /**
       * <pre>
       * key=playerId, val=zoneId
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
       */

      @java.lang.Override
      public boolean containsPlayerZoneIds(
          long key) {
        
        return internalGetPlayerZoneIds().getMap().containsKey(key);
      }
      /**
       * Use {@link #getPlayerZoneIdsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.Integer> getPlayerZoneIds() {
        return getPlayerZoneIdsMap();
      }
      /**
       * <pre>
       * key=playerId, val=zoneId
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, java.lang.Integer> getPlayerZoneIdsMap() {
        return internalGetPlayerZoneIds().getMap();
      }
      /**
       * <pre>
       * key=playerId, val=zoneId
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
       */
      @java.lang.Override

      public int getPlayerZoneIdsOrDefault(
          long key,
          int defaultValue) {
        
        java.util.Map<java.lang.Long, java.lang.Integer> map =
            internalGetPlayerZoneIds().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * key=playerId, val=zoneId
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
       */
      @java.lang.Override

      public int getPlayerZoneIdsOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, java.lang.Integer> map =
            internalGetPlayerZoneIds().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearPlayerZoneIds() {
        internalGetMutablePlayerZoneIds().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * key=playerId, val=zoneId
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
       */

      public Builder removePlayerZoneIds(
          long key) {
        
        internalGetMutablePlayerZoneIds().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, java.lang.Integer>
      getMutablePlayerZoneIds() {
        return internalGetMutablePlayerZoneIds().getMutableMap();
      }
      /**
       * <pre>
       * key=playerId, val=zoneId
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
       */
      public Builder putPlayerZoneIds(
          long key,
          int value) {
        
        
        internalGetMutablePlayerZoneIds().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * key=playerId, val=zoneId
       * </pre>
       *
       * <code>map&lt;int64, int32&gt; playerZoneIds = 1;</code>
       */

      public Builder putAllPlayerZoneIds(
          java.util.Map<java.lang.Long, java.lang.Integer> values) {
        internalGetMutablePlayerZoneIds().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryPlayerZoneIdAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryPlayerZoneIdAns)
    private static final com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns();
    }

    public static com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryPlayerZoneIdAns>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryPlayerZoneIdAns>() {
      @java.lang.Override
      public BatchQueryPlayerZoneIdAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryPlayerZoneIdAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryPlayerZoneIdAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryPlayerZoneIdAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerCard.BatchQueryPlayerZoneIdAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryPlayerCardAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryPlayerCardAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryPlayerCardAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryPlayerCardAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerCardAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerCardAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerCardAns_CardInfosEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerCardAns_CardInfosEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryPlayerCardHeadAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryPlayerCardHeadAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_CardHeadListEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_CardHeadListEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_UpdatePlayerCardCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_UpdatePlayerCardCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_PlayerZoneIdsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_PlayerZoneIdsEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/playerCard/ss_playerCard." +
      "proto\022\017com.yorha.proto\032\"cs_proto/gen/com" +
      "mon/structPB.proto\"&\n\022QueryPlayerCardAsk" +
      "\022\020\n\010playerId\030\001 \001(\003\"I\n\022QueryPlayerCardAns" +
      "\0223\n\010cardInfo\030\001 \001(\0132!.com.yorha.proto.Pla" +
      "yerCardInfoPB\"*\n\027BatchQueryPlayerCardAsk" +
      "\022\017\n\007players\030\001 \003(\003\"\272\001\n\027BatchQueryPlayerCa" +
      "rdAns\022J\n\tcardInfos\030\001 \003(\01327.com.yorha.pro" +
      "to.BatchQueryPlayerCardAns.CardInfosEntr" +
      "y\032S\n\016CardInfosEntry\022\013\n\003key\030\001 \001(\003\0220\n\005valu" +
      "e\030\002 \001(\0132!.com.yorha.proto.PlayerCardInfo" +
      "PB:\0028\001\"*\n\026QueryPlayerCardHeadAsk\022\020\n\010play" +
      "erId\030\001 \001(\003\"M\n\026QueryPlayerCardHeadAns\0223\n\010" +
      "cardHead\030\001 \001(\0132!.com.yorha.proto.PlayerC" +
      "ardHeadPB\"0\n\033BatchQueryPlayerCardHeadAsk" +
      "\022\021\n\tplayerIds\030\001 \003(\003\"\313\001\n\033BatchQueryPlayer" +
      "CardHeadAns\022T\n\014cardHeadList\030\001 \003(\0132>.com." +
      "yorha.proto.BatchQueryPlayerCardHeadAns." +
      "CardHeadListEntry\032V\n\021CardHeadListEntry\022\013" +
      "\n\003key\030\001 \001(\003\0220\n\005value\030\002 \001(\0132!.com.yorha.p" +
      "roto.PlayerCardHeadPB:\0028\001\"J\n\023UpdatePlaye" +
      "rCardCmd\0223\n\010cardInfo\030\001 \001(\0132!.com.yorha.p" +
      "roto.PlayerCardInfoPB\".\n\031BatchQueryPlaye" +
      "rZoneIdAsk\022\021\n\tplayerIds\030\001 \003(\003\"\247\001\n\031BatchQ" +
      "ueryPlayerZoneIdAns\022T\n\rplayerZoneIds\030\001 \003" +
      "(\0132=.com.yorha.proto.BatchQueryPlayerZon" +
      "eIdAns.PlayerZoneIdsEntry\0324\n\022PlayerZoneI" +
      "dsEntry\022\013\n\003key\030\001 \001(\003\022\r\n\005value\030\002 \001(\005:\0028\001B" +
      "\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
        });
    internal_static_com_yorha_proto_QueryPlayerCardAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_QueryPlayerCardAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryPlayerCardAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_QueryPlayerCardAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_QueryPlayerCardAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryPlayerCardAns_descriptor,
        new java.lang.String[] { "CardInfo", });
    internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerCardAsk_descriptor,
        new java.lang.String[] { "Players", });
    internal_static_com_yorha_proto_BatchQueryPlayerCardAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_BatchQueryPlayerCardAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerCardAns_descriptor,
        new java.lang.String[] { "CardInfos", });
    internal_static_com_yorha_proto_BatchQueryPlayerCardAns_CardInfosEntry_descriptor =
      internal_static_com_yorha_proto_BatchQueryPlayerCardAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_BatchQueryPlayerCardAns_CardInfosEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerCardAns_CardInfosEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryPlayerCardHeadAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_QueryPlayerCardHeadAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_QueryPlayerCardHeadAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryPlayerCardHeadAns_descriptor,
        new java.lang.String[] { "CardHead", });
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAsk_descriptor,
        new java.lang.String[] { "PlayerIds", });
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_descriptor,
        new java.lang.String[] { "CardHeadList", });
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_CardHeadListEntry_descriptor =
      internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_CardHeadListEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerCardHeadAns_CardHeadListEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_UpdatePlayerCardCmd_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_UpdatePlayerCardCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_UpdatePlayerCardCmd_descriptor,
        new java.lang.String[] { "CardInfo", });
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAsk_descriptor,
        new java.lang.String[] { "PlayerIds", });
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_descriptor,
        new java.lang.String[] { "PlayerZoneIds", });
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_PlayerZoneIdsEntry_descriptor =
      internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_PlayerZoneIdsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryPlayerZoneIdAns_PlayerZoneIdsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.yorha.proto.StructPB.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

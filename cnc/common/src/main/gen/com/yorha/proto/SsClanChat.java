// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clan/ss_clan_chat.proto

package com.yorha.proto;

public final class SsClanChat {
  private SsClanChat() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FetchClanChatAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanChatAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 fromId = 1;</code>
     * @return Whether the fromId field is set.
     */
    boolean hasFromId();
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return The fromId.
     */
    long getFromId();

    /**
     * <code>optional int64 toId = 2;</code>
     * @return Whether the toId field is set.
     */
    boolean hasToId();
    /**
     * <code>optional int64 toId = 2;</code>
     * @return The toId.
     */
    long getToId();

    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return A list containing the shieldList.
     */
    java.util.List<java.lang.Long> getShieldListList();
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return The count of shieldList.
     */
    int getShieldListCount();
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @param index The index of the element to return.
     * @return The shieldList at the given index.
     */
    long getShieldList(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanChatAsk}
   */
  public static final class FetchClanChatAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanChatAsk)
      FetchClanChatAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanChatAsk.newBuilder() to construct.
    private FetchClanChatAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanChatAsk() {
      shieldList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanChatAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanChatAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              fromId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toId_ = input.readInt64();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                shieldList_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              shieldList_.addLong(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                shieldList_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                shieldList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          shieldList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanChat.FetchClanChatAsk.class, com.yorha.proto.SsClanChat.FetchClanChatAsk.Builder.class);
    }

    private int bitField0_;
    public static final int FROMID_FIELD_NUMBER = 1;
    private long fromId_;
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return Whether the fromId field is set.
     */
    @java.lang.Override
    public boolean hasFromId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return The fromId.
     */
    @java.lang.Override
    public long getFromId() {
      return fromId_;
    }

    public static final int TOID_FIELD_NUMBER = 2;
    private long toId_;
    /**
     * <code>optional int64 toId = 2;</code>
     * @return Whether the toId field is set.
     */
    @java.lang.Override
    public boolean hasToId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 toId = 2;</code>
     * @return The toId.
     */
    @java.lang.Override
    public long getToId() {
      return toId_;
    }

    public static final int SHIELDLIST_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.LongList shieldList_;
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return A list containing the shieldList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getShieldListList() {
      return shieldList_;
    }
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return The count of shieldList.
     */
    public int getShieldListCount() {
      return shieldList_.size();
    }
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @param index The index of the element to return.
     * @return The shieldList at the given index.
     */
    public long getShieldList(int index) {
      return shieldList_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, fromId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toId_);
      }
      for (int i = 0; i < shieldList_.size(); i++) {
        output.writeInt64(3, shieldList_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, fromId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < shieldList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(shieldList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getShieldListList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanChat.FetchClanChatAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanChat.FetchClanChatAsk other = (com.yorha.proto.SsClanChat.FetchClanChatAsk) obj;

      if (hasFromId() != other.hasFromId()) return false;
      if (hasFromId()) {
        if (getFromId()
            != other.getFromId()) return false;
      }
      if (hasToId() != other.hasToId()) return false;
      if (hasToId()) {
        if (getToId()
            != other.getToId()) return false;
      }
      if (!getShieldListList()
          .equals(other.getShieldListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasFromId()) {
        hash = (37 * hash) + FROMID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getFromId());
      }
      if (hasToId()) {
        hash = (37 * hash) + TOID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToId());
      }
      if (getShieldListCount() > 0) {
        hash = (37 * hash) + SHIELDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getShieldListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanChat.FetchClanChatAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanChatAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanChatAsk)
        com.yorha.proto.SsClanChat.FetchClanChatAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanChat.FetchClanChatAsk.class, com.yorha.proto.SsClanChat.FetchClanChatAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanChat.FetchClanChatAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fromId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        toId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        shieldList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.FetchClanChatAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanChat.FetchClanChatAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.FetchClanChatAsk build() {
        com.yorha.proto.SsClanChat.FetchClanChatAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.FetchClanChatAsk buildPartial() {
        com.yorha.proto.SsClanChat.FetchClanChatAsk result = new com.yorha.proto.SsClanChat.FetchClanChatAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.fromId_ = fromId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toId_ = toId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          shieldList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.shieldList_ = shieldList_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanChat.FetchClanChatAsk) {
          return mergeFrom((com.yorha.proto.SsClanChat.FetchClanChatAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanChat.FetchClanChatAsk other) {
        if (other == com.yorha.proto.SsClanChat.FetchClanChatAsk.getDefaultInstance()) return this;
        if (other.hasFromId()) {
          setFromId(other.getFromId());
        }
        if (other.hasToId()) {
          setToId(other.getToId());
        }
        if (!other.shieldList_.isEmpty()) {
          if (shieldList_.isEmpty()) {
            shieldList_ = other.shieldList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureShieldListIsMutable();
            shieldList_.addAll(other.shieldList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanChat.FetchClanChatAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanChat.FetchClanChatAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long fromId_ ;
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return Whether the fromId field is set.
       */
      @java.lang.Override
      public boolean hasFromId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return The fromId.
       */
      @java.lang.Override
      public long getFromId() {
        return fromId_;
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @param value The fromId to set.
       * @return This builder for chaining.
       */
      public Builder setFromId(long value) {
        bitField0_ |= 0x00000001;
        fromId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        fromId_ = 0L;
        onChanged();
        return this;
      }

      private long toId_ ;
      /**
       * <code>optional int64 toId = 2;</code>
       * @return Whether the toId field is set.
       */
      @java.lang.Override
      public boolean hasToId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @return The toId.
       */
      @java.lang.Override
      public long getToId() {
        return toId_;
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @param value The toId to set.
       * @return This builder for chaining.
       */
      public Builder setToId(long value) {
        bitField0_ |= 0x00000002;
        toId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList shieldList_ = emptyLongList();
      private void ensureShieldListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          shieldList_ = mutableCopy(shieldList_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return A list containing the shieldList.
       */
      public java.util.List<java.lang.Long>
          getShieldListList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(shieldList_) : shieldList_;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return The count of shieldList.
       */
      public int getShieldListCount() {
        return shieldList_.size();
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param index The index of the element to return.
       * @return The shieldList at the given index.
       */
      public long getShieldList(int index) {
        return shieldList_.getLong(index);
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param index The index to set the value at.
       * @param value The shieldList to set.
       * @return This builder for chaining.
       */
      public Builder setShieldList(
          int index, long value) {
        ensureShieldListIsMutable();
        shieldList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param value The shieldList to add.
       * @return This builder for chaining.
       */
      public Builder addShieldList(long value) {
        ensureShieldListIsMutable();
        shieldList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param values The shieldList to add.
       * @return This builder for chaining.
       */
      public Builder addAllShieldList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureShieldListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, shieldList_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearShieldList() {
        shieldList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanChatAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanChatAsk)
    private static final com.yorha.proto.SsClanChat.FetchClanChatAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanChat.FetchClanChatAsk();
    }

    public static com.yorha.proto.SsClanChat.FetchClanChatAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanChatAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanChatAsk>() {
      @java.lang.Override
      public FetchClanChatAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanChatAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanChatAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanChatAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanChat.FetchClanChatAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanChatAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanChatAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    java.util.List<com.yorha.proto.CommonMsg.ChatMessage> 
        getChatMsgsList();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index);
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    int getChatMsgsCount();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
        getChatMsgsOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanChatAns}
   */
  public static final class FetchClanChatAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanChatAns)
      FetchClanChatAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanChatAns.newBuilder() to construct.
    private FetchClanChatAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanChatAns() {
      chatMsgs_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanChatAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanChatAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                chatMsgs_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ChatMessage>();
                mutable_bitField0_ |= 0x00000001;
              }
              chatMsgs_.add(
                  input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          chatMsgs_ = java.util.Collections.unmodifiableList(chatMsgs_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanChat.FetchClanChatAns.class, com.yorha.proto.SsClanChat.FetchClanChatAns.Builder.class);
    }

    public static final int CHATMSGS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.CommonMsg.ChatMessage> chatMsgs_;
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.CommonMsg.ChatMessage> getChatMsgsList() {
      return chatMsgs_;
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
        getChatMsgsOrBuilderList() {
      return chatMsgs_;
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public int getChatMsgsCount() {
      return chatMsgs_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index) {
      return chatMsgs_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
        int index) {
      return chatMsgs_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < chatMsgs_.size(); i++) {
        output.writeMessage(1, chatMsgs_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < chatMsgs_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, chatMsgs_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanChat.FetchClanChatAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanChat.FetchClanChatAns other = (com.yorha.proto.SsClanChat.FetchClanChatAns) obj;

      if (!getChatMsgsList()
          .equals(other.getChatMsgsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChatMsgsCount() > 0) {
        hash = (37 * hash) + CHATMSGS_FIELD_NUMBER;
        hash = (53 * hash) + getChatMsgsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.FetchClanChatAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanChat.FetchClanChatAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanChatAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanChatAns)
        com.yorha.proto.SsClanChat.FetchClanChatAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanChat.FetchClanChatAns.class, com.yorha.proto.SsClanChat.FetchClanChatAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanChat.FetchClanChatAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMsgsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatMsgsBuilder_ == null) {
          chatMsgs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          chatMsgsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_FetchClanChatAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.FetchClanChatAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanChat.FetchClanChatAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.FetchClanChatAns build() {
        com.yorha.proto.SsClanChat.FetchClanChatAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.FetchClanChatAns buildPartial() {
        com.yorha.proto.SsClanChat.FetchClanChatAns result = new com.yorha.proto.SsClanChat.FetchClanChatAns(this);
        int from_bitField0_ = bitField0_;
        if (chatMsgsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            chatMsgs_ = java.util.Collections.unmodifiableList(chatMsgs_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.chatMsgs_ = chatMsgs_;
        } else {
          result.chatMsgs_ = chatMsgsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanChat.FetchClanChatAns) {
          return mergeFrom((com.yorha.proto.SsClanChat.FetchClanChatAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanChat.FetchClanChatAns other) {
        if (other == com.yorha.proto.SsClanChat.FetchClanChatAns.getDefaultInstance()) return this;
        if (chatMsgsBuilder_ == null) {
          if (!other.chatMsgs_.isEmpty()) {
            if (chatMsgs_.isEmpty()) {
              chatMsgs_ = other.chatMsgs_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureChatMsgsIsMutable();
              chatMsgs_.addAll(other.chatMsgs_);
            }
            onChanged();
          }
        } else {
          if (!other.chatMsgs_.isEmpty()) {
            if (chatMsgsBuilder_.isEmpty()) {
              chatMsgsBuilder_.dispose();
              chatMsgsBuilder_ = null;
              chatMsgs_ = other.chatMsgs_;
              bitField0_ = (bitField0_ & ~0x00000001);
              chatMsgsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChatMsgsFieldBuilder() : null;
            } else {
              chatMsgsBuilder_.addAllMessages(other.chatMsgs_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanChat.FetchClanChatAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanChat.FetchClanChatAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.CommonMsg.ChatMessage> chatMsgs_ =
        java.util.Collections.emptyList();
      private void ensureChatMsgsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          chatMsgs_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ChatMessage>(chatMsgs_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMsgsBuilder_;

      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ChatMessage> getChatMsgsList() {
        if (chatMsgsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(chatMsgs_);
        } else {
          return chatMsgsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public int getChatMsgsCount() {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.size();
        } else {
          return chatMsgsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index) {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.get(index);
        } else {
          return chatMsgsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder setChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.set(index, value);
          onChanged();
        } else {
          chatMsgsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder setChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.set(index, builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.add(value);
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.add(index, value);
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.add(builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.add(index, builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addAllChatMsgs(
          java.lang.Iterable<? extends com.yorha.proto.CommonMsg.ChatMessage> values) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, chatMsgs_);
          onChanged();
        } else {
          chatMsgsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder clearChatMsgs() {
        if (chatMsgsBuilder_ == null) {
          chatMsgs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          chatMsgsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder removeChatMsgs(int index) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.remove(index);
          onChanged();
        } else {
          chatMsgsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMsgsBuilder(
          int index) {
        return getChatMsgsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
          int index) {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.get(index);  } else {
          return chatMsgsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
           getChatMsgsOrBuilderList() {
        if (chatMsgsBuilder_ != null) {
          return chatMsgsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(chatMsgs_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder addChatMsgsBuilder() {
        return getChatMsgsFieldBuilder().addBuilder(
            com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder addChatMsgsBuilder(
          int index) {
        return getChatMsgsFieldBuilder().addBuilder(
            index, com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ChatMessage.Builder> 
           getChatMsgsBuilderList() {
        return getChatMsgsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMsgsFieldBuilder() {
        if (chatMsgsBuilder_ == null) {
          chatMsgsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  chatMsgs_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          chatMsgs_ = null;
        }
        return chatMsgsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanChatAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanChatAns)
    private static final com.yorha.proto.SsClanChat.FetchClanChatAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanChat.FetchClanChatAns();
    }

    public static com.yorha.proto.SsClanChat.FetchClanChatAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanChatAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanChatAns>() {
      @java.lang.Override
      public FetchClanChatAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanChatAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanChatAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanChatAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanChat.FetchClanChatAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendClanChatAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendClanChatAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return Whether the chatMessage field is set.
     */
    boolean hasChatMessage();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return The chatMessage.
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMessage();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendClanChatAsk}
   */
  public static final class SendClanChatAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendClanChatAsk)
      SendClanChatAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendClanChatAsk.newBuilder() to construct.
    private SendClanChatAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendClanChatAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendClanChatAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendClanChatAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ChatMessage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = chatMessage_.toBuilder();
              }
              chatMessage_ = input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatMessage_);
                chatMessage_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanChat.SendClanChatAsk.class, com.yorha.proto.SsClanChat.SendClanChatAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHATMESSAGE_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return Whether the chatMessage field is set.
     */
    @java.lang.Override
    public boolean hasChatMessage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return The chatMessage.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getChatMessage());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getChatMessage());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanChat.SendClanChatAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanChat.SendClanChatAsk other = (com.yorha.proto.SsClanChat.SendClanChatAsk) obj;

      if (hasChatMessage() != other.hasChatMessage()) return false;
      if (hasChatMessage()) {
        if (!getChatMessage()
            .equals(other.getChatMessage())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatMessage()) {
        hash = (37 * hash) + CHATMESSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getChatMessage().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanChat.SendClanChatAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendClanChatAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendClanChatAsk)
        com.yorha.proto.SsClanChat.SendClanChatAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanChat.SendClanChatAsk.class, com.yorha.proto.SsClanChat.SendClanChatAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanChat.SendClanChatAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMessageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.SendClanChatAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanChat.SendClanChatAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.SendClanChatAsk build() {
        com.yorha.proto.SsClanChat.SendClanChatAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.SendClanChatAsk buildPartial() {
        com.yorha.proto.SsClanChat.SendClanChatAsk result = new com.yorha.proto.SsClanChat.SendClanChatAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (chatMessageBuilder_ == null) {
            result.chatMessage_ = chatMessage_;
          } else {
            result.chatMessage_ = chatMessageBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanChat.SendClanChatAsk) {
          return mergeFrom((com.yorha.proto.SsClanChat.SendClanChatAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanChat.SendClanChatAsk other) {
        if (other == com.yorha.proto.SsClanChat.SendClanChatAsk.getDefaultInstance()) return this;
        if (other.hasChatMessage()) {
          mergeChatMessage(other.getChatMessage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanChat.SendClanChatAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanChat.SendClanChatAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMessageBuilder_;
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       * @return Whether the chatMessage field is set.
       */
      public boolean hasChatMessage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       * @return The chatMessage.
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
        if (chatMessageBuilder_ == null) {
          return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        } else {
          return chatMessageBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder setChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatMessage_ = value;
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder setChatMessage(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = builderForValue.build();
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder mergeChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              chatMessage_ != null &&
              chatMessage_ != com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance()) {
            chatMessage_ =
              com.yorha.proto.CommonMsg.ChatMessage.newBuilder(chatMessage_).mergeFrom(value).buildPartial();
          } else {
            chatMessage_ = value;
          }
          onChanged();
        } else {
          chatMessageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder clearChatMessage() {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
          onChanged();
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMessageBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatMessageFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
        if (chatMessageBuilder_ != null) {
          return chatMessageBuilder_.getMessageOrBuilder();
        } else {
          return chatMessage_ == null ?
              com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMessageFieldBuilder() {
        if (chatMessageBuilder_ == null) {
          chatMessageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  getChatMessage(),
                  getParentForChildren(),
                  isClean());
          chatMessage_ = null;
        }
        return chatMessageBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendClanChatAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendClanChatAsk)
    private static final com.yorha.proto.SsClanChat.SendClanChatAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanChat.SendClanChatAsk();
    }

    public static com.yorha.proto.SsClanChat.SendClanChatAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendClanChatAsk>
        PARSER = new com.google.protobuf.AbstractParser<SendClanChatAsk>() {
      @java.lang.Override
      public SendClanChatAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendClanChatAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendClanChatAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendClanChatAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanChat.SendClanChatAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendClanChatAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendClanChatAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return Whether the messageId field is set.
     */
    boolean hasMessageId();
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return The messageId.
     */
    long getMessageId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendClanChatAns}
   */
  public static final class SendClanChatAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendClanChatAns)
      SendClanChatAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendClanChatAns.newBuilder() to construct.
    private SendClanChatAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendClanChatAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendClanChatAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendClanChatAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              messageId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanChat.SendClanChatAns.class, com.yorha.proto.SsClanChat.SendClanChatAns.Builder.class);
    }

    private int bitField0_;
    public static final int MESSAGEID_FIELD_NUMBER = 1;
    private long messageId_;
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return Whether the messageId field is set.
     */
    @java.lang.Override
    public boolean hasMessageId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return The messageId.
     */
    @java.lang.Override
    public long getMessageId() {
      return messageId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, messageId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, messageId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanChat.SendClanChatAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanChat.SendClanChatAns other = (com.yorha.proto.SsClanChat.SendClanChatAns) obj;

      if (hasMessageId() != other.hasMessageId()) return false;
      if (hasMessageId()) {
        if (getMessageId()
            != other.getMessageId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMessageId()) {
        hash = (37 * hash) + MESSAGEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMessageId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanChat.SendClanChatAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanChat.SendClanChatAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendClanChatAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendClanChatAns)
        com.yorha.proto.SsClanChat.SendClanChatAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanChat.SendClanChatAns.class, com.yorha.proto.SsClanChat.SendClanChatAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanChat.SendClanChatAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        messageId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanChat.internal_static_com_yorha_proto_SendClanChatAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.SendClanChatAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanChat.SendClanChatAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.SendClanChatAns build() {
        com.yorha.proto.SsClanChat.SendClanChatAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanChat.SendClanChatAns buildPartial() {
        com.yorha.proto.SsClanChat.SendClanChatAns result = new com.yorha.proto.SsClanChat.SendClanChatAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.messageId_ = messageId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanChat.SendClanChatAns) {
          return mergeFrom((com.yorha.proto.SsClanChat.SendClanChatAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanChat.SendClanChatAns other) {
        if (other == com.yorha.proto.SsClanChat.SendClanChatAns.getDefaultInstance()) return this;
        if (other.hasMessageId()) {
          setMessageId(other.getMessageId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanChat.SendClanChatAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanChat.SendClanChatAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long messageId_ ;
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return Whether the messageId field is set.
       */
      @java.lang.Override
      public boolean hasMessageId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return The messageId.
       */
      @java.lang.Override
      public long getMessageId() {
        return messageId_;
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @param value The messageId to set.
       * @return This builder for chaining.
       */
      public Builder setMessageId(long value) {
        bitField0_ |= 0x00000001;
        messageId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMessageId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        messageId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendClanChatAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendClanChatAns)
    private static final com.yorha.proto.SsClanChat.SendClanChatAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanChat.SendClanChatAns();
    }

    public static com.yorha.proto.SsClanChat.SendClanChatAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendClanChatAns>
        PARSER = new com.google.protobuf.AbstractParser<SendClanChatAns>() {
      @java.lang.Override
      public SendClanChatAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendClanChatAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendClanChatAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendClanChatAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanChat.SendClanChatAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanChatAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanChatAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanChatAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanChatAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendClanChatAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendClanChatAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendClanChatAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendClanChatAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$ss_proto/gen/clan/ss_clan_chat.proto\022\017" +
      "com.yorha.proto\032$ss_proto/gen/common/com" +
      "mon_msg.proto\"D\n\020FetchClanChatAsk\022\016\n\006fro" +
      "mId\030\001 \001(\003\022\014\n\004toId\030\002 \001(\003\022\022\n\nshieldList\030\003 " +
      "\003(\003\"B\n\020FetchClanChatAns\022.\n\010chatMsgs\030\001 \003(" +
      "\0132\034.com.yorha.proto.ChatMessage\"D\n\017SendC" +
      "lanChatAsk\0221\n\013chatMessage\030\001 \001(\0132\034.com.yo" +
      "rha.proto.ChatMessage\"$\n\017SendClanChatAns" +
      "\022\021\n\tmessageId\030\001 \001(\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_FetchClanChatAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_FetchClanChatAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanChatAsk_descriptor,
        new java.lang.String[] { "FromId", "ToId", "ShieldList", });
    internal_static_com_yorha_proto_FetchClanChatAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_FetchClanChatAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanChatAns_descriptor,
        new java.lang.String[] { "ChatMsgs", });
    internal_static_com_yorha_proto_SendClanChatAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SendClanChatAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendClanChatAsk_descriptor,
        new java.lang.String[] { "ChatMessage", });
    internal_static_com_yorha_proto_SendClanChatAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_SendClanChatAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendClanChatAns_descriptor,
        new java.lang.String[] { "MessageId", });
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

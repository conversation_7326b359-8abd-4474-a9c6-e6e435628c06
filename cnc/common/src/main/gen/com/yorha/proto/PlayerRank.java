// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_rank.proto

package com.yorha.proto;

public final class PlayerRank {
  private PlayerRank() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_GetTopRankInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetTopRankInfo_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>repeated int32 rankIdList = 2;</code>
     * @return A list containing the rankIdList.
     */
    java.util.List<java.lang.Integer> getRankIdListList();
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>repeated int32 rankIdList = 2;</code>
     * @return The count of rankIdList.
     */
    int getRankIdListCount();
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>repeated int32 rankIdList = 2;</code>
     * @param index The index of the element to return.
     * @return The rankIdList at the given index.
     */
    int getRankIdList(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetTopRankInfo_C2S}
   */
  public static final class Player_GetTopRankInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetTopRankInfo_C2S)
      Player_GetTopRankInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetTopRankInfo_C2S.newBuilder() to construct.
    private Player_GetTopRankInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetTopRankInfo_C2S() {
      rankIdList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetTopRankInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetTopRankInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 16: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rankIdList_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              rankIdList_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                rankIdList_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                rankIdList_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rankIdList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S.class, com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S.Builder.class);
    }

    public static final int RANKIDLIST_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList rankIdList_;
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>repeated int32 rankIdList = 2;</code>
     * @return A list containing the rankIdList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getRankIdListList() {
      return rankIdList_;
    }
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>repeated int32 rankIdList = 2;</code>
     * @return The count of rankIdList.
     */
    public int getRankIdListCount() {
      return rankIdList_.size();
    }
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>repeated int32 rankIdList = 2;</code>
     * @param index The index of the element to return.
     * @return The rankIdList at the given index.
     */
    public int getRankIdList(int index) {
      return rankIdList_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rankIdList_.size(); i++) {
        output.writeInt32(2, rankIdList_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < rankIdList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(rankIdList_.getInt(i));
        }
        size += dataSize;
        size += 1 * getRankIdListList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S other = (com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S) obj;

      if (!getRankIdListList()
          .equals(other.getRankIdListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRankIdListCount() > 0) {
        hash = (37 * hash) + RANKIDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getRankIdListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetTopRankInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetTopRankInfo_C2S)
        com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S.class, com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rankIdList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S build() {
        com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S buildPartial() {
        com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S result = new com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          rankIdList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.rankIdList_ = rankIdList_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S other) {
        if (other == com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S.getDefaultInstance()) return this;
        if (!other.rankIdList_.isEmpty()) {
          if (rankIdList_.isEmpty()) {
            rankIdList_ = other.rankIdList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureRankIdListIsMutable();
            rankIdList_.addAll(other.rankIdList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList rankIdList_ = emptyIntList();
      private void ensureRankIdListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rankIdList_ = mutableCopy(rankIdList_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>repeated int32 rankIdList = 2;</code>
       * @return A list containing the rankIdList.
       */
      public java.util.List<java.lang.Integer>
          getRankIdListList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(rankIdList_) : rankIdList_;
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>repeated int32 rankIdList = 2;</code>
       * @return The count of rankIdList.
       */
      public int getRankIdListCount() {
        return rankIdList_.size();
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>repeated int32 rankIdList = 2;</code>
       * @param index The index of the element to return.
       * @return The rankIdList at the given index.
       */
      public int getRankIdList(int index) {
        return rankIdList_.getInt(index);
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>repeated int32 rankIdList = 2;</code>
       * @param index The index to set the value at.
       * @param value The rankIdList to set.
       * @return This builder for chaining.
       */
      public Builder setRankIdList(
          int index, int value) {
        ensureRankIdListIsMutable();
        rankIdList_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>repeated int32 rankIdList = 2;</code>
       * @param value The rankIdList to add.
       * @return This builder for chaining.
       */
      public Builder addRankIdList(int value) {
        ensureRankIdListIsMutable();
        rankIdList_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>repeated int32 rankIdList = 2;</code>
       * @param values The rankIdList to add.
       * @return This builder for chaining.
       */
      public Builder addAllRankIdList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureRankIdListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rankIdList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>repeated int32 rankIdList = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankIdList() {
        rankIdList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetTopRankInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetTopRankInfo_C2S)
    private static final com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S();
    }

    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetTopRankInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetTopRankInfo_C2S>() {
      @java.lang.Override
      public Player_GetTopRankInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetTopRankInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetTopRankInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetTopRankInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerRank.Player_GetTopRankInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetTopRankInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetTopRankInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> 
        getDtoList();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTO getDto(int index);
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    int getDtoCount();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
        getDtoOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetTopRankInfo_S2C}
   */
  public static final class Player_GetTopRankInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetTopRankInfo_S2C)
      Player_GetTopRankInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetTopRankInfo_S2C.newBuilder() to construct.
    private Player_GetTopRankInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetTopRankInfo_S2C() {
      dto_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetTopRankInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetTopRankInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                dto_ = new java.util.ArrayList<com.yorha.proto.StructMsg.RankInfoDTO>();
                mutable_bitField0_ |= 0x00000001;
              }
              dto_.add(
                  input.readMessage(com.yorha.proto.StructMsg.RankInfoDTO.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          dto_ = java.util.Collections.unmodifiableList(dto_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C.class, com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C.Builder.class);
    }

    public static final int DTO_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> dto_;
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> getDtoList() {
      return dto_;
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
        getDtoOrBuilderList() {
      return dto_;
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public int getDtoCount() {
      return dto_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTO getDto(int index) {
      return dto_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
        int index) {
      return dto_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < dto_.size(); i++) {
        output.writeMessage(1, dto_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < dto_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, dto_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C other = (com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C) obj;

      if (!getDtoList()
          .equals(other.getDtoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDtoCount() > 0) {
        hash = (37 * hash) + DTO_FIELD_NUMBER;
        hash = (53 * hash) + getDtoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetTopRankInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetTopRankInfo_S2C)
        com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C.class, com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDtoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dtoBuilder_ == null) {
          dto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          dtoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C build() {
        com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C buildPartial() {
        com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C result = new com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        if (dtoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            dto_ = java.util.Collections.unmodifiableList(dto_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.dto_ = dto_;
        } else {
          result.dto_ = dtoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C other) {
        if (other == com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C.getDefaultInstance()) return this;
        if (dtoBuilder_ == null) {
          if (!other.dto_.isEmpty()) {
            if (dto_.isEmpty()) {
              dto_ = other.dto_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureDtoIsMutable();
              dto_.addAll(other.dto_);
            }
            onChanged();
          }
        } else {
          if (!other.dto_.isEmpty()) {
            if (dtoBuilder_.isEmpty()) {
              dtoBuilder_.dispose();
              dtoBuilder_ = null;
              dto_ = other.dto_;
              bitField0_ = (bitField0_ & ~0x00000001);
              dtoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDtoFieldBuilder() : null;
            } else {
              dtoBuilder_.addAllMessages(other.dto_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> dto_ =
        java.util.Collections.emptyList();
      private void ensureDtoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          dto_ = new java.util.ArrayList<com.yorha.proto.StructMsg.RankInfoDTO>(dto_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> dtoBuilder_;

      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> getDtoList() {
        if (dtoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dto_);
        } else {
          return dtoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public int getDtoCount() {
        if (dtoBuilder_ == null) {
          return dto_.size();
        } else {
          return dtoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO getDto(int index) {
        if (dtoBuilder_ == null) {
          return dto_.get(index);
        } else {
          return dtoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder setDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.set(index, value);
          onChanged();
        } else {
          dtoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder setDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.set(index, builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addDto(com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.add(value);
          onChanged();
        } else {
          dtoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.add(index, value);
          onChanged();
        } else {
          dtoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addDto(
          com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.add(builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.add(index, builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addAllDto(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.RankInfoDTO> values) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dto_);
          onChanged();
        } else {
          dtoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder clearDto() {
        if (dtoBuilder_ == null) {
          dto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          dtoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder removeDto(int index) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.remove(index);
          onChanged();
        } else {
          dtoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder getDtoBuilder(
          int index) {
        return getDtoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
          int index) {
        if (dtoBuilder_ == null) {
          return dto_.get(index);  } else {
          return dtoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
           getDtoOrBuilderList() {
        if (dtoBuilder_ != null) {
          return dtoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dto_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder addDtoBuilder() {
        return getDtoFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder addDtoBuilder(
          int index) {
        return getDtoFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO.Builder> 
           getDtoBuilderList() {
        return getDtoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
          getDtoFieldBuilder() {
        if (dtoBuilder_ == null) {
          dtoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder>(
                  dto_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          dto_ = null;
        }
        return dtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetTopRankInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetTopRankInfo_S2C)
    private static final com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C();
    }

    public static com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetTopRankInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetTopRankInfo_S2C>() {
      @java.lang.Override
      public Player_GetTopRankInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetTopRankInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetTopRankInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetTopRankInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerRank.Player_GetTopRankInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetRankPageInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetRankPageInfo_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 page = 2;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <code>optional int32 page = 2;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>optional int32 rankId = 3;</code>
     * @return Whether the rankId field is set.
     */
    boolean hasRankId();
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>optional int32 rankId = 3;</code>
     * @return The rankId.
     */
    int getRankId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetRankPageInfo_C2S}
   */
  public static final class Player_GetRankPageInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetRankPageInfo_C2S)
      Player_GetRankPageInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetRankPageInfo_C2S.newBuilder() to construct.
    private Player_GetRankPageInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetRankPageInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetRankPageInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetRankPageInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 16: {
              bitField0_ |= 0x00000001;
              page_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              rankId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S.class, com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PAGE_FIELD_NUMBER = 2;
    private int page_;
    /**
     * <code>optional int32 page = 2;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int RANKID_FIELD_NUMBER = 3;
    private int rankId_;
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>optional int32 rankId = 3;</code>
     * @return Whether the rankId field is set.
     */
    @java.lang.Override
    public boolean hasRankId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>optional int32 rankId = 3;</code>
     * @return The rankId.
     */
    @java.lang.Override
    public int getRankId() {
      return rankId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(2, page_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, rankId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, page_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, rankId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S other = (com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S) obj;

      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (getPage()
            != other.getPage()) return false;
      }
      if (hasRankId() != other.hasRankId()) return false;
      if (hasRankId()) {
        if (getRankId()
            != other.getRankId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage();
      }
      if (hasRankId()) {
        hash = (37 * hash) + RANKID_FIELD_NUMBER;
        hash = (53 * hash) + getRankId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetRankPageInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetRankPageInfo_C2S)
        com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S.class, com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        page_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        rankId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S build() {
        com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S buildPartial() {
        com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S result = new com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.page_ = page_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rankId_ = rankId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S other) {
        if (other == com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S.getDefaultInstance()) return this;
        if (other.hasPage()) {
          setPage(other.getPage());
        }
        if (other.hasRankId()) {
          setRankId(other.getRankId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int page_ ;
      /**
       * <code>optional int32 page = 2;</code>
       * @return Whether the page field is set.
       */
      @java.lang.Override
      public boolean hasPage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        bitField0_ |= 0x00000001;
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        onChanged();
        return this;
      }

      private int rankId_ ;
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>optional int32 rankId = 3;</code>
       * @return Whether the rankId field is set.
       */
      @java.lang.Override
      public boolean hasRankId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>optional int32 rankId = 3;</code>
       * @return The rankId.
       */
      @java.lang.Override
      public int getRankId() {
        return rankId_;
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>optional int32 rankId = 3;</code>
       * @param value The rankId to set.
       * @return This builder for chaining.
       */
      public Builder setRankId(int value) {
        bitField0_ |= 0x00000002;
        rankId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>optional int32 rankId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rankId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetRankPageInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetRankPageInfo_C2S)
    private static final com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S();
    }

    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetRankPageInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetRankPageInfo_C2S>() {
      @java.lang.Override
      public Player_GetRankPageInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetRankPageInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetRankPageInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetRankPageInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerRank.Player_GetRankPageInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetRankPageInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetRankPageInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * page从1开始算的，填0或者负数的话，一页都不会发，但是下面的myRankInfoDTO会照常下发
     * </pre>
     *
     * <code>optional int32 page = 2;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <pre>
     * page从1开始算的，填0或者负数的话，一页都不会发，但是下面的myRankInfoDTO会照常下发
     * </pre>
     *
     * <code>optional int32 page = 2;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     * 能拉取到的排行榜总个数
     * </pre>
     *
     * <code>optional int32 total = 3;</code>
     * @return Whether the total field is set.
     */
    boolean hasTotal();
    /**
     * <pre>
     * 能拉取到的排行榜总个数
     * </pre>
     *
     * <code>optional int32 total = 3;</code>
     * @return The total.
     */
    int getTotal();

    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> 
        getDtoList();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTO getDto(int index);
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    int getDtoCount();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
        getDtoOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
        int index);

    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     * @return Whether the myRankInfoDTO field is set.
     */
    boolean hasMyRankInfoDTO();
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     * @return The myRankInfoDTO.
     */
    com.yorha.proto.StructMsg.RankInfoDTO getMyRankInfoDTO();
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getMyRankInfoDTOOrBuilder();

    /**
     * <code>optional int32 maxRank = 6;</code>
     * @return Whether the maxRank field is set.
     */
    boolean hasMaxRank();
    /**
     * <code>optional int32 maxRank = 6;</code>
     * @return The maxRank.
     */
    int getMaxRank();

    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>optional int32 rankId = 7;</code>
     * @return Whether the rankId field is set.
     */
    boolean hasRankId();
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>optional int32 rankId = 7;</code>
     * @return The rankId.
     */
    int getRankId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetRankPageInfo_S2C}
   */
  public static final class Player_GetRankPageInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetRankPageInfo_S2C)
      Player_GetRankPageInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetRankPageInfo_S2C.newBuilder() to construct.
    private Player_GetRankPageInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetRankPageInfo_S2C() {
      dto_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetRankPageInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetRankPageInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 16: {
              bitField0_ |= 0x00000001;
              page_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              total_ = input.readInt32();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                dto_ = new java.util.ArrayList<com.yorha.proto.StructMsg.RankInfoDTO>();
                mutable_bitField0_ |= 0x00000004;
              }
              dto_.add(
                  input.readMessage(com.yorha.proto.StructMsg.RankInfoDTO.PARSER, extensionRegistry));
              break;
            }
            case 42: {
              com.yorha.proto.StructMsg.RankInfoDTO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = myRankInfoDTO_.toBuilder();
              }
              myRankInfoDTO_ = input.readMessage(com.yorha.proto.StructMsg.RankInfoDTO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(myRankInfoDTO_);
                myRankInfoDTO_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000008;
              maxRank_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000010;
              rankId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          dto_ = java.util.Collections.unmodifiableList(dto_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C.class, com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int PAGE_FIELD_NUMBER = 2;
    private int page_;
    /**
     * <pre>
     * page从1开始算的，填0或者负数的话，一页都不会发，但是下面的myRankInfoDTO会照常下发
     * </pre>
     *
     * <code>optional int32 page = 2;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * page从1开始算的，填0或者负数的话，一页都不会发，但是下面的myRankInfoDTO会照常下发
     * </pre>
     *
     * <code>optional int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int TOTAL_FIELD_NUMBER = 3;
    private int total_;
    /**
     * <pre>
     * 能拉取到的排行榜总个数
     * </pre>
     *
     * <code>optional int32 total = 3;</code>
     * @return Whether the total field is set.
     */
    @java.lang.Override
    public boolean hasTotal() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 能拉取到的排行榜总个数
     * </pre>
     *
     * <code>optional int32 total = 3;</code>
     * @return The total.
     */
    @java.lang.Override
    public int getTotal() {
      return total_;
    }

    public static final int DTO_FIELD_NUMBER = 4;
    private java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> dto_;
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> getDtoList() {
      return dto_;
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
        getDtoOrBuilderList() {
      return dto_;
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public int getDtoCount() {
      return dto_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTO getDto(int index) {
      return dto_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
        int index) {
      return dto_.get(index);
    }

    public static final int MYRANKINFODTO_FIELD_NUMBER = 5;
    private com.yorha.proto.StructMsg.RankInfoDTO myRankInfoDTO_;
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     * @return Whether the myRankInfoDTO field is set.
     */
    @java.lang.Override
    public boolean hasMyRankInfoDTO() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     * @return The myRankInfoDTO.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTO getMyRankInfoDTO() {
      return myRankInfoDTO_ == null ? com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance() : myRankInfoDTO_;
    }
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getMyRankInfoDTOOrBuilder() {
      return myRankInfoDTO_ == null ? com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance() : myRankInfoDTO_;
    }

    public static final int MAXRANK_FIELD_NUMBER = 6;
    private int maxRank_;
    /**
     * <code>optional int32 maxRank = 6;</code>
     * @return Whether the maxRank field is set.
     */
    @java.lang.Override
    public boolean hasMaxRank() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 maxRank = 6;</code>
     * @return The maxRank.
     */
    @java.lang.Override
    public int getMaxRank() {
      return maxRank_;
    }

    public static final int RANKID_FIELD_NUMBER = 7;
    private int rankId_;
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>optional int32 rankId = 7;</code>
     * @return Whether the rankId field is set.
     */
    @java.lang.Override
    public boolean hasRankId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 排行榜的唯一id
     * </pre>
     *
     * <code>optional int32 rankId = 7;</code>
     * @return The rankId.
     */
    @java.lang.Override
    public int getRankId() {
      return rankId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(2, page_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, total_);
      }
      for (int i = 0; i < dto_.size(); i++) {
        output.writeMessage(4, dto_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(5, getMyRankInfoDTO());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(6, maxRank_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(7, rankId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, page_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, total_);
      }
      for (int i = 0; i < dto_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, dto_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getMyRankInfoDTO());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, maxRank_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, rankId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C other = (com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C) obj;

      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (getPage()
            != other.getPage()) return false;
      }
      if (hasTotal() != other.hasTotal()) return false;
      if (hasTotal()) {
        if (getTotal()
            != other.getTotal()) return false;
      }
      if (!getDtoList()
          .equals(other.getDtoList())) return false;
      if (hasMyRankInfoDTO() != other.hasMyRankInfoDTO()) return false;
      if (hasMyRankInfoDTO()) {
        if (!getMyRankInfoDTO()
            .equals(other.getMyRankInfoDTO())) return false;
      }
      if (hasMaxRank() != other.hasMaxRank()) return false;
      if (hasMaxRank()) {
        if (getMaxRank()
            != other.getMaxRank()) return false;
      }
      if (hasRankId() != other.hasRankId()) return false;
      if (hasRankId()) {
        if (getRankId()
            != other.getRankId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage();
      }
      if (hasTotal()) {
        hash = (37 * hash) + TOTAL_FIELD_NUMBER;
        hash = (53 * hash) + getTotal();
      }
      if (getDtoCount() > 0) {
        hash = (37 * hash) + DTO_FIELD_NUMBER;
        hash = (53 * hash) + getDtoList().hashCode();
      }
      if (hasMyRankInfoDTO()) {
        hash = (37 * hash) + MYRANKINFODTO_FIELD_NUMBER;
        hash = (53 * hash) + getMyRankInfoDTO().hashCode();
      }
      if (hasMaxRank()) {
        hash = (37 * hash) + MAXRANK_FIELD_NUMBER;
        hash = (53 * hash) + getMaxRank();
      }
      if (hasRankId()) {
        hash = (37 * hash) + RANKID_FIELD_NUMBER;
        hash = (53 * hash) + getRankId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetRankPageInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetRankPageInfo_S2C)
        com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C.class, com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDtoFieldBuilder();
          getMyRankInfoDTOFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        page_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        total_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (dtoBuilder_ == null) {
          dto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          dtoBuilder_.clear();
        }
        if (myRankInfoDTOBuilder_ == null) {
          myRankInfoDTO_ = null;
        } else {
          myRankInfoDTOBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        maxRank_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        rankId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerRank.internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C build() {
        com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C buildPartial() {
        com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C result = new com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.page_ = page_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.total_ = total_;
          to_bitField0_ |= 0x00000002;
        }
        if (dtoBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            dto_ = java.util.Collections.unmodifiableList(dto_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.dto_ = dto_;
        } else {
          result.dto_ = dtoBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (myRankInfoDTOBuilder_ == null) {
            result.myRankInfoDTO_ = myRankInfoDTO_;
          } else {
            result.myRankInfoDTO_ = myRankInfoDTOBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.maxRank_ = maxRank_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.rankId_ = rankId_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C other) {
        if (other == com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C.getDefaultInstance()) return this;
        if (other.hasPage()) {
          setPage(other.getPage());
        }
        if (other.hasTotal()) {
          setTotal(other.getTotal());
        }
        if (dtoBuilder_ == null) {
          if (!other.dto_.isEmpty()) {
            if (dto_.isEmpty()) {
              dto_ = other.dto_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureDtoIsMutable();
              dto_.addAll(other.dto_);
            }
            onChanged();
          }
        } else {
          if (!other.dto_.isEmpty()) {
            if (dtoBuilder_.isEmpty()) {
              dtoBuilder_.dispose();
              dtoBuilder_ = null;
              dto_ = other.dto_;
              bitField0_ = (bitField0_ & ~0x00000004);
              dtoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDtoFieldBuilder() : null;
            } else {
              dtoBuilder_.addAllMessages(other.dto_);
            }
          }
        }
        if (other.hasMyRankInfoDTO()) {
          mergeMyRankInfoDTO(other.getMyRankInfoDTO());
        }
        if (other.hasMaxRank()) {
          setMaxRank(other.getMaxRank());
        }
        if (other.hasRankId()) {
          setRankId(other.getRankId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int page_ ;
      /**
       * <pre>
       * page从1开始算的，填0或者负数的话，一页都不会发，但是下面的myRankInfoDTO会照常下发
       * </pre>
       *
       * <code>optional int32 page = 2;</code>
       * @return Whether the page field is set.
       */
      @java.lang.Override
      public boolean hasPage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * page从1开始算的，填0或者负数的话，一页都不会发，但是下面的myRankInfoDTO会照常下发
       * </pre>
       *
       * <code>optional int32 page = 2;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       * page从1开始算的，填0或者负数的话，一页都不会发，但是下面的myRankInfoDTO会照常下发
       * </pre>
       *
       * <code>optional int32 page = 2;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        bitField0_ |= 0x00000001;
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * page从1开始算的，填0或者负数的话，一页都不会发，但是下面的myRankInfoDTO会照常下发
       * </pre>
       *
       * <code>optional int32 page = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        onChanged();
        return this;
      }

      private int total_ ;
      /**
       * <pre>
       * 能拉取到的排行榜总个数
       * </pre>
       *
       * <code>optional int32 total = 3;</code>
       * @return Whether the total field is set.
       */
      @java.lang.Override
      public boolean hasTotal() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 能拉取到的排行榜总个数
       * </pre>
       *
       * <code>optional int32 total = 3;</code>
       * @return The total.
       */
      @java.lang.Override
      public int getTotal() {
        return total_;
      }
      /**
       * <pre>
       * 能拉取到的排行榜总个数
       * </pre>
       *
       * <code>optional int32 total = 3;</code>
       * @param value The total to set.
       * @return This builder for chaining.
       */
      public Builder setTotal(int value) {
        bitField0_ |= 0x00000002;
        total_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 能拉取到的排行榜总个数
       * </pre>
       *
       * <code>optional int32 total = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotal() {
        bitField0_ = (bitField0_ & ~0x00000002);
        total_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> dto_ =
        java.util.Collections.emptyList();
      private void ensureDtoIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          dto_ = new java.util.ArrayList<com.yorha.proto.StructMsg.RankInfoDTO>(dto_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> dtoBuilder_;

      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> getDtoList() {
        if (dtoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dto_);
        } else {
          return dtoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public int getDtoCount() {
        if (dtoBuilder_ == null) {
          return dto_.size();
        } else {
          return dtoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO getDto(int index) {
        if (dtoBuilder_ == null) {
          return dto_.get(index);
        } else {
          return dtoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder setDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.set(index, value);
          onChanged();
        } else {
          dtoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder setDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.set(index, builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addDto(com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.add(value);
          onChanged();
        } else {
          dtoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.add(index, value);
          onChanged();
        } else {
          dtoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addDto(
          com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.add(builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.add(index, builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addAllDto(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.RankInfoDTO> values) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dto_);
          onChanged();
        } else {
          dtoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder clearDto() {
        if (dtoBuilder_ == null) {
          dto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          dtoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder removeDto(int index) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.remove(index);
          onChanged();
        } else {
          dtoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder getDtoBuilder(
          int index) {
        return getDtoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
          int index) {
        if (dtoBuilder_ == null) {
          return dto_.get(index);  } else {
          return dtoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
           getDtoOrBuilderList() {
        if (dtoBuilder_ != null) {
          return dtoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dto_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder addDtoBuilder() {
        return getDtoFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder addDtoBuilder(
          int index) {
        return getDtoFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO.Builder> 
           getDtoBuilderList() {
        return getDtoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
          getDtoFieldBuilder() {
        if (dtoBuilder_ == null) {
          dtoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder>(
                  dto_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          dto_ = null;
        }
        return dtoBuilder_;
      }

      private com.yorha.proto.StructMsg.RankInfoDTO myRankInfoDTO_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> myRankInfoDTOBuilder_;
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       * @return Whether the myRankInfoDTO field is set.
       */
      public boolean hasMyRankInfoDTO() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       * @return The myRankInfoDTO.
       */
      public com.yorha.proto.StructMsg.RankInfoDTO getMyRankInfoDTO() {
        if (myRankInfoDTOBuilder_ == null) {
          return myRankInfoDTO_ == null ? com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance() : myRankInfoDTO_;
        } else {
          return myRankInfoDTOBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public Builder setMyRankInfoDTO(com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (myRankInfoDTOBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          myRankInfoDTO_ = value;
          onChanged();
        } else {
          myRankInfoDTOBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public Builder setMyRankInfoDTO(
          com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (myRankInfoDTOBuilder_ == null) {
          myRankInfoDTO_ = builderForValue.build();
          onChanged();
        } else {
          myRankInfoDTOBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public Builder mergeMyRankInfoDTO(com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (myRankInfoDTOBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              myRankInfoDTO_ != null &&
              myRankInfoDTO_ != com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance()) {
            myRankInfoDTO_ =
              com.yorha.proto.StructMsg.RankInfoDTO.newBuilder(myRankInfoDTO_).mergeFrom(value).buildPartial();
          } else {
            myRankInfoDTO_ = value;
          }
          onChanged();
        } else {
          myRankInfoDTOBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public Builder clearMyRankInfoDTO() {
        if (myRankInfoDTOBuilder_ == null) {
          myRankInfoDTO_ = null;
          onChanged();
        } else {
          myRankInfoDTOBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder getMyRankInfoDTOBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getMyRankInfoDTOFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getMyRankInfoDTOOrBuilder() {
        if (myRankInfoDTOBuilder_ != null) {
          return myRankInfoDTOBuilder_.getMessageOrBuilder();
        } else {
          return myRankInfoDTO_ == null ?
              com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance() : myRankInfoDTO_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
          getMyRankInfoDTOFieldBuilder() {
        if (myRankInfoDTOBuilder_ == null) {
          myRankInfoDTOBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder>(
                  getMyRankInfoDTO(),
                  getParentForChildren(),
                  isClean());
          myRankInfoDTO_ = null;
        }
        return myRankInfoDTOBuilder_;
      }

      private int maxRank_ ;
      /**
       * <code>optional int32 maxRank = 6;</code>
       * @return Whether the maxRank field is set.
       */
      @java.lang.Override
      public boolean hasMaxRank() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 maxRank = 6;</code>
       * @return The maxRank.
       */
      @java.lang.Override
      public int getMaxRank() {
        return maxRank_;
      }
      /**
       * <code>optional int32 maxRank = 6;</code>
       * @param value The maxRank to set.
       * @return This builder for chaining.
       */
      public Builder setMaxRank(int value) {
        bitField0_ |= 0x00000010;
        maxRank_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 maxRank = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxRank() {
        bitField0_ = (bitField0_ & ~0x00000010);
        maxRank_ = 0;
        onChanged();
        return this;
      }

      private int rankId_ ;
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>optional int32 rankId = 7;</code>
       * @return Whether the rankId field is set.
       */
      @java.lang.Override
      public boolean hasRankId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>optional int32 rankId = 7;</code>
       * @return The rankId.
       */
      @java.lang.Override
      public int getRankId() {
        return rankId_;
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>optional int32 rankId = 7;</code>
       * @param value The rankId to set.
       * @return This builder for chaining.
       */
      public Builder setRankId(int value) {
        bitField0_ |= 0x00000020;
        rankId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜的唯一id
       * </pre>
       *
       * <code>optional int32 rankId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        rankId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetRankPageInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetRankPageInfo_S2C)
    private static final com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C();
    }

    public static com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetRankPageInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetRankPageInfo_S2C>() {
      @java.lang.Override
      public Player_GetRankPageInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetRankPageInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetRankPageInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetRankPageInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerRank.Player_GetRankPageInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n(ss_proto/gen/player/cs/player_rank.pro" +
      "to\022\017com.yorha.proto\032$ss_proto/gen/common" +
      "/struct_msg.proto\"/\n\031Player_GetTopRankIn" +
      "fo_C2S\022\022\n\nrankIdList\030\002 \003(\005\"F\n\031Player_Get" +
      "TopRankInfo_S2C\022)\n\003dto\030\001 \003(\0132\034.com.yorha" +
      ".proto.RankInfoDTO\":\n\032Player_GetRankPage" +
      "Info_C2S\022\014\n\004page\030\002 \001(\005\022\016\n\006rankId\030\003 \001(\005\"\272" +
      "\001\n\032Player_GetRankPageInfo_S2C\022\014\n\004page\030\002 " +
      "\001(\005\022\r\n\005total\030\003 \001(\005\022)\n\003dto\030\004 \003(\0132\034.com.yo" +
      "rha.proto.RankInfoDTO\0223\n\rmyRankInfoDTO\030\005" +
      " \001(\0132\034.com.yorha.proto.RankInfoDTO\022\017\n\007ma" +
      "xRank\030\006 \001(\005\022\016\n\006rankId\030\007 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetTopRankInfo_C2S_descriptor,
        new java.lang.String[] { "RankIdList", });
    internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetTopRankInfo_S2C_descriptor,
        new java.lang.String[] { "Dto", });
    internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetRankPageInfo_C2S_descriptor,
        new java.lang.String[] { "Page", "RankId", });
    internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetRankPageInfo_S2C_descriptor,
        new java.lang.String[] { "Page", "Total", "Dto", "MyRankInfoDTO", "MaxRank", "RankId", });
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

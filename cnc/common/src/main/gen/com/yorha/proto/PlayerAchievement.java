// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_achievement.proto

package com.yorha.proto;

public final class PlayerAchievement {
  private PlayerAchievement() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_FetchAchievement_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchAchievement_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 成就类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.AchievementType achievementType = 1;</code>
     * @return Whether the achievementType field is set.
     */
    boolean hasAchievementType();
    /**
     * <pre>
     * 成就类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.AchievementType achievementType = 1;</code>
     * @return The achievementType.
     */
    com.yorha.proto.CommonEnum.AchievementType getAchievementType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchAchievement_C2S}
   */
  public static final class Player_FetchAchievement_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchAchievement_C2S)
      Player_FetchAchievement_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchAchievement_C2S.newBuilder() to construct.
    private Player_FetchAchievement_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchAchievement_C2S() {
      achievementType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchAchievement_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchAchievement_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.AchievementType value = com.yorha.proto.CommonEnum.AchievementType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                achievementType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S.class, com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ACHIEVEMENTTYPE_FIELD_NUMBER = 1;
    private int achievementType_;
    /**
     * <pre>
     * 成就类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.AchievementType achievementType = 1;</code>
     * @return Whether the achievementType field is set.
     */
    @java.lang.Override public boolean hasAchievementType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 成就类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.AchievementType achievementType = 1;</code>
     * @return The achievementType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.AchievementType getAchievementType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.AchievementType result = com.yorha.proto.CommonEnum.AchievementType.valueOf(achievementType_);
      return result == null ? com.yorha.proto.CommonEnum.AchievementType.ACHT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, achievementType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, achievementType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S other = (com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S) obj;

      if (hasAchievementType() != other.hasAchievementType()) return false;
      if (hasAchievementType()) {
        if (achievementType_ != other.achievementType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAchievementType()) {
        hash = (37 * hash) + ACHIEVEMENTTYPE_FIELD_NUMBER;
        hash = (53 * hash) + achievementType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchAchievement_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchAchievement_C2S)
        com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S.class, com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        achievementType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S build() {
        com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S buildPartial() {
        com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S result = new com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.achievementType_ = achievementType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S) {
          return mergeFrom((com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S other) {
        if (other == com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S.getDefaultInstance()) return this;
        if (other.hasAchievementType()) {
          setAchievementType(other.getAchievementType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int achievementType_ = 0;
      /**
       * <pre>
       * 成就类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.AchievementType achievementType = 1;</code>
       * @return Whether the achievementType field is set.
       */
      @java.lang.Override public boolean hasAchievementType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 成就类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.AchievementType achievementType = 1;</code>
       * @return The achievementType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.AchievementType getAchievementType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.AchievementType result = com.yorha.proto.CommonEnum.AchievementType.valueOf(achievementType_);
        return result == null ? com.yorha.proto.CommonEnum.AchievementType.ACHT_NONE : result;
      }
      /**
       * <pre>
       * 成就类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.AchievementType achievementType = 1;</code>
       * @param value The achievementType to set.
       * @return This builder for chaining.
       */
      public Builder setAchievementType(com.yorha.proto.CommonEnum.AchievementType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        achievementType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成就类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.AchievementType achievementType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAchievementType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        achievementType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchAchievement_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchAchievement_C2S)
    private static final com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S();
    }

    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchAchievement_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchAchievement_C2S>() {
      @java.lang.Override
      public Player_FetchAchievement_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchAchievement_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchAchievement_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchAchievement_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAchievement.Player_FetchAchievement_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchAchievement_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchAchievement_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */
    int getAchievementsCount();
    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */
    boolean containsAchievements(
        int key);
    /**
     * Use {@link #getAchievementsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>
    getAchievements();
    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>
    getAchievementsMap();
    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */

    com.yorha.proto.StructPB.AchievementPB getAchievementsOrDefault(
        int key,
        com.yorha.proto.StructPB.AchievementPB defaultValue);
    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */

    com.yorha.proto.StructPB.AchievementPB getAchievementsOrThrow(
        int key);

    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */
    int getDemandsCount();
    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */
    boolean containsDemands(
        int key);
    /**
     * Use {@link #getDemandsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Long>
    getDemands();
    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Long>
    getDemandsMap();
    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */

    long getDemandsOrDefault(
        int key,
        long defaultValue);
    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */

    long getDemandsOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchAchievement_S2C}
   */
  public static final class Player_FetchAchievement_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchAchievement_S2C)
      Player_FetchAchievement_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchAchievement_S2C.newBuilder() to construct.
    private Player_FetchAchievement_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchAchievement_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchAchievement_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchAchievement_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                achievements_ = com.google.protobuf.MapField.newMapField(
                    AchievementsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>
              achievements__ = input.readMessage(
                  AchievementsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              achievements_.getMutableMap().put(
                  achievements__.getKey(), achievements__.getValue());
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                demands_ = com.google.protobuf.MapField.newMapField(
                    DemandsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
              demands__ = input.readMessage(
                  DemandsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              demands_.getMutableMap().put(
                  demands__.getKey(), demands__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetAchievements();
        case 3:
          return internalGetDemands();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C.class, com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C.Builder.class);
    }

    public static final int ACHIEVEMENTS_FIELD_NUMBER = 1;
    private static final class AchievementsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>newDefaultInstance(
                  com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_S2C_AchievementsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructPB.AchievementPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> achievements_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>
    internalGetAchievements() {
      if (achievements_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            AchievementsDefaultEntryHolder.defaultEntry);
      }
      return achievements_;
    }

    public int getAchievementsCount() {
      return internalGetAchievements().getMap().size();
    }
    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */

    @java.lang.Override
    public boolean containsAchievements(
        int key) {
      
      return internalGetAchievements().getMap().containsKey(key);
    }
    /**
     * Use {@link #getAchievementsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> getAchievements() {
      return getAchievementsMap();
    }
    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> getAchievementsMap() {
      return internalGetAchievements().getMap();
    }
    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.AchievementPB getAchievementsOrDefault(
        int key,
        com.yorha.proto.StructPB.AchievementPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> map =
          internalGetAchievements().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 成就信息
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.AchievementPB getAchievementsOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> map =
          internalGetAchievements().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DEMANDS_FIELD_NUMBER = 3;
    private static final class DemandsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Long>newDefaultInstance(
                  com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_S2C_DemandsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Long> demands_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
    internalGetDemands() {
      if (demands_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DemandsDefaultEntryHolder.defaultEntry);
      }
      return demands_;
    }

    public int getDemandsCount() {
      return internalGetDemands().getMap().size();
    }
    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */

    @java.lang.Override
    public boolean containsDemands(
        int key) {
      
      return internalGetDemands().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDemandsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Long> getDemands() {
      return getDemandsMap();
    }
    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Long> getDemandsMap() {
      return internalGetDemands().getMap();
    }
    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */
    @java.lang.Override

    public long getDemandsOrDefault(
        int key,
        long defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetDemands().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 成就需求数值
     * </pre>
     *
     * <code>map&lt;int32, int64&gt; demands = 3;</code>
     */
    @java.lang.Override

    public long getDemandsOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetDemands().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetAchievements(),
          AchievementsDefaultEntryHolder.defaultEntry,
          1);
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDemands(),
          DemandsDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> entry
           : internalGetAchievements().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>
        achievements__ = AchievementsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, achievements__);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Long> entry
           : internalGetDemands().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
        demands__ = DemandsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, demands__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C other = (com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C) obj;

      if (!internalGetAchievements().equals(
          other.internalGetAchievements())) return false;
      if (!internalGetDemands().equals(
          other.internalGetDemands())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetAchievements().getMap().isEmpty()) {
        hash = (37 * hash) + ACHIEVEMENTS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetAchievements().hashCode();
      }
      if (!internalGetDemands().getMap().isEmpty()) {
        hash = (37 * hash) + DEMANDS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDemands().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchAchievement_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchAchievement_S2C)
        com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetAchievements();
          case 3:
            return internalGetDemands();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableAchievements();
          case 3:
            return internalGetMutableDemands();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C.class, com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableAchievements().clear();
        internalGetMutableDemands().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_FetchAchievement_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C build() {
        com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C buildPartial() {
        com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C result = new com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C(this);
        int from_bitField0_ = bitField0_;
        result.achievements_ = internalGetAchievements();
        result.achievements_.makeImmutable();
        result.demands_ = internalGetDemands();
        result.demands_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C) {
          return mergeFrom((com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C other) {
        if (other == com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C.getDefaultInstance()) return this;
        internalGetMutableAchievements().mergeFrom(
            other.internalGetAchievements());
        internalGetMutableDemands().mergeFrom(
            other.internalGetDemands());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> achievements_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>
      internalGetAchievements() {
        if (achievements_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              AchievementsDefaultEntryHolder.defaultEntry);
        }
        return achievements_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>
      internalGetMutableAchievements() {
        onChanged();;
        if (achievements_ == null) {
          achievements_ = com.google.protobuf.MapField.newMapField(
              AchievementsDefaultEntryHolder.defaultEntry);
        }
        if (!achievements_.isMutable()) {
          achievements_ = achievements_.copy();
        }
        return achievements_;
      }

      public int getAchievementsCount() {
        return internalGetAchievements().getMap().size();
      }
      /**
       * <pre>
       * 成就信息
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
       */

      @java.lang.Override
      public boolean containsAchievements(
          int key) {
        
        return internalGetAchievements().getMap().containsKey(key);
      }
      /**
       * Use {@link #getAchievementsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> getAchievements() {
        return getAchievementsMap();
      }
      /**
       * <pre>
       * 成就信息
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> getAchievementsMap() {
        return internalGetAchievements().getMap();
      }
      /**
       * <pre>
       * 成就信息
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.AchievementPB getAchievementsOrDefault(
          int key,
          com.yorha.proto.StructPB.AchievementPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> map =
            internalGetAchievements().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 成就信息
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.AchievementPB getAchievementsOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> map =
            internalGetAchievements().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearAchievements() {
        internalGetMutableAchievements().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 成就信息
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
       */

      public Builder removeAchievements(
          int key) {
        
        internalGetMutableAchievements().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB>
      getMutableAchievements() {
        return internalGetMutableAchievements().getMutableMap();
      }
      /**
       * <pre>
       * 成就信息
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
       */
      public Builder putAchievements(
          int key,
          com.yorha.proto.StructPB.AchievementPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableAchievements().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 成就信息
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.AchievementPB&gt; achievements = 1;</code>
       */

      public Builder putAllAchievements(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.AchievementPB> values) {
        internalGetMutableAchievements().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Long> demands_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetDemands() {
        if (demands_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DemandsDefaultEntryHolder.defaultEntry);
        }
        return demands_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetMutableDemands() {
        onChanged();;
        if (demands_ == null) {
          demands_ = com.google.protobuf.MapField.newMapField(
              DemandsDefaultEntryHolder.defaultEntry);
        }
        if (!demands_.isMutable()) {
          demands_ = demands_.copy();
        }
        return demands_;
      }

      public int getDemandsCount() {
        return internalGetDemands().getMap().size();
      }
      /**
       * <pre>
       * 成就需求数值
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; demands = 3;</code>
       */

      @java.lang.Override
      public boolean containsDemands(
          int key) {
        
        return internalGetDemands().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDemandsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long> getDemands() {
        return getDemandsMap();
      }
      /**
       * <pre>
       * 成就需求数值
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; demands = 3;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Long> getDemandsMap() {
        return internalGetDemands().getMap();
      }
      /**
       * <pre>
       * 成就需求数值
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; demands = 3;</code>
       */
      @java.lang.Override

      public long getDemandsOrDefault(
          int key,
          long defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetDemands().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 成就需求数值
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; demands = 3;</code>
       */
      @java.lang.Override

      public long getDemandsOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetDemands().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDemands() {
        internalGetMutableDemands().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 成就需求数值
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; demands = 3;</code>
       */

      public Builder removeDemands(
          int key) {
        
        internalGetMutableDemands().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long>
      getMutableDemands() {
        return internalGetMutableDemands().getMutableMap();
      }
      /**
       * <pre>
       * 成就需求数值
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; demands = 3;</code>
       */
      public Builder putDemands(
          int key,
          long value) {
        
        
        internalGetMutableDemands().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 成就需求数值
       * </pre>
       *
       * <code>map&lt;int32, int64&gt; demands = 3;</code>
       */

      public Builder putAllDemands(
          java.util.Map<java.lang.Integer, java.lang.Long> values) {
        internalGetMutableDemands().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchAchievement_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchAchievement_S2C)
    private static final com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C();
    }

    public static com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchAchievement_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchAchievement_S2C>() {
      @java.lang.Override
      public Player_FetchAchievement_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchAchievement_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchAchievement_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchAchievement_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAchievement.Player_FetchAchievement_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_TakeAchievementReward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TakeAchievementReward_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 成就id
     * </pre>
     *
     * <code>optional int32 achievementId = 1;</code>
     * @return Whether the achievementId field is set.
     */
    boolean hasAchievementId();
    /**
     * <pre>
     * 成就id
     * </pre>
     *
     * <code>optional int32 achievementId = 1;</code>
     * @return The achievementId.
     */
    int getAchievementId();

    /**
     * <pre>
     * 成就类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.AchievementType type = 2;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <pre>
     * 成就类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.AchievementType type = 2;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.AchievementType getType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TakeAchievementReward_C2S}
   */
  public static final class Player_TakeAchievementReward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TakeAchievementReward_C2S)
      Player_TakeAchievementReward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TakeAchievementReward_C2S.newBuilder() to construct.
    private Player_TakeAchievementReward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TakeAchievementReward_C2S() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TakeAchievementReward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TakeAchievementReward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              achievementId_ = input.readInt32();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.AchievementType value = com.yorha.proto.CommonEnum.AchievementType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                type_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S.class, com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ACHIEVEMENTID_FIELD_NUMBER = 1;
    private int achievementId_;
    /**
     * <pre>
     * 成就id
     * </pre>
     *
     * <code>optional int32 achievementId = 1;</code>
     * @return Whether the achievementId field is set.
     */
    @java.lang.Override
    public boolean hasAchievementId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 成就id
     * </pre>
     *
     * <code>optional int32 achievementId = 1;</code>
     * @return The achievementId.
     */
    @java.lang.Override
    public int getAchievementId() {
      return achievementId_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     * 成就类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.AchievementType type = 2;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 成就类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.AchievementType type = 2;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.AchievementType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.AchievementType result = com.yorha.proto.CommonEnum.AchievementType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.AchievementType.ACHT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, achievementId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, achievementId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S other = (com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S) obj;

      if (hasAchievementId() != other.hasAchievementId()) return false;
      if (hasAchievementId()) {
        if (getAchievementId()
            != other.getAchievementId()) return false;
      }
      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAchievementId()) {
        hash = (37 * hash) + ACHIEVEMENTID_FIELD_NUMBER;
        hash = (53 * hash) + getAchievementId();
      }
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TakeAchievementReward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TakeAchievementReward_C2S)
        com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S.class, com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        achievementId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S build() {
        com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S buildPartial() {
        com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S result = new com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.achievementId_ = achievementId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.type_ = type_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S other) {
        if (other == com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S.getDefaultInstance()) return this;
        if (other.hasAchievementId()) {
          setAchievementId(other.getAchievementId());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int achievementId_ ;
      /**
       * <pre>
       * 成就id
       * </pre>
       *
       * <code>optional int32 achievementId = 1;</code>
       * @return Whether the achievementId field is set.
       */
      @java.lang.Override
      public boolean hasAchievementId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 成就id
       * </pre>
       *
       * <code>optional int32 achievementId = 1;</code>
       * @return The achievementId.
       */
      @java.lang.Override
      public int getAchievementId() {
        return achievementId_;
      }
      /**
       * <pre>
       * 成就id
       * </pre>
       *
       * <code>optional int32 achievementId = 1;</code>
       * @param value The achievementId to set.
       * @return This builder for chaining.
       */
      public Builder setAchievementId(int value) {
        bitField0_ |= 0x00000001;
        achievementId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成就id
       * </pre>
       *
       * <code>optional int32 achievementId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAchievementId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        achievementId_ = 0;
        onChanged();
        return this;
      }

      private int type_ = 0;
      /**
       * <pre>
       * 成就类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.AchievementType type = 2;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 成就类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.AchievementType type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.AchievementType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.AchievementType result = com.yorha.proto.CommonEnum.AchievementType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.AchievementType.ACHT_NONE : result;
      }
      /**
       * <pre>
       * 成就类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.AchievementType type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.AchievementType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成就类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.AchievementType type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TakeAchievementReward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TakeAchievementReward_C2S)
    private static final com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S();
    }

    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TakeAchievementReward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_TakeAchievementReward_C2S>() {
      @java.lang.Override
      public Player_TakeAchievementReward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TakeAchievementReward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TakeAchievementReward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TakeAchievementReward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_TakeAchievementReward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TakeAchievementReward_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    boolean hasReward();
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    com.yorha.proto.StructPB.YoAssetPackagePB getReward();
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TakeAchievementReward_S2C}
   */
  public static final class Player_TakeAchievementReward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TakeAchievementReward_S2C)
      Player_TakeAchievementReward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TakeAchievementReward_S2C.newBuilder() to construct.
    private Player_TakeAchievementReward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TakeAchievementReward_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TakeAchievementReward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TakeAchievementReward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.YoAssetPackagePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = reward_.toBuilder();
              }
              reward_ = input.readMessage(com.yorha.proto.StructPB.YoAssetPackagePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(reward_);
                reward_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C.class, com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int REWARD_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    @java.lang.Override
    public boolean hasReward() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }
    /**
     * <pre>
     * 奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getReward());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getReward());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C other = (com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C) obj;

      if (hasReward() != other.hasReward()) return false;
      if (hasReward()) {
        if (!getReward()
            .equals(other.getReward())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReward()) {
        hash = (37 * hash) + REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getReward().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TakeAchievementReward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TakeAchievementReward_S2C)
        com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C.class, com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardBuilder_ == null) {
          reward_ = null;
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C build() {
        com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C buildPartial() {
        com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C result = new com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (rewardBuilder_ == null) {
            result.reward_ = reward_;
          } else {
            result.reward_ = rewardBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C other) {
        if (other == com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C.getDefaultInstance()) return this;
        if (other.hasReward()) {
          mergeReward(other.getReward());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> rewardBuilder_;
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return Whether the reward field is set.
       */
      public boolean hasReward() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return The reward.
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
        if (rewardBuilder_ == null) {
          return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        } else {
          return rewardBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          reward_ = value;
          onChanged();
        } else {
          rewardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(
          com.yorha.proto.StructPB.YoAssetPackagePB.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          reward_ = builderForValue.build();
          onChanged();
        } else {
          rewardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder mergeReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              reward_ != null &&
              reward_ != com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance()) {
            reward_ =
              com.yorha.proto.StructPB.YoAssetPackagePB.newBuilder(reward_).mergeFrom(value).buildPartial();
          } else {
            reward_ = value;
          }
          onChanged();
        } else {
          rewardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder clearReward() {
        if (rewardBuilder_ == null) {
          reward_ = null;
          onChanged();
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB.Builder getRewardBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRewardFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
        if (rewardBuilder_ != null) {
          return rewardBuilder_.getMessageOrBuilder();
        } else {
          return reward_ == null ?
              com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        }
      }
      /**
       * <pre>
       * 奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> 
          getRewardFieldBuilder() {
        if (rewardBuilder_ == null) {
          rewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder>(
                  getReward(),
                  getParentForChildren(),
                  isClean());
          reward_ = null;
        }
        return rewardBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TakeAchievementReward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TakeAchievementReward_S2C)
    private static final com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C();
    }

    public static com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TakeAchievementReward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_TakeAchievementReward_S2C>() {
      @java.lang.Override
      public Player_TakeAchievementReward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TakeAchievementReward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TakeAchievementReward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TakeAchievementReward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAchievement.Player_TakeAchievementReward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AchievementCompleteNtfOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AchievementCompleteNtf)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int32 achievements = 1;</code>
     * @return A list containing the achievements.
     */
    java.util.List<java.lang.Integer> getAchievementsList();
    /**
     * <code>repeated int32 achievements = 1;</code>
     * @return The count of achievements.
     */
    int getAchievementsCount();
    /**
     * <code>repeated int32 achievements = 1;</code>
     * @param index The index of the element to return.
     * @return The achievements at the given index.
     */
    int getAchievements(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.AchievementCompleteNtf}
   */
  public static final class AchievementCompleteNtf extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AchievementCompleteNtf)
      AchievementCompleteNtfOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AchievementCompleteNtf.newBuilder() to construct.
    private AchievementCompleteNtf(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AchievementCompleteNtf() {
      achievements_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AchievementCompleteNtf();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AchievementCompleteNtf(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                achievements_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              achievements_.addInt(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                achievements_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                achievements_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          achievements_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_AchievementCompleteNtf_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_AchievementCompleteNtf_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAchievement.AchievementCompleteNtf.class, com.yorha.proto.PlayerAchievement.AchievementCompleteNtf.Builder.class);
    }

    public static final int ACHIEVEMENTS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.IntList achievements_;
    /**
     * <code>repeated int32 achievements = 1;</code>
     * @return A list containing the achievements.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getAchievementsList() {
      return achievements_;
    }
    /**
     * <code>repeated int32 achievements = 1;</code>
     * @return The count of achievements.
     */
    public int getAchievementsCount() {
      return achievements_.size();
    }
    /**
     * <code>repeated int32 achievements = 1;</code>
     * @param index The index of the element to return.
     * @return The achievements at the given index.
     */
    public int getAchievements(int index) {
      return achievements_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < achievements_.size(); i++) {
        output.writeInt32(1, achievements_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < achievements_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(achievements_.getInt(i));
        }
        size += dataSize;
        size += 1 * getAchievementsList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAchievement.AchievementCompleteNtf)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAchievement.AchievementCompleteNtf other = (com.yorha.proto.PlayerAchievement.AchievementCompleteNtf) obj;

      if (!getAchievementsList()
          .equals(other.getAchievementsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getAchievementsCount() > 0) {
        hash = (37 * hash) + ACHIEVEMENTS_FIELD_NUMBER;
        hash = (53 * hash) + getAchievementsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAchievement.AchievementCompleteNtf prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AchievementCompleteNtf}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AchievementCompleteNtf)
        com.yorha.proto.PlayerAchievement.AchievementCompleteNtfOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_AchievementCompleteNtf_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_AchievementCompleteNtf_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAchievement.AchievementCompleteNtf.class, com.yorha.proto.PlayerAchievement.AchievementCompleteNtf.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAchievement.AchievementCompleteNtf.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        achievements_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_AchievementCompleteNtf_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.AchievementCompleteNtf getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAchievement.AchievementCompleteNtf.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.AchievementCompleteNtf build() {
        com.yorha.proto.PlayerAchievement.AchievementCompleteNtf result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.AchievementCompleteNtf buildPartial() {
        com.yorha.proto.PlayerAchievement.AchievementCompleteNtf result = new com.yorha.proto.PlayerAchievement.AchievementCompleteNtf(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          achievements_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.achievements_ = achievements_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAchievement.AchievementCompleteNtf) {
          return mergeFrom((com.yorha.proto.PlayerAchievement.AchievementCompleteNtf)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAchievement.AchievementCompleteNtf other) {
        if (other == com.yorha.proto.PlayerAchievement.AchievementCompleteNtf.getDefaultInstance()) return this;
        if (!other.achievements_.isEmpty()) {
          if (achievements_.isEmpty()) {
            achievements_ = other.achievements_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAchievementsIsMutable();
            achievements_.addAll(other.achievements_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAchievement.AchievementCompleteNtf parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAchievement.AchievementCompleteNtf) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList achievements_ = emptyIntList();
      private void ensureAchievementsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          achievements_ = mutableCopy(achievements_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int32 achievements = 1;</code>
       * @return A list containing the achievements.
       */
      public java.util.List<java.lang.Integer>
          getAchievementsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(achievements_) : achievements_;
      }
      /**
       * <code>repeated int32 achievements = 1;</code>
       * @return The count of achievements.
       */
      public int getAchievementsCount() {
        return achievements_.size();
      }
      /**
       * <code>repeated int32 achievements = 1;</code>
       * @param index The index of the element to return.
       * @return The achievements at the given index.
       */
      public int getAchievements(int index) {
        return achievements_.getInt(index);
      }
      /**
       * <code>repeated int32 achievements = 1;</code>
       * @param index The index to set the value at.
       * @param value The achievements to set.
       * @return This builder for chaining.
       */
      public Builder setAchievements(
          int index, int value) {
        ensureAchievementsIsMutable();
        achievements_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 achievements = 1;</code>
       * @param value The achievements to add.
       * @return This builder for chaining.
       */
      public Builder addAchievements(int value) {
        ensureAchievementsIsMutable();
        achievements_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 achievements = 1;</code>
       * @param values The achievements to add.
       * @return This builder for chaining.
       */
      public Builder addAllAchievements(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureAchievementsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, achievements_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 achievements = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAchievements() {
        achievements_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AchievementCompleteNtf)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AchievementCompleteNtf)
    private static final com.yorha.proto.PlayerAchievement.AchievementCompleteNtf DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAchievement.AchievementCompleteNtf();
    }

    public static com.yorha.proto.PlayerAchievement.AchievementCompleteNtf getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AchievementCompleteNtf>
        PARSER = new com.google.protobuf.AbstractParser<AchievementCompleteNtf>() {
      @java.lang.Override
      public AchievementCompleteNtf parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AchievementCompleteNtf(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AchievementCompleteNtf> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AchievementCompleteNtf> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAchievement.AchievementCompleteNtf getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ShareAchievement_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ShareAchievement_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 成就id
     * </pre>
     *
     * <code>optional int32 achievementId = 1;</code>
     * @return Whether the achievementId field is set.
     */
    boolean hasAchievementId();
    /**
     * <pre>
     * 成就id
     * </pre>
     *
     * <code>optional int32 achievementId = 1;</code>
     * @return The achievementId.
     */
    int getAchievementId();

    /**
     * <pre>
     * 发送到聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
     * @return Whether the chatSession field is set.
     */
    boolean hasChatSession();
    /**
     * <pre>
     * 发送到聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
     * @return The chatSession.
     */
    com.yorha.proto.CommonMsg.ChatSession getChatSession();
    /**
     * <pre>
     * 发送到聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
     */
    com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ShareAchievement_C2S}
   */
  public static final class Player_ShareAchievement_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ShareAchievement_C2S)
      Player_ShareAchievement_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ShareAchievement_C2S.newBuilder() to construct.
    private Player_ShareAchievement_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ShareAchievement_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ShareAchievement_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ShareAchievement_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              achievementId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.ChatSession.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = chatSession_.toBuilder();
              }
              chatSession_ = input.readMessage(com.yorha.proto.CommonMsg.ChatSession.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatSession_);
                chatSession_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S.class, com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ACHIEVEMENTID_FIELD_NUMBER = 1;
    private int achievementId_;
    /**
     * <pre>
     * 成就id
     * </pre>
     *
     * <code>optional int32 achievementId = 1;</code>
     * @return Whether the achievementId field is set.
     */
    @java.lang.Override
    public boolean hasAchievementId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 成就id
     * </pre>
     *
     * <code>optional int32 achievementId = 1;</code>
     * @return The achievementId.
     */
    @java.lang.Override
    public int getAchievementId() {
      return achievementId_;
    }

    public static final int CHATSESSION_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.ChatSession chatSession_;
    /**
     * <pre>
     * 发送到聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
     * @return Whether the chatSession field is set.
     */
    @java.lang.Override
    public boolean hasChatSession() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 发送到聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
     * @return The chatSession.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatSession getChatSession() {
      return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
    }
    /**
     * <pre>
     * 发送到聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder() {
      return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, achievementId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getChatSession());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, achievementId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getChatSession());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S other = (com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S) obj;

      if (hasAchievementId() != other.hasAchievementId()) return false;
      if (hasAchievementId()) {
        if (getAchievementId()
            != other.getAchievementId()) return false;
      }
      if (hasChatSession() != other.hasChatSession()) return false;
      if (hasChatSession()) {
        if (!getChatSession()
            .equals(other.getChatSession())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAchievementId()) {
        hash = (37 * hash) + ACHIEVEMENTID_FIELD_NUMBER;
        hash = (53 * hash) + getAchievementId();
      }
      if (hasChatSession()) {
        hash = (37 * hash) + CHATSESSION_FIELD_NUMBER;
        hash = (53 * hash) + getChatSession().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ShareAchievement_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ShareAchievement_C2S)
        com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S.class, com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatSessionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        achievementId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (chatSessionBuilder_ == null) {
          chatSession_ = null;
        } else {
          chatSessionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S build() {
        com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S buildPartial() {
        com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S result = new com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.achievementId_ = achievementId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (chatSessionBuilder_ == null) {
            result.chatSession_ = chatSession_;
          } else {
            result.chatSession_ = chatSessionBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S) {
          return mergeFrom((com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S other) {
        if (other == com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S.getDefaultInstance()) return this;
        if (other.hasAchievementId()) {
          setAchievementId(other.getAchievementId());
        }
        if (other.hasChatSession()) {
          mergeChatSession(other.getChatSession());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int achievementId_ ;
      /**
       * <pre>
       * 成就id
       * </pre>
       *
       * <code>optional int32 achievementId = 1;</code>
       * @return Whether the achievementId field is set.
       */
      @java.lang.Override
      public boolean hasAchievementId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 成就id
       * </pre>
       *
       * <code>optional int32 achievementId = 1;</code>
       * @return The achievementId.
       */
      @java.lang.Override
      public int getAchievementId() {
        return achievementId_;
      }
      /**
       * <pre>
       * 成就id
       * </pre>
       *
       * <code>optional int32 achievementId = 1;</code>
       * @param value The achievementId to set.
       * @return This builder for chaining.
       */
      public Builder setAchievementId(int value) {
        bitField0_ |= 0x00000001;
        achievementId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成就id
       * </pre>
       *
       * <code>optional int32 achievementId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAchievementId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        achievementId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.ChatSession chatSession_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder> chatSessionBuilder_;
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       * @return Whether the chatSession field is set.
       */
      public boolean hasChatSession() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       * @return The chatSession.
       */
      public com.yorha.proto.CommonMsg.ChatSession getChatSession() {
        if (chatSessionBuilder_ == null) {
          return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
        } else {
          return chatSessionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       */
      public Builder setChatSession(com.yorha.proto.CommonMsg.ChatSession value) {
        if (chatSessionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatSession_ = value;
          onChanged();
        } else {
          chatSessionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       */
      public Builder setChatSession(
          com.yorha.proto.CommonMsg.ChatSession.Builder builderForValue) {
        if (chatSessionBuilder_ == null) {
          chatSession_ = builderForValue.build();
          onChanged();
        } else {
          chatSessionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       */
      public Builder mergeChatSession(com.yorha.proto.CommonMsg.ChatSession value) {
        if (chatSessionBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              chatSession_ != null &&
              chatSession_ != com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance()) {
            chatSession_ =
              com.yorha.proto.CommonMsg.ChatSession.newBuilder(chatSession_).mergeFrom(value).buildPartial();
          } else {
            chatSession_ = value;
          }
          onChanged();
        } else {
          chatSessionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       */
      public Builder clearChatSession() {
        if (chatSessionBuilder_ == null) {
          chatSession_ = null;
          onChanged();
        } else {
          chatSessionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatSession.Builder getChatSessionBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getChatSessionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder() {
        if (chatSessionBuilder_ != null) {
          return chatSessionBuilder_.getMessageOrBuilder();
        } else {
          return chatSession_ == null ?
              com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
        }
      }
      /**
       * <pre>
       * 发送到聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder> 
          getChatSessionFieldBuilder() {
        if (chatSessionBuilder_ == null) {
          chatSessionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder>(
                  getChatSession(),
                  getParentForChildren(),
                  isClean());
          chatSession_ = null;
        }
        return chatSessionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ShareAchievement_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ShareAchievement_C2S)
    private static final com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S();
    }

    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ShareAchievement_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ShareAchievement_C2S>() {
      @java.lang.Override
      public Player_ShareAchievement_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ShareAchievement_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ShareAchievement_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ShareAchievement_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAchievement.Player_ShareAchievement_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ShareAchievement_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ShareAchievement_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ShareAchievement_S2C}
   */
  public static final class Player_ShareAchievement_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ShareAchievement_S2C)
      Player_ShareAchievement_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ShareAchievement_S2C.newBuilder() to construct.
    private Player_ShareAchievement_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ShareAchievement_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ShareAchievement_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ShareAchievement_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C.class, com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C other = (com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ShareAchievement_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ShareAchievement_S2C)
        com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C.class, com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAchievement.internal_static_com_yorha_proto_Player_ShareAchievement_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C build() {
        com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C buildPartial() {
        com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C result = new com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C) {
          return mergeFrom((com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C other) {
        if (other == com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ShareAchievement_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ShareAchievement_S2C)
    private static final com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C();
    }

    public static com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ShareAchievement_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ShareAchievement_S2C>() {
      @java.lang.Override
      public Player_ShareAchievement_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ShareAchievement_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ShareAchievement_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ShareAchievement_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAchievement.Player_ShareAchievement_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchAchievement_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchAchievement_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchAchievement_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_AchievementsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchAchievement_S2C_AchievementsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_DemandsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchAchievement_S2C_DemandsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AchievementCompleteNtf_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AchievementCompleteNtf_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ShareAchievement_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ShareAchievement_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ShareAchievement_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ShareAchievement_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n/ss_proto/gen/player/cs/player_achievem" +
      "ent.proto\022\017com.yorha.proto\032\"cs_proto/gen" +
      "/common/structPB.proto\032%ss_proto/gen/com" +
      "mon/common_enum.proto\032$ss_proto/gen/comm" +
      "on/common_msg.proto\"X\n\033Player_FetchAchie" +
      "vement_C2S\0229\n\017achievementType\030\001 \001(\0162 .co" +
      "m.yorha.proto.AchievementType\"\304\002\n\033Player" +
      "_FetchAchievement_S2C\022T\n\014achievements\030\001 " +
      "\003(\0132>.com.yorha.proto.Player_FetchAchiev" +
      "ement_S2C.AchievementsEntry\022J\n\007demands\030\003" +
      " \003(\01329.com.yorha.proto.Player_FetchAchie" +
      "vement_S2C.DemandsEntry\032S\n\021AchievementsE" +
      "ntry\022\013\n\003key\030\001 \001(\005\022-\n\005value\030\002 \001(\0132\036.com.y" +
      "orha.proto.AchievementPB:\0028\001\032.\n\014DemandsE" +
      "ntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\003:\0028\001\"i\n " +
      "Player_TakeAchievementReward_C2S\022\025\n\rachi" +
      "evementId\030\001 \001(\005\022.\n\004type\030\002 \001(\0162 .com.yorh" +
      "a.proto.AchievementType\"U\n Player_TakeAc" +
      "hievementReward_S2C\0221\n\006reward\030\001 \001(\0132!.co" +
      "m.yorha.proto.YoAssetPackagePB\".\n\026Achiev" +
      "ementCompleteNtf\022\024\n\014achievements\030\001 \003(\005\"g" +
      "\n\033Player_ShareAchievement_C2S\022\025\n\rachieve" +
      "mentId\030\001 \001(\005\0221\n\013chatSession\030\002 \001(\0132\034.com." +
      "yorha.proto.ChatSession\"\035\n\033Player_ShareA" +
      "chievement_S2CB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_FetchAchievement_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_FetchAchievement_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchAchievement_C2S_descriptor,
        new java.lang.String[] { "AchievementType", });
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchAchievement_S2C_descriptor,
        new java.lang.String[] { "Achievements", "Demands", });
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_AchievementsEntry_descriptor =
      internal_static_com_yorha_proto_Player_FetchAchievement_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_AchievementsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchAchievement_S2C_AchievementsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_DemandsEntry_descriptor =
      internal_static_com_yorha_proto_Player_FetchAchievement_S2C_descriptor.getNestedTypes().get(1);
    internal_static_com_yorha_proto_Player_FetchAchievement_S2C_DemandsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchAchievement_S2C_DemandsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TakeAchievementReward_C2S_descriptor,
        new java.lang.String[] { "AchievementId", "Type", });
    internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TakeAchievementReward_S2C_descriptor,
        new java.lang.String[] { "Reward", });
    internal_static_com_yorha_proto_AchievementCompleteNtf_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_AchievementCompleteNtf_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AchievementCompleteNtf_descriptor,
        new java.lang.String[] { "Achievements", });
    internal_static_com_yorha_proto_Player_ShareAchievement_C2S_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_ShareAchievement_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ShareAchievement_C2S_descriptor,
        new java.lang.String[] { "AchievementId", "ChatSession", });
    internal_static_com_yorha_proto_Player_ShareAchievement_S2C_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_ShareAchievement_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ShareAchievement_S2C_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_clan_store.proto

package com.yorha.proto;

public final class PlayerClanStore {
  private PlayerClanStore() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_FetchClanStore_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanStore_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    boolean hasInterfaceType();
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanStore_C2S}
   */
  public static final class Player_FetchClanStore_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanStore_C2S)
      Player_FetchClanStore_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanStore_C2S.newBuilder() to construct.
    private Player_FetchClanStore_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanStore_C2S() {
      interfaceType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanStore_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanStore_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanStoreInterfaceType value = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                interfaceType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S.class, com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int INTERFACETYPE_FIELD_NUMBER = 1;
    private int interfaceType_;
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    @java.lang.Override public boolean hasInterfaceType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, interfaceType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, interfaceType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S other = (com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S) obj;

      if (hasInterfaceType() != other.hasInterfaceType()) return false;
      if (hasInterfaceType()) {
        if (interfaceType_ != other.interfaceType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInterfaceType()) {
        hash = (37 * hash) + INTERFACETYPE_FIELD_NUMBER;
        hash = (53 * hash) + interfaceType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanStore_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanStore_C2S)
        com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S.class, com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        interfaceType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S build() {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S buildPartial() {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S result = new com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.interfaceType_ = interfaceType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S) {
          return mergeFrom((com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S other) {
        if (other == com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S.getDefaultInstance()) return this;
        if (other.hasInterfaceType()) {
          setInterfaceType(other.getInterfaceType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int interfaceType_ = 0;
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return Whether the interfaceType field is set.
       */
      @java.lang.Override public boolean hasInterfaceType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return The interfaceType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @param value The interfaceType to set.
       * @return This builder for chaining.
       */
      public Builder setInterfaceType(com.yorha.proto.CommonEnum.ClanStoreInterfaceType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        interfaceType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInterfaceType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        interfaceType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanStore_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanStore_C2S)
    private static final com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S();
    }

    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanStore_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanStore_C2S>() {
      @java.lang.Override
      public Player_FetchClanStore_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanStore_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanStore_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanStore_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanStore.Player_FetchClanStore_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchClanStore_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanStore_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */
    int getStoreInfoCount();
    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */
    boolean containsStoreInfo(
        int key);
    /**
     * Use {@link #getStoreInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>
    getStoreInfo();
    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>
    getStoreInfoMap();
    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */

    com.yorha.proto.StructPB.ClanStoreItemInfoPB getStoreInfoOrDefault(
        int key,
        com.yorha.proto.StructPB.ClanStoreItemInfoPB defaultValue);
    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */

    com.yorha.proto.StructPB.ClanStoreItemInfoPB getStoreInfoOrThrow(
        int key);

    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致也需要有这个返回值
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return Whether the clanScore field is set.
     */
    boolean hasClanScore();
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致也需要有这个返回值
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return The clanScore.
     */
    long getClanScore();

    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return Whether the territoryLv field is set.
     */
    boolean hasTerritoryLv();
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return The territoryLv.
     */
    int getTerritoryLv();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanStore_S2C}
   */
  public static final class Player_FetchClanStore_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanStore_S2C)
      Player_FetchClanStore_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanStore_S2C.newBuilder() to construct.
    private Player_FetchClanStore_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanStore_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanStore_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanStore_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                storeInfo_ = com.google.protobuf.MapField.newMapField(
                    StoreInfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>
              storeInfo__ = input.readMessage(
                  StoreInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              storeInfo_.getMutableMap().put(
                  storeInfo__.getKey(), storeInfo__.getValue());
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              clanScore_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              territoryLv_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetStoreInfo();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C.class, com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int STOREINFO_FIELD_NUMBER = 1;
    private static final class StoreInfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>newDefaultInstance(
                  com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_S2C_StoreInfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructPB.ClanStoreItemInfoPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> storeInfo_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>
    internalGetStoreInfo() {
      if (storeInfo_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            StoreInfoDefaultEntryHolder.defaultEntry);
      }
      return storeInfo_;
    }

    public int getStoreInfoCount() {
      return internalGetStoreInfo().getMap().size();
    }
    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */

    @java.lang.Override
    public boolean containsStoreInfo(
        int key) {
      
      return internalGetStoreInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getStoreInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> getStoreInfo() {
      return getStoreInfoMap();
    }
    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> getStoreInfoMap() {
      return internalGetStoreInfo().getMap();
    }
    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.ClanStoreItemInfoPB getStoreInfoOrDefault(
        int key,
        com.yorha.proto.StructPB.ClanStoreItemInfoPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> map =
          internalGetStoreInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.ClanStoreItemInfoPB getStoreInfoOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> map =
          internalGetStoreInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int CLANSCORE_FIELD_NUMBER = 2;
    private long clanScore_;
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致也需要有这个返回值
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return Whether the clanScore field is set.
     */
    @java.lang.Override
    public boolean hasClanScore() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致也需要有这个返回值
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return The clanScore.
     */
    @java.lang.Override
    public long getClanScore() {
      return clanScore_;
    }

    public static final int TERRITORYLV_FIELD_NUMBER = 3;
    private int territoryLv_;
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return Whether the territoryLv field is set.
     */
    @java.lang.Override
    public boolean hasTerritoryLv() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return The territoryLv.
     */
    @java.lang.Override
    public int getTerritoryLv() {
      return territoryLv_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetStoreInfo(),
          StoreInfoDefaultEntryHolder.defaultEntry,
          1);
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(2, clanScore_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, territoryLv_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> entry
           : internalGetStoreInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>
        storeInfo__ = StoreInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, storeInfo__);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, clanScore_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, territoryLv_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C other = (com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C) obj;

      if (!internalGetStoreInfo().equals(
          other.internalGetStoreInfo())) return false;
      if (hasClanScore() != other.hasClanScore()) return false;
      if (hasClanScore()) {
        if (getClanScore()
            != other.getClanScore()) return false;
      }
      if (hasTerritoryLv() != other.hasTerritoryLv()) return false;
      if (hasTerritoryLv()) {
        if (getTerritoryLv()
            != other.getTerritoryLv()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetStoreInfo().getMap().isEmpty()) {
        hash = (37 * hash) + STOREINFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetStoreInfo().hashCode();
      }
      if (hasClanScore()) {
        hash = (37 * hash) + CLANSCORE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanScore());
      }
      if (hasTerritoryLv()) {
        hash = (37 * hash) + TERRITORYLV_FIELD_NUMBER;
        hash = (53 * hash) + getTerritoryLv();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanStore_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanStore_S2C)
        com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetStoreInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableStoreInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C.class, com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableStoreInfo().clear();
        clanScore_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        territoryLv_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStore_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C build() {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C buildPartial() {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C result = new com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.storeInfo_ = internalGetStoreInfo();
        result.storeInfo_.makeImmutable();
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.clanScore_ = clanScore_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.territoryLv_ = territoryLv_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C) {
          return mergeFrom((com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C other) {
        if (other == com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C.getDefaultInstance()) return this;
        internalGetMutableStoreInfo().mergeFrom(
            other.internalGetStoreInfo());
        if (other.hasClanScore()) {
          setClanScore(other.getClanScore());
        }
        if (other.hasTerritoryLv()) {
          setTerritoryLv(other.getTerritoryLv());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> storeInfo_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>
      internalGetStoreInfo() {
        if (storeInfo_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              StoreInfoDefaultEntryHolder.defaultEntry);
        }
        return storeInfo_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>
      internalGetMutableStoreInfo() {
        onChanged();;
        if (storeInfo_ == null) {
          storeInfo_ = com.google.protobuf.MapField.newMapField(
              StoreInfoDefaultEntryHolder.defaultEntry);
        }
        if (!storeInfo_.isMutable()) {
          storeInfo_ = storeInfo_.copy();
        }
        return storeInfo_;
      }

      public int getStoreInfoCount() {
        return internalGetStoreInfo().getMap().size();
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
       */

      @java.lang.Override
      public boolean containsStoreInfo(
          int key) {
        
        return internalGetStoreInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getStoreInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> getStoreInfo() {
        return getStoreInfoMap();
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> getStoreInfoMap() {
        return internalGetStoreInfo().getMap();
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.ClanStoreItemInfoPB getStoreInfoOrDefault(
          int key,
          com.yorha.proto.StructPB.ClanStoreItemInfoPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> map =
            internalGetStoreInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.ClanStoreItemInfoPB getStoreInfoOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> map =
            internalGetStoreInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearStoreInfo() {
        internalGetMutableStoreInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
       */

      public Builder removeStoreInfo(
          int key) {
        
        internalGetMutableStoreInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB>
      getMutableStoreInfo() {
        return internalGetMutableStoreInfo().getMutableMap();
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
       */
      public Builder putStoreInfo(
          int key,
          com.yorha.proto.StructPB.ClanStoreItemInfoPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableStoreInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 商店道具列表，道具id + 个数 + 两个价格(?) + 军团势力值等级(?)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanStoreItemInfoPB&gt; storeInfo = 1;</code>
       */

      public Builder putAllStoreInfo(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.ClanStoreItemInfoPB> values) {
        internalGetMutableStoreInfo().getMutableMap()
            .putAll(values);
        return this;
      }

      private long clanScore_ ;
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致也需要有这个返回值
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return Whether the clanScore field is set.
       */
      @java.lang.Override
      public boolean hasClanScore() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致也需要有这个返回值
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return The clanScore.
       */
      @java.lang.Override
      public long getClanScore() {
        return clanScore_;
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致也需要有这个返回值
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @param value The clanScore to set.
       * @return This builder for chaining.
       */
      public Builder setClanScore(long value) {
        bitField0_ |= 0x00000002;
        clanScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致也需要有这个返回值
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanScore() {
        bitField0_ = (bitField0_ & ~0x00000002);
        clanScore_ = 0L;
        onChanged();
        return this;
      }

      private int territoryLv_ ;
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return Whether the territoryLv field is set.
       */
      @java.lang.Override
      public boolean hasTerritoryLv() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return The territoryLv.
       */
      @java.lang.Override
      public int getTerritoryLv() {
        return territoryLv_;
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @param value The territoryLv to set.
       * @return This builder for chaining.
       */
      public Builder setTerritoryLv(int value) {
        bitField0_ |= 0x00000004;
        territoryLv_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTerritoryLv() {
        bitField0_ = (bitField0_ & ~0x00000004);
        territoryLv_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanStore_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanStore_S2C)
    private static final com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C();
    }

    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanStore_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanStore_S2C>() {
      @java.lang.Override
      public Player_FetchClanStore_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanStore_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanStore_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanStore_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanStore.Player_FetchClanStore_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchClanStoreRecord_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanStoreRecord_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 需要的记录类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    boolean hasInterfaceType();
    /**
     * <pre>
     * 需要的记录类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanStoreRecord_C2S}
   */
  public static final class Player_FetchClanStoreRecord_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanStoreRecord_C2S)
      Player_FetchClanStoreRecord_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanStoreRecord_C2S.newBuilder() to construct.
    private Player_FetchClanStoreRecord_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanStoreRecord_C2S() {
      interfaceType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanStoreRecord_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanStoreRecord_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanStoreInterfaceType value = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                interfaceType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S.class, com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int INTERFACETYPE_FIELD_NUMBER = 1;
    private int interfaceType_;
    /**
     * <pre>
     * 需要的记录类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    @java.lang.Override public boolean hasInterfaceType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 需要的记录类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, interfaceType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, interfaceType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S other = (com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S) obj;

      if (hasInterfaceType() != other.hasInterfaceType()) return false;
      if (hasInterfaceType()) {
        if (interfaceType_ != other.interfaceType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInterfaceType()) {
        hash = (37 * hash) + INTERFACETYPE_FIELD_NUMBER;
        hash = (53 * hash) + interfaceType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanStoreRecord_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanStoreRecord_C2S)
        com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S.class, com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        interfaceType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S build() {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S buildPartial() {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S result = new com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.interfaceType_ = interfaceType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S) {
          return mergeFrom((com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S other) {
        if (other == com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S.getDefaultInstance()) return this;
        if (other.hasInterfaceType()) {
          setInterfaceType(other.getInterfaceType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int interfaceType_ = 0;
      /**
       * <pre>
       * 需要的记录类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return Whether the interfaceType field is set.
       */
      @java.lang.Override public boolean hasInterfaceType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 需要的记录类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return The interfaceType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
      }
      /**
       * <pre>
       * 需要的记录类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @param value The interfaceType to set.
       * @return This builder for chaining.
       */
      public Builder setInterfaceType(com.yorha.proto.CommonEnum.ClanStoreInterfaceType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        interfaceType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要的记录类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInterfaceType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        interfaceType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanStoreRecord_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanStoreRecord_C2S)
    private static final com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S();
    }

    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanStoreRecord_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanStoreRecord_C2S>() {
      @java.lang.Override
      public Player_FetchClanStoreRecord_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanStoreRecord_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanStoreRecord_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanStoreRecord_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchClanStoreRecord_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanStoreRecord_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    java.util.List<com.yorha.proto.StructPB.ClanStoreLogItemPB> 
        getRecordsList();
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    com.yorha.proto.StructPB.ClanStoreLogItemPB getRecords(int index);
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    int getRecordsCount();
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder> 
        getRecordsOrBuilderList();
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder getRecordsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanStoreRecord_S2C}
   */
  public static final class Player_FetchClanStoreRecord_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanStoreRecord_S2C)
      Player_FetchClanStoreRecord_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanStoreRecord_S2C.newBuilder() to construct.
    private Player_FetchClanStoreRecord_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanStoreRecord_S2C() {
      records_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanStoreRecord_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanStoreRecord_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                records_ = new java.util.ArrayList<com.yorha.proto.StructPB.ClanStoreLogItemPB>();
                mutable_bitField0_ |= 0x00000001;
              }
              records_.add(
                  input.readMessage(com.yorha.proto.StructPB.ClanStoreLogItemPB.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          records_ = java.util.Collections.unmodifiableList(records_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C.class, com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C.Builder.class);
    }

    public static final int RECORDS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.StructPB.ClanStoreLogItemPB> records_;
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructPB.ClanStoreLogItemPB> getRecordsList() {
      return records_;
    }
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder> 
        getRecordsOrBuilderList() {
      return records_;
    }
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    @java.lang.Override
    public int getRecordsCount() {
      return records_.size();
    }
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ClanStoreLogItemPB getRecords(int index) {
      return records_.get(index);
    }
    /**
     * <pre>
     * 商店记录
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder getRecordsOrBuilder(
        int index) {
      return records_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < records_.size(); i++) {
        output.writeMessage(1, records_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < records_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, records_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C other = (com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C) obj;

      if (!getRecordsList()
          .equals(other.getRecordsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRecordsCount() > 0) {
        hash = (37 * hash) + RECORDS_FIELD_NUMBER;
        hash = (53 * hash) + getRecordsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanStoreRecord_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanStoreRecord_S2C)
        com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C.class, com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRecordsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (recordsBuilder_ == null) {
          records_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          recordsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C build() {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C buildPartial() {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C result = new com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C(this);
        int from_bitField0_ = bitField0_;
        if (recordsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            records_ = java.util.Collections.unmodifiableList(records_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.records_ = records_;
        } else {
          result.records_ = recordsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C) {
          return mergeFrom((com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C other) {
        if (other == com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C.getDefaultInstance()) return this;
        if (recordsBuilder_ == null) {
          if (!other.records_.isEmpty()) {
            if (records_.isEmpty()) {
              records_ = other.records_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRecordsIsMutable();
              records_.addAll(other.records_);
            }
            onChanged();
          }
        } else {
          if (!other.records_.isEmpty()) {
            if (recordsBuilder_.isEmpty()) {
              recordsBuilder_.dispose();
              recordsBuilder_ = null;
              records_ = other.records_;
              bitField0_ = (bitField0_ & ~0x00000001);
              recordsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRecordsFieldBuilder() : null;
            } else {
              recordsBuilder_.addAllMessages(other.records_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.StructPB.ClanStoreLogItemPB> records_ =
        java.util.Collections.emptyList();
      private void ensureRecordsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          records_ = new java.util.ArrayList<com.yorha.proto.StructPB.ClanStoreLogItemPB>(records_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructPB.ClanStoreLogItemPB, com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder, com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder> recordsBuilder_;

      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructPB.ClanStoreLogItemPB> getRecordsList() {
        if (recordsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(records_);
        } else {
          return recordsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public int getRecordsCount() {
        if (recordsBuilder_ == null) {
          return records_.size();
        } else {
          return recordsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public com.yorha.proto.StructPB.ClanStoreLogItemPB getRecords(int index) {
        if (recordsBuilder_ == null) {
          return records_.get(index);
        } else {
          return recordsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder setRecords(
          int index, com.yorha.proto.StructPB.ClanStoreLogItemPB value) {
        if (recordsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordsIsMutable();
          records_.set(index, value);
          onChanged();
        } else {
          recordsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder setRecords(
          int index, com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder builderForValue) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          records_.set(index, builderForValue.build());
          onChanged();
        } else {
          recordsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder addRecords(com.yorha.proto.StructPB.ClanStoreLogItemPB value) {
        if (recordsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordsIsMutable();
          records_.add(value);
          onChanged();
        } else {
          recordsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder addRecords(
          int index, com.yorha.proto.StructPB.ClanStoreLogItemPB value) {
        if (recordsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecordsIsMutable();
          records_.add(index, value);
          onChanged();
        } else {
          recordsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder addRecords(
          com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder builderForValue) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          records_.add(builderForValue.build());
          onChanged();
        } else {
          recordsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder addRecords(
          int index, com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder builderForValue) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          records_.add(index, builderForValue.build());
          onChanged();
        } else {
          recordsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder addAllRecords(
          java.lang.Iterable<? extends com.yorha.proto.StructPB.ClanStoreLogItemPB> values) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, records_);
          onChanged();
        } else {
          recordsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder clearRecords() {
        if (recordsBuilder_ == null) {
          records_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          recordsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public Builder removeRecords(int index) {
        if (recordsBuilder_ == null) {
          ensureRecordsIsMutable();
          records_.remove(index);
          onChanged();
        } else {
          recordsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder getRecordsBuilder(
          int index) {
        return getRecordsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder getRecordsOrBuilder(
          int index) {
        if (recordsBuilder_ == null) {
          return records_.get(index);  } else {
          return recordsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder> 
           getRecordsOrBuilderList() {
        if (recordsBuilder_ != null) {
          return recordsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(records_);
        }
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder addRecordsBuilder() {
        return getRecordsFieldBuilder().addBuilder(
            com.yorha.proto.StructPB.ClanStoreLogItemPB.getDefaultInstance());
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder addRecordsBuilder(
          int index) {
        return getRecordsFieldBuilder().addBuilder(
            index, com.yorha.proto.StructPB.ClanStoreLogItemPB.getDefaultInstance());
      }
      /**
       * <pre>
       * 商店记录
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ClanStoreLogItemPB records = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder> 
           getRecordsBuilderList() {
        return getRecordsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructPB.ClanStoreLogItemPB, com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder, com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder> 
          getRecordsFieldBuilder() {
        if (recordsBuilder_ == null) {
          recordsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructPB.ClanStoreLogItemPB, com.yorha.proto.StructPB.ClanStoreLogItemPB.Builder, com.yorha.proto.StructPB.ClanStoreLogItemPBOrBuilder>(
                  records_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          records_ = null;
        }
        return recordsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanStoreRecord_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanStoreRecord_S2C)
    private static final com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C();
    }

    public static com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanStoreRecord_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanStoreRecord_S2C>() {
      @java.lang.Override
      public Player_FetchClanStoreRecord_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanStoreRecord_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanStoreRecord_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanStoreRecord_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanStore.Player_FetchClanStoreRecord_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_OperateClanStoreItem_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_OperateClanStoreItem_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    boolean hasInterfaceType();
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType();

    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 2;</code>
     * @return Whether the itemId field is set.
     */
    boolean hasItemId();
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 2;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <pre>
     * 道具数目(可代表购买数目，进货数目，回收数目)
     * </pre>
     *
     * <code>optional int32 itemNum = 3;</code>
     * @return Whether the itemNum field is set.
     */
    boolean hasItemNum();
    /**
     * <pre>
     * 道具数目(可代表购买数目，进货数目，回收数目)
     * </pre>
     *
     * <code>optional int32 itemNum = 3;</code>
     * @return The itemNum.
     */
    int getItemNum();

    /**
     * <pre>
     * 客户端传给服务器用作校验的总价
     * </pre>
     *
     * <code>optional int32 totalPrice = 4;</code>
     * @return Whether the totalPrice field is set.
     */
    boolean hasTotalPrice();
    /**
     * <pre>
     * 客户端传给服务器用作校验的总价
     * </pre>
     *
     * <code>optional int32 totalPrice = 4;</code>
     * @return The totalPrice.
     */
    int getTotalPrice();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_OperateClanStoreItem_C2S}
   */
  public static final class Player_OperateClanStoreItem_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_OperateClanStoreItem_C2S)
      Player_OperateClanStoreItem_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_OperateClanStoreItem_C2S.newBuilder() to construct.
    private Player_OperateClanStoreItem_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_OperateClanStoreItem_C2S() {
      interfaceType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_OperateClanStoreItem_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_OperateClanStoreItem_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanStoreInterfaceType value = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                interfaceType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              itemId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              itemNum_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              totalPrice_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S.class, com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int INTERFACETYPE_FIELD_NUMBER = 1;
    private int interfaceType_;
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return Whether the interfaceType field is set.
     */
    @java.lang.Override public boolean hasInterfaceType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 需要进行的操作类型，用界面类型来做区分
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
     * @return The interfaceType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
    }

    public static final int ITEMID_FIELD_NUMBER = 2;
    private int itemId_;
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 2;</code>
     * @return Whether the itemId field is set.
     */
    @java.lang.Override
    public boolean hasItemId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 2;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int ITEMNUM_FIELD_NUMBER = 3;
    private int itemNum_;
    /**
     * <pre>
     * 道具数目(可代表购买数目，进货数目，回收数目)
     * </pre>
     *
     * <code>optional int32 itemNum = 3;</code>
     * @return Whether the itemNum field is set.
     */
    @java.lang.Override
    public boolean hasItemNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 道具数目(可代表购买数目，进货数目，回收数目)
     * </pre>
     *
     * <code>optional int32 itemNum = 3;</code>
     * @return The itemNum.
     */
    @java.lang.Override
    public int getItemNum() {
      return itemNum_;
    }

    public static final int TOTALPRICE_FIELD_NUMBER = 4;
    private int totalPrice_;
    /**
     * <pre>
     * 客户端传给服务器用作校验的总价
     * </pre>
     *
     * <code>optional int32 totalPrice = 4;</code>
     * @return Whether the totalPrice field is set.
     */
    @java.lang.Override
    public boolean hasTotalPrice() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 客户端传给服务器用作校验的总价
     * </pre>
     *
     * <code>optional int32 totalPrice = 4;</code>
     * @return The totalPrice.
     */
    @java.lang.Override
    public int getTotalPrice() {
      return totalPrice_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, interfaceType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, itemId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, itemNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, totalPrice_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, interfaceType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, itemId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, itemNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, totalPrice_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S other = (com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S) obj;

      if (hasInterfaceType() != other.hasInterfaceType()) return false;
      if (hasInterfaceType()) {
        if (interfaceType_ != other.interfaceType_) return false;
      }
      if (hasItemId() != other.hasItemId()) return false;
      if (hasItemId()) {
        if (getItemId()
            != other.getItemId()) return false;
      }
      if (hasItemNum() != other.hasItemNum()) return false;
      if (hasItemNum()) {
        if (getItemNum()
            != other.getItemNum()) return false;
      }
      if (hasTotalPrice() != other.hasTotalPrice()) return false;
      if (hasTotalPrice()) {
        if (getTotalPrice()
            != other.getTotalPrice()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInterfaceType()) {
        hash = (37 * hash) + INTERFACETYPE_FIELD_NUMBER;
        hash = (53 * hash) + interfaceType_;
      }
      if (hasItemId()) {
        hash = (37 * hash) + ITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getItemId();
      }
      if (hasItemNum()) {
        hash = (37 * hash) + ITEMNUM_FIELD_NUMBER;
        hash = (53 * hash) + getItemNum();
      }
      if (hasTotalPrice()) {
        hash = (37 * hash) + TOTALPRICE_FIELD_NUMBER;
        hash = (53 * hash) + getTotalPrice();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_OperateClanStoreItem_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_OperateClanStoreItem_C2S)
        com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S.class, com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        interfaceType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        itemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        itemNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        totalPrice_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S build() {
        com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S buildPartial() {
        com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S result = new com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.interfaceType_ = interfaceType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.itemId_ = itemId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.itemNum_ = itemNum_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.totalPrice_ = totalPrice_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S) {
          return mergeFrom((com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S other) {
        if (other == com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S.getDefaultInstance()) return this;
        if (other.hasInterfaceType()) {
          setInterfaceType(other.getInterfaceType());
        }
        if (other.hasItemId()) {
          setItemId(other.getItemId());
        }
        if (other.hasItemNum()) {
          setItemNum(other.getItemNum());
        }
        if (other.hasTotalPrice()) {
          setTotalPrice(other.getTotalPrice());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int interfaceType_ = 0;
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return Whether the interfaceType field is set.
       */
      @java.lang.Override public boolean hasInterfaceType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return The interfaceType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanStoreInterfaceType getInterfaceType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanStoreInterfaceType result = com.yorha.proto.CommonEnum.ClanStoreInterfaceType.valueOf(interfaceType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanStoreInterfaceType.CSRT_NONE : result;
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @param value The interfaceType to set.
       * @return This builder for chaining.
       */
      public Builder setInterfaceType(com.yorha.proto.CommonEnum.ClanStoreInterfaceType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        interfaceType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要进行的操作类型，用界面类型来做区分
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreInterfaceType interfaceType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInterfaceType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        interfaceType_ = 0;
        onChanged();
        return this;
      }

      private int itemId_ ;
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 2;</code>
       * @return Whether the itemId field is set.
       */
      @java.lang.Override
      public boolean hasItemId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 2;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 2;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        bitField0_ |= 0x00000002;
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        itemId_ = 0;
        onChanged();
        return this;
      }

      private int itemNum_ ;
      /**
       * <pre>
       * 道具数目(可代表购买数目，进货数目，回收数目)
       * </pre>
       *
       * <code>optional int32 itemNum = 3;</code>
       * @return Whether the itemNum field is set.
       */
      @java.lang.Override
      public boolean hasItemNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 道具数目(可代表购买数目，进货数目，回收数目)
       * </pre>
       *
       * <code>optional int32 itemNum = 3;</code>
       * @return The itemNum.
       */
      @java.lang.Override
      public int getItemNum() {
        return itemNum_;
      }
      /**
       * <pre>
       * 道具数目(可代表购买数目，进货数目，回收数目)
       * </pre>
       *
       * <code>optional int32 itemNum = 3;</code>
       * @param value The itemNum to set.
       * @return This builder for chaining.
       */
      public Builder setItemNum(int value) {
        bitField0_ |= 0x00000004;
        itemNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 道具数目(可代表购买数目，进货数目，回收数目)
       * </pre>
       *
       * <code>optional int32 itemNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        itemNum_ = 0;
        onChanged();
        return this;
      }

      private int totalPrice_ ;
      /**
       * <pre>
       * 客户端传给服务器用作校验的总价
       * </pre>
       *
       * <code>optional int32 totalPrice = 4;</code>
       * @return Whether the totalPrice field is set.
       */
      @java.lang.Override
      public boolean hasTotalPrice() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 客户端传给服务器用作校验的总价
       * </pre>
       *
       * <code>optional int32 totalPrice = 4;</code>
       * @return The totalPrice.
       */
      @java.lang.Override
      public int getTotalPrice() {
        return totalPrice_;
      }
      /**
       * <pre>
       * 客户端传给服务器用作校验的总价
       * </pre>
       *
       * <code>optional int32 totalPrice = 4;</code>
       * @param value The totalPrice to set.
       * @return This builder for chaining.
       */
      public Builder setTotalPrice(int value) {
        bitField0_ |= 0x00000008;
        totalPrice_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户端传给服务器用作校验的总价
       * </pre>
       *
       * <code>optional int32 totalPrice = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalPrice() {
        bitField0_ = (bitField0_ & ~0x00000008);
        totalPrice_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_OperateClanStoreItem_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_OperateClanStoreItem_C2S)
    private static final com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S();
    }

    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_OperateClanStoreItem_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_OperateClanStoreItem_C2S>() {
      @java.lang.Override
      public Player_OperateClanStoreItem_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_OperateClanStoreItem_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_OperateClanStoreItem_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_OperateClanStoreItem_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_OperateClanStoreItem_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_OperateClanStoreItem_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 依赖返回的枚举来显示是否购买成功或显示对应tips，tips会影响后续逻辑，详见对应枚举说明
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
     * @return Whether the returnType field is set.
     */
    boolean hasReturnType();
    /**
     * <pre>
     * 依赖返回的枚举来显示是否购买成功或显示对应tips，tips会影响后续逻辑，详见对应枚举说明
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
     * @return The returnType.
     */
    com.yorha.proto.CommonEnum.ClanStoreOperateReturnType getReturnType();

    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致此处不会有这个值，应该会通过额外ntf下发，【需要删除】
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return Whether the clanScore field is set.
     */
    boolean hasClanScore();
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致此处不会有这个值，应该会通过额外ntf下发，【需要删除】
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return The clanScore.
     */
    long getClanScore();

    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回，【需要删除】
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return Whether the territoryLv field is set.
     */
    boolean hasTerritoryLv();
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回，【需要删除】
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return The territoryLv.
     */
    int getTerritoryLv();

    /**
     * <pre>
     * 剩余商品个数，仅在界面类型为CSRT_BUY(购买界面时)返回，可能返回失败，【需要删除】
     * </pre>
     *
     * <code>optional int32 leftNum = 4;</code>
     * @return Whether the leftNum field is set.
     */
    boolean hasLeftNum();
    /**
     * <pre>
     * 剩余商品个数，仅在界面类型为CSRT_BUY(购买界面时)返回，可能返回失败，【需要删除】
     * </pre>
     *
     * <code>optional int32 leftNum = 4;</code>
     * @return The leftNum.
     */
    int getLeftNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_OperateClanStoreItem_S2C}
   */
  public static final class Player_OperateClanStoreItem_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_OperateClanStoreItem_S2C)
      Player_OperateClanStoreItem_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_OperateClanStoreItem_S2C.newBuilder() to construct.
    private Player_OperateClanStoreItem_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_OperateClanStoreItem_S2C() {
      returnType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_OperateClanStoreItem_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_OperateClanStoreItem_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanStoreOperateReturnType value = com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                returnType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              clanScore_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              territoryLv_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              leftNum_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C.class, com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int RETURNTYPE_FIELD_NUMBER = 1;
    private int returnType_;
    /**
     * <pre>
     * 依赖返回的枚举来显示是否购买成功或显示对应tips，tips会影响后续逻辑，详见对应枚举说明
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
     * @return Whether the returnType field is set.
     */
    @java.lang.Override public boolean hasReturnType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 依赖返回的枚举来显示是否购买成功或显示对应tips，tips会影响后续逻辑，详见对应枚举说明
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
     * @return The returnType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanStoreOperateReturnType getReturnType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanStoreOperateReturnType result = com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.valueOf(returnType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.CSORT_NONE : result;
    }

    public static final int CLANSCORE_FIELD_NUMBER = 2;
    private long clanScore_;
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致此处不会有这个值，应该会通过额外ntf下发，【需要删除】
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return Whether the clanScore field is set.
     */
    @java.lang.Override
    public boolean hasClanScore() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致此处不会有这个值，应该会通过额外ntf下发，【需要删除】
     * </pre>
     *
     * <code>optional int64 clanScore = 2;</code>
     * @return The clanScore.
     */
    @java.lang.Override
    public long getClanScore() {
      return clanScore_;
    }

    public static final int TERRITORYLV_FIELD_NUMBER = 3;
    private int territoryLv_;
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回，【需要删除】
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return Whether the territoryLv field is set.
     */
    @java.lang.Override
    public boolean hasTerritoryLv() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回，【需要删除】
     * </pre>
     *
     * <code>optional int32 territoryLv = 3;</code>
     * @return The territoryLv.
     */
    @java.lang.Override
    public int getTerritoryLv() {
      return territoryLv_;
    }

    public static final int LEFTNUM_FIELD_NUMBER = 4;
    private int leftNum_;
    /**
     * <pre>
     * 剩余商品个数，仅在界面类型为CSRT_BUY(购买界面时)返回，可能返回失败，【需要删除】
     * </pre>
     *
     * <code>optional int32 leftNum = 4;</code>
     * @return Whether the leftNum field is set.
     */
    @java.lang.Override
    public boolean hasLeftNum() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 剩余商品个数，仅在界面类型为CSRT_BUY(购买界面时)返回，可能返回失败，【需要删除】
     * </pre>
     *
     * <code>optional int32 leftNum = 4;</code>
     * @return The leftNum.
     */
    @java.lang.Override
    public int getLeftNum() {
      return leftNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, returnType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, clanScore_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, territoryLv_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, leftNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, returnType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, clanScore_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, territoryLv_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, leftNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C other = (com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C) obj;

      if (hasReturnType() != other.hasReturnType()) return false;
      if (hasReturnType()) {
        if (returnType_ != other.returnType_) return false;
      }
      if (hasClanScore() != other.hasClanScore()) return false;
      if (hasClanScore()) {
        if (getClanScore()
            != other.getClanScore()) return false;
      }
      if (hasTerritoryLv() != other.hasTerritoryLv()) return false;
      if (hasTerritoryLv()) {
        if (getTerritoryLv()
            != other.getTerritoryLv()) return false;
      }
      if (hasLeftNum() != other.hasLeftNum()) return false;
      if (hasLeftNum()) {
        if (getLeftNum()
            != other.getLeftNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReturnType()) {
        hash = (37 * hash) + RETURNTYPE_FIELD_NUMBER;
        hash = (53 * hash) + returnType_;
      }
      if (hasClanScore()) {
        hash = (37 * hash) + CLANSCORE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanScore());
      }
      if (hasTerritoryLv()) {
        hash = (37 * hash) + TERRITORYLV_FIELD_NUMBER;
        hash = (53 * hash) + getTerritoryLv();
      }
      if (hasLeftNum()) {
        hash = (37 * hash) + LEFTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getLeftNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_OperateClanStoreItem_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_OperateClanStoreItem_S2C)
        com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C.class, com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        returnType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        clanScore_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        territoryLv_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        leftNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C build() {
        com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C buildPartial() {
        com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C result = new com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.returnType_ = returnType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.clanScore_ = clanScore_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.territoryLv_ = territoryLv_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.leftNum_ = leftNum_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C) {
          return mergeFrom((com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C other) {
        if (other == com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C.getDefaultInstance()) return this;
        if (other.hasReturnType()) {
          setReturnType(other.getReturnType());
        }
        if (other.hasClanScore()) {
          setClanScore(other.getClanScore());
        }
        if (other.hasTerritoryLv()) {
          setTerritoryLv(other.getTerritoryLv());
        }
        if (other.hasLeftNum()) {
          setLeftNum(other.getLeftNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int returnType_ = 0;
      /**
       * <pre>
       * 依赖返回的枚举来显示是否购买成功或显示对应tips，tips会影响后续逻辑，详见对应枚举说明
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
       * @return Whether the returnType field is set.
       */
      @java.lang.Override public boolean hasReturnType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 依赖返回的枚举来显示是否购买成功或显示对应tips，tips会影响后续逻辑，详见对应枚举说明
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
       * @return The returnType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanStoreOperateReturnType getReturnType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanStoreOperateReturnType result = com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.valueOf(returnType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanStoreOperateReturnType.CSORT_NONE : result;
      }
      /**
       * <pre>
       * 依赖返回的枚举来显示是否购买成功或显示对应tips，tips会影响后续逻辑，详见对应枚举说明
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
       * @param value The returnType to set.
       * @return This builder for chaining.
       */
      public Builder setReturnType(com.yorha.proto.CommonEnum.ClanStoreOperateReturnType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        returnType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 依赖返回的枚举来显示是否购买成功或显示对应tips，tips会影响后续逻辑，详见对应枚举说明
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanStoreOperateReturnType returnType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearReturnType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        returnType_ = 0;
        onChanged();
        return this;
      }

      private long clanScore_ ;
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致此处不会有这个值，应该会通过额外ntf下发，【需要删除】
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return Whether the clanScore field is set.
       */
      @java.lang.Override
      public boolean hasClanScore() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致此处不会有这个值，应该会通过额外ntf下发，【需要删除】
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return The clanScore.
       */
      @java.lang.Override
      public long getClanScore() {
        return clanScore_;
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致此处不会有这个值，应该会通过额外ntf下发，【需要删除】
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @param value The clanScore to set.
       * @return This builder for chaining.
       */
      public Builder setClanScore(long value) {
        bitField0_ |= 0x00000002;
        clanScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 军团积分，仅在界面类型为CSRT_STOCK(进货界面时)返回，如果跟ROK一致此处不会有这个值，应该会通过额外ntf下发，【需要删除】
       * </pre>
       *
       * <code>optional int64 clanScore = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanScore() {
        bitField0_ = (bitField0_ & ~0x00000002);
        clanScore_ = 0L;
        onChanged();
        return this;
      }

      private int territoryLv_ ;
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回，【需要删除】
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return Whether the territoryLv field is set.
       */
      @java.lang.Override
      public boolean hasTerritoryLv() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回，【需要删除】
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return The territoryLv.
       */
      @java.lang.Override
      public int getTerritoryLv() {
        return territoryLv_;
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回，【需要删除】
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @param value The territoryLv to set.
       * @return This builder for chaining.
       */
      public Builder setTerritoryLv(int value) {
        bitField0_ |= 0x00000004;
        territoryLv_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 军团势力值等级，仅在界面类型为CSRT_STOCK(进货界面时)返回，【需要删除】
       * </pre>
       *
       * <code>optional int32 territoryLv = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTerritoryLv() {
        bitField0_ = (bitField0_ & ~0x00000004);
        territoryLv_ = 0;
        onChanged();
        return this;
      }

      private int leftNum_ ;
      /**
       * <pre>
       * 剩余商品个数，仅在界面类型为CSRT_BUY(购买界面时)返回，可能返回失败，【需要删除】
       * </pre>
       *
       * <code>optional int32 leftNum = 4;</code>
       * @return Whether the leftNum field is set.
       */
      @java.lang.Override
      public boolean hasLeftNum() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 剩余商品个数，仅在界面类型为CSRT_BUY(购买界面时)返回，可能返回失败，【需要删除】
       * </pre>
       *
       * <code>optional int32 leftNum = 4;</code>
       * @return The leftNum.
       */
      @java.lang.Override
      public int getLeftNum() {
        return leftNum_;
      }
      /**
       * <pre>
       * 剩余商品个数，仅在界面类型为CSRT_BUY(购买界面时)返回，可能返回失败，【需要删除】
       * </pre>
       *
       * <code>optional int32 leftNum = 4;</code>
       * @param value The leftNum to set.
       * @return This builder for chaining.
       */
      public Builder setLeftNum(int value) {
        bitField0_ |= 0x00000008;
        leftNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 剩余商品个数，仅在界面类型为CSRT_BUY(购买界面时)返回，可能返回失败，【需要删除】
       * </pre>
       *
       * <code>optional int32 leftNum = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLeftNum() {
        bitField0_ = (bitField0_ & ~0x00000008);
        leftNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_OperateClanStoreItem_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_OperateClanStoreItem_S2C)
    private static final com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C();
    }

    public static com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_OperateClanStoreItem_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_OperateClanStoreItem_S2C>() {
      @java.lang.Override
      public Player_OperateClanStoreItem_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_OperateClanStoreItem_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_OperateClanStoreItem_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_OperateClanStoreItem_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanStore.Player_OperateClanStoreItem_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnClanStoreItemChangeNtfOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnClanStoreItemChangeNtf)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return Whether the itemId field is set.
     */
    boolean hasItemId();
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <pre>
     * 道具数目，道具数目为X(X != 0)时，为新增道具，为0时为下架道具，均需要客户端刷新列表，重排序
     * </pre>
     *
     * <code>optional int32 itemNum = 2;</code>
     * @return Whether the itemNum field is set.
     */
    boolean hasItemNum();
    /**
     * <pre>
     * 道具数目，道具数目为X(X != 0)时，为新增道具，为0时为下架道具，均需要客户端刷新列表，重排序
     * </pre>
     *
     * <code>optional int32 itemNum = 2;</code>
     * @return The itemNum.
     */
    int getItemNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnClanStoreItemChangeNtf}
   */
  public static final class OnClanStoreItemChangeNtf extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnClanStoreItemChangeNtf)
      OnClanStoreItemChangeNtfOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnClanStoreItemChangeNtf.newBuilder() to construct.
    private OnClanStoreItemChangeNtf(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnClanStoreItemChangeNtf() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnClanStoreItemChangeNtf();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnClanStoreItemChangeNtf(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              itemId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              itemNum_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf.class, com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf.Builder.class);
    }

    private int bitField0_;
    public static final int ITEMID_FIELD_NUMBER = 1;
    private int itemId_;
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return Whether the itemId field is set.
     */
    @java.lang.Override
    public boolean hasItemId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 道具id
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int ITEMNUM_FIELD_NUMBER = 2;
    private int itemNum_;
    /**
     * <pre>
     * 道具数目，道具数目为X(X != 0)时，为新增道具，为0时为下架道具，均需要客户端刷新列表，重排序
     * </pre>
     *
     * <code>optional int32 itemNum = 2;</code>
     * @return Whether the itemNum field is set.
     */
    @java.lang.Override
    public boolean hasItemNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 道具数目，道具数目为X(X != 0)时，为新增道具，为0时为下架道具，均需要客户端刷新列表，重排序
     * </pre>
     *
     * <code>optional int32 itemNum = 2;</code>
     * @return The itemNum.
     */
    @java.lang.Override
    public int getItemNum() {
      return itemNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, itemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, itemNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, itemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, itemNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf other = (com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf) obj;

      if (hasItemId() != other.hasItemId()) return false;
      if (hasItemId()) {
        if (getItemId()
            != other.getItemId()) return false;
      }
      if (hasItemNum() != other.hasItemNum()) return false;
      if (hasItemNum()) {
        if (getItemNum()
            != other.getItemNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasItemId()) {
        hash = (37 * hash) + ITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getItemId();
      }
      if (hasItemNum()) {
        hash = (37 * hash) + ITEMNUM_FIELD_NUMBER;
        hash = (53 * hash) + getItemNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnClanStoreItemChangeNtf}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnClanStoreItemChangeNtf)
        com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtfOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf.class, com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        itemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        itemNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf build() {
        com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf buildPartial() {
        com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf result = new com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.itemId_ = itemId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.itemNum_ = itemNum_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf) {
          return mergeFrom((com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf other) {
        if (other == com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf.getDefaultInstance()) return this;
        if (other.hasItemId()) {
          setItemId(other.getItemId());
        }
        if (other.hasItemNum()) {
          setItemNum(other.getItemNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int itemId_ ;
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return Whether the itemId field is set.
       */
      @java.lang.Override
      public boolean hasItemId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        bitField0_ |= 0x00000001;
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 道具id
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        itemId_ = 0;
        onChanged();
        return this;
      }

      private int itemNum_ ;
      /**
       * <pre>
       * 道具数目，道具数目为X(X != 0)时，为新增道具，为0时为下架道具，均需要客户端刷新列表，重排序
       * </pre>
       *
       * <code>optional int32 itemNum = 2;</code>
       * @return Whether the itemNum field is set.
       */
      @java.lang.Override
      public boolean hasItemNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 道具数目，道具数目为X(X != 0)时，为新增道具，为0时为下架道具，均需要客户端刷新列表，重排序
       * </pre>
       *
       * <code>optional int32 itemNum = 2;</code>
       * @return The itemNum.
       */
      @java.lang.Override
      public int getItemNum() {
        return itemNum_;
      }
      /**
       * <pre>
       * 道具数目，道具数目为X(X != 0)时，为新增道具，为0时为下架道具，均需要客户端刷新列表，重排序
       * </pre>
       *
       * <code>optional int32 itemNum = 2;</code>
       * @param value The itemNum to set.
       * @return This builder for chaining.
       */
      public Builder setItemNum(int value) {
        bitField0_ |= 0x00000002;
        itemNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 道具数目，道具数目为X(X != 0)时，为新增道具，为0时为下架道具，均需要客户端刷新列表，重排序
       * </pre>
       *
       * <code>optional int32 itemNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        itemNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnClanStoreItemChangeNtf)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnClanStoreItemChangeNtf)
    private static final com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf();
    }

    public static com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnClanStoreItemChangeNtf>
        PARSER = new com.google.protobuf.AbstractParser<OnClanStoreItemChangeNtf>() {
      @java.lang.Override
      public OnClanStoreItemChangeNtf parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnClanStoreItemChangeNtf(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnClanStoreItemChangeNtf> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnClanStoreItemChangeNtf> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanStore.OnClanStoreItemChangeNtf getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnClanStoreStockNtfOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnClanStoreStockNtf)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 新的积分
     * </pre>
     *
     * <code>optional int64 newClanScore = 1;</code>
     * @return Whether the newClanScore field is set.
     */
    boolean hasNewClanScore();
    /**
     * <pre>
     * 新的积分
     * </pre>
     *
     * <code>optional int64 newClanScore = 1;</code>
     * @return The newClanScore.
     */
    long getNewClanScore();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnClanStoreStockNtf}
   */
  public static final class OnClanStoreStockNtf extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnClanStoreStockNtf)
      OnClanStoreStockNtfOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnClanStoreStockNtf.newBuilder() to construct.
    private OnClanStoreStockNtf(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnClanStoreStockNtf() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnClanStoreStockNtf();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnClanStoreStockNtf(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              newClanScore_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreStockNtf_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreStockNtf_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf.class, com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf.Builder.class);
    }

    private int bitField0_;
    public static final int NEWCLANSCORE_FIELD_NUMBER = 1;
    private long newClanScore_;
    /**
     * <pre>
     * 新的积分
     * </pre>
     *
     * <code>optional int64 newClanScore = 1;</code>
     * @return Whether the newClanScore field is set.
     */
    @java.lang.Override
    public boolean hasNewClanScore() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 新的积分
     * </pre>
     *
     * <code>optional int64 newClanScore = 1;</code>
     * @return The newClanScore.
     */
    @java.lang.Override
    public long getNewClanScore() {
      return newClanScore_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, newClanScore_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, newClanScore_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf other = (com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf) obj;

      if (hasNewClanScore() != other.hasNewClanScore()) return false;
      if (hasNewClanScore()) {
        if (getNewClanScore()
            != other.getNewClanScore()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNewClanScore()) {
        hash = (37 * hash) + NEWCLANSCORE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getNewClanScore());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnClanStoreStockNtf}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnClanStoreStockNtf)
        com.yorha.proto.PlayerClanStore.OnClanStoreStockNtfOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreStockNtf_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreStockNtf_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf.class, com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        newClanScore_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerClanStore.internal_static_com_yorha_proto_OnClanStoreStockNtf_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf getDefaultInstanceForType() {
        return com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf build() {
        com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf buildPartial() {
        com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf result = new com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.newClanScore_ = newClanScore_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf) {
          return mergeFrom((com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf other) {
        if (other == com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf.getDefaultInstance()) return this;
        if (other.hasNewClanScore()) {
          setNewClanScore(other.getNewClanScore());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long newClanScore_ ;
      /**
       * <pre>
       * 新的积分
       * </pre>
       *
       * <code>optional int64 newClanScore = 1;</code>
       * @return Whether the newClanScore field is set.
       */
      @java.lang.Override
      public boolean hasNewClanScore() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 新的积分
       * </pre>
       *
       * <code>optional int64 newClanScore = 1;</code>
       * @return The newClanScore.
       */
      @java.lang.Override
      public long getNewClanScore() {
        return newClanScore_;
      }
      /**
       * <pre>
       * 新的积分
       * </pre>
       *
       * <code>optional int64 newClanScore = 1;</code>
       * @param value The newClanScore to set.
       * @return This builder for chaining.
       */
      public Builder setNewClanScore(long value) {
        bitField0_ |= 0x00000001;
        newClanScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新的积分
       * </pre>
       *
       * <code>optional int64 newClanScore = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewClanScore() {
        bitField0_ = (bitField0_ & ~0x00000001);
        newClanScore_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnClanStoreStockNtf)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnClanStoreStockNtf)
    private static final com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf();
    }

    public static com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnClanStoreStockNtf>
        PARSER = new com.google.protobuf.AbstractParser<OnClanStoreStockNtf>() {
      @java.lang.Override
      public OnClanStoreStockNtf parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnClanStoreStockNtf(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnClanStoreStockNtf> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnClanStoreStockNtf> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerClanStore.OnClanStoreStockNtf getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanStore_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanStore_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanStore_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanStore_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanStore_S2C_StoreInfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanStore_S2C_StoreInfoEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnClanStoreStockNtf_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnClanStoreStockNtf_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/player/cs/player_clan_sto" +
      "re.proto\022\017com.yorha.proto\032\"cs_proto/gen/" +
      "common/structPB.proto\032%ss_proto/gen/comm" +
      "on/common_enum.proto\"[\n\031Player_FetchClan" +
      "Store_C2S\022>\n\rinterfaceType\030\001 \001(\0162\'.com.y" +
      "orha.proto.ClanStoreInterfaceType\"\351\001\n\031Pl" +
      "ayer_FetchClanStore_S2C\022L\n\tstoreInfo\030\001 \003" +
      "(\01329.com.yorha.proto.Player_FetchClanSto" +
      "re_S2C.StoreInfoEntry\022\021\n\tclanScore\030\002 \001(\003" +
      "\022\023\n\013territoryLv\030\003 \001(\005\032V\n\016StoreInfoEntry\022" +
      "\013\n\003key\030\001 \001(\005\0223\n\005value\030\002 \001(\0132$.com.yorha." +
      "proto.ClanStoreItemInfoPB:\0028\001\"a\n\037Player_" +
      "FetchClanStoreRecord_C2S\022>\n\rinterfaceTyp" +
      "e\030\001 \001(\0162\'.com.yorha.proto.ClanStoreInter" +
      "faceType\"W\n\037Player_FetchClanStoreRecord_" +
      "S2C\0224\n\007records\030\001 \003(\0132#.com.yorha.proto.C" +
      "lanStoreLogItemPB\"\226\001\n\037Player_OperateClan" +
      "StoreItem_C2S\022>\n\rinterfaceType\030\001 \001(\0162\'.c" +
      "om.yorha.proto.ClanStoreInterfaceType\022\016\n" +
      "\006itemId\030\002 \001(\005\022\017\n\007itemNum\030\003 \001(\005\022\022\n\ntotalP" +
      "rice\030\004 \001(\005\"\233\001\n\037Player_OperateClanStoreIt" +
      "em_S2C\022?\n\nreturnType\030\001 \001(\0162+.com.yorha.p" +
      "roto.ClanStoreOperateReturnType\022\021\n\tclanS" +
      "core\030\002 \001(\003\022\023\n\013territoryLv\030\003 \001(\005\022\017\n\007leftN" +
      "um\030\004 \001(\005\";\n\030OnClanStoreItemChangeNtf\022\016\n\006" +
      "itemId\030\001 \001(\005\022\017\n\007itemNum\030\002 \001(\005\"+\n\023OnClanS" +
      "toreStockNtf\022\024\n\014newClanScore\030\001 \001(\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_FetchClanStore_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_FetchClanStore_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanStore_C2S_descriptor,
        new java.lang.String[] { "InterfaceType", });
    internal_static_com_yorha_proto_Player_FetchClanStore_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_FetchClanStore_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanStore_S2C_descriptor,
        new java.lang.String[] { "StoreInfo", "ClanScore", "TerritoryLv", });
    internal_static_com_yorha_proto_Player_FetchClanStore_S2C_StoreInfoEntry_descriptor =
      internal_static_com_yorha_proto_Player_FetchClanStore_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_FetchClanStore_S2C_StoreInfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanStore_S2C_StoreInfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanStoreRecord_C2S_descriptor,
        new java.lang.String[] { "InterfaceType", });
    internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanStoreRecord_S2C_descriptor,
        new java.lang.String[] { "Records", });
    internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_OperateClanStoreItem_C2S_descriptor,
        new java.lang.String[] { "InterfaceType", "ItemId", "ItemNum", "TotalPrice", });
    internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_OperateClanStoreItem_S2C_descriptor,
        new java.lang.String[] { "ReturnType", "ClanScore", "TerritoryLv", "LeftNum", });
    internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnClanStoreItemChangeNtf_descriptor,
        new java.lang.String[] { "ItemId", "ItemNum", });
    internal_static_com_yorha_proto_OnClanStoreStockNtf_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_OnClanStoreStockNtf_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnClanStoreStockNtf_descriptor,
        new java.lang.String[] { "NewClanScore", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

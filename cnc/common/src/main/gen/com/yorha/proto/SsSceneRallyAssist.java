// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_rally_assist.proto

package com.yorha.proto;

public final class SsSceneRallyAssist {
  private SsSceneRallyAssist() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FetchRallyListAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchRallyListAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchRallyListAsk}
   */
  public static final class FetchRallyListAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchRallyListAsk)
      FetchRallyListAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchRallyListAsk.newBuilder() to construct.
    private FetchRallyListAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchRallyListAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchRallyListAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchRallyListAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk.class, com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk other = (com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchRallyListAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchRallyListAsk)
        com.yorha.proto.SsSceneRallyAssist.FetchRallyListAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk.class, com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk build() {
        com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk result = new com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchRallyListAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchRallyListAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchRallyListAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchRallyListAsk>() {
      @java.lang.Override
      public FetchRallyListAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchRallyListAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchRallyListAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchRallyListAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.FetchRallyListAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchRallyListAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchRallyListAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchRallyListAns}
   */
  public static final class FetchRallyListAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchRallyListAns)
      FetchRallyListAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchRallyListAns.newBuilder() to construct.
    private FetchRallyListAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchRallyListAns() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchRallyListAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchRallyListAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              bitField0_ |= 0x00000001;
              msgBytes_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns.class, com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns.Builder.class);
    }

    private int bitField0_;
    public static final int MSGBYTES_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, msgBytes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, msgBytes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns other = (com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns) obj;

      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchRallyListAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchRallyListAns)
        com.yorha.proto.SsSceneRallyAssist.FetchRallyListAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns.class, com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchRallyListAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns build() {
        com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns result = new com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.msgBytes_ = msgBytes_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns.getDefaultInstance()) return this;
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchRallyListAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchRallyListAns)
    private static final com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchRallyListAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchRallyListAns>() {
      @java.lang.Override
      public FetchRallyListAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchRallyListAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchRallyListAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchRallyListAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.FetchRallyListAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchOneRallyAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchOneRallyAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return Whether the rallyId field is set.
     */
    boolean hasRallyId();
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return The rallyId.
     */
    long getRallyId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchOneRallyAsk}
   */
  public static final class FetchOneRallyAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchOneRallyAsk)
      FetchOneRallyAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchOneRallyAsk.newBuilder() to construct.
    private FetchOneRallyAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchOneRallyAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchOneRallyAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchOneRallyAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              rallyId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk.class, com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int RALLYID_FIELD_NUMBER = 2;
    private long rallyId_;
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return Whether the rallyId field is set.
     */
    @java.lang.Override
    public boolean hasRallyId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return The rallyId.
     */
    @java.lang.Override
    public long getRallyId() {
      return rallyId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, rallyId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, rallyId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk other = (com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasRallyId() != other.hasRallyId()) return false;
      if (hasRallyId()) {
        if (getRallyId()
            != other.getRallyId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasRallyId()) {
        hash = (37 * hash) + RALLYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRallyId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchOneRallyAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchOneRallyAsk)
        com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk.class, com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        rallyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk build() {
        com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk result = new com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rallyId_ = rallyId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasRallyId()) {
          setRallyId(other.getRallyId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long rallyId_ ;
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return Whether the rallyId field is set.
       */
      @java.lang.Override
      public boolean hasRallyId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return The rallyId.
       */
      @java.lang.Override
      public long getRallyId() {
        return rallyId_;
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @param value The rallyId to set.
       * @return This builder for chaining.
       */
      public Builder setRallyId(long value) {
        bitField0_ |= 0x00000002;
        rallyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRallyId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rallyId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchOneRallyAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchOneRallyAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchOneRallyAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchOneRallyAsk>() {
      @java.lang.Override
      public FetchOneRallyAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchOneRallyAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchOneRallyAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchOneRallyAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchOneRallyAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchOneRallyAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchOneRallyAns}
   */
  public static final class FetchOneRallyAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchOneRallyAns)
      FetchOneRallyAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchOneRallyAns.newBuilder() to construct.
    private FetchOneRallyAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchOneRallyAns() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchOneRallyAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchOneRallyAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              bitField0_ |= 0x00000001;
              msgBytes_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns.class, com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns.Builder.class);
    }

    private int bitField0_;
    public static final int MSGBYTES_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, msgBytes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, msgBytes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns other = (com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns) obj;

      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchOneRallyAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchOneRallyAns)
        com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns.class, com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchOneRallyAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns build() {
        com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns result = new com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.msgBytes_ = msgBytes_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns.getDefaultInstance()) return this;
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchOneRallyAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchOneRallyAns)
    private static final com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchOneRallyAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchOneRallyAns>() {
      @java.lang.Override
      public FetchOneRallyAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchOneRallyAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchOneRallyAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchOneRallyAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.FetchOneRallyAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlayerCancelRallyAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlayerCancelRallyAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return Whether the rallyId field is set.
     */
    boolean hasRallyId();
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return The rallyId.
     */
    long getRallyId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlayerCancelRallyAsk}
   */
  public static final class PlayerCancelRallyAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlayerCancelRallyAsk)
      PlayerCancelRallyAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlayerCancelRallyAsk.newBuilder() to construct.
    private PlayerCancelRallyAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlayerCancelRallyAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlayerCancelRallyAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlayerCancelRallyAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              rallyId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk.class, com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int RALLYID_FIELD_NUMBER = 2;
    private long rallyId_;
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return Whether the rallyId field is set.
     */
    @java.lang.Override
    public boolean hasRallyId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return The rallyId.
     */
    @java.lang.Override
    public long getRallyId() {
      return rallyId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, rallyId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, rallyId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk other = (com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasRallyId() != other.hasRallyId()) return false;
      if (hasRallyId()) {
        if (getRallyId()
            != other.getRallyId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasRallyId()) {
        hash = (37 * hash) + RALLYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRallyId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlayerCancelRallyAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlayerCancelRallyAsk)
        com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk.class, com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        rallyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk build() {
        com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk result = new com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rallyId_ = rallyId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasRallyId()) {
          setRallyId(other.getRallyId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long rallyId_ ;
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return Whether the rallyId field is set.
       */
      @java.lang.Override
      public boolean hasRallyId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return The rallyId.
       */
      @java.lang.Override
      public long getRallyId() {
        return rallyId_;
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @param value The rallyId to set.
       * @return This builder for chaining.
       */
      public Builder setRallyId(long value) {
        bitField0_ |= 0x00000002;
        rallyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRallyId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rallyId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlayerCancelRallyAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlayerCancelRallyAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlayerCancelRallyAsk>
        PARSER = new com.google.protobuf.AbstractParser<PlayerCancelRallyAsk>() {
      @java.lang.Override
      public PlayerCancelRallyAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlayerCancelRallyAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlayerCancelRallyAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlayerCancelRallyAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlayerCancelRallyAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlayerCancelRallyAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlayerCancelRallyAns}
   */
  public static final class PlayerCancelRallyAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlayerCancelRallyAns)
      PlayerCancelRallyAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlayerCancelRallyAns.newBuilder() to construct.
    private PlayerCancelRallyAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlayerCancelRallyAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlayerCancelRallyAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlayerCancelRallyAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns.class, com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns other = (com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlayerCancelRallyAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlayerCancelRallyAns)
        com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns.class, com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_PlayerCancelRallyAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns build() {
        com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns result = new com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlayerCancelRallyAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlayerCancelRallyAns)
    private static final com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlayerCancelRallyAns>
        PARSER = new com.google.protobuf.AbstractParser<PlayerCancelRallyAns>() {
      @java.lang.Override
      public PlayerCancelRallyAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlayerCancelRallyAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlayerCancelRallyAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlayerCancelRallyAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.PlayerCancelRallyAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RepatriateRallyMemberAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RepatriateRallyMemberAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return Whether the rallyId field is set.
     */
    boolean hasRallyId();
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return The rallyId.
     */
    long getRallyId();

    /**
     * <code>optional int64 armyId = 3;</code>
     * @return Whether the armyId field is set.
     */
    boolean hasArmyId();
    /**
     * <code>optional int64 armyId = 3;</code>
     * @return The armyId.
     */
    long getArmyId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RepatriateRallyMemberAsk}
   */
  public static final class RepatriateRallyMemberAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RepatriateRallyMemberAsk)
      RepatriateRallyMemberAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RepatriateRallyMemberAsk.newBuilder() to construct.
    private RepatriateRallyMemberAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RepatriateRallyMemberAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RepatriateRallyMemberAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RepatriateRallyMemberAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              rallyId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              armyId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk.class, com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int RALLYID_FIELD_NUMBER = 2;
    private long rallyId_;
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return Whether the rallyId field is set.
     */
    @java.lang.Override
    public boolean hasRallyId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 rallyId = 2;</code>
     * @return The rallyId.
     */
    @java.lang.Override
    public long getRallyId() {
      return rallyId_;
    }

    public static final int ARMYID_FIELD_NUMBER = 3;
    private long armyId_;
    /**
     * <code>optional int64 armyId = 3;</code>
     * @return Whether the armyId field is set.
     */
    @java.lang.Override
    public boolean hasArmyId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 armyId = 3;</code>
     * @return The armyId.
     */
    @java.lang.Override
    public long getArmyId() {
      return armyId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, rallyId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, armyId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, rallyId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, armyId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk other = (com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasRallyId() != other.hasRallyId()) return false;
      if (hasRallyId()) {
        if (getRallyId()
            != other.getRallyId()) return false;
      }
      if (hasArmyId() != other.hasArmyId()) return false;
      if (hasArmyId()) {
        if (getArmyId()
            != other.getArmyId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasRallyId()) {
        hash = (37 * hash) + RALLYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRallyId());
      }
      if (hasArmyId()) {
        hash = (37 * hash) + ARMYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getArmyId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RepatriateRallyMemberAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RepatriateRallyMemberAsk)
        com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk.class, com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        rallyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        armyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk build() {
        com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk result = new com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rallyId_ = rallyId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.armyId_ = armyId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasRallyId()) {
          setRallyId(other.getRallyId());
        }
        if (other.hasArmyId()) {
          setArmyId(other.getArmyId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long rallyId_ ;
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return Whether the rallyId field is set.
       */
      @java.lang.Override
      public boolean hasRallyId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return The rallyId.
       */
      @java.lang.Override
      public long getRallyId() {
        return rallyId_;
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @param value The rallyId to set.
       * @return This builder for chaining.
       */
      public Builder setRallyId(long value) {
        bitField0_ |= 0x00000002;
        rallyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 rallyId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRallyId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rallyId_ = 0L;
        onChanged();
        return this;
      }

      private long armyId_ ;
      /**
       * <code>optional int64 armyId = 3;</code>
       * @return Whether the armyId field is set.
       */
      @java.lang.Override
      public boolean hasArmyId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 armyId = 3;</code>
       * @return The armyId.
       */
      @java.lang.Override
      public long getArmyId() {
        return armyId_;
      }
      /**
       * <code>optional int64 armyId = 3;</code>
       * @param value The armyId to set.
       * @return This builder for chaining.
       */
      public Builder setArmyId(long value) {
        bitField0_ |= 0x00000004;
        armyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 armyId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearArmyId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        armyId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RepatriateRallyMemberAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RepatriateRallyMemberAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RepatriateRallyMemberAsk>
        PARSER = new com.google.protobuf.AbstractParser<RepatriateRallyMemberAsk>() {
      @java.lang.Override
      public RepatriateRallyMemberAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RepatriateRallyMemberAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RepatriateRallyMemberAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RepatriateRallyMemberAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RepatriateRallyMemberAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RepatriateRallyMemberAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.RepatriateRallyMemberAns}
   */
  public static final class RepatriateRallyMemberAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RepatriateRallyMemberAns)
      RepatriateRallyMemberAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RepatriateRallyMemberAns.newBuilder() to construct.
    private RepatriateRallyMemberAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RepatriateRallyMemberAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RepatriateRallyMemberAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RepatriateRallyMemberAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns.class, com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns other = (com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RepatriateRallyMemberAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RepatriateRallyMemberAns)
        com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns.class, com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateRallyMemberAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns build() {
        com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns result = new com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RepatriateRallyMemberAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RepatriateRallyMemberAns)
    private static final com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RepatriateRallyMemberAns>
        PARSER = new com.google.protobuf.AbstractParser<RepatriateRallyMemberAns>() {
      @java.lang.Override
      public RepatriateRallyMemberAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RepatriateRallyMemberAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RepatriateRallyMemberAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RepatriateRallyMemberAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.RepatriateRallyMemberAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetRallyRecommendSoldierAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SetRallyRecommendSoldierAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 entityId = 2;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <code>optional int64 entityId = 2;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
     * @return Whether the soldierType field is set.
     */
    boolean hasSoldierType();
    /**
     * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
     * @return The soldierType.
     */
    com.yorha.proto.Basic.Int32List getSoldierType();
    /**
     * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
     */
    com.yorha.proto.Basic.Int32ListOrBuilder getSoldierTypeOrBuilder();

    /**
     * <code>optional bool isAssist = 4;</code>
     * @return Whether the isAssist field is set.
     */
    boolean hasIsAssist();
    /**
     * <code>optional bool isAssist = 4;</code>
     * @return The isAssist.
     */
    boolean getIsAssist();

    /**
     * <code>optional bool isPermission = 5;</code>
     * @return Whether the isPermission field is set.
     */
    boolean hasIsPermission();
    /**
     * <code>optional bool isPermission = 5;</code>
     * @return The isPermission.
     */
    boolean getIsPermission();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SetRallyRecommendSoldierAsk}
   */
  public static final class SetRallyRecommendSoldierAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SetRallyRecommendSoldierAsk)
      SetRallyRecommendSoldierAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetRallyRecommendSoldierAsk.newBuilder() to construct.
    private SetRallyRecommendSoldierAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetRallyRecommendSoldierAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetRallyRecommendSoldierAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SetRallyRecommendSoldierAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              entityId_ = input.readInt64();
              break;
            }
            case 26: {
              com.yorha.proto.Basic.Int32List.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = soldierType_.toBuilder();
              }
              soldierType_ = input.readMessage(com.yorha.proto.Basic.Int32List.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(soldierType_);
                soldierType_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              isAssist_ = input.readBool();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              isPermission_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk.class, com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ENTITYID_FIELD_NUMBER = 2;
    private long entityId_;
    /**
     * <code>optional int64 entityId = 2;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 entityId = 2;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int SOLDIERTYPE_FIELD_NUMBER = 3;
    private com.yorha.proto.Basic.Int32List soldierType_;
    /**
     * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
     * @return Whether the soldierType field is set.
     */
    @java.lang.Override
    public boolean hasSoldierType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
     * @return The soldierType.
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int32List getSoldierType() {
      return soldierType_ == null ? com.yorha.proto.Basic.Int32List.getDefaultInstance() : soldierType_;
    }
    /**
     * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int32ListOrBuilder getSoldierTypeOrBuilder() {
      return soldierType_ == null ? com.yorha.proto.Basic.Int32List.getDefaultInstance() : soldierType_;
    }

    public static final int ISASSIST_FIELD_NUMBER = 4;
    private boolean isAssist_;
    /**
     * <code>optional bool isAssist = 4;</code>
     * @return Whether the isAssist field is set.
     */
    @java.lang.Override
    public boolean hasIsAssist() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool isAssist = 4;</code>
     * @return The isAssist.
     */
    @java.lang.Override
    public boolean getIsAssist() {
      return isAssist_;
    }

    public static final int ISPERMISSION_FIELD_NUMBER = 5;
    private boolean isPermission_;
    /**
     * <code>optional bool isPermission = 5;</code>
     * @return Whether the isPermission field is set.
     */
    @java.lang.Override
    public boolean hasIsPermission() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bool isPermission = 5;</code>
     * @return The isPermission.
     */
    @java.lang.Override
    public boolean getIsPermission() {
      return isPermission_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, entityId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getSoldierType());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, isAssist_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBool(5, isPermission_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, entityId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getSoldierType());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, isAssist_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, isPermission_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk other = (com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (hasSoldierType() != other.hasSoldierType()) return false;
      if (hasSoldierType()) {
        if (!getSoldierType()
            .equals(other.getSoldierType())) return false;
      }
      if (hasIsAssist() != other.hasIsAssist()) return false;
      if (hasIsAssist()) {
        if (getIsAssist()
            != other.getIsAssist()) return false;
      }
      if (hasIsPermission() != other.hasIsPermission()) return false;
      if (hasIsPermission()) {
        if (getIsPermission()
            != other.getIsPermission()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      if (hasSoldierType()) {
        hash = (37 * hash) + SOLDIERTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierType().hashCode();
      }
      if (hasIsAssist()) {
        hash = (37 * hash) + ISASSIST_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsAssist());
      }
      if (hasIsPermission()) {
        hash = (37 * hash) + ISPERMISSION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsPermission());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SetRallyRecommendSoldierAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SetRallyRecommendSoldierAsk)
        com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk.class, com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSoldierTypeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (soldierTypeBuilder_ == null) {
          soldierType_ = null;
        } else {
          soldierTypeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        isAssist_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        isPermission_ = false;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk build() {
        com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk result = new com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (soldierTypeBuilder_ == null) {
            result.soldierType_ = soldierType_;
          } else {
            result.soldierType_ = soldierTypeBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.isAssist_ = isAssist_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.isPermission_ = isPermission_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        if (other.hasSoldierType()) {
          mergeSoldierType(other.getSoldierType());
        }
        if (other.hasIsAssist()) {
          setIsAssist(other.getIsAssist());
        }
        if (other.hasIsPermission()) {
          setIsPermission(other.getIsPermission());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long entityId_ ;
      /**
       * <code>optional int64 entityId = 2;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 entityId = 2;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>optional int64 entityId = 2;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000002;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.Basic.Int32List soldierType_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int32List, com.yorha.proto.Basic.Int32List.Builder, com.yorha.proto.Basic.Int32ListOrBuilder> soldierTypeBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       * @return Whether the soldierType field is set.
       */
      public boolean hasSoldierType() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       * @return The soldierType.
       */
      public com.yorha.proto.Basic.Int32List getSoldierType() {
        if (soldierTypeBuilder_ == null) {
          return soldierType_ == null ? com.yorha.proto.Basic.Int32List.getDefaultInstance() : soldierType_;
        } else {
          return soldierTypeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       */
      public Builder setSoldierType(com.yorha.proto.Basic.Int32List value) {
        if (soldierTypeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          soldierType_ = value;
          onChanged();
        } else {
          soldierTypeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       */
      public Builder setSoldierType(
          com.yorha.proto.Basic.Int32List.Builder builderForValue) {
        if (soldierTypeBuilder_ == null) {
          soldierType_ = builderForValue.build();
          onChanged();
        } else {
          soldierTypeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       */
      public Builder mergeSoldierType(com.yorha.proto.Basic.Int32List value) {
        if (soldierTypeBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              soldierType_ != null &&
              soldierType_ != com.yorha.proto.Basic.Int32List.getDefaultInstance()) {
            soldierType_ =
              com.yorha.proto.Basic.Int32List.newBuilder(soldierType_).mergeFrom(value).buildPartial();
          } else {
            soldierType_ = value;
          }
          onChanged();
        } else {
          soldierTypeBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       */
      public Builder clearSoldierType() {
        if (soldierTypeBuilder_ == null) {
          soldierType_ = null;
          onChanged();
        } else {
          soldierTypeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       */
      public com.yorha.proto.Basic.Int32List.Builder getSoldierTypeBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getSoldierTypeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       */
      public com.yorha.proto.Basic.Int32ListOrBuilder getSoldierTypeOrBuilder() {
        if (soldierTypeBuilder_ != null) {
          return soldierTypeBuilder_.getMessageOrBuilder();
        } else {
          return soldierType_ == null ?
              com.yorha.proto.Basic.Int32List.getDefaultInstance() : soldierType_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32List soldierType = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int32List, com.yorha.proto.Basic.Int32List.Builder, com.yorha.proto.Basic.Int32ListOrBuilder> 
          getSoldierTypeFieldBuilder() {
        if (soldierTypeBuilder_ == null) {
          soldierTypeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Basic.Int32List, com.yorha.proto.Basic.Int32List.Builder, com.yorha.proto.Basic.Int32ListOrBuilder>(
                  getSoldierType(),
                  getParentForChildren(),
                  isClean());
          soldierType_ = null;
        }
        return soldierTypeBuilder_;
      }

      private boolean isAssist_ ;
      /**
       * <code>optional bool isAssist = 4;</code>
       * @return Whether the isAssist field is set.
       */
      @java.lang.Override
      public boolean hasIsAssist() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bool isAssist = 4;</code>
       * @return The isAssist.
       */
      @java.lang.Override
      public boolean getIsAssist() {
        return isAssist_;
      }
      /**
       * <code>optional bool isAssist = 4;</code>
       * @param value The isAssist to set.
       * @return This builder for chaining.
       */
      public Builder setIsAssist(boolean value) {
        bitField0_ |= 0x00000008;
        isAssist_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isAssist = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsAssist() {
        bitField0_ = (bitField0_ & ~0x00000008);
        isAssist_ = false;
        onChanged();
        return this;
      }

      private boolean isPermission_ ;
      /**
       * <code>optional bool isPermission = 5;</code>
       * @return Whether the isPermission field is set.
       */
      @java.lang.Override
      public boolean hasIsPermission() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bool isPermission = 5;</code>
       * @return The isPermission.
       */
      @java.lang.Override
      public boolean getIsPermission() {
        return isPermission_;
      }
      /**
       * <code>optional bool isPermission = 5;</code>
       * @param value The isPermission to set.
       * @return This builder for chaining.
       */
      public Builder setIsPermission(boolean value) {
        bitField0_ |= 0x00000010;
        isPermission_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isPermission = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsPermission() {
        bitField0_ = (bitField0_ & ~0x00000010);
        isPermission_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SetRallyRecommendSoldierAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SetRallyRecommendSoldierAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SetRallyRecommendSoldierAsk>
        PARSER = new com.google.protobuf.AbstractParser<SetRallyRecommendSoldierAsk>() {
      @java.lang.Override
      public SetRallyRecommendSoldierAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SetRallyRecommendSoldierAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SetRallyRecommendSoldierAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetRallyRecommendSoldierAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetRallyRecommendSoldierAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SetRallyRecommendSoldierAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.SetRallyRecommendSoldierAns}
   */
  public static final class SetRallyRecommendSoldierAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SetRallyRecommendSoldierAns)
      SetRallyRecommendSoldierAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetRallyRecommendSoldierAns.newBuilder() to construct.
    private SetRallyRecommendSoldierAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetRallyRecommendSoldierAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetRallyRecommendSoldierAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SetRallyRecommendSoldierAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns.class, com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns other = (com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SetRallyRecommendSoldierAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SetRallyRecommendSoldierAns)
        com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns.class, com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns build() {
        com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns result = new com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SetRallyRecommendSoldierAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SetRallyRecommendSoldierAns)
    private static final com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SetRallyRecommendSoldierAns>
        PARSER = new com.google.protobuf.AbstractParser<SetRallyRecommendSoldierAns>() {
      @java.lang.Override
      public SetRallyRecommendSoldierAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SetRallyRecommendSoldierAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SetRallyRecommendSoldierAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetRallyRecommendSoldierAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.SetRallyRecommendSoldierAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchWarningAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchWarningAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchWarningAsk}
   */
  public static final class FetchWarningAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchWarningAsk)
      FetchWarningAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchWarningAsk.newBuilder() to construct.
    private FetchWarningAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchWarningAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchWarningAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchWarningAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk.class, com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk other = (com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchWarningAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchWarningAsk)
        com.yorha.proto.SsSceneRallyAssist.FetchWarningAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk.class, com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk build() {
        com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk result = new com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchWarningAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchWarningAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchWarningAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchWarningAsk>() {
      @java.lang.Override
      public FetchWarningAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchWarningAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchWarningAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchWarningAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.FetchWarningAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchWarningAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchWarningAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchWarningAns}
   */
  public static final class FetchWarningAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchWarningAns)
      FetchWarningAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchWarningAns.newBuilder() to construct.
    private FetchWarningAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchWarningAns() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchWarningAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchWarningAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              bitField0_ |= 0x00000001;
              msgBytes_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.FetchWarningAns.class, com.yorha.proto.SsSceneRallyAssist.FetchWarningAns.Builder.class);
    }

    private int bitField0_;
    public static final int MSGBYTES_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, msgBytes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, msgBytes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.FetchWarningAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.FetchWarningAns other = (com.yorha.proto.SsSceneRallyAssist.FetchWarningAns) obj;

      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.FetchWarningAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchWarningAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchWarningAns)
        com.yorha.proto.SsSceneRallyAssist.FetchWarningAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.FetchWarningAns.class, com.yorha.proto.SsSceneRallyAssist.FetchWarningAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.FetchWarningAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchWarningAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchWarningAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.FetchWarningAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchWarningAns build() {
        com.yorha.proto.SsSceneRallyAssist.FetchWarningAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchWarningAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.FetchWarningAns result = new com.yorha.proto.SsSceneRallyAssist.FetchWarningAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.msgBytes_ = msgBytes_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.FetchWarningAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.FetchWarningAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.FetchWarningAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.FetchWarningAns.getDefaultInstance()) return this;
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.FetchWarningAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.FetchWarningAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchWarningAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchWarningAns)
    private static final com.yorha.proto.SsSceneRallyAssist.FetchWarningAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.FetchWarningAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchWarningAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchWarningAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchWarningAns>() {
      @java.lang.Override
      public FetchWarningAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchWarningAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchWarningAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchWarningAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.FetchWarningAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetWarningItemTagAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SetWarningItemTagAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 armyId = 2;</code>
     * @return Whether the armyId field is set.
     */
    boolean hasArmyId();
    /**
     * <code>optional int64 armyId = 2;</code>
     * @return The armyId.
     */
    long getArmyId();

    /**
     * <code>optional bool ignore = 3;</code>
     * @return Whether the ignore field is set.
     */
    boolean hasIgnore();
    /**
     * <code>optional bool ignore = 3;</code>
     * @return The ignore.
     */
    boolean getIgnore();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SetWarningItemTagAsk}
   */
  public static final class SetWarningItemTagAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SetWarningItemTagAsk)
      SetWarningItemTagAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetWarningItemTagAsk.newBuilder() to construct.
    private SetWarningItemTagAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetWarningItemTagAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetWarningItemTagAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SetWarningItemTagAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              armyId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              ignore_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk.class, com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ARMYID_FIELD_NUMBER = 2;
    private long armyId_;
    /**
     * <code>optional int64 armyId = 2;</code>
     * @return Whether the armyId field is set.
     */
    @java.lang.Override
    public boolean hasArmyId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 armyId = 2;</code>
     * @return The armyId.
     */
    @java.lang.Override
    public long getArmyId() {
      return armyId_;
    }

    public static final int IGNORE_FIELD_NUMBER = 3;
    private boolean ignore_;
    /**
     * <code>optional bool ignore = 3;</code>
     * @return Whether the ignore field is set.
     */
    @java.lang.Override
    public boolean hasIgnore() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool ignore = 3;</code>
     * @return The ignore.
     */
    @java.lang.Override
    public boolean getIgnore() {
      return ignore_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, armyId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(3, ignore_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, armyId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, ignore_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk other = (com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasArmyId() != other.hasArmyId()) return false;
      if (hasArmyId()) {
        if (getArmyId()
            != other.getArmyId()) return false;
      }
      if (hasIgnore() != other.hasIgnore()) return false;
      if (hasIgnore()) {
        if (getIgnore()
            != other.getIgnore()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasArmyId()) {
        hash = (37 * hash) + ARMYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getArmyId());
      }
      if (hasIgnore()) {
        hash = (37 * hash) + IGNORE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIgnore());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SetWarningItemTagAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SetWarningItemTagAsk)
        com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk.class, com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        armyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        ignore_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk build() {
        com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk result = new com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.armyId_ = armyId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.ignore_ = ignore_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasArmyId()) {
          setArmyId(other.getArmyId());
        }
        if (other.hasIgnore()) {
          setIgnore(other.getIgnore());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long armyId_ ;
      /**
       * <code>optional int64 armyId = 2;</code>
       * @return Whether the armyId field is set.
       */
      @java.lang.Override
      public boolean hasArmyId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 armyId = 2;</code>
       * @return The armyId.
       */
      @java.lang.Override
      public long getArmyId() {
        return armyId_;
      }
      /**
       * <code>optional int64 armyId = 2;</code>
       * @param value The armyId to set.
       * @return This builder for chaining.
       */
      public Builder setArmyId(long value) {
        bitField0_ |= 0x00000002;
        armyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 armyId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearArmyId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        armyId_ = 0L;
        onChanged();
        return this;
      }

      private boolean ignore_ ;
      /**
       * <code>optional bool ignore = 3;</code>
       * @return Whether the ignore field is set.
       */
      @java.lang.Override
      public boolean hasIgnore() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool ignore = 3;</code>
       * @return The ignore.
       */
      @java.lang.Override
      public boolean getIgnore() {
        return ignore_;
      }
      /**
       * <code>optional bool ignore = 3;</code>
       * @param value The ignore to set.
       * @return This builder for chaining.
       */
      public Builder setIgnore(boolean value) {
        bitField0_ |= 0x00000004;
        ignore_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool ignore = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIgnore() {
        bitField0_ = (bitField0_ & ~0x00000004);
        ignore_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SetWarningItemTagAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SetWarningItemTagAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SetWarningItemTagAsk>
        PARSER = new com.google.protobuf.AbstractParser<SetWarningItemTagAsk>() {
      @java.lang.Override
      public SetWarningItemTagAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SetWarningItemTagAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SetWarningItemTagAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetWarningItemTagAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetWarningItemTagAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SetWarningItemTagAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.SetWarningItemTagAns}
   */
  public static final class SetWarningItemTagAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SetWarningItemTagAns)
      SetWarningItemTagAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetWarningItemTagAns.newBuilder() to construct.
    private SetWarningItemTagAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetWarningItemTagAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SetWarningItemTagAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SetWarningItemTagAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns.class, com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns other = (com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SetWarningItemTagAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SetWarningItemTagAns)
        com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns.class, com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_SetWarningItemTagAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns build() {
        com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns result = new com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SetWarningItemTagAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SetWarningItemTagAns)
    private static final com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SetWarningItemTagAns>
        PARSER = new com.google.protobuf.AbstractParser<SetWarningItemTagAns>() {
      @java.lang.Override
      public SetWarningItemTagAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SetWarningItemTagAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SetWarningItemTagAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetWarningItemTagAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.SetWarningItemTagAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IgnoreAllWarningAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.IgnoreAllWarningAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.IgnoreAllWarningAsk}
   */
  public static final class IgnoreAllWarningAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.IgnoreAllWarningAsk)
      IgnoreAllWarningAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IgnoreAllWarningAsk.newBuilder() to construct.
    private IgnoreAllWarningAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IgnoreAllWarningAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IgnoreAllWarningAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IgnoreAllWarningAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk.class, com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk other = (com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.IgnoreAllWarningAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.IgnoreAllWarningAsk)
        com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk.class, com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk build() {
        com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk result = new com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.IgnoreAllWarningAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.IgnoreAllWarningAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IgnoreAllWarningAsk>
        PARSER = new com.google.protobuf.AbstractParser<IgnoreAllWarningAsk>() {
      @java.lang.Override
      public IgnoreAllWarningAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IgnoreAllWarningAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IgnoreAllWarningAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IgnoreAllWarningAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IgnoreAllWarningAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.IgnoreAllWarningAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.IgnoreAllWarningAns}
   */
  public static final class IgnoreAllWarningAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.IgnoreAllWarningAns)
      IgnoreAllWarningAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IgnoreAllWarningAns.newBuilder() to construct.
    private IgnoreAllWarningAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IgnoreAllWarningAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IgnoreAllWarningAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IgnoreAllWarningAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns.class, com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns other = (com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.IgnoreAllWarningAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.IgnoreAllWarningAns)
        com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns.class, com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_IgnoreAllWarningAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns build() {
        com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns result = new com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.IgnoreAllWarningAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.IgnoreAllWarningAns)
    private static final com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IgnoreAllWarningAns>
        PARSER = new com.google.protobuf.AbstractParser<IgnoreAllWarningAns>() {
      @java.lang.Override
      public IgnoreAllWarningAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IgnoreAllWarningAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IgnoreAllWarningAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IgnoreAllWarningAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.IgnoreAllWarningAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchInnerArmyAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchInnerArmyAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 发起行为的玩家id
     * </pre>
     *
     * <code>optional int64 operatorId = 1;</code>
     * @return Whether the operatorId field is set.
     */
    boolean hasOperatorId();
    /**
     * <pre>
     * 发起行为的玩家id
     * </pre>
     *
     * <code>optional int64 operatorId = 1;</code>
     * @return The operatorId.
     */
    long getOperatorId();

    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 2;</code>
     * @return Whether the targetId field is set.
     */
    boolean hasTargetId();
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 2;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>optional int64 targetPlayerId = 3;</code>
     * @return Whether the targetPlayerId field is set.
     */
    boolean hasTargetPlayerId();
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>optional int64 targetPlayerId = 3;</code>
     * @return The targetPlayerId.
     */
    long getTargetPlayerId();

    /**
     * <pre>
     * 请求的版本号
     * </pre>
     *
     * <code>optional int64 version = 4;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <pre>
     * 请求的版本号
     * </pre>
     *
     * <code>optional int64 version = 4;</code>
     * @return The version.
     */
    long getVersion();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchInnerArmyAsk}
   */
  public static final class FetchInnerArmyAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchInnerArmyAsk)
      FetchInnerArmyAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchInnerArmyAsk.newBuilder() to construct.
    private FetchInnerArmyAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchInnerArmyAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchInnerArmyAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchInnerArmyAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              operatorId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              targetId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              targetPlayerId_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              version_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk.class, com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk.Builder.class);
    }

    private int bitField0_;
    public static final int OPERATORID_FIELD_NUMBER = 1;
    private long operatorId_;
    /**
     * <pre>
     * 发起行为的玩家id
     * </pre>
     *
     * <code>optional int64 operatorId = 1;</code>
     * @return Whether the operatorId field is set.
     */
    @java.lang.Override
    public boolean hasOperatorId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 发起行为的玩家id
     * </pre>
     *
     * <code>optional int64 operatorId = 1;</code>
     * @return The operatorId.
     */
    @java.lang.Override
    public long getOperatorId() {
      return operatorId_;
    }

    public static final int TARGETID_FIELD_NUMBER = 2;
    private long targetId_;
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 2;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 2;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int TARGETPLAYERID_FIELD_NUMBER = 3;
    private long targetPlayerId_;
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>optional int64 targetPlayerId = 3;</code>
     * @return Whether the targetPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasTargetPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>optional int64 targetPlayerId = 3;</code>
     * @return The targetPlayerId.
     */
    @java.lang.Override
    public long getTargetPlayerId() {
      return targetPlayerId_;
    }

    public static final int VERSION_FIELD_NUMBER = 4;
    private long version_;
    /**
     * <pre>
     * 请求的版本号
     * </pre>
     *
     * <code>optional int64 version = 4;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 请求的版本号
     * </pre>
     *
     * <code>optional int64 version = 4;</code>
     * @return The version.
     */
    @java.lang.Override
    public long getVersion() {
      return version_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, operatorId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, targetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, targetPlayerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, version_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, operatorId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, targetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, targetPlayerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, version_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk other = (com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk) obj;

      if (hasOperatorId() != other.hasOperatorId()) return false;
      if (hasOperatorId()) {
        if (getOperatorId()
            != other.getOperatorId()) return false;
      }
      if (hasTargetId() != other.hasTargetId()) return false;
      if (hasTargetId()) {
        if (getTargetId()
            != other.getTargetId()) return false;
      }
      if (hasTargetPlayerId() != other.hasTargetPlayerId()) return false;
      if (hasTargetPlayerId()) {
        if (getTargetPlayerId()
            != other.getTargetPlayerId()) return false;
      }
      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOperatorId()) {
        hash = (37 * hash) + OPERATORID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOperatorId());
      }
      if (hasTargetId()) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetId());
      }
      if (hasTargetPlayerId()) {
        hash = (37 * hash) + TARGETPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetPlayerId());
      }
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getVersion());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchInnerArmyAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchInnerArmyAsk)
        com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk.class, com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        operatorId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        targetPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        version_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk build() {
        com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk result = new com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.operatorId_ = operatorId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetId_ = targetId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.targetPlayerId_ = targetPlayerId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk.getDefaultInstance()) return this;
        if (other.hasOperatorId()) {
          setOperatorId(other.getOperatorId());
        }
        if (other.hasTargetId()) {
          setTargetId(other.getTargetId());
        }
        if (other.hasTargetPlayerId()) {
          setTargetPlayerId(other.getTargetPlayerId());
        }
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long operatorId_ ;
      /**
       * <pre>
       * 发起行为的玩家id
       * </pre>
       *
       * <code>optional int64 operatorId = 1;</code>
       * @return Whether the operatorId field is set.
       */
      @java.lang.Override
      public boolean hasOperatorId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 发起行为的玩家id
       * </pre>
       *
       * <code>optional int64 operatorId = 1;</code>
       * @return The operatorId.
       */
      @java.lang.Override
      public long getOperatorId() {
        return operatorId_;
      }
      /**
       * <pre>
       * 发起行为的玩家id
       * </pre>
       *
       * <code>optional int64 operatorId = 1;</code>
       * @param value The operatorId to set.
       * @return This builder for chaining.
       */
      public Builder setOperatorId(long value) {
        bitField0_ |= 0x00000001;
        operatorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发起行为的玩家id
       * </pre>
       *
       * <code>optional int64 operatorId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperatorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        operatorId_ = 0L;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 2;</code>
       * @return Whether the targetId field is set.
       */
      @java.lang.Override
      public boolean hasTargetId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 2;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 2;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        bitField0_ |= 0x00000002;
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private long targetPlayerId_ ;
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>optional int64 targetPlayerId = 3;</code>
       * @return Whether the targetPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasTargetPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>optional int64 targetPlayerId = 3;</code>
       * @return The targetPlayerId.
       */
      @java.lang.Override
      public long getTargetPlayerId() {
        return targetPlayerId_;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>optional int64 targetPlayerId = 3;</code>
       * @param value The targetPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetPlayerId(long value) {
        bitField0_ |= 0x00000004;
        targetPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>optional int64 targetPlayerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        targetPlayerId_ = 0L;
        onChanged();
        return this;
      }

      private long version_ ;
      /**
       * <pre>
       * 请求的版本号
       * </pre>
       *
       * <code>optional int64 version = 4;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 请求的版本号
       * </pre>
       *
       * <code>optional int64 version = 4;</code>
       * @return The version.
       */
      @java.lang.Override
      public long getVersion() {
        return version_;
      }
      /**
       * <pre>
       * 请求的版本号
       * </pre>
       *
       * <code>optional int64 version = 4;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(long value) {
        bitField0_ |= 0x00000008;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求的版本号
       * </pre>
       *
       * <code>optional int64 version = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000008);
        version_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchInnerArmyAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchInnerArmyAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchInnerArmyAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchInnerArmyAsk>() {
      @java.lang.Override
      public FetchInnerArmyAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchInnerArmyAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchInnerArmyAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchInnerArmyAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchInnerArmyAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchInnerArmyAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();

    /**
     * <pre>
     * 当以目标玩家id的方式发送ask时，需要返回对应的targetId
     * </pre>
     *
     * <code>optional int64 targetId = 2;</code>
     * @return Whether the targetId field is set.
     */
    boolean hasTargetId();
    /**
     * <pre>
     * 当以目标玩家id的方式发送ask时，需要返回对应的targetId
     * </pre>
     *
     * <code>optional int64 targetId = 2;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <pre>
     * 返回的版本号，仅当请求中有targetId时返回
     * </pre>
     *
     * <code>optional int64 version = 3;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <pre>
     * 返回的版本号，仅当请求中有targetId时返回
     * </pre>
     *
     * <code>optional int64 version = 3;</code>
     * @return The version.
     */
    long getVersion();

    /**
     * <pre>
     * 界面上据点、军团建筑、军团资源中心等的进度条信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
     * @return Whether the progress field is set.
     */
    boolean hasProgress();
    /**
     * <pre>
     * 界面上据点、军团建筑、军团资源中心等的进度条信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
     * @return The progress.
     */
    com.yorha.proto.StructCommon.ProgressInfo getProgress();
    /**
     * <pre>
     * 界面上据点、军团建筑、军团资源中心等的进度条信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
     */
    com.yorha.proto.StructCommon.ProgressInfoOrBuilder getProgressOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchInnerArmyAns}
   */
  public static final class FetchInnerArmyAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchInnerArmyAns)
      FetchInnerArmyAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchInnerArmyAns.newBuilder() to construct.
    private FetchInnerArmyAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchInnerArmyAns() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchInnerArmyAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchInnerArmyAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              bitField0_ |= 0x00000001;
              msgBytes_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              targetId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              version_ = input.readInt64();
              break;
            }
            case 34: {
              com.yorha.proto.StructCommon.ProgressInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = progress_.toBuilder();
              }
              progress_ = input.readMessage(com.yorha.proto.StructCommon.ProgressInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(progress_);
                progress_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns.class, com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns.Builder.class);
    }

    private int bitField0_;
    public static final int MSGBYTES_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 1;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    public static final int TARGETID_FIELD_NUMBER = 2;
    private long targetId_;
    /**
     * <pre>
     * 当以目标玩家id的方式发送ask时，需要返回对应的targetId
     * </pre>
     *
     * <code>optional int64 targetId = 2;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 当以目标玩家id的方式发送ask时，需要返回对应的targetId
     * </pre>
     *
     * <code>optional int64 targetId = 2;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int VERSION_FIELD_NUMBER = 3;
    private long version_;
    /**
     * <pre>
     * 返回的版本号，仅当请求中有targetId时返回
     * </pre>
     *
     * <code>optional int64 version = 3;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 返回的版本号，仅当请求中有targetId时返回
     * </pre>
     *
     * <code>optional int64 version = 3;</code>
     * @return The version.
     */
    @java.lang.Override
    public long getVersion() {
      return version_;
    }

    public static final int PROGRESS_FIELD_NUMBER = 4;
    private com.yorha.proto.StructCommon.ProgressInfo progress_;
    /**
     * <pre>
     * 界面上据点、军团建筑、军团资源中心等的进度条信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
     * @return Whether the progress field is set.
     */
    @java.lang.Override
    public boolean hasProgress() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 界面上据点、军团建筑、军团资源中心等的进度条信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
     * @return The progress.
     */
    @java.lang.Override
    public com.yorha.proto.StructCommon.ProgressInfo getProgress() {
      return progress_ == null ? com.yorha.proto.StructCommon.ProgressInfo.getDefaultInstance() : progress_;
    }
    /**
     * <pre>
     * 界面上据点、军团建筑、军团资源中心等的进度条信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructCommon.ProgressInfoOrBuilder getProgressOrBuilder() {
      return progress_ == null ? com.yorha.proto.StructCommon.ProgressInfo.getDefaultInstance() : progress_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, msgBytes_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, targetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, version_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getProgress());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, msgBytes_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, targetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, version_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getProgress());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns other = (com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns) obj;

      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (hasTargetId() != other.hasTargetId()) return false;
      if (hasTargetId()) {
        if (getTargetId()
            != other.getTargetId()) return false;
      }
      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (hasProgress() != other.hasProgress()) return false;
      if (hasProgress()) {
        if (!getProgress()
            .equals(other.getProgress())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      if (hasTargetId()) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetId());
      }
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getVersion());
      }
      if (hasProgress()) {
        hash = (37 * hash) + PROGRESS_FIELD_NUMBER;
        hash = (53 * hash) + getProgress().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchInnerArmyAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchInnerArmyAns)
        com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns.class, com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getProgressFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        version_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (progressBuilder_ == null) {
          progress_ = null;
        } else {
          progressBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_FetchInnerArmyAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns build() {
        com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns result = new com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.msgBytes_ = msgBytes_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetId_ = targetId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (progressBuilder_ == null) {
            result.progress_ = progress_;
          } else {
            result.progress_ = progressBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns.getDefaultInstance()) return this;
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        if (other.hasTargetId()) {
          setTargetId(other.getTargetId());
        }
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        if (other.hasProgress()) {
          mergeProgress(other.getProgress());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <pre>
       * 当以目标玩家id的方式发送ask时，需要返回对应的targetId
       * </pre>
       *
       * <code>optional int64 targetId = 2;</code>
       * @return Whether the targetId field is set.
       */
      @java.lang.Override
      public boolean hasTargetId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 当以目标玩家id的方式发送ask时，需要返回对应的targetId
       * </pre>
       *
       * <code>optional int64 targetId = 2;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 当以目标玩家id的方式发送ask时，需要返回对应的targetId
       * </pre>
       *
       * <code>optional int64 targetId = 2;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        bitField0_ |= 0x00000002;
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当以目标玩家id的方式发送ask时，需要返回对应的targetId
       * </pre>
       *
       * <code>optional int64 targetId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private long version_ ;
      /**
       * <pre>
       * 返回的版本号，仅当请求中有targetId时返回
       * </pre>
       *
       * <code>optional int64 version = 3;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 返回的版本号，仅当请求中有targetId时返回
       * </pre>
       *
       * <code>optional int64 version = 3;</code>
       * @return The version.
       */
      @java.lang.Override
      public long getVersion() {
        return version_;
      }
      /**
       * <pre>
       * 返回的版本号，仅当请求中有targetId时返回
       * </pre>
       *
       * <code>optional int64 version = 3;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(long value) {
        bitField0_ |= 0x00000004;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 返回的版本号，仅当请求中有targetId时返回
       * </pre>
       *
       * <code>optional int64 version = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000004);
        version_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructCommon.ProgressInfo progress_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommon.ProgressInfo, com.yorha.proto.StructCommon.ProgressInfo.Builder, com.yorha.proto.StructCommon.ProgressInfoOrBuilder> progressBuilder_;
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       * @return Whether the progress field is set.
       */
      public boolean hasProgress() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       * @return The progress.
       */
      public com.yorha.proto.StructCommon.ProgressInfo getProgress() {
        if (progressBuilder_ == null) {
          return progress_ == null ? com.yorha.proto.StructCommon.ProgressInfo.getDefaultInstance() : progress_;
        } else {
          return progressBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       */
      public Builder setProgress(com.yorha.proto.StructCommon.ProgressInfo value) {
        if (progressBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          progress_ = value;
          onChanged();
        } else {
          progressBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       */
      public Builder setProgress(
          com.yorha.proto.StructCommon.ProgressInfo.Builder builderForValue) {
        if (progressBuilder_ == null) {
          progress_ = builderForValue.build();
          onChanged();
        } else {
          progressBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       */
      public Builder mergeProgress(com.yorha.proto.StructCommon.ProgressInfo value) {
        if (progressBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              progress_ != null &&
              progress_ != com.yorha.proto.StructCommon.ProgressInfo.getDefaultInstance()) {
            progress_ =
              com.yorha.proto.StructCommon.ProgressInfo.newBuilder(progress_).mergeFrom(value).buildPartial();
          } else {
            progress_ = value;
          }
          onChanged();
        } else {
          progressBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       */
      public Builder clearProgress() {
        if (progressBuilder_ == null) {
          progress_ = null;
          onChanged();
        } else {
          progressBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       */
      public com.yorha.proto.StructCommon.ProgressInfo.Builder getProgressBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getProgressFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       */
      public com.yorha.proto.StructCommon.ProgressInfoOrBuilder getProgressOrBuilder() {
        if (progressBuilder_ != null) {
          return progressBuilder_.getMessageOrBuilder();
        } else {
          return progress_ == null ?
              com.yorha.proto.StructCommon.ProgressInfo.getDefaultInstance() : progress_;
        }
      }
      /**
       * <pre>
       * 界面上据点、军团建筑、军团资源中心等的进度条信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfo progress = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommon.ProgressInfo, com.yorha.proto.StructCommon.ProgressInfo.Builder, com.yorha.proto.StructCommon.ProgressInfoOrBuilder> 
          getProgressFieldBuilder() {
        if (progressBuilder_ == null) {
          progressBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructCommon.ProgressInfo, com.yorha.proto.StructCommon.ProgressInfo.Builder, com.yorha.proto.StructCommon.ProgressInfoOrBuilder>(
                  getProgress(),
                  getParentForChildren(),
                  isClean());
          progress_ = null;
        }
        return progressBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchInnerArmyAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchInnerArmyAns)
    private static final com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchInnerArmyAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchInnerArmyAns>() {
      @java.lang.Override
      public FetchInnerArmyAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchInnerArmyAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchInnerArmyAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchInnerArmyAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.FetchInnerArmyAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RepatriateAssistMemberAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RepatriateAssistMemberAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 targetId = 2;</code>
     * @return Whether the targetId field is set.
     */
    boolean hasTargetId();
    /**
     * <code>optional int64 targetId = 2;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <code>optional int64 armyId = 3;</code>
     * @return Whether the armyId field is set.
     */
    boolean hasArmyId();
    /**
     * <code>optional int64 armyId = 3;</code>
     * @return The armyId.
     */
    long getArmyId();

    /**
     * <code>optional bool isPermission = 4;</code>
     * @return Whether the isPermission field is set.
     */
    boolean hasIsPermission();
    /**
     * <code>optional bool isPermission = 4;</code>
     * @return The isPermission.
     */
    boolean getIsPermission();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RepatriateAssistMemberAsk}
   */
  public static final class RepatriateAssistMemberAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RepatriateAssistMemberAsk)
      RepatriateAssistMemberAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RepatriateAssistMemberAsk.newBuilder() to construct.
    private RepatriateAssistMemberAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RepatriateAssistMemberAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RepatriateAssistMemberAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RepatriateAssistMemberAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              targetId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              armyId_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              isPermission_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk.class, com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int TARGETID_FIELD_NUMBER = 2;
    private long targetId_;
    /**
     * <code>optional int64 targetId = 2;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 targetId = 2;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int ARMYID_FIELD_NUMBER = 3;
    private long armyId_;
    /**
     * <code>optional int64 armyId = 3;</code>
     * @return Whether the armyId field is set.
     */
    @java.lang.Override
    public boolean hasArmyId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 armyId = 3;</code>
     * @return The armyId.
     */
    @java.lang.Override
    public long getArmyId() {
      return armyId_;
    }

    public static final int ISPERMISSION_FIELD_NUMBER = 4;
    private boolean isPermission_;
    /**
     * <code>optional bool isPermission = 4;</code>
     * @return Whether the isPermission field is set.
     */
    @java.lang.Override
    public boolean hasIsPermission() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool isPermission = 4;</code>
     * @return The isPermission.
     */
    @java.lang.Override
    public boolean getIsPermission() {
      return isPermission_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, targetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, armyId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, isPermission_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, targetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, armyId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, isPermission_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk other = (com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasTargetId() != other.hasTargetId()) return false;
      if (hasTargetId()) {
        if (getTargetId()
            != other.getTargetId()) return false;
      }
      if (hasArmyId() != other.hasArmyId()) return false;
      if (hasArmyId()) {
        if (getArmyId()
            != other.getArmyId()) return false;
      }
      if (hasIsPermission() != other.hasIsPermission()) return false;
      if (hasIsPermission()) {
        if (getIsPermission()
            != other.getIsPermission()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasTargetId()) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetId());
      }
      if (hasArmyId()) {
        hash = (37 * hash) + ARMYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getArmyId());
      }
      if (hasIsPermission()) {
        hash = (37 * hash) + ISPERMISSION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsPermission());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RepatriateAssistMemberAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RepatriateAssistMemberAsk)
        com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk.class, com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        armyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        isPermission_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk build() {
        com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk result = new com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetId_ = targetId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.armyId_ = armyId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.isPermission_ = isPermission_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasTargetId()) {
          setTargetId(other.getTargetId());
        }
        if (other.hasArmyId()) {
          setArmyId(other.getArmyId());
        }
        if (other.hasIsPermission()) {
          setIsPermission(other.getIsPermission());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <code>optional int64 targetId = 2;</code>
       * @return Whether the targetId field is set.
       */
      @java.lang.Override
      public boolean hasTargetId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 targetId = 2;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>optional int64 targetId = 2;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        bitField0_ |= 0x00000002;
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 targetId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private long armyId_ ;
      /**
       * <code>optional int64 armyId = 3;</code>
       * @return Whether the armyId field is set.
       */
      @java.lang.Override
      public boolean hasArmyId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 armyId = 3;</code>
       * @return The armyId.
       */
      @java.lang.Override
      public long getArmyId() {
        return armyId_;
      }
      /**
       * <code>optional int64 armyId = 3;</code>
       * @param value The armyId to set.
       * @return This builder for chaining.
       */
      public Builder setArmyId(long value) {
        bitField0_ |= 0x00000004;
        armyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 armyId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearArmyId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        armyId_ = 0L;
        onChanged();
        return this;
      }

      private boolean isPermission_ ;
      /**
       * <code>optional bool isPermission = 4;</code>
       * @return Whether the isPermission field is set.
       */
      @java.lang.Override
      public boolean hasIsPermission() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bool isPermission = 4;</code>
       * @return The isPermission.
       */
      @java.lang.Override
      public boolean getIsPermission() {
        return isPermission_;
      }
      /**
       * <code>optional bool isPermission = 4;</code>
       * @param value The isPermission to set.
       * @return This builder for chaining.
       */
      public Builder setIsPermission(boolean value) {
        bitField0_ |= 0x00000008;
        isPermission_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isPermission = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsPermission() {
        bitField0_ = (bitField0_ & ~0x00000008);
        isPermission_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RepatriateAssistMemberAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RepatriateAssistMemberAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RepatriateAssistMemberAsk>
        PARSER = new com.google.protobuf.AbstractParser<RepatriateAssistMemberAsk>() {
      @java.lang.Override
      public RepatriateAssistMemberAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RepatriateAssistMemberAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RepatriateAssistMemberAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RepatriateAssistMemberAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RepatriateAssistMemberAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RepatriateAssistMemberAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.RepatriateAssistMemberAns}
   */
  public static final class RepatriateAssistMemberAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RepatriateAssistMemberAns)
      RepatriateAssistMemberAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RepatriateAssistMemberAns.newBuilder() to construct.
    private RepatriateAssistMemberAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RepatriateAssistMemberAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RepatriateAssistMemberAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RepatriateAssistMemberAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns.class, com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns other = (com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RepatriateAssistMemberAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RepatriateAssistMemberAns)
        com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns.class, com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_RepatriateAssistMemberAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns build() {
        com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns result = new com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RepatriateAssistMemberAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RepatriateAssistMemberAns)
    private static final com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RepatriateAssistMemberAns>
        PARSER = new com.google.protobuf.AbstractParser<RepatriateAssistMemberAns>() {
      @java.lang.Override
      public RepatriateAssistMemberAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RepatriateAssistMemberAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RepatriateAssistMemberAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RepatriateAssistMemberAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.RepatriateAssistMemberAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeAssistLeaderAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeAssistLeaderAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 cityId = 2;</code>
     * @return Whether the cityId field is set.
     */
    boolean hasCityId();
    /**
     * <code>optional int64 cityId = 2;</code>
     * @return The cityId.
     */
    long getCityId();

    /**
     * <code>optional int64 targetId = 3;</code>
     * @return Whether the targetId field is set.
     */
    boolean hasTargetId();
    /**
     * <code>optional int64 targetId = 3;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <code>optional bool isPermission = 4;</code>
     * @return Whether the isPermission field is set.
     */
    boolean hasIsPermission();
    /**
     * <code>optional bool isPermission = 4;</code>
     * @return The isPermission.
     */
    boolean getIsPermission();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeAssistLeaderAsk}
   */
  public static final class ChangeAssistLeaderAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeAssistLeaderAsk)
      ChangeAssistLeaderAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeAssistLeaderAsk.newBuilder() to construct.
    private ChangeAssistLeaderAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeAssistLeaderAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeAssistLeaderAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeAssistLeaderAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              cityId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              targetId_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              isPermission_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk.class, com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int CITYID_FIELD_NUMBER = 2;
    private long cityId_;
    /**
     * <code>optional int64 cityId = 2;</code>
     * @return Whether the cityId field is set.
     */
    @java.lang.Override
    public boolean hasCityId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 cityId = 2;</code>
     * @return The cityId.
     */
    @java.lang.Override
    public long getCityId() {
      return cityId_;
    }

    public static final int TARGETID_FIELD_NUMBER = 3;
    private long targetId_;
    /**
     * <code>optional int64 targetId = 3;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 targetId = 3;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int ISPERMISSION_FIELD_NUMBER = 4;
    private boolean isPermission_;
    /**
     * <code>optional bool isPermission = 4;</code>
     * @return Whether the isPermission field is set.
     */
    @java.lang.Override
    public boolean hasIsPermission() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bool isPermission = 4;</code>
     * @return The isPermission.
     */
    @java.lang.Override
    public boolean getIsPermission() {
      return isPermission_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, cityId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, targetId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, isPermission_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, cityId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, targetId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, isPermission_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk other = (com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasCityId() != other.hasCityId()) return false;
      if (hasCityId()) {
        if (getCityId()
            != other.getCityId()) return false;
      }
      if (hasTargetId() != other.hasTargetId()) return false;
      if (hasTargetId()) {
        if (getTargetId()
            != other.getTargetId()) return false;
      }
      if (hasIsPermission() != other.hasIsPermission()) return false;
      if (hasIsPermission()) {
        if (getIsPermission()
            != other.getIsPermission()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasCityId()) {
        hash = (37 * hash) + CITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCityId());
      }
      if (hasTargetId()) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetId());
      }
      if (hasIsPermission()) {
        hash = (37 * hash) + ISPERMISSION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsPermission());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeAssistLeaderAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeAssistLeaderAsk)
        com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk.class, com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        cityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        isPermission_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk build() {
        com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk result = new com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.cityId_ = cityId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.targetId_ = targetId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.isPermission_ = isPermission_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasCityId()) {
          setCityId(other.getCityId());
        }
        if (other.hasTargetId()) {
          setTargetId(other.getTargetId());
        }
        if (other.hasIsPermission()) {
          setIsPermission(other.getIsPermission());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long cityId_ ;
      /**
       * <code>optional int64 cityId = 2;</code>
       * @return Whether the cityId field is set.
       */
      @java.lang.Override
      public boolean hasCityId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 cityId = 2;</code>
       * @return The cityId.
       */
      @java.lang.Override
      public long getCityId() {
        return cityId_;
      }
      /**
       * <code>optional int64 cityId = 2;</code>
       * @param value The cityId to set.
       * @return This builder for chaining.
       */
      public Builder setCityId(long value) {
        bitField0_ |= 0x00000002;
        cityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 cityId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCityId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        cityId_ = 0L;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <code>optional int64 targetId = 3;</code>
       * @return Whether the targetId field is set.
       */
      @java.lang.Override
      public boolean hasTargetId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 targetId = 3;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>optional int64 targetId = 3;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        bitField0_ |= 0x00000004;
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 targetId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private boolean isPermission_ ;
      /**
       * <code>optional bool isPermission = 4;</code>
       * @return Whether the isPermission field is set.
       */
      @java.lang.Override
      public boolean hasIsPermission() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bool isPermission = 4;</code>
       * @return The isPermission.
       */
      @java.lang.Override
      public boolean getIsPermission() {
        return isPermission_;
      }
      /**
       * <code>optional bool isPermission = 4;</code>
       * @param value The isPermission to set.
       * @return This builder for chaining.
       */
      public Builder setIsPermission(boolean value) {
        bitField0_ |= 0x00000008;
        isPermission_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isPermission = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsPermission() {
        bitField0_ = (bitField0_ & ~0x00000008);
        isPermission_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeAssistLeaderAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeAssistLeaderAsk)
    private static final com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk();
    }

    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeAssistLeaderAsk>
        PARSER = new com.google.protobuf.AbstractParser<ChangeAssistLeaderAsk>() {
      @java.lang.Override
      public ChangeAssistLeaderAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeAssistLeaderAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeAssistLeaderAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeAssistLeaderAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeAssistLeaderAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeAssistLeaderAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeAssistLeaderAns}
   */
  public static final class ChangeAssistLeaderAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeAssistLeaderAns)
      ChangeAssistLeaderAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeAssistLeaderAns.newBuilder() to construct.
    private ChangeAssistLeaderAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeAssistLeaderAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeAssistLeaderAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeAssistLeaderAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns.class, com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns other = (com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeAssistLeaderAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeAssistLeaderAns)
        com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns.class, com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneRallyAssist.internal_static_com_yorha_proto_ChangeAssistLeaderAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns build() {
        com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns buildPartial() {
        com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns result = new com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns) {
          return mergeFrom((com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns other) {
        if (other == com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeAssistLeaderAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeAssistLeaderAns)
    private static final com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns();
    }

    public static com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeAssistLeaderAns>
        PARSER = new com.google.protobuf.AbstractParser<ChangeAssistLeaderAns>() {
      @java.lang.Override
      public ChangeAssistLeaderAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeAssistLeaderAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeAssistLeaderAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeAssistLeaderAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneRallyAssist.ChangeAssistLeaderAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchRallyListAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchRallyListAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchRallyListAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchRallyListAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchOneRallyAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchOneRallyAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchOneRallyAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchOneRallyAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlayerCancelRallyAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlayerCancelRallyAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlayerCancelRallyAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlayerCancelRallyAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RepatriateRallyMemberAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RepatriateRallyMemberAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RepatriateRallyMemberAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RepatriateRallyMemberAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchWarningAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchWarningAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchWarningAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchWarningAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SetWarningItemTagAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SetWarningItemTagAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SetWarningItemTagAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SetWarningItemTagAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_IgnoreAllWarningAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_IgnoreAllWarningAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_IgnoreAllWarningAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_IgnoreAllWarningAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchInnerArmyAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchInnerArmyAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchInnerArmyAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchInnerArmyAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RepatriateAssistMemberAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RepatriateAssistMemberAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RepatriateAssistMemberAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RepatriateAssistMemberAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeAssistLeaderAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeAssistLeaderAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeAssistLeaderAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeAssistLeaderAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/scene/ss_scene_rally_assi" +
      "st.proto\022\017com.yorha.proto\032\034ss_proto/gen/" +
      "cnc/basic.proto\032\'ss_proto/gen/common/str" +
      "uct_common.proto\"%\n\021FetchRallyListAsk\022\020\n" +
      "\010playerId\030\001 \001(\003\"%\n\021FetchRallyListAns\022\020\n\010" +
      "msgBytes\030\001 \001(\014\"5\n\020FetchOneRallyAsk\022\020\n\010pl" +
      "ayerId\030\001 \001(\003\022\017\n\007rallyId\030\002 \001(\003\"$\n\020FetchOn" +
      "eRallyAns\022\020\n\010msgBytes\030\001 \001(\014\"9\n\024PlayerCan" +
      "celRallyAsk\022\020\n\010playerId\030\001 \001(\003\022\017\n\007rallyId" +
      "\030\002 \001(\003\"\026\n\024PlayerCancelRallyAns\"M\n\030Repatr" +
      "iateRallyMemberAsk\022\020\n\010playerId\030\001 \001(\003\022\017\n\007" +
      "rallyId\030\002 \001(\003\022\016\n\006armyId\030\003 \001(\003\"\032\n\030Repatri" +
      "ateRallyMemberAns\"\232\001\n\033SetRallyRecommendS" +
      "oldierAsk\022\020\n\010playerId\030\001 \001(\003\022\020\n\010entityId\030" +
      "\002 \001(\003\022/\n\013soldierType\030\003 \001(\0132\032.com.yorha.p" +
      "roto.Int32List\022\020\n\010isAssist\030\004 \001(\010\022\024\n\014isPe" +
      "rmission\030\005 \001(\010\"\035\n\033SetRallyRecommendSoldi" +
      "erAns\"#\n\017FetchWarningAsk\022\020\n\010playerId\030\001 \001" +
      "(\003\"#\n\017FetchWarningAns\022\020\n\010msgBytes\030\001 \001(\014\"" +
      "H\n\024SetWarningItemTagAsk\022\020\n\010playerId\030\001 \001(" +
      "\003\022\016\n\006armyId\030\002 \001(\003\022\016\n\006ignore\030\003 \001(\010\"\026\n\024Set" +
      "WarningItemTagAns\"\'\n\023IgnoreAllWarningAsk" +
      "\022\020\n\010playerId\030\001 \001(\003\"\025\n\023IgnoreAllWarningAn" +
      "s\"b\n\021FetchInnerArmyAsk\022\022\n\noperatorId\030\001 \001" +
      "(\003\022\020\n\010targetId\030\002 \001(\003\022\026\n\016targetPlayerId\030\003" +
      " \001(\003\022\017\n\007version\030\004 \001(\003\"y\n\021FetchInnerArmyA" +
      "ns\022\020\n\010msgBytes\030\001 \001(\014\022\020\n\010targetId\030\002 \001(\003\022\017" +
      "\n\007version\030\003 \001(\003\022/\n\010progress\030\004 \001(\0132\035.com." +
      "yorha.proto.ProgressInfo\"e\n\031RepatriateAs" +
      "sistMemberAsk\022\020\n\010playerId\030\001 \001(\003\022\020\n\010targe" +
      "tId\030\002 \001(\003\022\016\n\006armyId\030\003 \001(\003\022\024\n\014isPermissio" +
      "n\030\004 \001(\010\"\033\n\031RepatriateAssistMemberAns\"a\n\025" +
      "ChangeAssistLeaderAsk\022\020\n\010playerId\030\001 \001(\003\022" +
      "\016\n\006cityId\030\002 \001(\003\022\020\n\010targetId\030\003 \001(\003\022\024\n\014isP" +
      "ermission\030\004 \001(\010\"\027\n\025ChangeAssistLeaderAns" +
      "B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Basic.getDescriptor(),
          com.yorha.proto.StructCommon.getDescriptor(),
        });
    internal_static_com_yorha_proto_FetchRallyListAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_FetchRallyListAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchRallyListAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_FetchRallyListAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_FetchRallyListAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchRallyListAns_descriptor,
        new java.lang.String[] { "MsgBytes", });
    internal_static_com_yorha_proto_FetchOneRallyAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_FetchOneRallyAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchOneRallyAsk_descriptor,
        new java.lang.String[] { "PlayerId", "RallyId", });
    internal_static_com_yorha_proto_FetchOneRallyAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_FetchOneRallyAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchOneRallyAns_descriptor,
        new java.lang.String[] { "MsgBytes", });
    internal_static_com_yorha_proto_PlayerCancelRallyAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_PlayerCancelRallyAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlayerCancelRallyAsk_descriptor,
        new java.lang.String[] { "PlayerId", "RallyId", });
    internal_static_com_yorha_proto_PlayerCancelRallyAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_PlayerCancelRallyAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlayerCancelRallyAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_RepatriateRallyMemberAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_RepatriateRallyMemberAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RepatriateRallyMemberAsk_descriptor,
        new java.lang.String[] { "PlayerId", "RallyId", "ArmyId", });
    internal_static_com_yorha_proto_RepatriateRallyMemberAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_RepatriateRallyMemberAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RepatriateRallyMemberAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SetRallyRecommendSoldierAsk_descriptor,
        new java.lang.String[] { "PlayerId", "EntityId", "SoldierType", "IsAssist", "IsPermission", });
    internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SetRallyRecommendSoldierAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_FetchWarningAsk_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_FetchWarningAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchWarningAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_FetchWarningAns_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_FetchWarningAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchWarningAns_descriptor,
        new java.lang.String[] { "MsgBytes", });
    internal_static_com_yorha_proto_SetWarningItemTagAsk_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_SetWarningItemTagAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SetWarningItemTagAsk_descriptor,
        new java.lang.String[] { "PlayerId", "ArmyId", "Ignore", });
    internal_static_com_yorha_proto_SetWarningItemTagAns_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_SetWarningItemTagAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SetWarningItemTagAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_IgnoreAllWarningAsk_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_IgnoreAllWarningAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_IgnoreAllWarningAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_IgnoreAllWarningAns_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_yorha_proto_IgnoreAllWarningAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_IgnoreAllWarningAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_FetchInnerArmyAsk_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_com_yorha_proto_FetchInnerArmyAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchInnerArmyAsk_descriptor,
        new java.lang.String[] { "OperatorId", "TargetId", "TargetPlayerId", "Version", });
    internal_static_com_yorha_proto_FetchInnerArmyAns_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_com_yorha_proto_FetchInnerArmyAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchInnerArmyAns_descriptor,
        new java.lang.String[] { "MsgBytes", "TargetId", "Version", "Progress", });
    internal_static_com_yorha_proto_RepatriateAssistMemberAsk_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_com_yorha_proto_RepatriateAssistMemberAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RepatriateAssistMemberAsk_descriptor,
        new java.lang.String[] { "PlayerId", "TargetId", "ArmyId", "IsPermission", });
    internal_static_com_yorha_proto_RepatriateAssistMemberAns_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_com_yorha_proto_RepatriateAssistMemberAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RepatriateAssistMemberAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_ChangeAssistLeaderAsk_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_com_yorha_proto_ChangeAssistLeaderAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeAssistLeaderAsk_descriptor,
        new java.lang.String[] { "PlayerId", "CityId", "TargetId", "IsPermission", });
    internal_static_com_yorha_proto_ChangeAssistLeaderAns_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_com_yorha_proto_ChangeAssistLeaderAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeAssistLeaderAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.Basic.getDescriptor();
    com.yorha.proto.StructCommon.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

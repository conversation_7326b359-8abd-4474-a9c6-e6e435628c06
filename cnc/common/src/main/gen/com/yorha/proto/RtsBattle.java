// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/common/rts_battle.proto

package com.yorha.proto;

public final class RtsBattle {
  private RtsBattle() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RTSUnitOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RTSUnit)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>optional int32 unitId = 2;</code>
     * @return Whether the unitId field is set.
     */
    boolean hasUnitId();
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return The unitId.
     */
    int getUnitId();

    /**
     * <code>optional int32 hp = 3;</code>
     * @return Whether the hp field is set.
     */
    boolean hasHp();
    /**
     * <code>optional int32 hp = 3;</code>
     * @return The hp.
     */
    int getHp();

    /**
     * <code>optional int32 hpMax = 4;</code>
     * @return Whether the hpMax field is set.
     */
    boolean hasHpMax();
    /**
     * <code>optional int32 hpMax = 4;</code>
     * @return The hpMax.
     */
    int getHpMax();

    /**
     * <code>optional int32 primaryAttack = 5;</code>
     * @return Whether the primaryAttack field is set.
     */
    boolean hasPrimaryAttack();
    /**
     * <code>optional int32 primaryAttack = 5;</code>
     * @return The primaryAttack.
     */
    int getPrimaryAttack();

    /**
     * <code>optional int32 secondaryAttack = 6;</code>
     * @return Whether the secondaryAttack field is set.
     */
    boolean hasSecondaryAttack();
    /**
     * <code>optional int32 secondaryAttack = 6;</code>
     * @return The secondaryAttack.
     */
    int getSecondaryAttack();

    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 7;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 7;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <pre>
     * 士兵数量
     * </pre>
     *
     * <code>optional int32 number = 8;</code>
     * @return Whether the number field is set.
     */
    boolean hasNumber();
    /**
     * <pre>
     * 士兵数量
     * </pre>
     *
     * <code>optional int32 number = 8;</code>
     * @return The number.
     */
    int getNumber();

    /**
     * <pre>
     * 出生的建筑
     * </pre>
     *
     * <code>optional int32 buildId = 9;</code>
     * @return Whether the buildId field is set.
     */
    boolean hasBuildId();
    /**
     * <pre>
     * 出生的建筑
     * </pre>
     *
     * <code>optional int32 buildId = 9;</code>
     * @return The buildId.
     */
    int getBuildId();

    /**
     * <pre>
     * 小队槽位id
     * </pre>
     *
     * <code>optional int32 slotId = 10;</code>
     * @return Whether the slotId field is set.
     */
    boolean hasSlotId();
    /**
     * <pre>
     * 小队槽位id
     * </pre>
     *
     * <code>optional int32 slotId = 10;</code>
     * @return The slotId.
     */
    int getSlotId();

    /**
     * <pre>
     * 兵种等级
     * </pre>
     *
     * <code>optional int32 unitLevel = 11;</code>
     * @return Whether the unitLevel field is set.
     */
    boolean hasUnitLevel();
    /**
     * <pre>
     * 兵种等级
     * </pre>
     *
     * <code>optional int32 unitLevel = 11;</code>
     * @return The unitLevel.
     */
    int getUnitLevel();

    /**
     * <pre>
     * 英雄等级
     * </pre>
     *
     * <code>optional int32 heroLevel = 12;</code>
     * @return Whether the heroLevel field is set.
     */
    boolean hasHeroLevel();
    /**
     * <pre>
     * 英雄等级
     * </pre>
     *
     * <code>optional int32 heroLevel = 12;</code>
     * @return The heroLevel.
     */
    int getHeroLevel();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RTSUnit}
   */
  public static final class RTSUnit extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RTSUnit)
      RTSUnitOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RTSUnit.newBuilder() to construct.
    private RTSUnit(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RTSUnit() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RTSUnit();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RTSUnit(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              unitId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              hp_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              hpMax_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              primaryAttack_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              secondaryAttack_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              heroId_ = input.readInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              number_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              buildId_ = input.readInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              slotId_ = input.readInt32();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              unitLevel_ = input.readInt32();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              heroLevel_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSUnit_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSUnit_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.RtsBattle.RTSUnit.class, com.yorha.proto.RtsBattle.RTSUnit.Builder.class);
    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int UNITID_FIELD_NUMBER = 2;
    private int unitId_;
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return Whether the unitId field is set.
     */
    @java.lang.Override
    public boolean hasUnitId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return The unitId.
     */
    @java.lang.Override
    public int getUnitId() {
      return unitId_;
    }

    public static final int HP_FIELD_NUMBER = 3;
    private int hp_;
    /**
     * <code>optional int32 hp = 3;</code>
     * @return Whether the hp field is set.
     */
    @java.lang.Override
    public boolean hasHp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 hp = 3;</code>
     * @return The hp.
     */
    @java.lang.Override
    public int getHp() {
      return hp_;
    }

    public static final int HPMAX_FIELD_NUMBER = 4;
    private int hpMax_;
    /**
     * <code>optional int32 hpMax = 4;</code>
     * @return Whether the hpMax field is set.
     */
    @java.lang.Override
    public boolean hasHpMax() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 hpMax = 4;</code>
     * @return The hpMax.
     */
    @java.lang.Override
    public int getHpMax() {
      return hpMax_;
    }

    public static final int PRIMARYATTACK_FIELD_NUMBER = 5;
    private int primaryAttack_;
    /**
     * <code>optional int32 primaryAttack = 5;</code>
     * @return Whether the primaryAttack field is set.
     */
    @java.lang.Override
    public boolean hasPrimaryAttack() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 primaryAttack = 5;</code>
     * @return The primaryAttack.
     */
    @java.lang.Override
    public int getPrimaryAttack() {
      return primaryAttack_;
    }

    public static final int SECONDARYATTACK_FIELD_NUMBER = 6;
    private int secondaryAttack_;
    /**
     * <code>optional int32 secondaryAttack = 6;</code>
     * @return Whether the secondaryAttack field is set.
     */
    @java.lang.Override
    public boolean hasSecondaryAttack() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 secondaryAttack = 6;</code>
     * @return The secondaryAttack.
     */
    @java.lang.Override
    public int getSecondaryAttack() {
      return secondaryAttack_;
    }

    public static final int HEROID_FIELD_NUMBER = 7;
    private int heroId_;
    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 7;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 7;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int NUMBER_FIELD_NUMBER = 8;
    private int number_;
    /**
     * <pre>
     * 士兵数量
     * </pre>
     *
     * <code>optional int32 number = 8;</code>
     * @return Whether the number field is set.
     */
    @java.lang.Override
    public boolean hasNumber() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 士兵数量
     * </pre>
     *
     * <code>optional int32 number = 8;</code>
     * @return The number.
     */
    @java.lang.Override
    public int getNumber() {
      return number_;
    }

    public static final int BUILDID_FIELD_NUMBER = 9;
    private int buildId_;
    /**
     * <pre>
     * 出生的建筑
     * </pre>
     *
     * <code>optional int32 buildId = 9;</code>
     * @return Whether the buildId field is set.
     */
    @java.lang.Override
    public boolean hasBuildId() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 出生的建筑
     * </pre>
     *
     * <code>optional int32 buildId = 9;</code>
     * @return The buildId.
     */
    @java.lang.Override
    public int getBuildId() {
      return buildId_;
    }

    public static final int SLOTID_FIELD_NUMBER = 10;
    private int slotId_;
    /**
     * <pre>
     * 小队槽位id
     * </pre>
     *
     * <code>optional int32 slotId = 10;</code>
     * @return Whether the slotId field is set.
     */
    @java.lang.Override
    public boolean hasSlotId() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 小队槽位id
     * </pre>
     *
     * <code>optional int32 slotId = 10;</code>
     * @return The slotId.
     */
    @java.lang.Override
    public int getSlotId() {
      return slotId_;
    }

    public static final int UNITLEVEL_FIELD_NUMBER = 11;
    private int unitLevel_;
    /**
     * <pre>
     * 兵种等级
     * </pre>
     *
     * <code>optional int32 unitLevel = 11;</code>
     * @return Whether the unitLevel field is set.
     */
    @java.lang.Override
    public boolean hasUnitLevel() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 兵种等级
     * </pre>
     *
     * <code>optional int32 unitLevel = 11;</code>
     * @return The unitLevel.
     */
    @java.lang.Override
    public int getUnitLevel() {
      return unitLevel_;
    }

    public static final int HEROLEVEL_FIELD_NUMBER = 12;
    private int heroLevel_;
    /**
     * <pre>
     * 英雄等级
     * </pre>
     *
     * <code>optional int32 heroLevel = 12;</code>
     * @return Whether the heroLevel field is set.
     */
    @java.lang.Override
    public boolean hasHeroLevel() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 英雄等级
     * </pre>
     *
     * <code>optional int32 heroLevel = 12;</code>
     * @return The heroLevel.
     */
    @java.lang.Override
    public int getHeroLevel() {
      return heroLevel_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, unitId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, hp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, hpMax_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, primaryAttack_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, secondaryAttack_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(7, heroId_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt32(8, number_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt32(9, buildId_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt32(10, slotId_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeInt32(11, unitLevel_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeInt32(12, heroLevel_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, unitId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, hp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, hpMax_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, primaryAttack_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, secondaryAttack_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, heroId_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, number_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, buildId_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, slotId_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, unitLevel_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, heroLevel_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.RtsBattle.RTSUnit)) {
        return super.equals(obj);
      }
      com.yorha.proto.RtsBattle.RTSUnit other = (com.yorha.proto.RtsBattle.RTSUnit) obj;

      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (hasUnitId() != other.hasUnitId()) return false;
      if (hasUnitId()) {
        if (getUnitId()
            != other.getUnitId()) return false;
      }
      if (hasHp() != other.hasHp()) return false;
      if (hasHp()) {
        if (getHp()
            != other.getHp()) return false;
      }
      if (hasHpMax() != other.hasHpMax()) return false;
      if (hasHpMax()) {
        if (getHpMax()
            != other.getHpMax()) return false;
      }
      if (hasPrimaryAttack() != other.hasPrimaryAttack()) return false;
      if (hasPrimaryAttack()) {
        if (getPrimaryAttack()
            != other.getPrimaryAttack()) return false;
      }
      if (hasSecondaryAttack() != other.hasSecondaryAttack()) return false;
      if (hasSecondaryAttack()) {
        if (getSecondaryAttack()
            != other.getSecondaryAttack()) return false;
      }
      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasNumber() != other.hasNumber()) return false;
      if (hasNumber()) {
        if (getNumber()
            != other.getNumber()) return false;
      }
      if (hasBuildId() != other.hasBuildId()) return false;
      if (hasBuildId()) {
        if (getBuildId()
            != other.getBuildId()) return false;
      }
      if (hasSlotId() != other.hasSlotId()) return false;
      if (hasSlotId()) {
        if (getSlotId()
            != other.getSlotId()) return false;
      }
      if (hasUnitLevel() != other.hasUnitLevel()) return false;
      if (hasUnitLevel()) {
        if (getUnitLevel()
            != other.getUnitLevel()) return false;
      }
      if (hasHeroLevel() != other.hasHeroLevel()) return false;
      if (hasHeroLevel()) {
        if (getHeroLevel()
            != other.getHeroLevel()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getId();
      }
      if (hasUnitId()) {
        hash = (37 * hash) + UNITID_FIELD_NUMBER;
        hash = (53 * hash) + getUnitId();
      }
      if (hasHp()) {
        hash = (37 * hash) + HP_FIELD_NUMBER;
        hash = (53 * hash) + getHp();
      }
      if (hasHpMax()) {
        hash = (37 * hash) + HPMAX_FIELD_NUMBER;
        hash = (53 * hash) + getHpMax();
      }
      if (hasPrimaryAttack()) {
        hash = (37 * hash) + PRIMARYATTACK_FIELD_NUMBER;
        hash = (53 * hash) + getPrimaryAttack();
      }
      if (hasSecondaryAttack()) {
        hash = (37 * hash) + SECONDARYATTACK_FIELD_NUMBER;
        hash = (53 * hash) + getSecondaryAttack();
      }
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasNumber()) {
        hash = (37 * hash) + NUMBER_FIELD_NUMBER;
        hash = (53 * hash) + getNumber();
      }
      if (hasBuildId()) {
        hash = (37 * hash) + BUILDID_FIELD_NUMBER;
        hash = (53 * hash) + getBuildId();
      }
      if (hasSlotId()) {
        hash = (37 * hash) + SLOTID_FIELD_NUMBER;
        hash = (53 * hash) + getSlotId();
      }
      if (hasUnitLevel()) {
        hash = (37 * hash) + UNITLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getUnitLevel();
      }
      if (hasHeroLevel()) {
        hash = (37 * hash) + HEROLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getHeroLevel();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSUnit parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.RtsBattle.RTSUnit prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RTSUnit}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RTSUnit)
        com.yorha.proto.RtsBattle.RTSUnitOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSUnit_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSUnit_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.RtsBattle.RTSUnit.class, com.yorha.proto.RtsBattle.RTSUnit.Builder.class);
      }

      // Construct using com.yorha.proto.RtsBattle.RTSUnit.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        unitId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        hp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        hpMax_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        primaryAttack_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        secondaryAttack_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        number_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        buildId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        slotId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        unitLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        heroLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSUnit_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSUnit getDefaultInstanceForType() {
        return com.yorha.proto.RtsBattle.RTSUnit.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSUnit build() {
        com.yorha.proto.RtsBattle.RTSUnit result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSUnit buildPartial() {
        com.yorha.proto.RtsBattle.RTSUnit result = new com.yorha.proto.RtsBattle.RTSUnit(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.unitId_ = unitId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.hp_ = hp_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.hpMax_ = hpMax_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.primaryAttack_ = primaryAttack_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.secondaryAttack_ = secondaryAttack_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.number_ = number_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.buildId_ = buildId_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.slotId_ = slotId_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.unitLevel_ = unitLevel_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.heroLevel_ = heroLevel_;
          to_bitField0_ |= 0x00000800;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.RtsBattle.RTSUnit) {
          return mergeFrom((com.yorha.proto.RtsBattle.RTSUnit)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.RtsBattle.RTSUnit other) {
        if (other == com.yorha.proto.RtsBattle.RTSUnit.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasUnitId()) {
          setUnitId(other.getUnitId());
        }
        if (other.hasHp()) {
          setHp(other.getHp());
        }
        if (other.hasHpMax()) {
          setHpMax(other.getHpMax());
        }
        if (other.hasPrimaryAttack()) {
          setPrimaryAttack(other.getPrimaryAttack());
        }
        if (other.hasSecondaryAttack()) {
          setSecondaryAttack(other.getSecondaryAttack());
        }
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasNumber()) {
          setNumber(other.getNumber());
        }
        if (other.hasBuildId()) {
          setBuildId(other.getBuildId());
        }
        if (other.hasSlotId()) {
          setSlotId(other.getSlotId());
        }
        if (other.hasUnitLevel()) {
          setUnitLevel(other.getUnitLevel());
        }
        if (other.hasHeroLevel()) {
          setHeroLevel(other.getHeroLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.RtsBattle.RTSUnit parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.RtsBattle.RTSUnit) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>optional int32 id = 1;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int unitId_ ;
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return Whether the unitId field is set.
       */
      @java.lang.Override
      public boolean hasUnitId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return The unitId.
       */
      @java.lang.Override
      public int getUnitId() {
        return unitId_;
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @param value The unitId to set.
       * @return This builder for chaining.
       */
      public Builder setUnitId(int value) {
        bitField0_ |= 0x00000002;
        unitId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnitId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        unitId_ = 0;
        onChanged();
        return this;
      }

      private int hp_ ;
      /**
       * <code>optional int32 hp = 3;</code>
       * @return Whether the hp field is set.
       */
      @java.lang.Override
      public boolean hasHp() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 hp = 3;</code>
       * @return The hp.
       */
      @java.lang.Override
      public int getHp() {
        return hp_;
      }
      /**
       * <code>optional int32 hp = 3;</code>
       * @param value The hp to set.
       * @return This builder for chaining.
       */
      public Builder setHp(int value) {
        bitField0_ |= 0x00000004;
        hp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 hp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearHp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        hp_ = 0;
        onChanged();
        return this;
      }

      private int hpMax_ ;
      /**
       * <code>optional int32 hpMax = 4;</code>
       * @return Whether the hpMax field is set.
       */
      @java.lang.Override
      public boolean hasHpMax() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 hpMax = 4;</code>
       * @return The hpMax.
       */
      @java.lang.Override
      public int getHpMax() {
        return hpMax_;
      }
      /**
       * <code>optional int32 hpMax = 4;</code>
       * @param value The hpMax to set.
       * @return This builder for chaining.
       */
      public Builder setHpMax(int value) {
        bitField0_ |= 0x00000008;
        hpMax_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 hpMax = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHpMax() {
        bitField0_ = (bitField0_ & ~0x00000008);
        hpMax_ = 0;
        onChanged();
        return this;
      }

      private int primaryAttack_ ;
      /**
       * <code>optional int32 primaryAttack = 5;</code>
       * @return Whether the primaryAttack field is set.
       */
      @java.lang.Override
      public boolean hasPrimaryAttack() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 primaryAttack = 5;</code>
       * @return The primaryAttack.
       */
      @java.lang.Override
      public int getPrimaryAttack() {
        return primaryAttack_;
      }
      /**
       * <code>optional int32 primaryAttack = 5;</code>
       * @param value The primaryAttack to set.
       * @return This builder for chaining.
       */
      public Builder setPrimaryAttack(int value) {
        bitField0_ |= 0x00000010;
        primaryAttack_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 primaryAttack = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearPrimaryAttack() {
        bitField0_ = (bitField0_ & ~0x00000010);
        primaryAttack_ = 0;
        onChanged();
        return this;
      }

      private int secondaryAttack_ ;
      /**
       * <code>optional int32 secondaryAttack = 6;</code>
       * @return Whether the secondaryAttack field is set.
       */
      @java.lang.Override
      public boolean hasSecondaryAttack() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional int32 secondaryAttack = 6;</code>
       * @return The secondaryAttack.
       */
      @java.lang.Override
      public int getSecondaryAttack() {
        return secondaryAttack_;
      }
      /**
       * <code>optional int32 secondaryAttack = 6;</code>
       * @param value The secondaryAttack to set.
       * @return This builder for chaining.
       */
      public Builder setSecondaryAttack(int value) {
        bitField0_ |= 0x00000020;
        secondaryAttack_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 secondaryAttack = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearSecondaryAttack() {
        bitField0_ = (bitField0_ & ~0x00000020);
        secondaryAttack_ = 0;
        onChanged();
        return this;
      }

      private int heroId_ ;
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 7;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 7;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 7;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000040;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000040);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private int number_ ;
      /**
       * <pre>
       * 士兵数量
       * </pre>
       *
       * <code>optional int32 number = 8;</code>
       * @return Whether the number field is set.
       */
      @java.lang.Override
      public boolean hasNumber() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 士兵数量
       * </pre>
       *
       * <code>optional int32 number = 8;</code>
       * @return The number.
       */
      @java.lang.Override
      public int getNumber() {
        return number_;
      }
      /**
       * <pre>
       * 士兵数量
       * </pre>
       *
       * <code>optional int32 number = 8;</code>
       * @param value The number to set.
       * @return This builder for chaining.
       */
      public Builder setNumber(int value) {
        bitField0_ |= 0x00000080;
        number_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 士兵数量
       * </pre>
       *
       * <code>optional int32 number = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearNumber() {
        bitField0_ = (bitField0_ & ~0x00000080);
        number_ = 0;
        onChanged();
        return this;
      }

      private int buildId_ ;
      /**
       * <pre>
       * 出生的建筑
       * </pre>
       *
       * <code>optional int32 buildId = 9;</code>
       * @return Whether the buildId field is set.
       */
      @java.lang.Override
      public boolean hasBuildId() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 出生的建筑
       * </pre>
       *
       * <code>optional int32 buildId = 9;</code>
       * @return The buildId.
       */
      @java.lang.Override
      public int getBuildId() {
        return buildId_;
      }
      /**
       * <pre>
       * 出生的建筑
       * </pre>
       *
       * <code>optional int32 buildId = 9;</code>
       * @param value The buildId to set.
       * @return This builder for chaining.
       */
      public Builder setBuildId(int value) {
        bitField0_ |= 0x00000100;
        buildId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 出生的建筑
       * </pre>
       *
       * <code>optional int32 buildId = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuildId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        buildId_ = 0;
        onChanged();
        return this;
      }

      private int slotId_ ;
      /**
       * <pre>
       * 小队槽位id
       * </pre>
       *
       * <code>optional int32 slotId = 10;</code>
       * @return Whether the slotId field is set.
       */
      @java.lang.Override
      public boolean hasSlotId() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 小队槽位id
       * </pre>
       *
       * <code>optional int32 slotId = 10;</code>
       * @return The slotId.
       */
      @java.lang.Override
      public int getSlotId() {
        return slotId_;
      }
      /**
       * <pre>
       * 小队槽位id
       * </pre>
       *
       * <code>optional int32 slotId = 10;</code>
       * @param value The slotId to set.
       * @return This builder for chaining.
       */
      public Builder setSlotId(int value) {
        bitField0_ |= 0x00000200;
        slotId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 小队槽位id
       * </pre>
       *
       * <code>optional int32 slotId = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearSlotId() {
        bitField0_ = (bitField0_ & ~0x00000200);
        slotId_ = 0;
        onChanged();
        return this;
      }

      private int unitLevel_ ;
      /**
       * <pre>
       * 兵种等级
       * </pre>
       *
       * <code>optional int32 unitLevel = 11;</code>
       * @return Whether the unitLevel field is set.
       */
      @java.lang.Override
      public boolean hasUnitLevel() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 兵种等级
       * </pre>
       *
       * <code>optional int32 unitLevel = 11;</code>
       * @return The unitLevel.
       */
      @java.lang.Override
      public int getUnitLevel() {
        return unitLevel_;
      }
      /**
       * <pre>
       * 兵种等级
       * </pre>
       *
       * <code>optional int32 unitLevel = 11;</code>
       * @param value The unitLevel to set.
       * @return This builder for chaining.
       */
      public Builder setUnitLevel(int value) {
        bitField0_ |= 0x00000400;
        unitLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 兵种等级
       * </pre>
       *
       * <code>optional int32 unitLevel = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnitLevel() {
        bitField0_ = (bitField0_ & ~0x00000400);
        unitLevel_ = 0;
        onChanged();
        return this;
      }

      private int heroLevel_ ;
      /**
       * <pre>
       * 英雄等级
       * </pre>
       *
       * <code>optional int32 heroLevel = 12;</code>
       * @return Whether the heroLevel field is set.
       */
      @java.lang.Override
      public boolean hasHeroLevel() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 英雄等级
       * </pre>
       *
       * <code>optional int32 heroLevel = 12;</code>
       * @return The heroLevel.
       */
      @java.lang.Override
      public int getHeroLevel() {
        return heroLevel_;
      }
      /**
       * <pre>
       * 英雄等级
       * </pre>
       *
       * <code>optional int32 heroLevel = 12;</code>
       * @param value The heroLevel to set.
       * @return This builder for chaining.
       */
      public Builder setHeroLevel(int value) {
        bitField0_ |= 0x00000800;
        heroLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 英雄等级
       * </pre>
       *
       * <code>optional int32 heroLevel = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroLevel() {
        bitField0_ = (bitField0_ & ~0x00000800);
        heroLevel_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RTSUnit)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RTSUnit)
    private static final com.yorha.proto.RtsBattle.RTSUnit DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.RtsBattle.RTSUnit();
    }

    public static com.yorha.proto.RtsBattle.RTSUnit getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RTSUnit>
        PARSER = new com.google.protobuf.AbstractParser<RTSUnit>() {
      @java.lang.Override
      public RTSUnit parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RTSUnit(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RTSUnit> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RTSUnit> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSUnit getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RTSBuildingOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RTSBuilding)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>optional int32 unitId = 2;</code>
     * @return Whether the unitId field is set.
     */
    boolean hasUnitId();
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return The unitId.
     */
    int getUnitId();

    /**
     * <code>optional int32 hp = 3;</code>
     * @return Whether the hp field is set.
     */
    boolean hasHp();
    /**
     * <code>optional int32 hp = 3;</code>
     * @return The hp.
     */
    int getHp();

    /**
     * <code>optional int32 hpMax = 4;</code>
     * @return Whether the hpMax field is set.
     */
    boolean hasHpMax();
    /**
     * <code>optional int32 hpMax = 4;</code>
     * @return The hpMax.
     */
    int getHpMax();

    /**
     * <code>optional int32 primaryAttack = 5;</code>
     * @return Whether the primaryAttack field is set.
     */
    boolean hasPrimaryAttack();
    /**
     * <code>optional int32 primaryAttack = 5;</code>
     * @return The primaryAttack.
     */
    int getPrimaryAttack();

    /**
     * <code>optional int32 secondaryAttack = 6;</code>
     * @return Whether the secondaryAttack field is set.
     */
    boolean hasSecondaryAttack();
    /**
     * <code>optional int32 secondaryAttack = 6;</code>
     * @return The secondaryAttack.
     */
    int getSecondaryAttack();

    /**
     * <code>optional string pointName = 7;</code>
     * @return Whether the pointName field is set.
     */
    boolean hasPointName();
    /**
     * <code>optional string pointName = 7;</code>
     * @return The pointName.
     */
    java.lang.String getPointName();
    /**
     * <code>optional string pointName = 7;</code>
     * @return The bytes for pointName.
     */
    com.google.protobuf.ByteString
        getPointNameBytes();

    /**
     * <code>optional int32 x = 8;</code>
     * @return Whether the x field is set.
     */
    boolean hasX();
    /**
     * <code>optional int32 x = 8;</code>
     * @return The x.
     */
    int getX();

    /**
     * <code>optional int32 y = 9;</code>
     * @return Whether the y field is set.
     */
    boolean hasY();
    /**
     * <code>optional int32 y = 9;</code>
     * @return The y.
     */
    int getY();

    /**
     * <code>optional bool isUnbeatable = 10;</code>
     * @return Whether the isUnbeatable field is set.
     */
    boolean hasIsUnbeatable();
    /**
     * <code>optional bool isUnbeatable = 10;</code>
     * @return The isUnbeatable.
     */
    boolean getIsUnbeatable();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RTSBuilding}
   */
  public static final class RTSBuilding extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RTSBuilding)
      RTSBuildingOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RTSBuilding.newBuilder() to construct.
    private RTSBuilding(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RTSBuilding() {
      pointName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RTSBuilding();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RTSBuilding(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              unitId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              hp_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              hpMax_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              primaryAttack_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              secondaryAttack_ = input.readInt32();
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              pointName_ = bs;
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              x_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              y_ = input.readInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              isUnbeatable_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBuilding_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBuilding_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.RtsBattle.RTSBuilding.class, com.yorha.proto.RtsBattle.RTSBuilding.Builder.class);
    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int UNITID_FIELD_NUMBER = 2;
    private int unitId_;
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return Whether the unitId field is set.
     */
    @java.lang.Override
    public boolean hasUnitId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return The unitId.
     */
    @java.lang.Override
    public int getUnitId() {
      return unitId_;
    }

    public static final int HP_FIELD_NUMBER = 3;
    private int hp_;
    /**
     * <code>optional int32 hp = 3;</code>
     * @return Whether the hp field is set.
     */
    @java.lang.Override
    public boolean hasHp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 hp = 3;</code>
     * @return The hp.
     */
    @java.lang.Override
    public int getHp() {
      return hp_;
    }

    public static final int HPMAX_FIELD_NUMBER = 4;
    private int hpMax_;
    /**
     * <code>optional int32 hpMax = 4;</code>
     * @return Whether the hpMax field is set.
     */
    @java.lang.Override
    public boolean hasHpMax() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 hpMax = 4;</code>
     * @return The hpMax.
     */
    @java.lang.Override
    public int getHpMax() {
      return hpMax_;
    }

    public static final int PRIMARYATTACK_FIELD_NUMBER = 5;
    private int primaryAttack_;
    /**
     * <code>optional int32 primaryAttack = 5;</code>
     * @return Whether the primaryAttack field is set.
     */
    @java.lang.Override
    public boolean hasPrimaryAttack() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 primaryAttack = 5;</code>
     * @return The primaryAttack.
     */
    @java.lang.Override
    public int getPrimaryAttack() {
      return primaryAttack_;
    }

    public static final int SECONDARYATTACK_FIELD_NUMBER = 6;
    private int secondaryAttack_;
    /**
     * <code>optional int32 secondaryAttack = 6;</code>
     * @return Whether the secondaryAttack field is set.
     */
    @java.lang.Override
    public boolean hasSecondaryAttack() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 secondaryAttack = 6;</code>
     * @return The secondaryAttack.
     */
    @java.lang.Override
    public int getSecondaryAttack() {
      return secondaryAttack_;
    }

    public static final int POINTNAME_FIELD_NUMBER = 7;
    private volatile java.lang.Object pointName_;
    /**
     * <code>optional string pointName = 7;</code>
     * @return Whether the pointName field is set.
     */
    @java.lang.Override
    public boolean hasPointName() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string pointName = 7;</code>
     * @return The pointName.
     */
    @java.lang.Override
    public java.lang.String getPointName() {
      java.lang.Object ref = pointName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pointName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string pointName = 7;</code>
     * @return The bytes for pointName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPointNameBytes() {
      java.lang.Object ref = pointName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pointName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int X_FIELD_NUMBER = 8;
    private int x_;
    /**
     * <code>optional int32 x = 8;</code>
     * @return Whether the x field is set.
     */
    @java.lang.Override
    public boolean hasX() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 x = 8;</code>
     * @return The x.
     */
    @java.lang.Override
    public int getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 9;
    private int y_;
    /**
     * <code>optional int32 y = 9;</code>
     * @return Whether the y field is set.
     */
    @java.lang.Override
    public boolean hasY() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int32 y = 9;</code>
     * @return The y.
     */
    @java.lang.Override
    public int getY() {
      return y_;
    }

    public static final int ISUNBEATABLE_FIELD_NUMBER = 10;
    private boolean isUnbeatable_;
    /**
     * <code>optional bool isUnbeatable = 10;</code>
     * @return Whether the isUnbeatable field is set.
     */
    @java.lang.Override
    public boolean hasIsUnbeatable() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bool isUnbeatable = 10;</code>
     * @return The isUnbeatable.
     */
    @java.lang.Override
    public boolean getIsUnbeatable() {
      return isUnbeatable_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, unitId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, hp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, hpMax_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, primaryAttack_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, secondaryAttack_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, pointName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt32(8, x_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt32(9, y_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeBool(10, isUnbeatable_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, unitId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, hp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, hpMax_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, primaryAttack_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, secondaryAttack_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, pointName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, x_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, y_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(10, isUnbeatable_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.RtsBattle.RTSBuilding)) {
        return super.equals(obj);
      }
      com.yorha.proto.RtsBattle.RTSBuilding other = (com.yorha.proto.RtsBattle.RTSBuilding) obj;

      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (hasUnitId() != other.hasUnitId()) return false;
      if (hasUnitId()) {
        if (getUnitId()
            != other.getUnitId()) return false;
      }
      if (hasHp() != other.hasHp()) return false;
      if (hasHp()) {
        if (getHp()
            != other.getHp()) return false;
      }
      if (hasHpMax() != other.hasHpMax()) return false;
      if (hasHpMax()) {
        if (getHpMax()
            != other.getHpMax()) return false;
      }
      if (hasPrimaryAttack() != other.hasPrimaryAttack()) return false;
      if (hasPrimaryAttack()) {
        if (getPrimaryAttack()
            != other.getPrimaryAttack()) return false;
      }
      if (hasSecondaryAttack() != other.hasSecondaryAttack()) return false;
      if (hasSecondaryAttack()) {
        if (getSecondaryAttack()
            != other.getSecondaryAttack()) return false;
      }
      if (hasPointName() != other.hasPointName()) return false;
      if (hasPointName()) {
        if (!getPointName()
            .equals(other.getPointName())) return false;
      }
      if (hasX() != other.hasX()) return false;
      if (hasX()) {
        if (getX()
            != other.getX()) return false;
      }
      if (hasY() != other.hasY()) return false;
      if (hasY()) {
        if (getY()
            != other.getY()) return false;
      }
      if (hasIsUnbeatable() != other.hasIsUnbeatable()) return false;
      if (hasIsUnbeatable()) {
        if (getIsUnbeatable()
            != other.getIsUnbeatable()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getId();
      }
      if (hasUnitId()) {
        hash = (37 * hash) + UNITID_FIELD_NUMBER;
        hash = (53 * hash) + getUnitId();
      }
      if (hasHp()) {
        hash = (37 * hash) + HP_FIELD_NUMBER;
        hash = (53 * hash) + getHp();
      }
      if (hasHpMax()) {
        hash = (37 * hash) + HPMAX_FIELD_NUMBER;
        hash = (53 * hash) + getHpMax();
      }
      if (hasPrimaryAttack()) {
        hash = (37 * hash) + PRIMARYATTACK_FIELD_NUMBER;
        hash = (53 * hash) + getPrimaryAttack();
      }
      if (hasSecondaryAttack()) {
        hash = (37 * hash) + SECONDARYATTACK_FIELD_NUMBER;
        hash = (53 * hash) + getSecondaryAttack();
      }
      if (hasPointName()) {
        hash = (37 * hash) + POINTNAME_FIELD_NUMBER;
        hash = (53 * hash) + getPointName().hashCode();
      }
      if (hasX()) {
        hash = (37 * hash) + X_FIELD_NUMBER;
        hash = (53 * hash) + getX();
      }
      if (hasY()) {
        hash = (37 * hash) + Y_FIELD_NUMBER;
        hash = (53 * hash) + getY();
      }
      if (hasIsUnbeatable()) {
        hash = (37 * hash) + ISUNBEATABLE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsUnbeatable());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBuilding parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.RtsBattle.RTSBuilding prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RTSBuilding}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RTSBuilding)
        com.yorha.proto.RtsBattle.RTSBuildingOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBuilding_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBuilding_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.RtsBattle.RTSBuilding.class, com.yorha.proto.RtsBattle.RTSBuilding.Builder.class);
      }

      // Construct using com.yorha.proto.RtsBattle.RTSBuilding.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        unitId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        hp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        hpMax_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        primaryAttack_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        secondaryAttack_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        pointName_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        x_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        y_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        isUnbeatable_ = false;
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBuilding_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBuilding getDefaultInstanceForType() {
        return com.yorha.proto.RtsBattle.RTSBuilding.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBuilding build() {
        com.yorha.proto.RtsBattle.RTSBuilding result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBuilding buildPartial() {
        com.yorha.proto.RtsBattle.RTSBuilding result = new com.yorha.proto.RtsBattle.RTSBuilding(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.unitId_ = unitId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.hp_ = hp_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.hpMax_ = hpMax_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.primaryAttack_ = primaryAttack_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.secondaryAttack_ = secondaryAttack_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.pointName_ = pointName_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.x_ = x_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.y_ = y_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.isUnbeatable_ = isUnbeatable_;
          to_bitField0_ |= 0x00000200;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.RtsBattle.RTSBuilding) {
          return mergeFrom((com.yorha.proto.RtsBattle.RTSBuilding)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.RtsBattle.RTSBuilding other) {
        if (other == com.yorha.proto.RtsBattle.RTSBuilding.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasUnitId()) {
          setUnitId(other.getUnitId());
        }
        if (other.hasHp()) {
          setHp(other.getHp());
        }
        if (other.hasHpMax()) {
          setHpMax(other.getHpMax());
        }
        if (other.hasPrimaryAttack()) {
          setPrimaryAttack(other.getPrimaryAttack());
        }
        if (other.hasSecondaryAttack()) {
          setSecondaryAttack(other.getSecondaryAttack());
        }
        if (other.hasPointName()) {
          bitField0_ |= 0x00000040;
          pointName_ = other.pointName_;
          onChanged();
        }
        if (other.hasX()) {
          setX(other.getX());
        }
        if (other.hasY()) {
          setY(other.getY());
        }
        if (other.hasIsUnbeatable()) {
          setIsUnbeatable(other.getIsUnbeatable());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.RtsBattle.RTSBuilding parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.RtsBattle.RTSBuilding) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>optional int32 id = 1;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int unitId_ ;
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return Whether the unitId field is set.
       */
      @java.lang.Override
      public boolean hasUnitId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return The unitId.
       */
      @java.lang.Override
      public int getUnitId() {
        return unitId_;
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @param value The unitId to set.
       * @return This builder for chaining.
       */
      public Builder setUnitId(int value) {
        bitField0_ |= 0x00000002;
        unitId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnitId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        unitId_ = 0;
        onChanged();
        return this;
      }

      private int hp_ ;
      /**
       * <code>optional int32 hp = 3;</code>
       * @return Whether the hp field is set.
       */
      @java.lang.Override
      public boolean hasHp() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 hp = 3;</code>
       * @return The hp.
       */
      @java.lang.Override
      public int getHp() {
        return hp_;
      }
      /**
       * <code>optional int32 hp = 3;</code>
       * @param value The hp to set.
       * @return This builder for chaining.
       */
      public Builder setHp(int value) {
        bitField0_ |= 0x00000004;
        hp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 hp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearHp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        hp_ = 0;
        onChanged();
        return this;
      }

      private int hpMax_ ;
      /**
       * <code>optional int32 hpMax = 4;</code>
       * @return Whether the hpMax field is set.
       */
      @java.lang.Override
      public boolean hasHpMax() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 hpMax = 4;</code>
       * @return The hpMax.
       */
      @java.lang.Override
      public int getHpMax() {
        return hpMax_;
      }
      /**
       * <code>optional int32 hpMax = 4;</code>
       * @param value The hpMax to set.
       * @return This builder for chaining.
       */
      public Builder setHpMax(int value) {
        bitField0_ |= 0x00000008;
        hpMax_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 hpMax = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHpMax() {
        bitField0_ = (bitField0_ & ~0x00000008);
        hpMax_ = 0;
        onChanged();
        return this;
      }

      private int primaryAttack_ ;
      /**
       * <code>optional int32 primaryAttack = 5;</code>
       * @return Whether the primaryAttack field is set.
       */
      @java.lang.Override
      public boolean hasPrimaryAttack() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 primaryAttack = 5;</code>
       * @return The primaryAttack.
       */
      @java.lang.Override
      public int getPrimaryAttack() {
        return primaryAttack_;
      }
      /**
       * <code>optional int32 primaryAttack = 5;</code>
       * @param value The primaryAttack to set.
       * @return This builder for chaining.
       */
      public Builder setPrimaryAttack(int value) {
        bitField0_ |= 0x00000010;
        primaryAttack_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 primaryAttack = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearPrimaryAttack() {
        bitField0_ = (bitField0_ & ~0x00000010);
        primaryAttack_ = 0;
        onChanged();
        return this;
      }

      private int secondaryAttack_ ;
      /**
       * <code>optional int32 secondaryAttack = 6;</code>
       * @return Whether the secondaryAttack field is set.
       */
      @java.lang.Override
      public boolean hasSecondaryAttack() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional int32 secondaryAttack = 6;</code>
       * @return The secondaryAttack.
       */
      @java.lang.Override
      public int getSecondaryAttack() {
        return secondaryAttack_;
      }
      /**
       * <code>optional int32 secondaryAttack = 6;</code>
       * @param value The secondaryAttack to set.
       * @return This builder for chaining.
       */
      public Builder setSecondaryAttack(int value) {
        bitField0_ |= 0x00000020;
        secondaryAttack_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 secondaryAttack = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearSecondaryAttack() {
        bitField0_ = (bitField0_ & ~0x00000020);
        secondaryAttack_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object pointName_ = "";
      /**
       * <code>optional string pointName = 7;</code>
       * @return Whether the pointName field is set.
       */
      public boolean hasPointName() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional string pointName = 7;</code>
       * @return The pointName.
       */
      public java.lang.String getPointName() {
        java.lang.Object ref = pointName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            pointName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string pointName = 7;</code>
       * @return The bytes for pointName.
       */
      public com.google.protobuf.ByteString
          getPointNameBytes() {
        java.lang.Object ref = pointName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          pointName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string pointName = 7;</code>
       * @param value The pointName to set.
       * @return This builder for chaining.
       */
      public Builder setPointName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        pointName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string pointName = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearPointName() {
        bitField0_ = (bitField0_ & ~0x00000040);
        pointName_ = getDefaultInstance().getPointName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string pointName = 7;</code>
       * @param value The bytes for pointName to set.
       * @return This builder for chaining.
       */
      public Builder setPointNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        pointName_ = value;
        onChanged();
        return this;
      }

      private int x_ ;
      /**
       * <code>optional int32 x = 8;</code>
       * @return Whether the x field is set.
       */
      @java.lang.Override
      public boolean hasX() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional int32 x = 8;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }
      /**
       * <code>optional int32 x = 8;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(int value) {
        bitField0_ |= 0x00000080;
        x_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 x = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        bitField0_ = (bitField0_ & ~0x00000080);
        x_ = 0;
        onChanged();
        return this;
      }

      private int y_ ;
      /**
       * <code>optional int32 y = 9;</code>
       * @return Whether the y field is set.
       */
      @java.lang.Override
      public boolean hasY() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional int32 y = 9;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }
      /**
       * <code>optional int32 y = 9;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(int value) {
        bitField0_ |= 0x00000100;
        y_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 y = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        bitField0_ = (bitField0_ & ~0x00000100);
        y_ = 0;
        onChanged();
        return this;
      }

      private boolean isUnbeatable_ ;
      /**
       * <code>optional bool isUnbeatable = 10;</code>
       * @return Whether the isUnbeatable field is set.
       */
      @java.lang.Override
      public boolean hasIsUnbeatable() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bool isUnbeatable = 10;</code>
       * @return The isUnbeatable.
       */
      @java.lang.Override
      public boolean getIsUnbeatable() {
        return isUnbeatable_;
      }
      /**
       * <code>optional bool isUnbeatable = 10;</code>
       * @param value The isUnbeatable to set.
       * @return This builder for chaining.
       */
      public Builder setIsUnbeatable(boolean value) {
        bitField0_ |= 0x00000200;
        isUnbeatable_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isUnbeatable = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsUnbeatable() {
        bitField0_ = (bitField0_ & ~0x00000200);
        isUnbeatable_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RTSBuilding)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RTSBuilding)
    private static final com.yorha.proto.RtsBattle.RTSBuilding DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.RtsBattle.RTSBuilding();
    }

    public static com.yorha.proto.RtsBattle.RTSBuilding getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RTSBuilding>
        PARSER = new com.google.protobuf.AbstractParser<RTSBuilding>() {
      @java.lang.Override
      public RTSBuilding parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RTSBuilding(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RTSBuilding> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RTSBuilding> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBuilding getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RTSBattleInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RTSBattleInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 随机数
     * </pre>
     *
     * <code>optional int32 seed = 1;</code>
     * @return Whether the seed field is set.
     */
    boolean hasSeed();
    /**
     * <pre>
     * 随机数
     * </pre>
     *
     * <code>optional int32 seed = 1;</code>
     * @return The seed.
     */
    int getSeed();

    /**
     * <pre>
     * 地图Id
     * </pre>
     *
     * <code>optional int32 mapId = 2;</code>
     * @return Whether the mapId field is set.
     */
    boolean hasMapId();
    /**
     * <pre>
     * 地图Id
     * </pre>
     *
     * <code>optional int32 mapId = 2;</code>
     * @return The mapId.
     */
    int getMapId();

    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    java.util.List<com.yorha.proto.RtsBattle.RTSUnit> 
        getUnitsList();
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    com.yorha.proto.RtsBattle.RTSUnit getUnits(int index);
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    int getUnitsCount();
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    java.util.List<? extends com.yorha.proto.RtsBattle.RTSUnitOrBuilder> 
        getUnitsOrBuilderList();
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    com.yorha.proto.RtsBattle.RTSUnitOrBuilder getUnitsOrBuilder(
        int index);

    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> 
        getBuildingsList();
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    com.yorha.proto.RtsBattle.RTSBuilding getBuildings(int index);
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    int getBuildingsCount();
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    java.util.List<? extends com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> 
        getBuildingsOrBuilderList();
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    com.yorha.proto.RtsBattle.RTSBuildingOrBuilder getBuildingsOrBuilder(
        int index);

    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 5;</code>
     * @return A list containing the skills.
     */
    java.util.List<java.lang.Integer> getSkillsList();
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 5;</code>
     * @return The count of skills.
     */
    int getSkillsCount();
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 5;</code>
     * @param index The index of the element to return.
     * @return The skills at the given index.
     */
    int getSkills(int index);

    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */
    int getItemSkillsCount();
    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */
    boolean containsItemSkills(
        int key);
    /**
     * Use {@link #getItemSkillsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getItemSkills();
    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getItemSkillsMap();
    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */

    int getItemSkillsOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */

    int getItemSkillsOrThrow(
        int key);

    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    java.util.List<com.yorha.proto.RtsBattle.RTSUnit> 
        getEnemyUnitsList();
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    com.yorha.proto.RtsBattle.RTSUnit getEnemyUnits(int index);
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    int getEnemyUnitsCount();
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    java.util.List<? extends com.yorha.proto.RtsBattle.RTSUnitOrBuilder> 
        getEnemyUnitsOrBuilderList();
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    com.yorha.proto.RtsBattle.RTSUnitOrBuilder getEnemyUnitsOrBuilder(
        int index);

    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> 
        getEnemyBuildingsList();
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    com.yorha.proto.RtsBattle.RTSBuilding getEnemyBuildings(int index);
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    int getEnemyBuildingsCount();
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    java.util.List<? extends com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> 
        getEnemyBuildingsOrBuilderList();
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    com.yorha.proto.RtsBattle.RTSBuildingOrBuilder getEnemyBuildingsOrBuilder(
        int index);
  }
  /**
   * <pre>
   * 战斗数据
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.RTSBattleInfo}
   */
  public static final class RTSBattleInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RTSBattleInfo)
      RTSBattleInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RTSBattleInfo.newBuilder() to construct.
    private RTSBattleInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RTSBattleInfo() {
      units_ = java.util.Collections.emptyList();
      buildings_ = java.util.Collections.emptyList();
      skills_ = emptyIntList();
      enemyUnits_ = java.util.Collections.emptyList();
      enemyBuildings_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RTSBattleInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RTSBattleInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              seed_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              mapId_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                units_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSUnit>();
                mutable_bitField0_ |= 0x00000004;
              }
              units_.add(
                  input.readMessage(com.yorha.proto.RtsBattle.RTSUnit.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) != 0)) {
                buildings_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSBuilding>();
                mutable_bitField0_ |= 0x00000008;
              }
              buildings_.add(
                  input.readMessage(com.yorha.proto.RtsBattle.RTSBuilding.PARSER, extensionRegistry));
              break;
            }
            case 40: {
              if (!((mutable_bitField0_ & 0x00000010) != 0)) {
                skills_ = newIntList();
                mutable_bitField0_ |= 0x00000010;
              }
              skills_.addInt(input.readInt32());
              break;
            }
            case 42: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000010) != 0) && input.getBytesUntilLimit() > 0) {
                skills_ = newIntList();
                mutable_bitField0_ |= 0x00000010;
              }
              while (input.getBytesUntilLimit() > 0) {
                skills_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000020) != 0)) {
                itemSkills_ = com.google.protobuf.MapField.newMapField(
                    ItemSkillsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000020;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              itemSkills__ = input.readMessage(
                  ItemSkillsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              itemSkills_.getMutableMap().put(
                  itemSkills__.getKey(), itemSkills__.getValue());
              break;
            }
            case 82: {
              if (!((mutable_bitField0_ & 0x00000040) != 0)) {
                enemyUnits_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSUnit>();
                mutable_bitField0_ |= 0x00000040;
              }
              enemyUnits_.add(
                  input.readMessage(com.yorha.proto.RtsBattle.RTSUnit.PARSER, extensionRegistry));
              break;
            }
            case 90: {
              if (!((mutable_bitField0_ & 0x00000080) != 0)) {
                enemyBuildings_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSBuilding>();
                mutable_bitField0_ |= 0x00000080;
              }
              enemyBuildings_.add(
                  input.readMessage(com.yorha.proto.RtsBattle.RTSBuilding.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          units_ = java.util.Collections.unmodifiableList(units_);
        }
        if (((mutable_bitField0_ & 0x00000008) != 0)) {
          buildings_ = java.util.Collections.unmodifiableList(buildings_);
        }
        if (((mutable_bitField0_ & 0x00000010) != 0)) {
          skills_.makeImmutable(); // C
        }
        if (((mutable_bitField0_ & 0x00000040) != 0)) {
          enemyUnits_ = java.util.Collections.unmodifiableList(enemyUnits_);
        }
        if (((mutable_bitField0_ & 0x00000080) != 0)) {
          enemyBuildings_ = java.util.Collections.unmodifiableList(enemyBuildings_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleInfo_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 6:
          return internalGetItemSkills();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.RtsBattle.RTSBattleInfo.class, com.yorha.proto.RtsBattle.RTSBattleInfo.Builder.class);
    }

    private int bitField0_;
    public static final int SEED_FIELD_NUMBER = 1;
    private int seed_;
    /**
     * <pre>
     * 随机数
     * </pre>
     *
     * <code>optional int32 seed = 1;</code>
     * @return Whether the seed field is set.
     */
    @java.lang.Override
    public boolean hasSeed() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 随机数
     * </pre>
     *
     * <code>optional int32 seed = 1;</code>
     * @return The seed.
     */
    @java.lang.Override
    public int getSeed() {
      return seed_;
    }

    public static final int MAPID_FIELD_NUMBER = 2;
    private int mapId_;
    /**
     * <pre>
     * 地图Id
     * </pre>
     *
     * <code>optional int32 mapId = 2;</code>
     * @return Whether the mapId field is set.
     */
    @java.lang.Override
    public boolean hasMapId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 地图Id
     * </pre>
     *
     * <code>optional int32 mapId = 2;</code>
     * @return The mapId.
     */
    @java.lang.Override
    public int getMapId() {
      return mapId_;
    }

    public static final int UNITS_FIELD_NUMBER = 3;
    private java.util.List<com.yorha.proto.RtsBattle.RTSUnit> units_;
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.RtsBattle.RTSUnit> getUnitsList() {
      return units_;
    }
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.RtsBattle.RTSUnitOrBuilder> 
        getUnitsOrBuilderList() {
      return units_;
    }
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    @java.lang.Override
    public int getUnitsCount() {
      return units_.size();
    }
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSUnit getUnits(int index) {
      return units_.get(index);
    }
    /**
     * <pre>
     * 作战单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSUnitOrBuilder getUnitsOrBuilder(
        int index) {
      return units_.get(index);
    }

    public static final int BUILDINGS_FIELD_NUMBER = 4;
    private java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> buildings_;
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> getBuildingsList() {
      return buildings_;
    }
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> 
        getBuildingsOrBuilderList() {
      return buildings_;
    }
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    @java.lang.Override
    public int getBuildingsCount() {
      return buildings_.size();
    }
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBuilding getBuildings(int index) {
      return buildings_.get(index);
    }
    /**
     * <pre>
     * 建筑单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBuildingOrBuilder getBuildingsOrBuilder(
        int index) {
      return buildings_.get(index);
    }

    public static final int SKILLS_FIELD_NUMBER = 5;
    private com.google.protobuf.Internal.IntList skills_;
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 5;</code>
     * @return A list containing the skills.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getSkillsList() {
      return skills_;
    }
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 5;</code>
     * @return The count of skills.
     */
    public int getSkillsCount() {
      return skills_.size();
    }
    /**
     * <pre>
     * 技能
     * </pre>
     *
     * <code>repeated int32 skills = 5;</code>
     * @param index The index of the element to return.
     * @return The skills at the given index.
     */
    public int getSkills(int index) {
      return skills_.getInt(index);
    }

    public static final int ITEMSKILLS_FIELD_NUMBER = 6;
    private static final class ItemSkillsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleInfo_ItemSkillsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> itemSkills_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetItemSkills() {
      if (itemSkills_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ItemSkillsDefaultEntryHolder.defaultEntry);
      }
      return itemSkills_;
    }

    public int getItemSkillsCount() {
      return internalGetItemSkills().getMap().size();
    }
    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */

    @java.lang.Override
    public boolean containsItemSkills(
        int key) {
      
      return internalGetItemSkills().getMap().containsKey(key);
    }
    /**
     * Use {@link #getItemSkillsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getItemSkills() {
      return getItemSkillsMap();
    }
    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Integer> getItemSkillsMap() {
      return internalGetItemSkills().getMap();
    }
    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */
    @java.lang.Override

    public int getItemSkillsOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetItemSkills().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 道具技能（skill -&gt; times）
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
     */
    @java.lang.Override

    public int getItemSkillsOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetItemSkills().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int ENEMYUNITS_FIELD_NUMBER = 10;
    private java.util.List<com.yorha.proto.RtsBattle.RTSUnit> enemyUnits_;
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.RtsBattle.RTSUnit> getEnemyUnitsList() {
      return enemyUnits_;
    }
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.RtsBattle.RTSUnitOrBuilder> 
        getEnemyUnitsOrBuilderList() {
      return enemyUnits_;
    }
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    @java.lang.Override
    public int getEnemyUnitsCount() {
      return enemyUnits_.size();
    }
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSUnit getEnemyUnits(int index) {
      return enemyUnits_.get(index);
    }
    /**
     * <pre>
     * 敌方单位
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSUnitOrBuilder getEnemyUnitsOrBuilder(
        int index) {
      return enemyUnits_.get(index);
    }

    public static final int ENEMYBUILDINGS_FIELD_NUMBER = 11;
    private java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> enemyBuildings_;
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> getEnemyBuildingsList() {
      return enemyBuildings_;
    }
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> 
        getEnemyBuildingsOrBuilderList() {
      return enemyBuildings_;
    }
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    @java.lang.Override
    public int getEnemyBuildingsCount() {
      return enemyBuildings_.size();
    }
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBuilding getEnemyBuildings(int index) {
      return enemyBuildings_.get(index);
    }
    /**
     * <pre>
     * 敌方建筑
     * </pre>
     *
     * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBuildingOrBuilder getEnemyBuildingsOrBuilder(
        int index) {
      return enemyBuildings_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, seed_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, mapId_);
      }
      for (int i = 0; i < units_.size(); i++) {
        output.writeMessage(3, units_.get(i));
      }
      for (int i = 0; i < buildings_.size(); i++) {
        output.writeMessage(4, buildings_.get(i));
      }
      for (int i = 0; i < skills_.size(); i++) {
        output.writeInt32(5, skills_.getInt(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetItemSkills(),
          ItemSkillsDefaultEntryHolder.defaultEntry,
          6);
      for (int i = 0; i < enemyUnits_.size(); i++) {
        output.writeMessage(10, enemyUnits_.get(i));
      }
      for (int i = 0; i < enemyBuildings_.size(); i++) {
        output.writeMessage(11, enemyBuildings_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, seed_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, mapId_);
      }
      for (int i = 0; i < units_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, units_.get(i));
      }
      for (int i = 0; i < buildings_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, buildings_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < skills_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(skills_.getInt(i));
        }
        size += dataSize;
        size += 1 * getSkillsList().size();
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetItemSkills().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        itemSkills__ = ItemSkillsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(6, itemSkills__);
      }
      for (int i = 0; i < enemyUnits_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, enemyUnits_.get(i));
      }
      for (int i = 0; i < enemyBuildings_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, enemyBuildings_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.RtsBattle.RTSBattleInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.RtsBattle.RTSBattleInfo other = (com.yorha.proto.RtsBattle.RTSBattleInfo) obj;

      if (hasSeed() != other.hasSeed()) return false;
      if (hasSeed()) {
        if (getSeed()
            != other.getSeed()) return false;
      }
      if (hasMapId() != other.hasMapId()) return false;
      if (hasMapId()) {
        if (getMapId()
            != other.getMapId()) return false;
      }
      if (!getUnitsList()
          .equals(other.getUnitsList())) return false;
      if (!getBuildingsList()
          .equals(other.getBuildingsList())) return false;
      if (!getSkillsList()
          .equals(other.getSkillsList())) return false;
      if (!internalGetItemSkills().equals(
          other.internalGetItemSkills())) return false;
      if (!getEnemyUnitsList()
          .equals(other.getEnemyUnitsList())) return false;
      if (!getEnemyBuildingsList()
          .equals(other.getEnemyBuildingsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSeed()) {
        hash = (37 * hash) + SEED_FIELD_NUMBER;
        hash = (53 * hash) + getSeed();
      }
      if (hasMapId()) {
        hash = (37 * hash) + MAPID_FIELD_NUMBER;
        hash = (53 * hash) + getMapId();
      }
      if (getUnitsCount() > 0) {
        hash = (37 * hash) + UNITS_FIELD_NUMBER;
        hash = (53 * hash) + getUnitsList().hashCode();
      }
      if (getBuildingsCount() > 0) {
        hash = (37 * hash) + BUILDINGS_FIELD_NUMBER;
        hash = (53 * hash) + getBuildingsList().hashCode();
      }
      if (getSkillsCount() > 0) {
        hash = (37 * hash) + SKILLS_FIELD_NUMBER;
        hash = (53 * hash) + getSkillsList().hashCode();
      }
      if (!internalGetItemSkills().getMap().isEmpty()) {
        hash = (37 * hash) + ITEMSKILLS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetItemSkills().hashCode();
      }
      if (getEnemyUnitsCount() > 0) {
        hash = (37 * hash) + ENEMYUNITS_FIELD_NUMBER;
        hash = (53 * hash) + getEnemyUnitsList().hashCode();
      }
      if (getEnemyBuildingsCount() > 0) {
        hash = (37 * hash) + ENEMYBUILDINGS_FIELD_NUMBER;
        hash = (53 * hash) + getEnemyBuildingsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.RtsBattle.RTSBattleInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 战斗数据
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.RTSBattleInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RTSBattleInfo)
        com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleInfo_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 6:
            return internalGetItemSkills();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 6:
            return internalGetMutableItemSkills();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.RtsBattle.RTSBattleInfo.class, com.yorha.proto.RtsBattle.RTSBattleInfo.Builder.class);
      }

      // Construct using com.yorha.proto.RtsBattle.RTSBattleInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getUnitsFieldBuilder();
          getBuildingsFieldBuilder();
          getEnemyUnitsFieldBuilder();
          getEnemyBuildingsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        seed_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        mapId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (unitsBuilder_ == null) {
          units_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          unitsBuilder_.clear();
        }
        if (buildingsBuilder_ == null) {
          buildings_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          buildingsBuilder_.clear();
        }
        skills_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000010);
        internalGetMutableItemSkills().clear();
        if (enemyUnitsBuilder_ == null) {
          enemyUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
        } else {
          enemyUnitsBuilder_.clear();
        }
        if (enemyBuildingsBuilder_ == null) {
          enemyBuildings_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);
        } else {
          enemyBuildingsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleInfo getDefaultInstanceForType() {
        return com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleInfo build() {
        com.yorha.proto.RtsBattle.RTSBattleInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleInfo buildPartial() {
        com.yorha.proto.RtsBattle.RTSBattleInfo result = new com.yorha.proto.RtsBattle.RTSBattleInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.seed_ = seed_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.mapId_ = mapId_;
          to_bitField0_ |= 0x00000002;
        }
        if (unitsBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            units_ = java.util.Collections.unmodifiableList(units_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.units_ = units_;
        } else {
          result.units_ = unitsBuilder_.build();
        }
        if (buildingsBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            buildings_ = java.util.Collections.unmodifiableList(buildings_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.buildings_ = buildings_;
        } else {
          result.buildings_ = buildingsBuilder_.build();
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          skills_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.skills_ = skills_;
        result.itemSkills_ = internalGetItemSkills();
        result.itemSkills_.makeImmutable();
        if (enemyUnitsBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0)) {
            enemyUnits_ = java.util.Collections.unmodifiableList(enemyUnits_);
            bitField0_ = (bitField0_ & ~0x00000040);
          }
          result.enemyUnits_ = enemyUnits_;
        } else {
          result.enemyUnits_ = enemyUnitsBuilder_.build();
        }
        if (enemyBuildingsBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0)) {
            enemyBuildings_ = java.util.Collections.unmodifiableList(enemyBuildings_);
            bitField0_ = (bitField0_ & ~0x00000080);
          }
          result.enemyBuildings_ = enemyBuildings_;
        } else {
          result.enemyBuildings_ = enemyBuildingsBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.RtsBattle.RTSBattleInfo) {
          return mergeFrom((com.yorha.proto.RtsBattle.RTSBattleInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.RtsBattle.RTSBattleInfo other) {
        if (other == com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance()) return this;
        if (other.hasSeed()) {
          setSeed(other.getSeed());
        }
        if (other.hasMapId()) {
          setMapId(other.getMapId());
        }
        if (unitsBuilder_ == null) {
          if (!other.units_.isEmpty()) {
            if (units_.isEmpty()) {
              units_ = other.units_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureUnitsIsMutable();
              units_.addAll(other.units_);
            }
            onChanged();
          }
        } else {
          if (!other.units_.isEmpty()) {
            if (unitsBuilder_.isEmpty()) {
              unitsBuilder_.dispose();
              unitsBuilder_ = null;
              units_ = other.units_;
              bitField0_ = (bitField0_ & ~0x00000004);
              unitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getUnitsFieldBuilder() : null;
            } else {
              unitsBuilder_.addAllMessages(other.units_);
            }
          }
        }
        if (buildingsBuilder_ == null) {
          if (!other.buildings_.isEmpty()) {
            if (buildings_.isEmpty()) {
              buildings_ = other.buildings_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureBuildingsIsMutable();
              buildings_.addAll(other.buildings_);
            }
            onChanged();
          }
        } else {
          if (!other.buildings_.isEmpty()) {
            if (buildingsBuilder_.isEmpty()) {
              buildingsBuilder_.dispose();
              buildingsBuilder_ = null;
              buildings_ = other.buildings_;
              bitField0_ = (bitField0_ & ~0x00000008);
              buildingsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getBuildingsFieldBuilder() : null;
            } else {
              buildingsBuilder_.addAllMessages(other.buildings_);
            }
          }
        }
        if (!other.skills_.isEmpty()) {
          if (skills_.isEmpty()) {
            skills_ = other.skills_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureSkillsIsMutable();
            skills_.addAll(other.skills_);
          }
          onChanged();
        }
        internalGetMutableItemSkills().mergeFrom(
            other.internalGetItemSkills());
        if (enemyUnitsBuilder_ == null) {
          if (!other.enemyUnits_.isEmpty()) {
            if (enemyUnits_.isEmpty()) {
              enemyUnits_ = other.enemyUnits_;
              bitField0_ = (bitField0_ & ~0x00000040);
            } else {
              ensureEnemyUnitsIsMutable();
              enemyUnits_.addAll(other.enemyUnits_);
            }
            onChanged();
          }
        } else {
          if (!other.enemyUnits_.isEmpty()) {
            if (enemyUnitsBuilder_.isEmpty()) {
              enemyUnitsBuilder_.dispose();
              enemyUnitsBuilder_ = null;
              enemyUnits_ = other.enemyUnits_;
              bitField0_ = (bitField0_ & ~0x00000040);
              enemyUnitsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEnemyUnitsFieldBuilder() : null;
            } else {
              enemyUnitsBuilder_.addAllMessages(other.enemyUnits_);
            }
          }
        }
        if (enemyBuildingsBuilder_ == null) {
          if (!other.enemyBuildings_.isEmpty()) {
            if (enemyBuildings_.isEmpty()) {
              enemyBuildings_ = other.enemyBuildings_;
              bitField0_ = (bitField0_ & ~0x00000080);
            } else {
              ensureEnemyBuildingsIsMutable();
              enemyBuildings_.addAll(other.enemyBuildings_);
            }
            onChanged();
          }
        } else {
          if (!other.enemyBuildings_.isEmpty()) {
            if (enemyBuildingsBuilder_.isEmpty()) {
              enemyBuildingsBuilder_.dispose();
              enemyBuildingsBuilder_ = null;
              enemyBuildings_ = other.enemyBuildings_;
              bitField0_ = (bitField0_ & ~0x00000080);
              enemyBuildingsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEnemyBuildingsFieldBuilder() : null;
            } else {
              enemyBuildingsBuilder_.addAllMessages(other.enemyBuildings_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.RtsBattle.RTSBattleInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.RtsBattle.RTSBattleInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int seed_ ;
      /**
       * <pre>
       * 随机数
       * </pre>
       *
       * <code>optional int32 seed = 1;</code>
       * @return Whether the seed field is set.
       */
      @java.lang.Override
      public boolean hasSeed() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 随机数
       * </pre>
       *
       * <code>optional int32 seed = 1;</code>
       * @return The seed.
       */
      @java.lang.Override
      public int getSeed() {
        return seed_;
      }
      /**
       * <pre>
       * 随机数
       * </pre>
       *
       * <code>optional int32 seed = 1;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(int value) {
        bitField0_ |= 0x00000001;
        seed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 随机数
       * </pre>
       *
       * <code>optional int32 seed = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        bitField0_ = (bitField0_ & ~0x00000001);
        seed_ = 0;
        onChanged();
        return this;
      }

      private int mapId_ ;
      /**
       * <pre>
       * 地图Id
       * </pre>
       *
       * <code>optional int32 mapId = 2;</code>
       * @return Whether the mapId field is set.
       */
      @java.lang.Override
      public boolean hasMapId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 地图Id
       * </pre>
       *
       * <code>optional int32 mapId = 2;</code>
       * @return The mapId.
       */
      @java.lang.Override
      public int getMapId() {
        return mapId_;
      }
      /**
       * <pre>
       * 地图Id
       * </pre>
       *
       * <code>optional int32 mapId = 2;</code>
       * @param value The mapId to set.
       * @return This builder for chaining.
       */
      public Builder setMapId(int value) {
        bitField0_ |= 0x00000002;
        mapId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 地图Id
       * </pre>
       *
       * <code>optional int32 mapId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMapId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        mapId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.RtsBattle.RTSUnit> units_ =
        java.util.Collections.emptyList();
      private void ensureUnitsIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          units_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSUnit>(units_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSUnit, com.yorha.proto.RtsBattle.RTSUnit.Builder, com.yorha.proto.RtsBattle.RTSUnitOrBuilder> unitsBuilder_;

      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSUnit> getUnitsList() {
        if (unitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(units_);
        } else {
          return unitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public int getUnitsCount() {
        if (unitsBuilder_ == null) {
          return units_.size();
        } else {
          return unitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnit getUnits(int index) {
        if (unitsBuilder_ == null) {
          return units_.get(index);
        } else {
          return unitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder setUnits(
          int index, com.yorha.proto.RtsBattle.RTSUnit value) {
        if (unitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUnitsIsMutable();
          units_.set(index, value);
          onChanged();
        } else {
          unitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder setUnits(
          int index, com.yorha.proto.RtsBattle.RTSUnit.Builder builderForValue) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          units_.set(index, builderForValue.build());
          onChanged();
        } else {
          unitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder addUnits(com.yorha.proto.RtsBattle.RTSUnit value) {
        if (unitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUnitsIsMutable();
          units_.add(value);
          onChanged();
        } else {
          unitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder addUnits(
          int index, com.yorha.proto.RtsBattle.RTSUnit value) {
        if (unitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUnitsIsMutable();
          units_.add(index, value);
          onChanged();
        } else {
          unitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder addUnits(
          com.yorha.proto.RtsBattle.RTSUnit.Builder builderForValue) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          units_.add(builderForValue.build());
          onChanged();
        } else {
          unitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder addUnits(
          int index, com.yorha.proto.RtsBattle.RTSUnit.Builder builderForValue) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          units_.add(index, builderForValue.build());
          onChanged();
        } else {
          unitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder addAllUnits(
          java.lang.Iterable<? extends com.yorha.proto.RtsBattle.RTSUnit> values) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, units_);
          onChanged();
        } else {
          unitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder clearUnits() {
        if (unitsBuilder_ == null) {
          units_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          unitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public Builder removeUnits(int index) {
        if (unitsBuilder_ == null) {
          ensureUnitsIsMutable();
          units_.remove(index);
          onChanged();
        } else {
          unitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnit.Builder getUnitsBuilder(
          int index) {
        return getUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnitOrBuilder getUnitsOrBuilder(
          int index) {
        if (unitsBuilder_ == null) {
          return units_.get(index);  } else {
          return unitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public java.util.List<? extends com.yorha.proto.RtsBattle.RTSUnitOrBuilder> 
           getUnitsOrBuilderList() {
        if (unitsBuilder_ != null) {
          return unitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(units_);
        }
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnit.Builder addUnitsBuilder() {
        return getUnitsFieldBuilder().addBuilder(
            com.yorha.proto.RtsBattle.RTSUnit.getDefaultInstance());
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnit.Builder addUnitsBuilder(
          int index) {
        return getUnitsFieldBuilder().addBuilder(
            index, com.yorha.proto.RtsBattle.RTSUnit.getDefaultInstance());
      }
      /**
       * <pre>
       * 作战单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit units = 3;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSUnit.Builder> 
           getUnitsBuilderList() {
        return getUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSUnit, com.yorha.proto.RtsBattle.RTSUnit.Builder, com.yorha.proto.RtsBattle.RTSUnitOrBuilder> 
          getUnitsFieldBuilder() {
        if (unitsBuilder_ == null) {
          unitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSUnit, com.yorha.proto.RtsBattle.RTSUnit.Builder, com.yorha.proto.RtsBattle.RTSUnitOrBuilder>(
                  units_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          units_ = null;
        }
        return unitsBuilder_;
      }

      private java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> buildings_ =
        java.util.Collections.emptyList();
      private void ensureBuildingsIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          buildings_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSBuilding>(buildings_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBuilding, com.yorha.proto.RtsBattle.RTSBuilding.Builder, com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> buildingsBuilder_;

      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> getBuildingsList() {
        if (buildingsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(buildings_);
        } else {
          return buildingsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public int getBuildingsCount() {
        if (buildingsBuilder_ == null) {
          return buildings_.size();
        } else {
          return buildingsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuilding getBuildings(int index) {
        if (buildingsBuilder_ == null) {
          return buildings_.get(index);
        } else {
          return buildingsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder setBuildings(
          int index, com.yorha.proto.RtsBattle.RTSBuilding value) {
        if (buildingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBuildingsIsMutable();
          buildings_.set(index, value);
          onChanged();
        } else {
          buildingsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder setBuildings(
          int index, com.yorha.proto.RtsBattle.RTSBuilding.Builder builderForValue) {
        if (buildingsBuilder_ == null) {
          ensureBuildingsIsMutable();
          buildings_.set(index, builderForValue.build());
          onChanged();
        } else {
          buildingsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder addBuildings(com.yorha.proto.RtsBattle.RTSBuilding value) {
        if (buildingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBuildingsIsMutable();
          buildings_.add(value);
          onChanged();
        } else {
          buildingsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder addBuildings(
          int index, com.yorha.proto.RtsBattle.RTSBuilding value) {
        if (buildingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBuildingsIsMutable();
          buildings_.add(index, value);
          onChanged();
        } else {
          buildingsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder addBuildings(
          com.yorha.proto.RtsBattle.RTSBuilding.Builder builderForValue) {
        if (buildingsBuilder_ == null) {
          ensureBuildingsIsMutable();
          buildings_.add(builderForValue.build());
          onChanged();
        } else {
          buildingsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder addBuildings(
          int index, com.yorha.proto.RtsBattle.RTSBuilding.Builder builderForValue) {
        if (buildingsBuilder_ == null) {
          ensureBuildingsIsMutable();
          buildings_.add(index, builderForValue.build());
          onChanged();
        } else {
          buildingsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder addAllBuildings(
          java.lang.Iterable<? extends com.yorha.proto.RtsBattle.RTSBuilding> values) {
        if (buildingsBuilder_ == null) {
          ensureBuildingsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, buildings_);
          onChanged();
        } else {
          buildingsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder clearBuildings() {
        if (buildingsBuilder_ == null) {
          buildings_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          buildingsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public Builder removeBuildings(int index) {
        if (buildingsBuilder_ == null) {
          ensureBuildingsIsMutable();
          buildings_.remove(index);
          onChanged();
        } else {
          buildingsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuilding.Builder getBuildingsBuilder(
          int index) {
        return getBuildingsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuildingOrBuilder getBuildingsOrBuilder(
          int index) {
        if (buildingsBuilder_ == null) {
          return buildings_.get(index);  } else {
          return buildingsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public java.util.List<? extends com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> 
           getBuildingsOrBuilderList() {
        if (buildingsBuilder_ != null) {
          return buildingsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(buildings_);
        }
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuilding.Builder addBuildingsBuilder() {
        return getBuildingsFieldBuilder().addBuilder(
            com.yorha.proto.RtsBattle.RTSBuilding.getDefaultInstance());
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuilding.Builder addBuildingsBuilder(
          int index) {
        return getBuildingsFieldBuilder().addBuilder(
            index, com.yorha.proto.RtsBattle.RTSBuilding.getDefaultInstance());
      }
      /**
       * <pre>
       * 建筑单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding buildings = 4;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSBuilding.Builder> 
           getBuildingsBuilderList() {
        return getBuildingsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBuilding, com.yorha.proto.RtsBattle.RTSBuilding.Builder, com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> 
          getBuildingsFieldBuilder() {
        if (buildingsBuilder_ == null) {
          buildingsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSBuilding, com.yorha.proto.RtsBattle.RTSBuilding.Builder, com.yorha.proto.RtsBattle.RTSBuildingOrBuilder>(
                  buildings_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          buildings_ = null;
        }
        return buildingsBuilder_;
      }

      private com.google.protobuf.Internal.IntList skills_ = emptyIntList();
      private void ensureSkillsIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          skills_ = mutableCopy(skills_);
          bitField0_ |= 0x00000010;
         }
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 5;</code>
       * @return A list containing the skills.
       */
      public java.util.List<java.lang.Integer>
          getSkillsList() {
        return ((bitField0_ & 0x00000010) != 0) ?
                 java.util.Collections.unmodifiableList(skills_) : skills_;
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 5;</code>
       * @return The count of skills.
       */
      public int getSkillsCount() {
        return skills_.size();
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 5;</code>
       * @param index The index of the element to return.
       * @return The skills at the given index.
       */
      public int getSkills(int index) {
        return skills_.getInt(index);
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 5;</code>
       * @param index The index to set the value at.
       * @param value The skills to set.
       * @return This builder for chaining.
       */
      public Builder setSkills(
          int index, int value) {
        ensureSkillsIsMutable();
        skills_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 5;</code>
       * @param value The skills to add.
       * @return This builder for chaining.
       */
      public Builder addSkills(int value) {
        ensureSkillsIsMutable();
        skills_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 5;</code>
       * @param values The skills to add.
       * @return This builder for chaining.
       */
      public Builder addAllSkills(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureSkillsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, skills_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能
       * </pre>
       *
       * <code>repeated int32 skills = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkills() {
        skills_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> itemSkills_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetItemSkills() {
        if (itemSkills_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ItemSkillsDefaultEntryHolder.defaultEntry);
        }
        return itemSkills_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableItemSkills() {
        onChanged();;
        if (itemSkills_ == null) {
          itemSkills_ = com.google.protobuf.MapField.newMapField(
              ItemSkillsDefaultEntryHolder.defaultEntry);
        }
        if (!itemSkills_.isMutable()) {
          itemSkills_ = itemSkills_.copy();
        }
        return itemSkills_;
      }

      public int getItemSkillsCount() {
        return internalGetItemSkills().getMap().size();
      }
      /**
       * <pre>
       * 道具技能（skill -&gt; times）
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
       */

      @java.lang.Override
      public boolean containsItemSkills(
          int key) {
        
        return internalGetItemSkills().getMap().containsKey(key);
      }
      /**
       * Use {@link #getItemSkillsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getItemSkills() {
        return getItemSkillsMap();
      }
      /**
       * <pre>
       * 道具技能（skill -&gt; times）
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Integer> getItemSkillsMap() {
        return internalGetItemSkills().getMap();
      }
      /**
       * <pre>
       * 道具技能（skill -&gt; times）
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
       */
      @java.lang.Override

      public int getItemSkillsOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetItemSkills().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 道具技能（skill -&gt; times）
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
       */
      @java.lang.Override

      public int getItemSkillsOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetItemSkills().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearItemSkills() {
        internalGetMutableItemSkills().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 道具技能（skill -&gt; times）
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
       */

      public Builder removeItemSkills(
          int key) {
        
        internalGetMutableItemSkills().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableItemSkills() {
        return internalGetMutableItemSkills().getMutableMap();
      }
      /**
       * <pre>
       * 道具技能（skill -&gt; times）
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
       */
      public Builder putItemSkills(
          int key,
          int value) {
        
        
        internalGetMutableItemSkills().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 道具技能（skill -&gt; times）
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; itemSkills = 6;</code>
       */

      public Builder putAllItemSkills(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableItemSkills().getMutableMap()
            .putAll(values);
        return this;
      }

      private java.util.List<com.yorha.proto.RtsBattle.RTSUnit> enemyUnits_ =
        java.util.Collections.emptyList();
      private void ensureEnemyUnitsIsMutable() {
        if (!((bitField0_ & 0x00000040) != 0)) {
          enemyUnits_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSUnit>(enemyUnits_);
          bitField0_ |= 0x00000040;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSUnit, com.yorha.proto.RtsBattle.RTSUnit.Builder, com.yorha.proto.RtsBattle.RTSUnitOrBuilder> enemyUnitsBuilder_;

      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSUnit> getEnemyUnitsList() {
        if (enemyUnitsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(enemyUnits_);
        } else {
          return enemyUnitsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public int getEnemyUnitsCount() {
        if (enemyUnitsBuilder_ == null) {
          return enemyUnits_.size();
        } else {
          return enemyUnitsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnit getEnemyUnits(int index) {
        if (enemyUnitsBuilder_ == null) {
          return enemyUnits_.get(index);
        } else {
          return enemyUnitsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder setEnemyUnits(
          int index, com.yorha.proto.RtsBattle.RTSUnit value) {
        if (enemyUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyUnitsIsMutable();
          enemyUnits_.set(index, value);
          onChanged();
        } else {
          enemyUnitsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder setEnemyUnits(
          int index, com.yorha.proto.RtsBattle.RTSUnit.Builder builderForValue) {
        if (enemyUnitsBuilder_ == null) {
          ensureEnemyUnitsIsMutable();
          enemyUnits_.set(index, builderForValue.build());
          onChanged();
        } else {
          enemyUnitsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder addEnemyUnits(com.yorha.proto.RtsBattle.RTSUnit value) {
        if (enemyUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyUnitsIsMutable();
          enemyUnits_.add(value);
          onChanged();
        } else {
          enemyUnitsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder addEnemyUnits(
          int index, com.yorha.proto.RtsBattle.RTSUnit value) {
        if (enemyUnitsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyUnitsIsMutable();
          enemyUnits_.add(index, value);
          onChanged();
        } else {
          enemyUnitsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder addEnemyUnits(
          com.yorha.proto.RtsBattle.RTSUnit.Builder builderForValue) {
        if (enemyUnitsBuilder_ == null) {
          ensureEnemyUnitsIsMutable();
          enemyUnits_.add(builderForValue.build());
          onChanged();
        } else {
          enemyUnitsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder addEnemyUnits(
          int index, com.yorha.proto.RtsBattle.RTSUnit.Builder builderForValue) {
        if (enemyUnitsBuilder_ == null) {
          ensureEnemyUnitsIsMutable();
          enemyUnits_.add(index, builderForValue.build());
          onChanged();
        } else {
          enemyUnitsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder addAllEnemyUnits(
          java.lang.Iterable<? extends com.yorha.proto.RtsBattle.RTSUnit> values) {
        if (enemyUnitsBuilder_ == null) {
          ensureEnemyUnitsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, enemyUnits_);
          onChanged();
        } else {
          enemyUnitsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder clearEnemyUnits() {
        if (enemyUnitsBuilder_ == null) {
          enemyUnits_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
        } else {
          enemyUnitsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public Builder removeEnemyUnits(int index) {
        if (enemyUnitsBuilder_ == null) {
          ensureEnemyUnitsIsMutable();
          enemyUnits_.remove(index);
          onChanged();
        } else {
          enemyUnitsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnit.Builder getEnemyUnitsBuilder(
          int index) {
        return getEnemyUnitsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnitOrBuilder getEnemyUnitsOrBuilder(
          int index) {
        if (enemyUnitsBuilder_ == null) {
          return enemyUnits_.get(index);  } else {
          return enemyUnitsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public java.util.List<? extends com.yorha.proto.RtsBattle.RTSUnitOrBuilder> 
           getEnemyUnitsOrBuilderList() {
        if (enemyUnitsBuilder_ != null) {
          return enemyUnitsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(enemyUnits_);
        }
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnit.Builder addEnemyUnitsBuilder() {
        return getEnemyUnitsFieldBuilder().addBuilder(
            com.yorha.proto.RtsBattle.RTSUnit.getDefaultInstance());
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public com.yorha.proto.RtsBattle.RTSUnit.Builder addEnemyUnitsBuilder(
          int index) {
        return getEnemyUnitsFieldBuilder().addBuilder(
            index, com.yorha.proto.RtsBattle.RTSUnit.getDefaultInstance());
      }
      /**
       * <pre>
       * 敌方单位
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSUnit enemyUnits = 10;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSUnit.Builder> 
           getEnemyUnitsBuilderList() {
        return getEnemyUnitsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSUnit, com.yorha.proto.RtsBattle.RTSUnit.Builder, com.yorha.proto.RtsBattle.RTSUnitOrBuilder> 
          getEnemyUnitsFieldBuilder() {
        if (enemyUnitsBuilder_ == null) {
          enemyUnitsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSUnit, com.yorha.proto.RtsBattle.RTSUnit.Builder, com.yorha.proto.RtsBattle.RTSUnitOrBuilder>(
                  enemyUnits_,
                  ((bitField0_ & 0x00000040) != 0),
                  getParentForChildren(),
                  isClean());
          enemyUnits_ = null;
        }
        return enemyUnitsBuilder_;
      }

      private java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> enemyBuildings_ =
        java.util.Collections.emptyList();
      private void ensureEnemyBuildingsIsMutable() {
        if (!((bitField0_ & 0x00000080) != 0)) {
          enemyBuildings_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSBuilding>(enemyBuildings_);
          bitField0_ |= 0x00000080;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBuilding, com.yorha.proto.RtsBattle.RTSBuilding.Builder, com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> enemyBuildingsBuilder_;

      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSBuilding> getEnemyBuildingsList() {
        if (enemyBuildingsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(enemyBuildings_);
        } else {
          return enemyBuildingsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public int getEnemyBuildingsCount() {
        if (enemyBuildingsBuilder_ == null) {
          return enemyBuildings_.size();
        } else {
          return enemyBuildingsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuilding getEnemyBuildings(int index) {
        if (enemyBuildingsBuilder_ == null) {
          return enemyBuildings_.get(index);
        } else {
          return enemyBuildingsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder setEnemyBuildings(
          int index, com.yorha.proto.RtsBattle.RTSBuilding value) {
        if (enemyBuildingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyBuildingsIsMutable();
          enemyBuildings_.set(index, value);
          onChanged();
        } else {
          enemyBuildingsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder setEnemyBuildings(
          int index, com.yorha.proto.RtsBattle.RTSBuilding.Builder builderForValue) {
        if (enemyBuildingsBuilder_ == null) {
          ensureEnemyBuildingsIsMutable();
          enemyBuildings_.set(index, builderForValue.build());
          onChanged();
        } else {
          enemyBuildingsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder addEnemyBuildings(com.yorha.proto.RtsBattle.RTSBuilding value) {
        if (enemyBuildingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyBuildingsIsMutable();
          enemyBuildings_.add(value);
          onChanged();
        } else {
          enemyBuildingsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder addEnemyBuildings(
          int index, com.yorha.proto.RtsBattle.RTSBuilding value) {
        if (enemyBuildingsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyBuildingsIsMutable();
          enemyBuildings_.add(index, value);
          onChanged();
        } else {
          enemyBuildingsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder addEnemyBuildings(
          com.yorha.proto.RtsBattle.RTSBuilding.Builder builderForValue) {
        if (enemyBuildingsBuilder_ == null) {
          ensureEnemyBuildingsIsMutable();
          enemyBuildings_.add(builderForValue.build());
          onChanged();
        } else {
          enemyBuildingsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder addEnemyBuildings(
          int index, com.yorha.proto.RtsBattle.RTSBuilding.Builder builderForValue) {
        if (enemyBuildingsBuilder_ == null) {
          ensureEnemyBuildingsIsMutable();
          enemyBuildings_.add(index, builderForValue.build());
          onChanged();
        } else {
          enemyBuildingsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder addAllEnemyBuildings(
          java.lang.Iterable<? extends com.yorha.proto.RtsBattle.RTSBuilding> values) {
        if (enemyBuildingsBuilder_ == null) {
          ensureEnemyBuildingsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, enemyBuildings_);
          onChanged();
        } else {
          enemyBuildingsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder clearEnemyBuildings() {
        if (enemyBuildingsBuilder_ == null) {
          enemyBuildings_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);
          onChanged();
        } else {
          enemyBuildingsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public Builder removeEnemyBuildings(int index) {
        if (enemyBuildingsBuilder_ == null) {
          ensureEnemyBuildingsIsMutable();
          enemyBuildings_.remove(index);
          onChanged();
        } else {
          enemyBuildingsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuilding.Builder getEnemyBuildingsBuilder(
          int index) {
        return getEnemyBuildingsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuildingOrBuilder getEnemyBuildingsOrBuilder(
          int index) {
        if (enemyBuildingsBuilder_ == null) {
          return enemyBuildings_.get(index);  } else {
          return enemyBuildingsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public java.util.List<? extends com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> 
           getEnemyBuildingsOrBuilderList() {
        if (enemyBuildingsBuilder_ != null) {
          return enemyBuildingsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(enemyBuildings_);
        }
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuilding.Builder addEnemyBuildingsBuilder() {
        return getEnemyBuildingsFieldBuilder().addBuilder(
            com.yorha.proto.RtsBattle.RTSBuilding.getDefaultInstance());
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBuilding.Builder addEnemyBuildingsBuilder(
          int index) {
        return getEnemyBuildingsFieldBuilder().addBuilder(
            index, com.yorha.proto.RtsBattle.RTSBuilding.getDefaultInstance());
      }
      /**
       * <pre>
       * 敌方建筑
       * </pre>
       *
       * <code>repeated .com.yorha.proto.RTSBuilding enemyBuildings = 11;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSBuilding.Builder> 
           getEnemyBuildingsBuilderList() {
        return getEnemyBuildingsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBuilding, com.yorha.proto.RtsBattle.RTSBuilding.Builder, com.yorha.proto.RtsBattle.RTSBuildingOrBuilder> 
          getEnemyBuildingsFieldBuilder() {
        if (enemyBuildingsBuilder_ == null) {
          enemyBuildingsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSBuilding, com.yorha.proto.RtsBattle.RTSBuilding.Builder, com.yorha.proto.RtsBattle.RTSBuildingOrBuilder>(
                  enemyBuildings_,
                  ((bitField0_ & 0x00000080) != 0),
                  getParentForChildren(),
                  isClean());
          enemyBuildings_ = null;
        }
        return enemyBuildingsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RTSBattleInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RTSBattleInfo)
    private static final com.yorha.proto.RtsBattle.RTSBattleInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.RtsBattle.RTSBattleInfo();
    }

    public static com.yorha.proto.RtsBattle.RTSBattleInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RTSBattleInfo>
        PARSER = new com.google.protobuf.AbstractParser<RTSBattleInfo>() {
      @java.lang.Override
      public RTSBattleInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RTSBattleInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RTSBattleInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RTSBattleInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RTSOrderOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RTSOrder)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>optional int32 sourceActorId = 2;</code>
     * @return Whether the sourceActorId field is set.
     */
    boolean hasSourceActorId();
    /**
     * <code>optional int32 sourceActorId = 2;</code>
     * @return The sourceActorId.
     */
    int getSourceActorId();

    /**
     * <code>optional int32 targetActorId = 3;</code>
     * @return Whether the targetActorId field is set.
     */
    boolean hasTargetActorId();
    /**
     * <code>optional int32 targetActorId = 3;</code>
     * @return The targetActorId.
     */
    int getTargetActorId();

    /**
     * <code>optional int32 targetCellPosX = 4;</code>
     * @return Whether the targetCellPosX field is set.
     */
    boolean hasTargetCellPosX();
    /**
     * <code>optional int32 targetCellPosX = 4;</code>
     * @return The targetCellPosX.
     */
    int getTargetCellPosX();

    /**
     * <code>optional int32 targetCellPosY = 5;</code>
     * @return Whether the targetCellPosY field is set.
     */
    boolean hasTargetCellPosY();
    /**
     * <code>optional int32 targetCellPosY = 5;</code>
     * @return The targetCellPosY.
     */
    int getTargetCellPosY();

    /**
     * <code>optional string targetString = 6;</code>
     * @return Whether the targetString field is set.
     */
    boolean hasTargetString();
    /**
     * <code>optional string targetString = 6;</code>
     * @return The targetString.
     */
    java.lang.String getTargetString();
    /**
     * <code>optional string targetString = 6;</code>
     * @return The bytes for targetString.
     */
    com.google.protobuf.ByteString
        getTargetStringBytes();

    /**
     * <code>optional int32 extraData = 7;</code>
     * @return Whether the extraData field is set.
     */
    boolean hasExtraData();
    /**
     * <code>optional int32 extraData = 7;</code>
     * @return The extraData.
     */
    int getExtraData();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RTSOrder}
   */
  public static final class RTSOrder extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RTSOrder)
      RTSOrderOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RTSOrder.newBuilder() to construct.
    private RTSOrder(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RTSOrder() {
      targetString_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RTSOrder();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RTSOrder(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              sourceActorId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              targetActorId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              targetCellPosX_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              targetCellPosY_ = input.readInt32();
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              targetString_ = bs;
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              extraData_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSOrder_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSOrder_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.RtsBattle.RTSOrder.class, com.yorha.proto.RtsBattle.RTSOrder.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>optional int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int SOURCEACTORID_FIELD_NUMBER = 2;
    private int sourceActorId_;
    /**
     * <code>optional int32 sourceActorId = 2;</code>
     * @return Whether the sourceActorId field is set.
     */
    @java.lang.Override
    public boolean hasSourceActorId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 sourceActorId = 2;</code>
     * @return The sourceActorId.
     */
    @java.lang.Override
    public int getSourceActorId() {
      return sourceActorId_;
    }

    public static final int TARGETACTORID_FIELD_NUMBER = 3;
    private int targetActorId_;
    /**
     * <code>optional int32 targetActorId = 3;</code>
     * @return Whether the targetActorId field is set.
     */
    @java.lang.Override
    public boolean hasTargetActorId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 targetActorId = 3;</code>
     * @return The targetActorId.
     */
    @java.lang.Override
    public int getTargetActorId() {
      return targetActorId_;
    }

    public static final int TARGETCELLPOSX_FIELD_NUMBER = 4;
    private int targetCellPosX_;
    /**
     * <code>optional int32 targetCellPosX = 4;</code>
     * @return Whether the targetCellPosX field is set.
     */
    @java.lang.Override
    public boolean hasTargetCellPosX() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 targetCellPosX = 4;</code>
     * @return The targetCellPosX.
     */
    @java.lang.Override
    public int getTargetCellPosX() {
      return targetCellPosX_;
    }

    public static final int TARGETCELLPOSY_FIELD_NUMBER = 5;
    private int targetCellPosY_;
    /**
     * <code>optional int32 targetCellPosY = 5;</code>
     * @return Whether the targetCellPosY field is set.
     */
    @java.lang.Override
    public boolean hasTargetCellPosY() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 targetCellPosY = 5;</code>
     * @return The targetCellPosY.
     */
    @java.lang.Override
    public int getTargetCellPosY() {
      return targetCellPosY_;
    }

    public static final int TARGETSTRING_FIELD_NUMBER = 6;
    private volatile java.lang.Object targetString_;
    /**
     * <code>optional string targetString = 6;</code>
     * @return Whether the targetString field is set.
     */
    @java.lang.Override
    public boolean hasTargetString() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string targetString = 6;</code>
     * @return The targetString.
     */
    @java.lang.Override
    public java.lang.String getTargetString() {
      java.lang.Object ref = targetString_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          targetString_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string targetString = 6;</code>
     * @return The bytes for targetString.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTargetStringBytes() {
      java.lang.Object ref = targetString_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetString_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EXTRADATA_FIELD_NUMBER = 7;
    private int extraData_;
    /**
     * <code>optional int32 extraData = 7;</code>
     * @return Whether the extraData field is set.
     */
    @java.lang.Override
    public boolean hasExtraData() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 extraData = 7;</code>
     * @return The extraData.
     */
    @java.lang.Override
    public int getExtraData() {
      return extraData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, sourceActorId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, targetActorId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, targetCellPosX_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, targetCellPosY_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, targetString_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(7, extraData_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, sourceActorId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, targetActorId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, targetCellPosX_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, targetCellPosY_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, targetString_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, extraData_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.RtsBattle.RTSOrder)) {
        return super.equals(obj);
      }
      com.yorha.proto.RtsBattle.RTSOrder other = (com.yorha.proto.RtsBattle.RTSOrder) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (getType()
            != other.getType()) return false;
      }
      if (hasSourceActorId() != other.hasSourceActorId()) return false;
      if (hasSourceActorId()) {
        if (getSourceActorId()
            != other.getSourceActorId()) return false;
      }
      if (hasTargetActorId() != other.hasTargetActorId()) return false;
      if (hasTargetActorId()) {
        if (getTargetActorId()
            != other.getTargetActorId()) return false;
      }
      if (hasTargetCellPosX() != other.hasTargetCellPosX()) return false;
      if (hasTargetCellPosX()) {
        if (getTargetCellPosX()
            != other.getTargetCellPosX()) return false;
      }
      if (hasTargetCellPosY() != other.hasTargetCellPosY()) return false;
      if (hasTargetCellPosY()) {
        if (getTargetCellPosY()
            != other.getTargetCellPosY()) return false;
      }
      if (hasTargetString() != other.hasTargetString()) return false;
      if (hasTargetString()) {
        if (!getTargetString()
            .equals(other.getTargetString())) return false;
      }
      if (hasExtraData() != other.hasExtraData()) return false;
      if (hasExtraData()) {
        if (getExtraData()
            != other.getExtraData()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType();
      }
      if (hasSourceActorId()) {
        hash = (37 * hash) + SOURCEACTORID_FIELD_NUMBER;
        hash = (53 * hash) + getSourceActorId();
      }
      if (hasTargetActorId()) {
        hash = (37 * hash) + TARGETACTORID_FIELD_NUMBER;
        hash = (53 * hash) + getTargetActorId();
      }
      if (hasTargetCellPosX()) {
        hash = (37 * hash) + TARGETCELLPOSX_FIELD_NUMBER;
        hash = (53 * hash) + getTargetCellPosX();
      }
      if (hasTargetCellPosY()) {
        hash = (37 * hash) + TARGETCELLPOSY_FIELD_NUMBER;
        hash = (53 * hash) + getTargetCellPosY();
      }
      if (hasTargetString()) {
        hash = (37 * hash) + TARGETSTRING_FIELD_NUMBER;
        hash = (53 * hash) + getTargetString().hashCode();
      }
      if (hasExtraData()) {
        hash = (37 * hash) + EXTRADATA_FIELD_NUMBER;
        hash = (53 * hash) + getExtraData();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSOrder parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.RtsBattle.RTSOrder prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RTSOrder}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RTSOrder)
        com.yorha.proto.RtsBattle.RTSOrderOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSOrder_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSOrder_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.RtsBattle.RTSOrder.class, com.yorha.proto.RtsBattle.RTSOrder.Builder.class);
      }

      // Construct using com.yorha.proto.RtsBattle.RTSOrder.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        sourceActorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        targetActorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        targetCellPosX_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        targetCellPosY_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        targetString_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        extraData_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSOrder_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSOrder getDefaultInstanceForType() {
        return com.yorha.proto.RtsBattle.RTSOrder.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSOrder build() {
        com.yorha.proto.RtsBattle.RTSOrder result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSOrder buildPartial() {
        com.yorha.proto.RtsBattle.RTSOrder result = new com.yorha.proto.RtsBattle.RTSOrder(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.sourceActorId_ = sourceActorId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.targetActorId_ = targetActorId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.targetCellPosX_ = targetCellPosX_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.targetCellPosY_ = targetCellPosY_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.targetString_ = targetString_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.extraData_ = extraData_;
          to_bitField0_ |= 0x00000040;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.RtsBattle.RTSOrder) {
          return mergeFrom((com.yorha.proto.RtsBattle.RTSOrder)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.RtsBattle.RTSOrder other) {
        if (other == com.yorha.proto.RtsBattle.RTSOrder.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasSourceActorId()) {
          setSourceActorId(other.getSourceActorId());
        }
        if (other.hasTargetActorId()) {
          setTargetActorId(other.getTargetActorId());
        }
        if (other.hasTargetCellPosX()) {
          setTargetCellPosX(other.getTargetCellPosX());
        }
        if (other.hasTargetCellPosY()) {
          setTargetCellPosY(other.getTargetCellPosY());
        }
        if (other.hasTargetString()) {
          bitField0_ |= 0x00000020;
          targetString_ = other.targetString_;
          onChanged();
        }
        if (other.hasExtraData()) {
          setExtraData(other.getExtraData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.RtsBattle.RTSOrder parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.RtsBattle.RTSOrder) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>optional int32 type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int sourceActorId_ ;
      /**
       * <code>optional int32 sourceActorId = 2;</code>
       * @return Whether the sourceActorId field is set.
       */
      @java.lang.Override
      public boolean hasSourceActorId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 sourceActorId = 2;</code>
       * @return The sourceActorId.
       */
      @java.lang.Override
      public int getSourceActorId() {
        return sourceActorId_;
      }
      /**
       * <code>optional int32 sourceActorId = 2;</code>
       * @param value The sourceActorId to set.
       * @return This builder for chaining.
       */
      public Builder setSourceActorId(int value) {
        bitField0_ |= 0x00000002;
        sourceActorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 sourceActorId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSourceActorId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        sourceActorId_ = 0;
        onChanged();
        return this;
      }

      private int targetActorId_ ;
      /**
       * <code>optional int32 targetActorId = 3;</code>
       * @return Whether the targetActorId field is set.
       */
      @java.lang.Override
      public boolean hasTargetActorId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 targetActorId = 3;</code>
       * @return The targetActorId.
       */
      @java.lang.Override
      public int getTargetActorId() {
        return targetActorId_;
      }
      /**
       * <code>optional int32 targetActorId = 3;</code>
       * @param value The targetActorId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetActorId(int value) {
        bitField0_ |= 0x00000004;
        targetActorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 targetActorId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetActorId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        targetActorId_ = 0;
        onChanged();
        return this;
      }

      private int targetCellPosX_ ;
      /**
       * <code>optional int32 targetCellPosX = 4;</code>
       * @return Whether the targetCellPosX field is set.
       */
      @java.lang.Override
      public boolean hasTargetCellPosX() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 targetCellPosX = 4;</code>
       * @return The targetCellPosX.
       */
      @java.lang.Override
      public int getTargetCellPosX() {
        return targetCellPosX_;
      }
      /**
       * <code>optional int32 targetCellPosX = 4;</code>
       * @param value The targetCellPosX to set.
       * @return This builder for chaining.
       */
      public Builder setTargetCellPosX(int value) {
        bitField0_ |= 0x00000008;
        targetCellPosX_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 targetCellPosX = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetCellPosX() {
        bitField0_ = (bitField0_ & ~0x00000008);
        targetCellPosX_ = 0;
        onChanged();
        return this;
      }

      private int targetCellPosY_ ;
      /**
       * <code>optional int32 targetCellPosY = 5;</code>
       * @return Whether the targetCellPosY field is set.
       */
      @java.lang.Override
      public boolean hasTargetCellPosY() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 targetCellPosY = 5;</code>
       * @return The targetCellPosY.
       */
      @java.lang.Override
      public int getTargetCellPosY() {
        return targetCellPosY_;
      }
      /**
       * <code>optional int32 targetCellPosY = 5;</code>
       * @param value The targetCellPosY to set.
       * @return This builder for chaining.
       */
      public Builder setTargetCellPosY(int value) {
        bitField0_ |= 0x00000010;
        targetCellPosY_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 targetCellPosY = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetCellPosY() {
        bitField0_ = (bitField0_ & ~0x00000010);
        targetCellPosY_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object targetString_ = "";
      /**
       * <code>optional string targetString = 6;</code>
       * @return Whether the targetString field is set.
       */
      public boolean hasTargetString() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional string targetString = 6;</code>
       * @return The targetString.
       */
      public java.lang.String getTargetString() {
        java.lang.Object ref = targetString_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            targetString_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string targetString = 6;</code>
       * @return The bytes for targetString.
       */
      public com.google.protobuf.ByteString
          getTargetStringBytes() {
        java.lang.Object ref = targetString_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          targetString_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string targetString = 6;</code>
       * @param value The targetString to set.
       * @return This builder for chaining.
       */
      public Builder setTargetString(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        targetString_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string targetString = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetString() {
        bitField0_ = (bitField0_ & ~0x00000020);
        targetString_ = getDefaultInstance().getTargetString();
        onChanged();
        return this;
      }
      /**
       * <code>optional string targetString = 6;</code>
       * @param value The bytes for targetString to set.
       * @return This builder for chaining.
       */
      public Builder setTargetStringBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        targetString_ = value;
        onChanged();
        return this;
      }

      private int extraData_ ;
      /**
       * <code>optional int32 extraData = 7;</code>
       * @return Whether the extraData field is set.
       */
      @java.lang.Override
      public boolean hasExtraData() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional int32 extraData = 7;</code>
       * @return The extraData.
       */
      @java.lang.Override
      public int getExtraData() {
        return extraData_;
      }
      /**
       * <code>optional int32 extraData = 7;</code>
       * @param value The extraData to set.
       * @return This builder for chaining.
       */
      public Builder setExtraData(int value) {
        bitField0_ |= 0x00000040;
        extraData_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 extraData = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraData() {
        bitField0_ = (bitField0_ & ~0x00000040);
        extraData_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RTSOrder)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RTSOrder)
    private static final com.yorha.proto.RtsBattle.RTSOrder DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.RtsBattle.RTSOrder();
    }

    public static com.yorha.proto.RtsBattle.RTSOrder getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RTSOrder>
        PARSER = new com.google.protobuf.AbstractParser<RTSOrder>() {
      @java.lang.Override
      public RTSOrder parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RTSOrder(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RTSOrder> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RTSOrder> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSOrder getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RTSFrameOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RTSFrame)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    java.util.List<com.yorha.proto.RtsBattle.RTSOrder> 
        getOrdersList();
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    com.yorha.proto.RtsBattle.RTSOrder getOrders(int index);
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    int getOrdersCount();
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.RtsBattle.RTSOrderOrBuilder> 
        getOrdersOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    com.yorha.proto.RtsBattle.RTSOrderOrBuilder getOrdersOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.RTSFrame}
   */
  public static final class RTSFrame extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RTSFrame)
      RTSFrameOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RTSFrame.newBuilder() to construct.
    private RTSFrame(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RTSFrame() {
      orders_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RTSFrame();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RTSFrame(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                orders_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSOrder>();
                mutable_bitField0_ |= 0x00000001;
              }
              orders_.add(
                  input.readMessage(com.yorha.proto.RtsBattle.RTSOrder.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          orders_ = java.util.Collections.unmodifiableList(orders_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSFrame_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSFrame_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.RtsBattle.RTSFrame.class, com.yorha.proto.RtsBattle.RTSFrame.Builder.class);
    }

    public static final int ORDERS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.RtsBattle.RTSOrder> orders_;
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.RtsBattle.RTSOrder> getOrdersList() {
      return orders_;
    }
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.RtsBattle.RTSOrderOrBuilder> 
        getOrdersOrBuilderList() {
      return orders_;
    }
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    @java.lang.Override
    public int getOrdersCount() {
      return orders_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSOrder getOrders(int index) {
      return orders_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSOrderOrBuilder getOrdersOrBuilder(
        int index) {
      return orders_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < orders_.size(); i++) {
        output.writeMessage(1, orders_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < orders_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, orders_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.RtsBattle.RTSFrame)) {
        return super.equals(obj);
      }
      com.yorha.proto.RtsBattle.RTSFrame other = (com.yorha.proto.RtsBattle.RTSFrame) obj;

      if (!getOrdersList()
          .equals(other.getOrdersList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getOrdersCount() > 0) {
        hash = (37 * hash) + ORDERS_FIELD_NUMBER;
        hash = (53 * hash) + getOrdersList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSFrame parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.RtsBattle.RTSFrame prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RTSFrame}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RTSFrame)
        com.yorha.proto.RtsBattle.RTSFrameOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSFrame_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSFrame_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.RtsBattle.RTSFrame.class, com.yorha.proto.RtsBattle.RTSFrame.Builder.class);
      }

      // Construct using com.yorha.proto.RtsBattle.RTSFrame.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getOrdersFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (ordersBuilder_ == null) {
          orders_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ordersBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSFrame_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSFrame getDefaultInstanceForType() {
        return com.yorha.proto.RtsBattle.RTSFrame.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSFrame build() {
        com.yorha.proto.RtsBattle.RTSFrame result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSFrame buildPartial() {
        com.yorha.proto.RtsBattle.RTSFrame result = new com.yorha.proto.RtsBattle.RTSFrame(this);
        int from_bitField0_ = bitField0_;
        if (ordersBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            orders_ = java.util.Collections.unmodifiableList(orders_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.orders_ = orders_;
        } else {
          result.orders_ = ordersBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.RtsBattle.RTSFrame) {
          return mergeFrom((com.yorha.proto.RtsBattle.RTSFrame)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.RtsBattle.RTSFrame other) {
        if (other == com.yorha.proto.RtsBattle.RTSFrame.getDefaultInstance()) return this;
        if (ordersBuilder_ == null) {
          if (!other.orders_.isEmpty()) {
            if (orders_.isEmpty()) {
              orders_ = other.orders_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureOrdersIsMutable();
              orders_.addAll(other.orders_);
            }
            onChanged();
          }
        } else {
          if (!other.orders_.isEmpty()) {
            if (ordersBuilder_.isEmpty()) {
              ordersBuilder_.dispose();
              ordersBuilder_ = null;
              orders_ = other.orders_;
              bitField0_ = (bitField0_ & ~0x00000001);
              ordersBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getOrdersFieldBuilder() : null;
            } else {
              ordersBuilder_.addAllMessages(other.orders_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.RtsBattle.RTSFrame parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.RtsBattle.RTSFrame) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.RtsBattle.RTSOrder> orders_ =
        java.util.Collections.emptyList();
      private void ensureOrdersIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          orders_ = new java.util.ArrayList<com.yorha.proto.RtsBattle.RTSOrder>(orders_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSOrder, com.yorha.proto.RtsBattle.RTSOrder.Builder, com.yorha.proto.RtsBattle.RTSOrderOrBuilder> ordersBuilder_;

      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSOrder> getOrdersList() {
        if (ordersBuilder_ == null) {
          return java.util.Collections.unmodifiableList(orders_);
        } else {
          return ordersBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public int getOrdersCount() {
        if (ordersBuilder_ == null) {
          return orders_.size();
        } else {
          return ordersBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public com.yorha.proto.RtsBattle.RTSOrder getOrders(int index) {
        if (ordersBuilder_ == null) {
          return orders_.get(index);
        } else {
          return ordersBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder setOrders(
          int index, com.yorha.proto.RtsBattle.RTSOrder value) {
        if (ordersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOrdersIsMutable();
          orders_.set(index, value);
          onChanged();
        } else {
          ordersBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder setOrders(
          int index, com.yorha.proto.RtsBattle.RTSOrder.Builder builderForValue) {
        if (ordersBuilder_ == null) {
          ensureOrdersIsMutable();
          orders_.set(index, builderForValue.build());
          onChanged();
        } else {
          ordersBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder addOrders(com.yorha.proto.RtsBattle.RTSOrder value) {
        if (ordersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOrdersIsMutable();
          orders_.add(value);
          onChanged();
        } else {
          ordersBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder addOrders(
          int index, com.yorha.proto.RtsBattle.RTSOrder value) {
        if (ordersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOrdersIsMutable();
          orders_.add(index, value);
          onChanged();
        } else {
          ordersBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder addOrders(
          com.yorha.proto.RtsBattle.RTSOrder.Builder builderForValue) {
        if (ordersBuilder_ == null) {
          ensureOrdersIsMutable();
          orders_.add(builderForValue.build());
          onChanged();
        } else {
          ordersBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder addOrders(
          int index, com.yorha.proto.RtsBattle.RTSOrder.Builder builderForValue) {
        if (ordersBuilder_ == null) {
          ensureOrdersIsMutable();
          orders_.add(index, builderForValue.build());
          onChanged();
        } else {
          ordersBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder addAllOrders(
          java.lang.Iterable<? extends com.yorha.proto.RtsBattle.RTSOrder> values) {
        if (ordersBuilder_ == null) {
          ensureOrdersIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, orders_);
          onChanged();
        } else {
          ordersBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder clearOrders() {
        if (ordersBuilder_ == null) {
          orders_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          ordersBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public Builder removeOrders(int index) {
        if (ordersBuilder_ == null) {
          ensureOrdersIsMutable();
          orders_.remove(index);
          onChanged();
        } else {
          ordersBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public com.yorha.proto.RtsBattle.RTSOrder.Builder getOrdersBuilder(
          int index) {
        return getOrdersFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public com.yorha.proto.RtsBattle.RTSOrderOrBuilder getOrdersOrBuilder(
          int index) {
        if (ordersBuilder_ == null) {
          return orders_.get(index);  } else {
          return ordersBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.RtsBattle.RTSOrderOrBuilder> 
           getOrdersOrBuilderList() {
        if (ordersBuilder_ != null) {
          return ordersBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(orders_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public com.yorha.proto.RtsBattle.RTSOrder.Builder addOrdersBuilder() {
        return getOrdersFieldBuilder().addBuilder(
            com.yorha.proto.RtsBattle.RTSOrder.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public com.yorha.proto.RtsBattle.RTSOrder.Builder addOrdersBuilder(
          int index) {
        return getOrdersFieldBuilder().addBuilder(
            index, com.yorha.proto.RtsBattle.RTSOrder.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RTSOrder orders = 1;</code>
       */
      public java.util.List<com.yorha.proto.RtsBattle.RTSOrder.Builder> 
           getOrdersBuilderList() {
        return getOrdersFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSOrder, com.yorha.proto.RtsBattle.RTSOrder.Builder, com.yorha.proto.RtsBattle.RTSOrderOrBuilder> 
          getOrdersFieldBuilder() {
        if (ordersBuilder_ == null) {
          ordersBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSOrder, com.yorha.proto.RtsBattle.RTSOrder.Builder, com.yorha.proto.RtsBattle.RTSOrderOrBuilder>(
                  orders_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          orders_ = null;
        }
        return ordersBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RTSFrame)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RTSFrame)
    private static final com.yorha.proto.RtsBattle.RTSFrame DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.RtsBattle.RTSFrame();
    }

    public static com.yorha.proto.RtsBattle.RTSFrame getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RTSFrame>
        PARSER = new com.google.protobuf.AbstractParser<RTSFrame>() {
      @java.lang.Override
      public RTSFrame parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RTSFrame(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RTSFrame> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RTSFrame> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSFrame getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RTSBattleRecordOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RTSBattleRecord)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */
    int getFramesCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */
    boolean containsFrames(
        int key);
    /**
     * Use {@link #getFramesMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>
    getFrames();
    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>
    getFramesMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */

    com.yorha.proto.RtsBattle.RTSFrame getFramesOrDefault(
        int key,
        com.yorha.proto.RtsBattle.RTSFrame defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */

    com.yorha.proto.RtsBattle.RTSFrame getFramesOrThrow(
        int key);
  }
  /**
   * <pre>
   * 战斗记录
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.RTSBattleRecord}
   */
  public static final class RTSBattleRecord extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RTSBattleRecord)
      RTSBattleRecordOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RTSBattleRecord.newBuilder() to construct.
    private RTSBattleRecord(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RTSBattleRecord() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RTSBattleRecord();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RTSBattleRecord(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                frames_ = com.google.protobuf.MapField.newMapField(
                    FramesDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>
              frames__ = input.readMessage(
                  FramesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              frames_.getMutableMap().put(
                  frames__.getKey(), frames__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleRecord_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetFrames();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleRecord_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.RtsBattle.RTSBattleRecord.class, com.yorha.proto.RtsBattle.RTSBattleRecord.Builder.class);
    }

    public static final int FRAMES_FIELD_NUMBER = 1;
    private static final class FramesDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>newDefaultInstance(
                  com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleRecord_FramesEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.RtsBattle.RTSFrame.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> frames_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>
    internalGetFrames() {
      if (frames_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            FramesDefaultEntryHolder.defaultEntry);
      }
      return frames_;
    }

    public int getFramesCount() {
      return internalGetFrames().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */

    @java.lang.Override
    public boolean containsFrames(
        int key) {
      
      return internalGetFrames().getMap().containsKey(key);
    }
    /**
     * Use {@link #getFramesMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> getFrames() {
      return getFramesMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> getFramesMap() {
      return internalGetFrames().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.RtsBattle.RTSFrame getFramesOrDefault(
        int key,
        com.yorha.proto.RtsBattle.RTSFrame defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> map =
          internalGetFrames().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.RtsBattle.RTSFrame getFramesOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> map =
          internalGetFrames().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetFrames(),
          FramesDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> entry
           : internalGetFrames().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>
        frames__ = FramesDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, frames__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.RtsBattle.RTSBattleRecord)) {
        return super.equals(obj);
      }
      com.yorha.proto.RtsBattle.RTSBattleRecord other = (com.yorha.proto.RtsBattle.RTSBattleRecord) obj;

      if (!internalGetFrames().equals(
          other.internalGetFrames())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetFrames().getMap().isEmpty()) {
        hash = (37 * hash) + FRAMES_FIELD_NUMBER;
        hash = (53 * hash) + internalGetFrames().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleRecord parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.RtsBattle.RTSBattleRecord prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 战斗记录
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.RTSBattleRecord}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RTSBattleRecord)
        com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleRecord_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetFrames();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableFrames();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleRecord_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.RtsBattle.RTSBattleRecord.class, com.yorha.proto.RtsBattle.RTSBattleRecord.Builder.class);
      }

      // Construct using com.yorha.proto.RtsBattle.RTSBattleRecord.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableFrames().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleRecord_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleRecord getDefaultInstanceForType() {
        return com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleRecord build() {
        com.yorha.proto.RtsBattle.RTSBattleRecord result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleRecord buildPartial() {
        com.yorha.proto.RtsBattle.RTSBattleRecord result = new com.yorha.proto.RtsBattle.RTSBattleRecord(this);
        int from_bitField0_ = bitField0_;
        result.frames_ = internalGetFrames();
        result.frames_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.RtsBattle.RTSBattleRecord) {
          return mergeFrom((com.yorha.proto.RtsBattle.RTSBattleRecord)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.RtsBattle.RTSBattleRecord other) {
        if (other == com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance()) return this;
        internalGetMutableFrames().mergeFrom(
            other.internalGetFrames());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.RtsBattle.RTSBattleRecord parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.RtsBattle.RTSBattleRecord) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> frames_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>
      internalGetFrames() {
        if (frames_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              FramesDefaultEntryHolder.defaultEntry);
        }
        return frames_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>
      internalGetMutableFrames() {
        onChanged();;
        if (frames_ == null) {
          frames_ = com.google.protobuf.MapField.newMapField(
              FramesDefaultEntryHolder.defaultEntry);
        }
        if (!frames_.isMutable()) {
          frames_ = frames_.copy();
        }
        return frames_;
      }

      public int getFramesCount() {
        return internalGetFrames().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
       */

      @java.lang.Override
      public boolean containsFrames(
          int key) {
        
        return internalGetFrames().getMap().containsKey(key);
      }
      /**
       * Use {@link #getFramesMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> getFrames() {
        return getFramesMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> getFramesMap() {
        return internalGetFrames().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.RtsBattle.RTSFrame getFramesOrDefault(
          int key,
          com.yorha.proto.RtsBattle.RTSFrame defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> map =
            internalGetFrames().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.RtsBattle.RTSFrame getFramesOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> map =
            internalGetFrames().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearFrames() {
        internalGetMutableFrames().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
       */

      public Builder removeFrames(
          int key) {
        
        internalGetMutableFrames().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame>
      getMutableFrames() {
        return internalGetMutableFrames().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
       */
      public Builder putFrames(
          int key,
          com.yorha.proto.RtsBattle.RTSFrame value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableFrames().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.RTSFrame&gt; frames = 1;</code>
       */

      public Builder putAllFrames(
          java.util.Map<java.lang.Integer, com.yorha.proto.RtsBattle.RTSFrame> values) {
        internalGetMutableFrames().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RTSBattleRecord)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RTSBattleRecord)
    private static final com.yorha.proto.RtsBattle.RTSBattleRecord DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.RtsBattle.RTSBattleRecord();
    }

    public static com.yorha.proto.RtsBattle.RTSBattleRecord getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RTSBattleRecord>
        PARSER = new com.google.protobuf.AbstractParser<RTSBattleRecord>() {
      @java.lang.Override
      public RTSBattleRecord parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RTSBattleRecord(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RTSBattleRecord> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RTSBattleRecord> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleRecord getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RTSBattleReportOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RTSBattleReport)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 reportId = 1;</code>
     * @return Whether the reportId field is set.
     */
    boolean hasReportId();
    /**
     * <code>optional int32 reportId = 1;</code>
     * @return The reportId.
     */
    int getReportId();

    /**
     * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
     * @return The info.
     */
    com.yorha.proto.RtsBattle.RTSBattleInfo getInfo();
    /**
     * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
     */
    com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder getInfoOrBuilder();

    /**
     * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
     * @return Whether the record field is set.
     */
    boolean hasRecord();
    /**
     * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
     * @return The record.
     */
    com.yorha.proto.RtsBattle.RTSBattleRecord getRecord();
    /**
     * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
     */
    com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder getRecordOrBuilder();
  }
  /**
   * <pre>
   * 战报
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.RTSBattleReport}
   */
  public static final class RTSBattleReport extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RTSBattleReport)
      RTSBattleReportOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RTSBattleReport.newBuilder() to construct.
    private RTSBattleReport(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RTSBattleReport() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RTSBattleReport();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RTSBattleReport(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              reportId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.RtsBattle.RTSBattleInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = info_.toBuilder();
              }
              info_ = input.readMessage(com.yorha.proto.RtsBattle.RTSBattleInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(info_);
                info_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.yorha.proto.RtsBattle.RTSBattleRecord.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = record_.toBuilder();
              }
              record_ = input.readMessage(com.yorha.proto.RtsBattle.RTSBattleRecord.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(record_);
                record_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleReport_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleReport_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.RtsBattle.RTSBattleReport.class, com.yorha.proto.RtsBattle.RTSBattleReport.Builder.class);
    }

    private int bitField0_;
    public static final int REPORTID_FIELD_NUMBER = 1;
    private int reportId_;
    /**
     * <code>optional int32 reportId = 1;</code>
     * @return Whether the reportId field is set.
     */
    @java.lang.Override
    public boolean hasReportId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 reportId = 1;</code>
     * @return The reportId.
     */
    @java.lang.Override
    public int getReportId() {
      return reportId_;
    }

    public static final int INFO_FIELD_NUMBER = 2;
    private com.yorha.proto.RtsBattle.RTSBattleInfo info_;
    /**
     * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
     * @return The info.
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleInfo getInfo() {
      return info_ == null ? com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance() : info_;
    }
    /**
     * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder getInfoOrBuilder() {
      return info_ == null ? com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance() : info_;
    }

    public static final int RECORD_FIELD_NUMBER = 3;
    private com.yorha.proto.RtsBattle.RTSBattleRecord record_;
    /**
     * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
     * @return Whether the record field is set.
     */
    @java.lang.Override
    public boolean hasRecord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
     * @return The record.
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleRecord getRecord() {
      return record_ == null ? com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance() : record_;
    }
    /**
     * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder getRecordOrBuilder() {
      return record_ == null ? com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance() : record_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, reportId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getInfo());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getRecord());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, reportId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getInfo());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getRecord());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.RtsBattle.RTSBattleReport)) {
        return super.equals(obj);
      }
      com.yorha.proto.RtsBattle.RTSBattleReport other = (com.yorha.proto.RtsBattle.RTSBattleReport) obj;

      if (hasReportId() != other.hasReportId()) return false;
      if (hasReportId()) {
        if (getReportId()
            != other.getReportId()) return false;
      }
      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (hasRecord() != other.hasRecord()) return false;
      if (hasRecord()) {
        if (!getRecord()
            .equals(other.getRecord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReportId()) {
        hash = (37 * hash) + REPORTID_FIELD_NUMBER;
        hash = (53 * hash) + getReportId();
      }
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      if (hasRecord()) {
        hash = (37 * hash) + RECORD_FIELD_NUMBER;
        hash = (53 * hash) + getRecord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.RtsBattle.RTSBattleReport parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.RtsBattle.RTSBattleReport prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 战报
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.RTSBattleReport}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RTSBattleReport)
        com.yorha.proto.RtsBattle.RTSBattleReportOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleReport_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleReport_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.RtsBattle.RTSBattleReport.class, com.yorha.proto.RtsBattle.RTSBattleReport.Builder.class);
      }

      // Construct using com.yorha.proto.RtsBattle.RTSBattleReport.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
          getRecordFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        reportId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (infoBuilder_ == null) {
          info_ = null;
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (recordBuilder_ == null) {
          record_ = null;
        } else {
          recordBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.RtsBattle.internal_static_com_yorha_proto_RTSBattleReport_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleReport getDefaultInstanceForType() {
        return com.yorha.proto.RtsBattle.RTSBattleReport.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleReport build() {
        com.yorha.proto.RtsBattle.RTSBattleReport result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.RtsBattle.RTSBattleReport buildPartial() {
        com.yorha.proto.RtsBattle.RTSBattleReport result = new com.yorha.proto.RtsBattle.RTSBattleReport(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.reportId_ = reportId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (infoBuilder_ == null) {
            result.info_ = info_;
          } else {
            result.info_ = infoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (recordBuilder_ == null) {
            result.record_ = record_;
          } else {
            result.record_ = recordBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.RtsBattle.RTSBattleReport) {
          return mergeFrom((com.yorha.proto.RtsBattle.RTSBattleReport)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.RtsBattle.RTSBattleReport other) {
        if (other == com.yorha.proto.RtsBattle.RTSBattleReport.getDefaultInstance()) return this;
        if (other.hasReportId()) {
          setReportId(other.getReportId());
        }
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        if (other.hasRecord()) {
          mergeRecord(other.getRecord());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.RtsBattle.RTSBattleReport parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.RtsBattle.RTSBattleReport) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int reportId_ ;
      /**
       * <code>optional int32 reportId = 1;</code>
       * @return Whether the reportId field is set.
       */
      @java.lang.Override
      public boolean hasReportId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 reportId = 1;</code>
       * @return The reportId.
       */
      @java.lang.Override
      public int getReportId() {
        return reportId_;
      }
      /**
       * <code>optional int32 reportId = 1;</code>
       * @param value The reportId to set.
       * @return This builder for chaining.
       */
      public Builder setReportId(int value) {
        bitField0_ |= 0x00000001;
        reportId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 reportId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearReportId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        reportId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.RtsBattle.RTSBattleInfo info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBattleInfo, com.yorha.proto.RtsBattle.RTSBattleInfo.Builder, com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder> infoBuilder_;
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       * @return The info.
       */
      public com.yorha.proto.RtsBattle.RTSBattleInfo getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       */
      public Builder setInfo(com.yorha.proto.RtsBattle.RTSBattleInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
          onChanged();
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       */
      public Builder setInfo(
          com.yorha.proto.RtsBattle.RTSBattleInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
          onChanged();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       */
      public Builder mergeInfo(com.yorha.proto.RtsBattle.RTSBattleInfo value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              info_ != null &&
              info_ != com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance()) {
            info_ =
              com.yorha.proto.RtsBattle.RTSBattleInfo.newBuilder(info_).mergeFrom(value).buildPartial();
          } else {
            info_ = value;
          }
          onChanged();
        } else {
          infoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = null;
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBattleInfo.Builder getInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance() : info_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleInfo info = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBattleInfo, com.yorha.proto.RtsBattle.RTSBattleInfo.Builder, com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSBattleInfo, com.yorha.proto.RtsBattle.RTSBattleInfo.Builder, com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }

      private com.yorha.proto.RtsBattle.RTSBattleRecord record_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBattleRecord, com.yorha.proto.RtsBattle.RTSBattleRecord.Builder, com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder> recordBuilder_;
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       * @return Whether the record field is set.
       */
      public boolean hasRecord() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       * @return The record.
       */
      public com.yorha.proto.RtsBattle.RTSBattleRecord getRecord() {
        if (recordBuilder_ == null) {
          return record_ == null ? com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance() : record_;
        } else {
          return recordBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       */
      public Builder setRecord(com.yorha.proto.RtsBattle.RTSBattleRecord value) {
        if (recordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          record_ = value;
          onChanged();
        } else {
          recordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       */
      public Builder setRecord(
          com.yorha.proto.RtsBattle.RTSBattleRecord.Builder builderForValue) {
        if (recordBuilder_ == null) {
          record_ = builderForValue.build();
          onChanged();
        } else {
          recordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       */
      public Builder mergeRecord(com.yorha.proto.RtsBattle.RTSBattleRecord value) {
        if (recordBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              record_ != null &&
              record_ != com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance()) {
            record_ =
              com.yorha.proto.RtsBattle.RTSBattleRecord.newBuilder(record_).mergeFrom(value).buildPartial();
          } else {
            record_ = value;
          }
          onChanged();
        } else {
          recordBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       */
      public Builder clearRecord() {
        if (recordBuilder_ == null) {
          record_ = null;
          onChanged();
        } else {
          recordBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBattleRecord.Builder getRecordBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getRecordFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder getRecordOrBuilder() {
        if (recordBuilder_ != null) {
          return recordBuilder_.getMessageOrBuilder();
        } else {
          return record_ == null ?
              com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance() : record_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.RTSBattleRecord record = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBattleRecord, com.yorha.proto.RtsBattle.RTSBattleRecord.Builder, com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder> 
          getRecordFieldBuilder() {
        if (recordBuilder_ == null) {
          recordBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSBattleRecord, com.yorha.proto.RtsBattle.RTSBattleRecord.Builder, com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder>(
                  getRecord(),
                  getParentForChildren(),
                  isClean());
          record_ = null;
        }
        return recordBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RTSBattleReport)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RTSBattleReport)
    private static final com.yorha.proto.RtsBattle.RTSBattleReport DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.RtsBattle.RTSBattleReport();
    }

    public static com.yorha.proto.RtsBattle.RTSBattleReport getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RTSBattleReport>
        PARSER = new com.google.protobuf.AbstractParser<RTSBattleReport>() {
      @java.lang.Override
      public RTSBattleReport parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RTSBattleReport(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RTSBattleReport> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RTSBattleReport> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleReport getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSUnit_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSUnit_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSBuilding_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSBuilding_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSBattleInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSBattleInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSBattleInfo_ItemSkillsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSBattleInfo_ItemSkillsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSOrder_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSOrder_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSFrame_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSFrame_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSBattleRecord_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSBattleRecord_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSBattleRecord_FramesEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSBattleRecord_FramesEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RTSBattleReport_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RTSBattleReport_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$ss_proto/gen/common/rts_battle.proto\022\017" +
      "com.yorha.proto\"\327\001\n\007RTSUnit\022\n\n\002id\030\001 \001(\005\022" +
      "\016\n\006unitId\030\002 \001(\005\022\n\n\002hp\030\003 \001(\005\022\r\n\005hpMax\030\004 \001" +
      "(\005\022\025\n\rprimaryAttack\030\005 \001(\005\022\027\n\017secondaryAt" +
      "tack\030\006 \001(\005\022\016\n\006heroId\030\007 \001(\005\022\016\n\006number\030\010 \001" +
      "(\005\022\017\n\007buildId\030\t \001(\005\022\016\n\006slotId\030\n \001(\005\022\021\n\tu" +
      "nitLevel\030\013 \001(\005\022\021\n\theroLevel\030\014 \001(\005\"\263\001\n\013RT" +
      "SBuilding\022\n\n\002id\030\001 \001(\005\022\016\n\006unitId\030\002 \001(\005\022\n\n" +
      "\002hp\030\003 \001(\005\022\r\n\005hpMax\030\004 \001(\005\022\025\n\rprimaryAttac" +
      "k\030\005 \001(\005\022\027\n\017secondaryAttack\030\006 \001(\005\022\021\n\tpoin" +
      "tName\030\007 \001(\t\022\t\n\001x\030\010 \001(\005\022\t\n\001y\030\t \001(\005\022\024\n\014isU" +
      "nbeatable\030\n \001(\010\"\361\002\n\rRTSBattleInfo\022\014\n\004see" +
      "d\030\001 \001(\005\022\r\n\005mapId\030\002 \001(\005\022\'\n\005units\030\003 \003(\0132\030." +
      "com.yorha.proto.RTSUnit\022/\n\tbuildings\030\004 \003" +
      "(\0132\034.com.yorha.proto.RTSBuilding\022\016\n\006skil" +
      "ls\030\005 \003(\005\022B\n\nitemSkills\030\006 \003(\0132..com.yorha" +
      ".proto.RTSBattleInfo.ItemSkillsEntry\022,\n\n" +
      "enemyUnits\030\n \003(\0132\030.com.yorha.proto.RTSUn" +
      "it\0224\n\016enemyBuildings\030\013 \003(\0132\034.com.yorha.p" +
      "roto.RTSBuilding\0321\n\017ItemSkillsEntry\022\013\n\003k" +
      "ey\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"\237\001\n\010RTSOrder" +
      "\022\014\n\004type\030\001 \001(\005\022\025\n\rsourceActorId\030\002 \001(\005\022\025\n" +
      "\rtargetActorId\030\003 \001(\005\022\026\n\016targetCellPosX\030\004" +
      " \001(\005\022\026\n\016targetCellPosY\030\005 \001(\005\022\024\n\014targetSt" +
      "ring\030\006 \001(\t\022\021\n\textraData\030\007 \001(\005\"5\n\010RTSFram" +
      "e\022)\n\006orders\030\001 \003(\0132\031.com.yorha.proto.RTSO" +
      "rder\"\231\001\n\017RTSBattleRecord\022<\n\006frames\030\001 \003(\013" +
      "2,.com.yorha.proto.RTSBattleRecord.Frame" +
      "sEntry\032H\n\013FramesEntry\022\013\n\003key\030\001 \001(\005\022(\n\005va" +
      "lue\030\002 \001(\0132\031.com.yorha.proto.RTSFrame:\0028\001" +
      "\"\203\001\n\017RTSBattleReport\022\020\n\010reportId\030\001 \001(\005\022," +
      "\n\004info\030\002 \001(\0132\036.com.yorha.proto.RTSBattle" +
      "Info\0220\n\006record\030\003 \001(\0132 .com.yorha.proto.R" +
      "TSBattleRecordB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_RTSUnit_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_RTSUnit_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSUnit_descriptor,
        new java.lang.String[] { "Id", "UnitId", "Hp", "HpMax", "PrimaryAttack", "SecondaryAttack", "HeroId", "Number", "BuildId", "SlotId", "UnitLevel", "HeroLevel", });
    internal_static_com_yorha_proto_RTSBuilding_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_RTSBuilding_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSBuilding_descriptor,
        new java.lang.String[] { "Id", "UnitId", "Hp", "HpMax", "PrimaryAttack", "SecondaryAttack", "PointName", "X", "Y", "IsUnbeatable", });
    internal_static_com_yorha_proto_RTSBattleInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_RTSBattleInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSBattleInfo_descriptor,
        new java.lang.String[] { "Seed", "MapId", "Units", "Buildings", "Skills", "ItemSkills", "EnemyUnits", "EnemyBuildings", });
    internal_static_com_yorha_proto_RTSBattleInfo_ItemSkillsEntry_descriptor =
      internal_static_com_yorha_proto_RTSBattleInfo_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_RTSBattleInfo_ItemSkillsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSBattleInfo_ItemSkillsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_RTSOrder_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_RTSOrder_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSOrder_descriptor,
        new java.lang.String[] { "Type", "SourceActorId", "TargetActorId", "TargetCellPosX", "TargetCellPosY", "TargetString", "ExtraData", });
    internal_static_com_yorha_proto_RTSFrame_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_RTSFrame_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSFrame_descriptor,
        new java.lang.String[] { "Orders", });
    internal_static_com_yorha_proto_RTSBattleRecord_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_RTSBattleRecord_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSBattleRecord_descriptor,
        new java.lang.String[] { "Frames", });
    internal_static_com_yorha_proto_RTSBattleRecord_FramesEntry_descriptor =
      internal_static_com_yorha_proto_RTSBattleRecord_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_RTSBattleRecord_FramesEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSBattleRecord_FramesEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_RTSBattleReport_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_RTSBattleReport_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RTSBattleReport_descriptor,
        new java.lang.String[] { "ReportId", "Info", "Record", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

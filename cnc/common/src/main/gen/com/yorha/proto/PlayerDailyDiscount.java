// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_daily_discount.proto

package com.yorha.proto;

public final class PlayerDailyDiscount {
  private PlayerDailyDiscount() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_SwitchDailyDiscountHero_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SwitchDailyDiscountHero_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SwitchDailyDiscountHero_C2S}
   */
  public static final class Player_SwitchDailyDiscountHero_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SwitchDailyDiscountHero_C2S)
      Player_SwitchDailyDiscountHero_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SwitchDailyDiscountHero_C2S.newBuilder() to construct.
    private Player_SwitchDailyDiscountHero_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SwitchDailyDiscountHero_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SwitchDailyDiscountHero_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SwitchDailyDiscountHero_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.class, com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S other = (com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SwitchDailyDiscountHero_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SwitchDailyDiscountHero_C2S)
        com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.class, com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S build() {
        com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S buildPartial() {
        com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S result = new com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S) {
          return mergeFrom((com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S other) {
        if (other == com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SwitchDailyDiscountHero_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SwitchDailyDiscountHero_C2S)
    private static final com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S();
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SwitchDailyDiscountHero_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SwitchDailyDiscountHero_C2S>() {
      @java.lang.Override
      public Player_SwitchDailyDiscountHero_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SwitchDailyDiscountHero_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SwitchDailyDiscountHero_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SwitchDailyDiscountHero_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SwitchDailyDiscountHero_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SwitchDailyDiscountHero_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SwitchDailyDiscountHero_S2C}
   */
  public static final class Player_SwitchDailyDiscountHero_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SwitchDailyDiscountHero_S2C)
      Player_SwitchDailyDiscountHero_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SwitchDailyDiscountHero_S2C.newBuilder() to construct.
    private Player_SwitchDailyDiscountHero_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SwitchDailyDiscountHero_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SwitchDailyDiscountHero_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SwitchDailyDiscountHero_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.class, com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C other = (com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SwitchDailyDiscountHero_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SwitchDailyDiscountHero_S2C)
        com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.class, com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C build() {
        com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C buildPartial() {
        com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C result = new com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C) {
          return mergeFrom((com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C other) {
        if (other == com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SwitchDailyDiscountHero_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SwitchDailyDiscountHero_S2C)
    private static final com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C();
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SwitchDailyDiscountHero_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SwitchDailyDiscountHero_S2C>() {
      @java.lang.Override
      public Player_SwitchDailyDiscountHero_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SwitchDailyDiscountHero_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SwitchDailyDiscountHero_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SwitchDailyDiscountHero_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetDailyDiscountReward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetDailyDiscountReward_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetDailyDiscountReward_C2S}
   */
  public static final class Player_GetDailyDiscountReward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetDailyDiscountReward_C2S)
      Player_GetDailyDiscountReward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetDailyDiscountReward_C2S.newBuilder() to construct.
    private Player_GetDailyDiscountReward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetDailyDiscountReward_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetDailyDiscountReward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetDailyDiscountReward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.class, com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S other = (com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetDailyDiscountReward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetDailyDiscountReward_C2S)
        com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.class, com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S build() {
        com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S buildPartial() {
        com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S result = new com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S other) {
        if (other == com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetDailyDiscountReward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetDailyDiscountReward_C2S)
    private static final com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S();
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetDailyDiscountReward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetDailyDiscountReward_C2S>() {
      @java.lang.Override
      public Player_GetDailyDiscountReward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetDailyDiscountReward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetDailyDiscountReward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetDailyDiscountReward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetDailyDiscountReward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetDailyDiscountReward_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return Whether the rewardInfo field is set.
     */
    boolean hasRewardInfo();
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return The rewardInfo.
     */
    com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo();
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     */
    com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetDailyDiscountReward_S2C}
   */
  public static final class Player_GetDailyDiscountReward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetDailyDiscountReward_S2C)
      Player_GetDailyDiscountReward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetDailyDiscountReward_S2C.newBuilder() to construct.
    private Player_GetDailyDiscountReward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetDailyDiscountReward_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetDailyDiscountReward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetDailyDiscountReward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.YoAssetPackagePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = rewardInfo_.toBuilder();
              }
              rewardInfo_ = input.readMessage(com.yorha.proto.StructPB.YoAssetPackagePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(rewardInfo_);
                rewardInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.class, com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int REWARDINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.YoAssetPackagePB rewardInfo_;
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return Whether the rewardInfo field is set.
     */
    @java.lang.Override
    public boolean hasRewardInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return The rewardInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo() {
      return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
    }
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder() {
      return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getRewardInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getRewardInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C other = (com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C) obj;

      if (hasRewardInfo() != other.hasRewardInfo()) return false;
      if (hasRewardInfo()) {
        if (!getRewardInfo()
            .equals(other.getRewardInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRewardInfo()) {
        hash = (37 * hash) + REWARDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRewardInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetDailyDiscountReward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetDailyDiscountReward_S2C)
        com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.class, com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = null;
        } else {
          rewardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C build() {
        com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C buildPartial() {
        com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C result = new com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (rewardInfoBuilder_ == null) {
            result.rewardInfo_ = rewardInfo_;
          } else {
            result.rewardInfo_ = rewardInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C other) {
        if (other == com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.getDefaultInstance()) return this;
        if (other.hasRewardInfo()) {
          mergeRewardInfo(other.getRewardInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.YoAssetPackagePB rewardInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> rewardInfoBuilder_;
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       * @return Whether the rewardInfo field is set.
       */
      public boolean hasRewardInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       * @return The rewardInfo.
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo() {
        if (rewardInfoBuilder_ == null) {
          return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
        } else {
          return rewardInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder setRewardInfo(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          rewardInfo_ = value;
          onChanged();
        } else {
          rewardInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder setRewardInfo(
          com.yorha.proto.StructPB.YoAssetPackagePB.Builder builderForValue) {
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = builderForValue.build();
          onChanged();
        } else {
          rewardInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder mergeRewardInfo(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              rewardInfo_ != null &&
              rewardInfo_ != com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance()) {
            rewardInfo_ =
              com.yorha.proto.StructPB.YoAssetPackagePB.newBuilder(rewardInfo_).mergeFrom(value).buildPartial();
          } else {
            rewardInfo_ = value;
          }
          onChanged();
        } else {
          rewardInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder clearRewardInfo() {
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = null;
          onChanged();
        } else {
          rewardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB.Builder getRewardInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRewardInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder() {
        if (rewardInfoBuilder_ != null) {
          return rewardInfoBuilder_.getMessageOrBuilder();
        } else {
          return rewardInfo_ == null ?
              com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
        }
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> 
          getRewardInfoFieldBuilder() {
        if (rewardInfoBuilder_ == null) {
          rewardInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder>(
                  getRewardInfo(),
                  getParentForChildren(),
                  isClean());
          rewardInfo_ = null;
        }
        return rewardInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetDailyDiscountReward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetDailyDiscountReward_S2C)
    private static final com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C();
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetDailyDiscountReward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetDailyDiscountReward_S2C>() {
      @java.lang.Override
      public Player_GetDailyDiscountReward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetDailyDiscountReward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetDailyDiscountReward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetDailyDiscountReward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetSuperDailyDiscountReward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetSuperDailyDiscountReward_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetSuperDailyDiscountReward_C2S}
   */
  public static final class Player_GetSuperDailyDiscountReward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetSuperDailyDiscountReward_C2S)
      Player_GetSuperDailyDiscountReward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetSuperDailyDiscountReward_C2S.newBuilder() to construct.
    private Player_GetSuperDailyDiscountReward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetSuperDailyDiscountReward_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetSuperDailyDiscountReward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetSuperDailyDiscountReward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S.class, com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S other = (com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetSuperDailyDiscountReward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetSuperDailyDiscountReward_C2S)
        com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S.class, com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S build() {
        com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S buildPartial() {
        com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S result = new com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S other) {
        if (other == com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetSuperDailyDiscountReward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetSuperDailyDiscountReward_C2S)
    private static final com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S();
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetSuperDailyDiscountReward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetSuperDailyDiscountReward_C2S>() {
      @java.lang.Override
      public Player_GetSuperDailyDiscountReward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetSuperDailyDiscountReward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetSuperDailyDiscountReward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetSuperDailyDiscountReward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetSuperDailyDiscountReward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetSuperDailyDiscountReward_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return Whether the rewardInfo field is set.
     */
    boolean hasRewardInfo();
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return The rewardInfo.
     */
    com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo();
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     */
    com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetSuperDailyDiscountReward_S2C}
   */
  public static final class Player_GetSuperDailyDiscountReward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetSuperDailyDiscountReward_S2C)
      Player_GetSuperDailyDiscountReward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetSuperDailyDiscountReward_S2C.newBuilder() to construct.
    private Player_GetSuperDailyDiscountReward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetSuperDailyDiscountReward_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetSuperDailyDiscountReward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetSuperDailyDiscountReward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.YoAssetPackagePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = rewardInfo_.toBuilder();
              }
              rewardInfo_ = input.readMessage(com.yorha.proto.StructPB.YoAssetPackagePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(rewardInfo_);
                rewardInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.class, com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int REWARDINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.YoAssetPackagePB rewardInfo_;
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return Whether the rewardInfo field is set.
     */
    @java.lang.Override
    public boolean hasRewardInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return The rewardInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo() {
      return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
    }
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder() {
      return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getRewardInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getRewardInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C other = (com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C) obj;

      if (hasRewardInfo() != other.hasRewardInfo()) return false;
      if (hasRewardInfo()) {
        if (!getRewardInfo()
            .equals(other.getRewardInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRewardInfo()) {
        hash = (37 * hash) + REWARDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRewardInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetSuperDailyDiscountReward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetSuperDailyDiscountReward_S2C)
        com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.class, com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = null;
        } else {
          rewardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDailyDiscount.internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C build() {
        com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C buildPartial() {
        com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C result = new com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (rewardInfoBuilder_ == null) {
            result.rewardInfo_ = rewardInfo_;
          } else {
            result.rewardInfo_ = rewardInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C other) {
        if (other == com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.getDefaultInstance()) return this;
        if (other.hasRewardInfo()) {
          mergeRewardInfo(other.getRewardInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.YoAssetPackagePB rewardInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> rewardInfoBuilder_;
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       * @return Whether the rewardInfo field is set.
       */
      public boolean hasRewardInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       * @return The rewardInfo.
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo() {
        if (rewardInfoBuilder_ == null) {
          return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
        } else {
          return rewardInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder setRewardInfo(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          rewardInfo_ = value;
          onChanged();
        } else {
          rewardInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder setRewardInfo(
          com.yorha.proto.StructPB.YoAssetPackagePB.Builder builderForValue) {
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = builderForValue.build();
          onChanged();
        } else {
          rewardInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder mergeRewardInfo(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              rewardInfo_ != null &&
              rewardInfo_ != com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance()) {
            rewardInfo_ =
              com.yorha.proto.StructPB.YoAssetPackagePB.newBuilder(rewardInfo_).mergeFrom(value).buildPartial();
          } else {
            rewardInfo_ = value;
          }
          onChanged();
        } else {
          rewardInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder clearRewardInfo() {
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = null;
          onChanged();
        } else {
          rewardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB.Builder getRewardInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRewardInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder() {
        if (rewardInfoBuilder_ != null) {
          return rewardInfoBuilder_.getMessageOrBuilder();
        } else {
          return rewardInfo_ == null ?
              com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
        }
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> 
          getRewardInfoFieldBuilder() {
        if (rewardInfoBuilder_ == null) {
          rewardInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder>(
                  getRewardInfo(),
                  getParentForChildren(),
                  isClean());
          rewardInfo_ = null;
        }
        return rewardInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetSuperDailyDiscountReward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetSuperDailyDiscountReward_S2C)
    private static final com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C();
    }

    public static com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetSuperDailyDiscountReward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetSuperDailyDiscountReward_S2C>() {
      @java.lang.Override
      public Player_GetSuperDailyDiscountReward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetSuperDailyDiscountReward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetSuperDailyDiscountReward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetSuperDailyDiscountReward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n2ss_proto/gen/player/cs/player_daily_di" +
      "scount.proto\022\017com.yorha.proto\032\"cs_proto/" +
      "gen/common/structPB.proto\"4\n\"Player_Swit" +
      "chDailyDiscountHero_C2S\022\016\n\006heroId\030\001 \001(\005\"" +
      "$\n\"Player_SwitchDailyDiscountHero_S2C\"#\n" +
      "!Player_GetDailyDiscountReward_C2S\"Z\n!Pl" +
      "ayer_GetDailyDiscountReward_S2C\0225\n\nrewar" +
      "dInfo\030\001 \001(\0132!.com.yorha.proto.YoAssetPac" +
      "kagePB\"(\n&Player_GetSuperDailyDiscountRe" +
      "ward_C2S\"_\n&Player_GetSuperDailyDiscount" +
      "Reward_S2C\0225\n\nrewardInfo\030\001 \001(\0132!.com.yor" +
      "ha.proto.YoAssetPackagePBB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_C2S_descriptor,
        new java.lang.String[] { "HeroId", });
    internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SwitchDailyDiscountHero_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetDailyDiscountReward_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetDailyDiscountReward_S2C_descriptor,
        new java.lang.String[] { "RewardInfo", });
    internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetSuperDailyDiscountReward_S2C_descriptor,
        new java.lang.String[] { "RewardInfo", });
    com.yorha.proto.StructPB.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

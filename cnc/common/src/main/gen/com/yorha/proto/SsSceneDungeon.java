// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_dungeon.proto

package com.yorha.proto;

public final class SsSceneDungeon {
  private SsSceneDungeon() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CreateDungeonAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateDungeonAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.DungeonType getType();

    /**
     * <code>optional int32 dungeonId = 2;</code>
     * @return Whether the dungeonId field is set.
     */
    boolean hasDungeonId();
    /**
     * <code>optional int32 dungeonId = 2;</code>
     * @return The dungeonId.
     */
    int getDungeonId();

    /**
     * <pre>
     * 准入玩家列表
     * </pre>
     *
     * <code>repeated int64 allowEnterPlayer = 3;</code>
     * @return A list containing the allowEnterPlayer.
     */
    java.util.List<java.lang.Long> getAllowEnterPlayerList();
    /**
     * <pre>
     * 准入玩家列表
     * </pre>
     *
     * <code>repeated int64 allowEnterPlayer = 3;</code>
     * @return The count of allowEnterPlayer.
     */
    int getAllowEnterPlayerCount();
    /**
     * <pre>
     * 准入玩家列表
     * </pre>
     *
     * <code>repeated int64 allowEnterPlayer = 3;</code>
     * @param index The index of the element to return.
     * @return The allowEnterPlayer at the given index.
     */
    long getAllowEnterPlayer(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateDungeonAsk}
   */
  public static final class CreateDungeonAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateDungeonAsk)
      CreateDungeonAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateDungeonAsk.newBuilder() to construct.
    private CreateDungeonAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateDungeonAsk() {
      type_ = 0;
      allowEnterPlayer_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateDungeonAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateDungeonAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.DungeonType value = com.yorha.proto.CommonEnum.DungeonType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                type_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              dungeonId_ = input.readInt32();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                allowEnterPlayer_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              allowEnterPlayer_.addLong(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                allowEnterPlayer_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                allowEnterPlayer_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          allowEnterPlayer_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.CreateDungeonAsk.class, com.yorha.proto.SsSceneDungeon.CreateDungeonAsk.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.DungeonType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
    }

    public static final int DUNGEONID_FIELD_NUMBER = 2;
    private int dungeonId_;
    /**
     * <code>optional int32 dungeonId = 2;</code>
     * @return Whether the dungeonId field is set.
     */
    @java.lang.Override
    public boolean hasDungeonId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 dungeonId = 2;</code>
     * @return The dungeonId.
     */
    @java.lang.Override
    public int getDungeonId() {
      return dungeonId_;
    }

    public static final int ALLOWENTERPLAYER_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.LongList allowEnterPlayer_;
    /**
     * <pre>
     * 准入玩家列表
     * </pre>
     *
     * <code>repeated int64 allowEnterPlayer = 3;</code>
     * @return A list containing the allowEnterPlayer.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getAllowEnterPlayerList() {
      return allowEnterPlayer_;
    }
    /**
     * <pre>
     * 准入玩家列表
     * </pre>
     *
     * <code>repeated int64 allowEnterPlayer = 3;</code>
     * @return The count of allowEnterPlayer.
     */
    public int getAllowEnterPlayerCount() {
      return allowEnterPlayer_.size();
    }
    /**
     * <pre>
     * 准入玩家列表
     * </pre>
     *
     * <code>repeated int64 allowEnterPlayer = 3;</code>
     * @param index The index of the element to return.
     * @return The allowEnterPlayer at the given index.
     */
    public long getAllowEnterPlayer(int index) {
      return allowEnterPlayer_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, dungeonId_);
      }
      for (int i = 0; i < allowEnterPlayer_.size(); i++) {
        output.writeInt64(3, allowEnterPlayer_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, dungeonId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < allowEnterPlayer_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(allowEnterPlayer_.getLong(i));
        }
        size += dataSize;
        size += 1 * getAllowEnterPlayerList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.CreateDungeonAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.CreateDungeonAsk other = (com.yorha.proto.SsSceneDungeon.CreateDungeonAsk) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasDungeonId() != other.hasDungeonId()) return false;
      if (hasDungeonId()) {
        if (getDungeonId()
            != other.getDungeonId()) return false;
      }
      if (!getAllowEnterPlayerList()
          .equals(other.getAllowEnterPlayerList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasDungeonId()) {
        hash = (37 * hash) + DUNGEONID_FIELD_NUMBER;
        hash = (53 * hash) + getDungeonId();
      }
      if (getAllowEnterPlayerCount() > 0) {
        hash = (37 * hash) + ALLOWENTERPLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getAllowEnterPlayerList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.CreateDungeonAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateDungeonAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateDungeonAsk)
        com.yorha.proto.SsSceneDungeon.CreateDungeonAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.CreateDungeonAsk.class, com.yorha.proto.SsSceneDungeon.CreateDungeonAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.CreateDungeonAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        dungeonId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        allowEnterPlayer_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.CreateDungeonAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.CreateDungeonAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.CreateDungeonAsk build() {
        com.yorha.proto.SsSceneDungeon.CreateDungeonAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.CreateDungeonAsk buildPartial() {
        com.yorha.proto.SsSceneDungeon.CreateDungeonAsk result = new com.yorha.proto.SsSceneDungeon.CreateDungeonAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.dungeonId_ = dungeonId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          allowEnterPlayer_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.allowEnterPlayer_ = allowEnterPlayer_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.CreateDungeonAsk) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.CreateDungeonAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.CreateDungeonAsk other) {
        if (other == com.yorha.proto.SsSceneDungeon.CreateDungeonAsk.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasDungeonId()) {
          setDungeonId(other.getDungeonId());
        }
        if (!other.allowEnterPlayer_.isEmpty()) {
          if (allowEnterPlayer_.isEmpty()) {
            allowEnterPlayer_ = other.allowEnterPlayer_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureAllowEnterPlayerIsMutable();
            allowEnterPlayer_.addAll(other.allowEnterPlayer_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.CreateDungeonAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.CreateDungeonAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.DungeonType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.DungeonType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int dungeonId_ ;
      /**
       * <code>optional int32 dungeonId = 2;</code>
       * @return Whether the dungeonId field is set.
       */
      @java.lang.Override
      public boolean hasDungeonId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 dungeonId = 2;</code>
       * @return The dungeonId.
       */
      @java.lang.Override
      public int getDungeonId() {
        return dungeonId_;
      }
      /**
       * <code>optional int32 dungeonId = 2;</code>
       * @param value The dungeonId to set.
       * @return This builder for chaining.
       */
      public Builder setDungeonId(int value) {
        bitField0_ |= 0x00000002;
        dungeonId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 dungeonId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDungeonId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        dungeonId_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList allowEnterPlayer_ = emptyLongList();
      private void ensureAllowEnterPlayerIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          allowEnterPlayer_ = mutableCopy(allowEnterPlayer_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <pre>
       * 准入玩家列表
       * </pre>
       *
       * <code>repeated int64 allowEnterPlayer = 3;</code>
       * @return A list containing the allowEnterPlayer.
       */
      public java.util.List<java.lang.Long>
          getAllowEnterPlayerList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(allowEnterPlayer_) : allowEnterPlayer_;
      }
      /**
       * <pre>
       * 准入玩家列表
       * </pre>
       *
       * <code>repeated int64 allowEnterPlayer = 3;</code>
       * @return The count of allowEnterPlayer.
       */
      public int getAllowEnterPlayerCount() {
        return allowEnterPlayer_.size();
      }
      /**
       * <pre>
       * 准入玩家列表
       * </pre>
       *
       * <code>repeated int64 allowEnterPlayer = 3;</code>
       * @param index The index of the element to return.
       * @return The allowEnterPlayer at the given index.
       */
      public long getAllowEnterPlayer(int index) {
        return allowEnterPlayer_.getLong(index);
      }
      /**
       * <pre>
       * 准入玩家列表
       * </pre>
       *
       * <code>repeated int64 allowEnterPlayer = 3;</code>
       * @param index The index to set the value at.
       * @param value The allowEnterPlayer to set.
       * @return This builder for chaining.
       */
      public Builder setAllowEnterPlayer(
          int index, long value) {
        ensureAllowEnterPlayerIsMutable();
        allowEnterPlayer_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 准入玩家列表
       * </pre>
       *
       * <code>repeated int64 allowEnterPlayer = 3;</code>
       * @param value The allowEnterPlayer to add.
       * @return This builder for chaining.
       */
      public Builder addAllowEnterPlayer(long value) {
        ensureAllowEnterPlayerIsMutable();
        allowEnterPlayer_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 准入玩家列表
       * </pre>
       *
       * <code>repeated int64 allowEnterPlayer = 3;</code>
       * @param values The allowEnterPlayer to add.
       * @return This builder for chaining.
       */
      public Builder addAllAllowEnterPlayer(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureAllowEnterPlayerIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, allowEnterPlayer_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 准入玩家列表
       * </pre>
       *
       * <code>repeated int64 allowEnterPlayer = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAllowEnterPlayer() {
        allowEnterPlayer_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateDungeonAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateDungeonAsk)
    private static final com.yorha.proto.SsSceneDungeon.CreateDungeonAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.CreateDungeonAsk();
    }

    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateDungeonAsk>
        PARSER = new com.google.protobuf.AbstractParser<CreateDungeonAsk>() {
      @java.lang.Override
      public CreateDungeonAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateDungeonAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateDungeonAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateDungeonAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.CreateDungeonAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DungeonPlayerDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DungeonPlayerData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * dungeonPlayer部分数据拷贝载体
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <pre>
     * dungeonPlayer部分数据拷贝载体
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
     * @return The info.
     */
    com.yorha.proto.Player.DungeonPlayer getInfo();
    /**
     * <pre>
     * dungeonPlayer部分数据拷贝载体
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
     */
    com.yorha.proto.Player.DungeonPlayerOrBuilder getInfoOrBuilder();

    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */
    int getArmyCount();
    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */
    boolean containsArmy(
        int key);
    /**
     * Use {@link #getArmyMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>
    getArmy();
    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>
    getArmyMap();
    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */

    com.yorha.proto.StructPlayer.Troop getArmyOrDefault(
        int key,
        com.yorha.proto.StructPlayer.Troop defaultValue);
    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */

    com.yorha.proto.StructPlayer.Troop getArmyOrThrow(
        int key);

    /**
     * <pre>
     * 主城等级
     * </pre>
     *
     * <code>optional int32 mainCityLevel = 3;</code>
     * @return Whether the mainCityLevel field is set.
     */
    boolean hasMainCityLevel();
    /**
     * <pre>
     * 主城等级
     * </pre>
     *
     * <code>optional int32 mainCityLevel = 3;</code>
     * @return The mainCityLevel.
     */
    int getMainCityLevel();
  }
  /**
   * <pre>
   * 带入副本的玩家数据
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.DungeonPlayerData}
   */
  public static final class DungeonPlayerData extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DungeonPlayerData)
      DungeonPlayerDataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DungeonPlayerData.newBuilder() to construct.
    private DungeonPlayerData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DungeonPlayerData() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DungeonPlayerData();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DungeonPlayerData(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Player.DungeonPlayer.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = info_.toBuilder();
              }
              info_ = input.readMessage(com.yorha.proto.Player.DungeonPlayer.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(info_);
                info_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                army_ = com.google.protobuf.MapField.newMapField(
                    ArmyDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>
              army__ = input.readMessage(
                  ArmyDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              army_.getMutableMap().put(
                  army__.getKey(), army__.getValue());
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              mainCityLevel_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_DungeonPlayerData_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetArmy();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_DungeonPlayerData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.DungeonPlayerData.class, com.yorha.proto.SsSceneDungeon.DungeonPlayerData.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 1;
    private com.yorha.proto.Player.DungeonPlayer info_;
    /**
     * <pre>
     * dungeonPlayer部分数据拷贝载体
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * dungeonPlayer部分数据拷贝载体
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
     * @return The info.
     */
    @java.lang.Override
    public com.yorha.proto.Player.DungeonPlayer getInfo() {
      return info_ == null ? com.yorha.proto.Player.DungeonPlayer.getDefaultInstance() : info_;
    }
    /**
     * <pre>
     * dungeonPlayer部分数据拷贝载体
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Player.DungeonPlayerOrBuilder getInfoOrBuilder() {
      return info_ == null ? com.yorha.proto.Player.DungeonPlayer.getDefaultInstance() : info_;
    }

    public static final int ARMY_FIELD_NUMBER = 2;
    private static final class ArmyDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructPlayer.Troop> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>newDefaultInstance(
                  com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_DungeonPlayerData_ArmyEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructPlayer.Troop.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructPlayer.Troop> army_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>
    internalGetArmy() {
      if (army_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ArmyDefaultEntryHolder.defaultEntry);
      }
      return army_;
    }

    public int getArmyCount() {
      return internalGetArmy().getMap().size();
    }
    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */

    @java.lang.Override
    public boolean containsArmy(
        int key) {
      
      return internalGetArmy().getMap().containsKey(key);
    }
    /**
     * Use {@link #getArmyMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> getArmy() {
      return getArmyMap();
    }
    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> getArmyMap() {
      return internalGetArmy().getMap();
    }
    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPlayer.Troop getArmyOrDefault(
        int key,
        com.yorha.proto.StructPlayer.Troop defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> map =
          internalGetArmy().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 带入的部队数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPlayer.Troop getArmyOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> map =
          internalGetArmy().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int MAINCITYLEVEL_FIELD_NUMBER = 3;
    private int mainCityLevel_;
    /**
     * <pre>
     * 主城等级
     * </pre>
     *
     * <code>optional int32 mainCityLevel = 3;</code>
     * @return Whether the mainCityLevel field is set.
     */
    @java.lang.Override
    public boolean hasMainCityLevel() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 主城等级
     * </pre>
     *
     * <code>optional int32 mainCityLevel = 3;</code>
     * @return The mainCityLevel.
     */
    @java.lang.Override
    public int getMainCityLevel() {
      return mainCityLevel_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getInfo());
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetArmy(),
          ArmyDefaultEntryHolder.defaultEntry,
          2);
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, mainCityLevel_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getInfo());
      }
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> entry
           : internalGetArmy().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>
        army__ = ArmyDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, army__);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, mainCityLevel_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.DungeonPlayerData)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.DungeonPlayerData other = (com.yorha.proto.SsSceneDungeon.DungeonPlayerData) obj;

      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (!internalGetArmy().equals(
          other.internalGetArmy())) return false;
      if (hasMainCityLevel() != other.hasMainCityLevel()) return false;
      if (hasMainCityLevel()) {
        if (getMainCityLevel()
            != other.getMainCityLevel()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      if (!internalGetArmy().getMap().isEmpty()) {
        hash = (37 * hash) + ARMY_FIELD_NUMBER;
        hash = (53 * hash) + internalGetArmy().hashCode();
      }
      if (hasMainCityLevel()) {
        hash = (37 * hash) + MAINCITYLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getMainCityLevel();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.DungeonPlayerData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 带入副本的玩家数据
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.DungeonPlayerData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DungeonPlayerData)
        com.yorha.proto.SsSceneDungeon.DungeonPlayerDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_DungeonPlayerData_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetArmy();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableArmy();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_DungeonPlayerData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.DungeonPlayerData.class, com.yorha.proto.SsSceneDungeon.DungeonPlayerData.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.DungeonPlayerData.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = null;
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        internalGetMutableArmy().clear();
        mainCityLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_DungeonPlayerData_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.DungeonPlayerData getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.DungeonPlayerData.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.DungeonPlayerData build() {
        com.yorha.proto.SsSceneDungeon.DungeonPlayerData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.DungeonPlayerData buildPartial() {
        com.yorha.proto.SsSceneDungeon.DungeonPlayerData result = new com.yorha.proto.SsSceneDungeon.DungeonPlayerData(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (infoBuilder_ == null) {
            result.info_ = info_;
          } else {
            result.info_ = infoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.army_ = internalGetArmy();
        result.army_.makeImmutable();
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.mainCityLevel_ = mainCityLevel_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.DungeonPlayerData) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.DungeonPlayerData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.DungeonPlayerData other) {
        if (other == com.yorha.proto.SsSceneDungeon.DungeonPlayerData.getDefaultInstance()) return this;
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        internalGetMutableArmy().mergeFrom(
            other.internalGetArmy());
        if (other.hasMainCityLevel()) {
          setMainCityLevel(other.getMainCityLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.DungeonPlayerData parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.DungeonPlayerData) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Player.DungeonPlayer info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Player.DungeonPlayer, com.yorha.proto.Player.DungeonPlayer.Builder, com.yorha.proto.Player.DungeonPlayerOrBuilder> infoBuilder_;
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       * @return The info.
       */
      public com.yorha.proto.Player.DungeonPlayer getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? com.yorha.proto.Player.DungeonPlayer.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       */
      public Builder setInfo(com.yorha.proto.Player.DungeonPlayer value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
          onChanged();
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       */
      public Builder setInfo(
          com.yorha.proto.Player.DungeonPlayer.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
          onChanged();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       */
      public Builder mergeInfo(com.yorha.proto.Player.DungeonPlayer value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              info_ != null &&
              info_ != com.yorha.proto.Player.DungeonPlayer.getDefaultInstance()) {
            info_ =
              com.yorha.proto.Player.DungeonPlayer.newBuilder(info_).mergeFrom(value).buildPartial();
          } else {
            info_ = value;
          }
          onChanged();
        } else {
          infoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = null;
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       */
      public com.yorha.proto.Player.DungeonPlayer.Builder getInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       */
      public com.yorha.proto.Player.DungeonPlayerOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              com.yorha.proto.Player.DungeonPlayer.getDefaultInstance() : info_;
        }
      }
      /**
       * <pre>
       * dungeonPlayer部分数据拷贝载体
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonPlayer info = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Player.DungeonPlayer, com.yorha.proto.Player.DungeonPlayer.Builder, com.yorha.proto.Player.DungeonPlayerOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Player.DungeonPlayer, com.yorha.proto.Player.DungeonPlayer.Builder, com.yorha.proto.Player.DungeonPlayerOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructPlayer.Troop> army_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>
      internalGetArmy() {
        if (army_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ArmyDefaultEntryHolder.defaultEntry);
        }
        return army_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>
      internalGetMutableArmy() {
        onChanged();;
        if (army_ == null) {
          army_ = com.google.protobuf.MapField.newMapField(
              ArmyDefaultEntryHolder.defaultEntry);
        }
        if (!army_.isMutable()) {
          army_ = army_.copy();
        }
        return army_;
      }

      public int getArmyCount() {
        return internalGetArmy().getMap().size();
      }
      /**
       * <pre>
       * 带入的部队数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
       */

      @java.lang.Override
      public boolean containsArmy(
          int key) {
        
        return internalGetArmy().getMap().containsKey(key);
      }
      /**
       * Use {@link #getArmyMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> getArmy() {
        return getArmyMap();
      }
      /**
       * <pre>
       * 带入的部队数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> getArmyMap() {
        return internalGetArmy().getMap();
      }
      /**
       * <pre>
       * 带入的部队数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPlayer.Troop getArmyOrDefault(
          int key,
          com.yorha.proto.StructPlayer.Troop defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> map =
            internalGetArmy().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 带入的部队数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPlayer.Troop getArmyOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> map =
            internalGetArmy().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearArmy() {
        internalGetMutableArmy().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 带入的部队数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
       */

      public Builder removeArmy(
          int key) {
        
        internalGetMutableArmy().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop>
      getMutableArmy() {
        return internalGetMutableArmy().getMutableMap();
      }
      /**
       * <pre>
       * 带入的部队数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
       */
      public Builder putArmy(
          int key,
          com.yorha.proto.StructPlayer.Troop value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableArmy().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 带入的部队数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.Troop&gt; army = 2;</code>
       */

      public Builder putAllArmy(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructPlayer.Troop> values) {
        internalGetMutableArmy().getMutableMap()
            .putAll(values);
        return this;
      }

      private int mainCityLevel_ ;
      /**
       * <pre>
       * 主城等级
       * </pre>
       *
       * <code>optional int32 mainCityLevel = 3;</code>
       * @return Whether the mainCityLevel field is set.
       */
      @java.lang.Override
      public boolean hasMainCityLevel() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 主城等级
       * </pre>
       *
       * <code>optional int32 mainCityLevel = 3;</code>
       * @return The mainCityLevel.
       */
      @java.lang.Override
      public int getMainCityLevel() {
        return mainCityLevel_;
      }
      /**
       * <pre>
       * 主城等级
       * </pre>
       *
       * <code>optional int32 mainCityLevel = 3;</code>
       * @param value The mainCityLevel to set.
       * @return This builder for chaining.
       */
      public Builder setMainCityLevel(int value) {
        bitField0_ |= 0x00000004;
        mainCityLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 主城等级
       * </pre>
       *
       * <code>optional int32 mainCityLevel = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMainCityLevel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        mainCityLevel_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DungeonPlayerData)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DungeonPlayerData)
    private static final com.yorha.proto.SsSceneDungeon.DungeonPlayerData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.DungeonPlayerData();
    }

    public static com.yorha.proto.SsSceneDungeon.DungeonPlayerData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DungeonPlayerData>
        PARSER = new com.google.protobuf.AbstractParser<DungeonPlayerData>() {
      @java.lang.Override
      public DungeonPlayerData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DungeonPlayerData(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DungeonPlayerData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DungeonPlayerData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.DungeonPlayerData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreateDungeonAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateDungeonAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateDungeonAns}
   */
  public static final class CreateDungeonAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateDungeonAns)
      CreateDungeonAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateDungeonAns.newBuilder() to construct.
    private CreateDungeonAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateDungeonAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateDungeonAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateDungeonAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.CreateDungeonAns.class, com.yorha.proto.SsSceneDungeon.CreateDungeonAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.CreateDungeonAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.CreateDungeonAns other = (com.yorha.proto.SsSceneDungeon.CreateDungeonAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.CreateDungeonAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateDungeonAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateDungeonAns)
        com.yorha.proto.SsSceneDungeon.CreateDungeonAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.CreateDungeonAns.class, com.yorha.proto.SsSceneDungeon.CreateDungeonAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.CreateDungeonAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_CreateDungeonAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.CreateDungeonAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.CreateDungeonAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.CreateDungeonAns build() {
        com.yorha.proto.SsSceneDungeon.CreateDungeonAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.CreateDungeonAns buildPartial() {
        com.yorha.proto.SsSceneDungeon.CreateDungeonAns result = new com.yorha.proto.SsSceneDungeon.CreateDungeonAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.CreateDungeonAns) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.CreateDungeonAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.CreateDungeonAns other) {
        if (other == com.yorha.proto.SsSceneDungeon.CreateDungeonAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.CreateDungeonAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.CreateDungeonAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateDungeonAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateDungeonAns)
    private static final com.yorha.proto.SsSceneDungeon.CreateDungeonAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.CreateDungeonAns();
    }

    public static com.yorha.proto.SsSceneDungeon.CreateDungeonAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateDungeonAns>
        PARSER = new com.google.protobuf.AbstractParser<CreateDungeonAns>() {
      @java.lang.Override
      public CreateDungeonAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateDungeonAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateDungeonAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateDungeonAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.CreateDungeonAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface EnterDungeonAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.EnterDungeonAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     * @return Whether the sessionRef field is set.
     */
    boolean hasSessionRef();
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     * @return The sessionRef.
     */
    com.yorha.proto.CommonMsg.ActorRefData getSessionRef();
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     */
    com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder();

    /**
     * <code>optional .com.yorha.proto.DungeonType type = 3;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 3;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.DungeonType getType();

    /**
     * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
     * @return Whether the data field is set.
     */
    boolean hasData();
    /**
     * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
     * @return The data.
     */
    com.yorha.proto.SsSceneDungeon.DungeonPlayerData getData();
    /**
     * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
     */
    com.yorha.proto.SsSceneDungeon.DungeonPlayerDataOrBuilder getDataOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.EnterDungeonAsk}
   */
  public static final class EnterDungeonAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.EnterDungeonAsk)
      EnterDungeonAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EnterDungeonAsk.newBuilder() to construct.
    private EnterDungeonAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EnterDungeonAsk() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EnterDungeonAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private EnterDungeonAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.ActorRefData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = sessionRef_.toBuilder();
              }
              sessionRef_ = input.readMessage(com.yorha.proto.CommonMsg.ActorRefData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sessionRef_);
                sessionRef_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.DungeonType value = com.yorha.proto.CommonEnum.DungeonType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(3, rawValue);
              } else {
                bitField0_ |= 0x00000004;
                type_ = rawValue;
              }
              break;
            }
            case 34: {
              com.yorha.proto.SsSceneDungeon.DungeonPlayerData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = data_.toBuilder();
              }
              data_ = input.readMessage(com.yorha.proto.SsSceneDungeon.DungeonPlayerData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(data_);
                data_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.EnterDungeonAsk.class, com.yorha.proto.SsSceneDungeon.EnterDungeonAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int SESSIONREF_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.ActorRefData sessionRef_;
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     * @return Whether the sessionRef field is set.
     */
    @java.lang.Override
    public boolean hasSessionRef() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     * @return The sessionRef.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefData getSessionRef() {
      return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
    }
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder() {
      return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
    }

    public static final int TYPE_FIELD_NUMBER = 3;
    private int type_;
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 3;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 3;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.DungeonType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
    }

    public static final int DATA_FIELD_NUMBER = 4;
    private com.yorha.proto.SsSceneDungeon.DungeonPlayerData data_;
    /**
     * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
     * @return Whether the data field is set.
     */
    @java.lang.Override
    public boolean hasData() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
     * @return The data.
     */
    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.DungeonPlayerData getData() {
      return data_ == null ? com.yorha.proto.SsSceneDungeon.DungeonPlayerData.getDefaultInstance() : data_;
    }
    /**
     * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.DungeonPlayerDataOrBuilder getDataOrBuilder() {
      return data_ == null ? com.yorha.proto.SsSceneDungeon.DungeonPlayerData.getDefaultInstance() : data_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getSessionRef());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeEnum(3, type_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSessionRef());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, type_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.EnterDungeonAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.EnterDungeonAsk other = (com.yorha.proto.SsSceneDungeon.EnterDungeonAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasSessionRef() != other.hasSessionRef()) return false;
      if (hasSessionRef()) {
        if (!getSessionRef()
            .equals(other.getSessionRef())) return false;
      }
      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasData() != other.hasData()) return false;
      if (hasData()) {
        if (!getData()
            .equals(other.getData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasSessionRef()) {
        hash = (37 * hash) + SESSIONREF_FIELD_NUMBER;
        hash = (53 * hash) + getSessionRef().hashCode();
      }
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasData()) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.EnterDungeonAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.EnterDungeonAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.EnterDungeonAsk)
        com.yorha.proto.SsSceneDungeon.EnterDungeonAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.EnterDungeonAsk.class, com.yorha.proto.SsSceneDungeon.EnterDungeonAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.EnterDungeonAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSessionRefFieldBuilder();
          getDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (sessionRefBuilder_ == null) {
          sessionRef_ = null;
        } else {
          sessionRefBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (dataBuilder_ == null) {
          data_ = null;
        } else {
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.EnterDungeonAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.EnterDungeonAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.EnterDungeonAsk build() {
        com.yorha.proto.SsSceneDungeon.EnterDungeonAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.EnterDungeonAsk buildPartial() {
        com.yorha.proto.SsSceneDungeon.EnterDungeonAsk result = new com.yorha.proto.SsSceneDungeon.EnterDungeonAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (sessionRefBuilder_ == null) {
            result.sessionRef_ = sessionRef_;
          } else {
            result.sessionRef_ = sessionRefBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (dataBuilder_ == null) {
            result.data_ = data_;
          } else {
            result.data_ = dataBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.EnterDungeonAsk) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.EnterDungeonAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.EnterDungeonAsk other) {
        if (other == com.yorha.proto.SsSceneDungeon.EnterDungeonAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasSessionRef()) {
          mergeSessionRef(other.getSessionRef());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasData()) {
          mergeData(other.getData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.EnterDungeonAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.EnterDungeonAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.ActorRefData sessionRef_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> sessionRefBuilder_;
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       * @return Whether the sessionRef field is set.
       */
      public boolean hasSessionRef() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       * @return The sessionRef.
       */
      public com.yorha.proto.CommonMsg.ActorRefData getSessionRef() {
        if (sessionRefBuilder_ == null) {
          return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
        } else {
          return sessionRefBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public Builder setSessionRef(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (sessionRefBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sessionRef_ = value;
          onChanged();
        } else {
          sessionRefBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public Builder setSessionRef(
          com.yorha.proto.CommonMsg.ActorRefData.Builder builderForValue) {
        if (sessionRefBuilder_ == null) {
          sessionRef_ = builderForValue.build();
          onChanged();
        } else {
          sessionRefBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public Builder mergeSessionRef(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (sessionRefBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              sessionRef_ != null &&
              sessionRef_ != com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance()) {
            sessionRef_ =
              com.yorha.proto.CommonMsg.ActorRefData.newBuilder(sessionRef_).mergeFrom(value).buildPartial();
          } else {
            sessionRef_ = value;
          }
          onChanged();
        } else {
          sessionRefBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public Builder clearSessionRef() {
        if (sessionRefBuilder_ == null) {
          sessionRef_ = null;
          onChanged();
        } else {
          sessionRefBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData.Builder getSessionRefBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSessionRefFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder() {
        if (sessionRefBuilder_ != null) {
          return sessionRefBuilder_.getMessageOrBuilder();
        } else {
          return sessionRef_ == null ?
              com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
          getSessionRefFieldBuilder() {
        if (sessionRefBuilder_ == null) {
          sessionRefBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder>(
                  getSessionRef(),
                  getParentForChildren(),
                  isClean());
          sessionRef_ = null;
        }
        return sessionRefBuilder_;
      }

      private int type_ = 0;
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 3;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 3;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.DungeonType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 3;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.DungeonType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        type_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.SsSceneDungeon.DungeonPlayerData data_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsSceneDungeon.DungeonPlayerData, com.yorha.proto.SsSceneDungeon.DungeonPlayerData.Builder, com.yorha.proto.SsSceneDungeon.DungeonPlayerDataOrBuilder> dataBuilder_;
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       * @return Whether the data field is set.
       */
      public boolean hasData() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       * @return The data.
       */
      public com.yorha.proto.SsSceneDungeon.DungeonPlayerData getData() {
        if (dataBuilder_ == null) {
          return data_ == null ? com.yorha.proto.SsSceneDungeon.DungeonPlayerData.getDefaultInstance() : data_;
        } else {
          return dataBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       */
      public Builder setData(com.yorha.proto.SsSceneDungeon.DungeonPlayerData value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          data_ = value;
          onChanged();
        } else {
          dataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       */
      public Builder setData(
          com.yorha.proto.SsSceneDungeon.DungeonPlayerData.Builder builderForValue) {
        if (dataBuilder_ == null) {
          data_ = builderForValue.build();
          onChanged();
        } else {
          dataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       */
      public Builder mergeData(com.yorha.proto.SsSceneDungeon.DungeonPlayerData value) {
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              data_ != null &&
              data_ != com.yorha.proto.SsSceneDungeon.DungeonPlayerData.getDefaultInstance()) {
            data_ =
              com.yorha.proto.SsSceneDungeon.DungeonPlayerData.newBuilder(data_).mergeFrom(value).buildPartial();
          } else {
            data_ = value;
          }
          onChanged();
        } else {
          dataBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = null;
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       */
      public com.yorha.proto.SsSceneDungeon.DungeonPlayerData.Builder getDataBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getDataFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       */
      public com.yorha.proto.SsSceneDungeon.DungeonPlayerDataOrBuilder getDataOrBuilder() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilder();
        } else {
          return data_ == null ?
              com.yorha.proto.SsSceneDungeon.DungeonPlayerData.getDefaultInstance() : data_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.DungeonPlayerData data = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsSceneDungeon.DungeonPlayerData, com.yorha.proto.SsSceneDungeon.DungeonPlayerData.Builder, com.yorha.proto.SsSceneDungeon.DungeonPlayerDataOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsSceneDungeon.DungeonPlayerData, com.yorha.proto.SsSceneDungeon.DungeonPlayerData.Builder, com.yorha.proto.SsSceneDungeon.DungeonPlayerDataOrBuilder>(
                  getData(),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.EnterDungeonAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.EnterDungeonAsk)
    private static final com.yorha.proto.SsSceneDungeon.EnterDungeonAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.EnterDungeonAsk();
    }

    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<EnterDungeonAsk>
        PARSER = new com.google.protobuf.AbstractParser<EnterDungeonAsk>() {
      @java.lang.Override
      public EnterDungeonAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EnterDungeonAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<EnterDungeonAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EnterDungeonAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.EnterDungeonAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface EnterDungeonAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.EnterDungeonAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.EnterDungeonAns}
   */
  public static final class EnterDungeonAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.EnterDungeonAns)
      EnterDungeonAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EnterDungeonAns.newBuilder() to construct.
    private EnterDungeonAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EnterDungeonAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EnterDungeonAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private EnterDungeonAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.EnterDungeonAns.class, com.yorha.proto.SsSceneDungeon.EnterDungeonAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.EnterDungeonAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.EnterDungeonAns other = (com.yorha.proto.SsSceneDungeon.EnterDungeonAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.EnterDungeonAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.EnterDungeonAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.EnterDungeonAns)
        com.yorha.proto.SsSceneDungeon.EnterDungeonAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.EnterDungeonAns.class, com.yorha.proto.SsSceneDungeon.EnterDungeonAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.EnterDungeonAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_EnterDungeonAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.EnterDungeonAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.EnterDungeonAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.EnterDungeonAns build() {
        com.yorha.proto.SsSceneDungeon.EnterDungeonAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.EnterDungeonAns buildPartial() {
        com.yorha.proto.SsSceneDungeon.EnterDungeonAns result = new com.yorha.proto.SsSceneDungeon.EnterDungeonAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.EnterDungeonAns) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.EnterDungeonAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.EnterDungeonAns other) {
        if (other == com.yorha.proto.SsSceneDungeon.EnterDungeonAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.EnterDungeonAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.EnterDungeonAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.EnterDungeonAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.EnterDungeonAns)
    private static final com.yorha.proto.SsSceneDungeon.EnterDungeonAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.EnterDungeonAns();
    }

    public static com.yorha.proto.SsSceneDungeon.EnterDungeonAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<EnterDungeonAns>
        PARSER = new com.google.protobuf.AbstractParser<EnterDungeonAns>() {
      @java.lang.Override
      public EnterDungeonAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EnterDungeonAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<EnterDungeonAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EnterDungeonAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.EnterDungeonAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LeaveDungeonAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.LeaveDungeonAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.LeaveDungeonAsk}
   */
  public static final class LeaveDungeonAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.LeaveDungeonAsk)
      LeaveDungeonAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LeaveDungeonAsk.newBuilder() to construct.
    private LeaveDungeonAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LeaveDungeonAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LeaveDungeonAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private LeaveDungeonAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk.class, com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk other = (com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.LeaveDungeonAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.LeaveDungeonAsk)
        com.yorha.proto.SsSceneDungeon.LeaveDungeonAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk.class, com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk build() {
        com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk buildPartial() {
        com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk result = new com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk other) {
        if (other == com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.LeaveDungeonAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.LeaveDungeonAsk)
    private static final com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk();
    }

    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<LeaveDungeonAsk>
        PARSER = new com.google.protobuf.AbstractParser<LeaveDungeonAsk>() {
      @java.lang.Override
      public LeaveDungeonAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LeaveDungeonAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<LeaveDungeonAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LeaveDungeonAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.LeaveDungeonAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LeaveDungeonAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.LeaveDungeonAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.LeaveDungeonAns}
   */
  public static final class LeaveDungeonAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.LeaveDungeonAns)
      LeaveDungeonAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LeaveDungeonAns.newBuilder() to construct.
    private LeaveDungeonAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LeaveDungeonAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LeaveDungeonAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private LeaveDungeonAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.LeaveDungeonAns.class, com.yorha.proto.SsSceneDungeon.LeaveDungeonAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.LeaveDungeonAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.LeaveDungeonAns other = (com.yorha.proto.SsSceneDungeon.LeaveDungeonAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.LeaveDungeonAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.LeaveDungeonAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.LeaveDungeonAns)
        com.yorha.proto.SsSceneDungeon.LeaveDungeonAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.LeaveDungeonAns.class, com.yorha.proto.SsSceneDungeon.LeaveDungeonAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.LeaveDungeonAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_LeaveDungeonAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.LeaveDungeonAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.LeaveDungeonAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.LeaveDungeonAns build() {
        com.yorha.proto.SsSceneDungeon.LeaveDungeonAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.LeaveDungeonAns buildPartial() {
        com.yorha.proto.SsSceneDungeon.LeaveDungeonAns result = new com.yorha.proto.SsSceneDungeon.LeaveDungeonAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.LeaveDungeonAns) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.LeaveDungeonAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.LeaveDungeonAns other) {
        if (other == com.yorha.proto.SsSceneDungeon.LeaveDungeonAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.LeaveDungeonAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.LeaveDungeonAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.LeaveDungeonAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.LeaveDungeonAns)
    private static final com.yorha.proto.SsSceneDungeon.LeaveDungeonAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.LeaveDungeonAns();
    }

    public static com.yorha.proto.SsSceneDungeon.LeaveDungeonAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<LeaveDungeonAns>
        PARSER = new com.google.protobuf.AbstractParser<LeaveDungeonAns>() {
      @java.lang.Override
      public LeaveDungeonAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LeaveDungeonAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<LeaveDungeonAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LeaveDungeonAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.LeaveDungeonAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlayerLoginAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlayerLoginAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     * @return Whether the sessionRef field is set.
     */
    boolean hasSessionRef();
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     * @return The sessionRef.
     */
    com.yorha.proto.CommonMsg.ActorRefData getSessionRef();
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     */
    com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder();

    /**
     * <code>optional bool isNewbie = 3;</code>
     * @return Whether the isNewbie field is set.
     */
    boolean hasIsNewbie();
    /**
     * <code>optional bool isNewbie = 3;</code>
     * @return The isNewbie.
     */
    boolean getIsNewbie();

    /**
     * <pre>
     * 玩家上次登出时间
     * </pre>
     *
     * <code>optional int64 lastLoginOutTsMs = 7;</code>
     * @return Whether the lastLoginOutTsMs field is set.
     */
    boolean hasLastLoginOutTsMs();
    /**
     * <pre>
     * 玩家上次登出时间
     * </pre>
     *
     * <code>optional int64 lastLoginOutTsMs = 7;</code>
     * @return The lastLoginOutTsMs.
     */
    long getLastLoginOutTsMs();

    /**
     * <pre>
     * 玩家内城信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
     * @return Whether the buildInfo field is set.
     */
    boolean hasBuildInfo();
    /**
     * <pre>
     * 玩家内城信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
     * @return The buildInfo.
     */
    com.yorha.proto.CommonMsg.SceneInnerBuildInfo getBuildInfo();
    /**
     * <pre>
     * 玩家内城信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
     */
    com.yorha.proto.CommonMsg.SceneInnerBuildInfoOrBuilder getBuildInfoOrBuilder();

    /**
     * <pre>
     * 玩家openId（bigScene专属）
     * </pre>
     *
     * <code>optional string openId = 9;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <pre>
     * 玩家openId（bigScene专属）
     * </pre>
     *
     * <code>optional string openId = 9;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <pre>
     * 玩家openId（bigScene专属）
     * </pre>
     *
     * <code>optional string openId = 9;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <pre>
     * Intl推送token
     * </pre>
     *
     * <code>optional string intlNtfToken = 10;</code>
     * @return Whether the intlNtfToken field is set.
     */
    boolean hasIntlNtfToken();
    /**
     * <pre>
     * Intl推送token
     * </pre>
     *
     * <code>optional string intlNtfToken = 10;</code>
     * @return The intlNtfToken.
     */
    java.lang.String getIntlNtfToken();
    /**
     * <pre>
     * Intl推送token
     * </pre>
     *
     * <code>optional string intlNtfToken = 10;</code>
     * @return The bytes for intlNtfToken.
     */
    com.google.protobuf.ByteString
        getIntlNtfTokenBytes();

    /**
     * <pre>
     * 玩家当前语言
     * </pre>
     *
     * <code>optional .com.yorha.proto.Language language = 11;</code>
     * @return Whether the language field is set.
     */
    boolean hasLanguage();
    /**
     * <pre>
     * 玩家当前语言
     * </pre>
     *
     * <code>optional .com.yorha.proto.Language language = 11;</code>
     * @return The language.
     */
    com.yorha.proto.CommonEnum.Language getLanguage();

    /**
     * <pre>
     * 天网关注野怪
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 12;</code>
     * @return A list containing the skynetMonsterList.
     */
    java.util.List<java.lang.Long> getSkynetMonsterListList();
    /**
     * <pre>
     * 天网关注野怪
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 12;</code>
     * @return The count of skynetMonsterList.
     */
    int getSkynetMonsterListCount();
    /**
     * <pre>
     * 天网关注野怪
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 12;</code>
     * @param index The index of the element to return.
     * @return The skynetMonsterList at the given index.
     */
    long getSkynetMonsterList(int index);

    /**
     * <pre>
     * 如果直接进副本 这个字段需要填充 告诉大世界登陆了 但是会在副本里
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonType dungeonType = 13;</code>
     * @return Whether the dungeonType field is set.
     */
    boolean hasDungeonType();
    /**
     * <pre>
     * 如果直接进副本 这个字段需要填充 告诉大世界登陆了 但是会在副本里
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonType dungeonType = 13;</code>
     * @return The dungeonType.
     */
    com.yorha.proto.CommonEnum.DungeonType getDungeonType();

    /**
     * <pre>
     * 玩家里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
     * @return Whether the playerMilestoneInfo field is set.
     */
    boolean hasPlayerMilestoneInfo();
    /**
     * <pre>
     * 玩家里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
     * @return The playerMilestoneInfo.
     */
    com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin getPlayerMilestoneInfo();
    /**
     * <pre>
     * 玩家里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
     */
    com.yorha.proto.StructMsg.PlayerMilestoneInfoByLoginOrBuilder getPlayerMilestoneInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlayerLoginAsk}
   */
  public static final class PlayerLoginAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlayerLoginAsk)
      PlayerLoginAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlayerLoginAsk.newBuilder() to construct.
    private PlayerLoginAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlayerLoginAsk() {
      openId_ = "";
      intlNtfToken_ = "";
      language_ = 0;
      skynetMonsterList_ = emptyLongList();
      dungeonType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlayerLoginAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlayerLoginAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.ActorRefData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = sessionRef_.toBuilder();
              }
              sessionRef_ = input.readMessage(com.yorha.proto.CommonMsg.ActorRefData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sessionRef_);
                sessionRef_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              isNewbie_ = input.readBool();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000008;
              lastLoginOutTsMs_ = input.readInt64();
              break;
            }
            case 66: {
              com.yorha.proto.CommonMsg.SceneInnerBuildInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = buildInfo_.toBuilder();
              }
              buildInfo_ = input.readMessage(com.yorha.proto.CommonMsg.SceneInnerBuildInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buildInfo_);
                buildInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              openId_ = bs;
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              intlNtfToken_ = bs;
              break;
            }
            case 88: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Language value = com.yorha.proto.CommonEnum.Language.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(11, rawValue);
              } else {
                bitField0_ |= 0x00000080;
                language_ = rawValue;
              }
              break;
            }
            case 96: {
              if (!((mutable_bitField0_ & 0x00000100) != 0)) {
                skynetMonsterList_ = newLongList();
                mutable_bitField0_ |= 0x00000100;
              }
              skynetMonsterList_.addLong(input.readInt64());
              break;
            }
            case 98: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000100) != 0) && input.getBytesUntilLimit() > 0) {
                skynetMonsterList_ = newLongList();
                mutable_bitField0_ |= 0x00000100;
              }
              while (input.getBytesUntilLimit() > 0) {
                skynetMonsterList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 104: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.DungeonType value = com.yorha.proto.CommonEnum.DungeonType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(13, rawValue);
              } else {
                bitField0_ |= 0x00000100;
                dungeonType_ = rawValue;
              }
              break;
            }
            case 130: {
              com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.Builder subBuilder = null;
              if (((bitField0_ & 0x00000200) != 0)) {
                subBuilder = playerMilestoneInfo_.toBuilder();
              }
              playerMilestoneInfo_ = input.readMessage(com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(playerMilestoneInfo_);
                playerMilestoneInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000200;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000100) != 0)) {
          skynetMonsterList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.PlayerLoginAsk.class, com.yorha.proto.SsSceneDungeon.PlayerLoginAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int SESSIONREF_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.ActorRefData sessionRef_;
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     * @return Whether the sessionRef field is set.
     */
    @java.lang.Override
    public boolean hasSessionRef() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     * @return The sessionRef.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefData getSessionRef() {
      return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
    }
    /**
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder() {
      return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
    }

    public static final int ISNEWBIE_FIELD_NUMBER = 3;
    private boolean isNewbie_;
    /**
     * <code>optional bool isNewbie = 3;</code>
     * @return Whether the isNewbie field is set.
     */
    @java.lang.Override
    public boolean hasIsNewbie() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool isNewbie = 3;</code>
     * @return The isNewbie.
     */
    @java.lang.Override
    public boolean getIsNewbie() {
      return isNewbie_;
    }

    public static final int LASTLOGINOUTTSMS_FIELD_NUMBER = 7;
    private long lastLoginOutTsMs_;
    /**
     * <pre>
     * 玩家上次登出时间
     * </pre>
     *
     * <code>optional int64 lastLoginOutTsMs = 7;</code>
     * @return Whether the lastLoginOutTsMs field is set.
     */
    @java.lang.Override
    public boolean hasLastLoginOutTsMs() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 玩家上次登出时间
     * </pre>
     *
     * <code>optional int64 lastLoginOutTsMs = 7;</code>
     * @return The lastLoginOutTsMs.
     */
    @java.lang.Override
    public long getLastLoginOutTsMs() {
      return lastLoginOutTsMs_;
    }

    public static final int BUILDINFO_FIELD_NUMBER = 8;
    private com.yorha.proto.CommonMsg.SceneInnerBuildInfo buildInfo_;
    /**
     * <pre>
     * 玩家内城信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
     * @return Whether the buildInfo field is set.
     */
    @java.lang.Override
    public boolean hasBuildInfo() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 玩家内城信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
     * @return The buildInfo.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.SceneInnerBuildInfo getBuildInfo() {
      return buildInfo_ == null ? com.yorha.proto.CommonMsg.SceneInnerBuildInfo.getDefaultInstance() : buildInfo_;
    }
    /**
     * <pre>
     * 玩家内城信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.SceneInnerBuildInfoOrBuilder getBuildInfoOrBuilder() {
      return buildInfo_ == null ? com.yorha.proto.CommonMsg.SceneInnerBuildInfo.getDefaultInstance() : buildInfo_;
    }

    public static final int OPENID_FIELD_NUMBER = 9;
    private volatile java.lang.Object openId_;
    /**
     * <pre>
     * 玩家openId（bigScene专属）
     * </pre>
     *
     * <code>optional string openId = 9;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 玩家openId（bigScene专属）
     * </pre>
     *
     * <code>optional string openId = 9;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 玩家openId（bigScene专属）
     * </pre>
     *
     * <code>optional string openId = 9;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INTLNTFTOKEN_FIELD_NUMBER = 10;
    private volatile java.lang.Object intlNtfToken_;
    /**
     * <pre>
     * Intl推送token
     * </pre>
     *
     * <code>optional string intlNtfToken = 10;</code>
     * @return Whether the intlNtfToken field is set.
     */
    @java.lang.Override
    public boolean hasIntlNtfToken() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * Intl推送token
     * </pre>
     *
     * <code>optional string intlNtfToken = 10;</code>
     * @return The intlNtfToken.
     */
    @java.lang.Override
    public java.lang.String getIntlNtfToken() {
      java.lang.Object ref = intlNtfToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          intlNtfToken_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * Intl推送token
     * </pre>
     *
     * <code>optional string intlNtfToken = 10;</code>
     * @return The bytes for intlNtfToken.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIntlNtfTokenBytes() {
      java.lang.Object ref = intlNtfToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        intlNtfToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LANGUAGE_FIELD_NUMBER = 11;
    private int language_;
    /**
     * <pre>
     * 玩家当前语言
     * </pre>
     *
     * <code>optional .com.yorha.proto.Language language = 11;</code>
     * @return Whether the language field is set.
     */
    @java.lang.Override public boolean hasLanguage() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 玩家当前语言
     * </pre>
     *
     * <code>optional .com.yorha.proto.Language language = 11;</code>
     * @return The language.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Language getLanguage() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Language result = com.yorha.proto.CommonEnum.Language.valueOf(language_);
      return result == null ? com.yorha.proto.CommonEnum.Language.L_NONE : result;
    }

    public static final int SKYNETMONSTERLIST_FIELD_NUMBER = 12;
    private com.google.protobuf.Internal.LongList skynetMonsterList_;
    /**
     * <pre>
     * 天网关注野怪
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 12;</code>
     * @return A list containing the skynetMonsterList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getSkynetMonsterListList() {
      return skynetMonsterList_;
    }
    /**
     * <pre>
     * 天网关注野怪
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 12;</code>
     * @return The count of skynetMonsterList.
     */
    public int getSkynetMonsterListCount() {
      return skynetMonsterList_.size();
    }
    /**
     * <pre>
     * 天网关注野怪
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 12;</code>
     * @param index The index of the element to return.
     * @return The skynetMonsterList at the given index.
     */
    public long getSkynetMonsterList(int index) {
      return skynetMonsterList_.getLong(index);
    }

    public static final int DUNGEONTYPE_FIELD_NUMBER = 13;
    private int dungeonType_;
    /**
     * <pre>
     * 如果直接进副本 这个字段需要填充 告诉大世界登陆了 但是会在副本里
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonType dungeonType = 13;</code>
     * @return Whether the dungeonType field is set.
     */
    @java.lang.Override public boolean hasDungeonType() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 如果直接进副本 这个字段需要填充 告诉大世界登陆了 但是会在副本里
     * </pre>
     *
     * <code>optional .com.yorha.proto.DungeonType dungeonType = 13;</code>
     * @return The dungeonType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.DungeonType getDungeonType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(dungeonType_);
      return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
    }

    public static final int PLAYERMILESTONEINFO_FIELD_NUMBER = 16;
    private com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin playerMilestoneInfo_;
    /**
     * <pre>
     * 玩家里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
     * @return Whether the playerMilestoneInfo field is set.
     */
    @java.lang.Override
    public boolean hasPlayerMilestoneInfo() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 玩家里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
     * @return The playerMilestoneInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin getPlayerMilestoneInfo() {
      return playerMilestoneInfo_ == null ? com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.getDefaultInstance() : playerMilestoneInfo_;
    }
    /**
     * <pre>
     * 玩家里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.PlayerMilestoneInfoByLoginOrBuilder getPlayerMilestoneInfoOrBuilder() {
      return playerMilestoneInfo_ == null ? com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.getDefaultInstance() : playerMilestoneInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getSessionRef());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(3, isNewbie_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(7, lastLoginOutTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(8, getBuildInfo());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, openId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, intlNtfToken_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeEnum(11, language_);
      }
      for (int i = 0; i < skynetMonsterList_.size(); i++) {
        output.writeInt64(12, skynetMonsterList_.getLong(i));
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeEnum(13, dungeonType_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeMessage(16, getPlayerMilestoneInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSessionRef());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, isNewbie_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, lastLoginOutTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getBuildInfo());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, openId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, intlNtfToken_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(11, language_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < skynetMonsterList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(skynetMonsterList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getSkynetMonsterListList().size();
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(13, dungeonType_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, getPlayerMilestoneInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.PlayerLoginAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.PlayerLoginAsk other = (com.yorha.proto.SsSceneDungeon.PlayerLoginAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasSessionRef() != other.hasSessionRef()) return false;
      if (hasSessionRef()) {
        if (!getSessionRef()
            .equals(other.getSessionRef())) return false;
      }
      if (hasIsNewbie() != other.hasIsNewbie()) return false;
      if (hasIsNewbie()) {
        if (getIsNewbie()
            != other.getIsNewbie()) return false;
      }
      if (hasLastLoginOutTsMs() != other.hasLastLoginOutTsMs()) return false;
      if (hasLastLoginOutTsMs()) {
        if (getLastLoginOutTsMs()
            != other.getLastLoginOutTsMs()) return false;
      }
      if (hasBuildInfo() != other.hasBuildInfo()) return false;
      if (hasBuildInfo()) {
        if (!getBuildInfo()
            .equals(other.getBuildInfo())) return false;
      }
      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasIntlNtfToken() != other.hasIntlNtfToken()) return false;
      if (hasIntlNtfToken()) {
        if (!getIntlNtfToken()
            .equals(other.getIntlNtfToken())) return false;
      }
      if (hasLanguage() != other.hasLanguage()) return false;
      if (hasLanguage()) {
        if (language_ != other.language_) return false;
      }
      if (!getSkynetMonsterListList()
          .equals(other.getSkynetMonsterListList())) return false;
      if (hasDungeonType() != other.hasDungeonType()) return false;
      if (hasDungeonType()) {
        if (dungeonType_ != other.dungeonType_) return false;
      }
      if (hasPlayerMilestoneInfo() != other.hasPlayerMilestoneInfo()) return false;
      if (hasPlayerMilestoneInfo()) {
        if (!getPlayerMilestoneInfo()
            .equals(other.getPlayerMilestoneInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasSessionRef()) {
        hash = (37 * hash) + SESSIONREF_FIELD_NUMBER;
        hash = (53 * hash) + getSessionRef().hashCode();
      }
      if (hasIsNewbie()) {
        hash = (37 * hash) + ISNEWBIE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsNewbie());
      }
      if (hasLastLoginOutTsMs()) {
        hash = (37 * hash) + LASTLOGINOUTTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLastLoginOutTsMs());
      }
      if (hasBuildInfo()) {
        hash = (37 * hash) + BUILDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getBuildInfo().hashCode();
      }
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasIntlNtfToken()) {
        hash = (37 * hash) + INTLNTFTOKEN_FIELD_NUMBER;
        hash = (53 * hash) + getIntlNtfToken().hashCode();
      }
      if (hasLanguage()) {
        hash = (37 * hash) + LANGUAGE_FIELD_NUMBER;
        hash = (53 * hash) + language_;
      }
      if (getSkynetMonsterListCount() > 0) {
        hash = (37 * hash) + SKYNETMONSTERLIST_FIELD_NUMBER;
        hash = (53 * hash) + getSkynetMonsterListList().hashCode();
      }
      if (hasDungeonType()) {
        hash = (37 * hash) + DUNGEONTYPE_FIELD_NUMBER;
        hash = (53 * hash) + dungeonType_;
      }
      if (hasPlayerMilestoneInfo()) {
        hash = (37 * hash) + PLAYERMILESTONEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerMilestoneInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.PlayerLoginAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlayerLoginAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlayerLoginAsk)
        com.yorha.proto.SsSceneDungeon.PlayerLoginAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.PlayerLoginAsk.class, com.yorha.proto.SsSceneDungeon.PlayerLoginAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.PlayerLoginAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSessionRefFieldBuilder();
          getBuildInfoFieldBuilder();
          getPlayerMilestoneInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (sessionRefBuilder_ == null) {
          sessionRef_ = null;
        } else {
          sessionRefBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        isNewbie_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        lastLoginOutTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        if (buildInfoBuilder_ == null) {
          buildInfo_ = null;
        } else {
          buildInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        intlNtfToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        language_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        skynetMonsterList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000100);
        dungeonType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        if (playerMilestoneInfoBuilder_ == null) {
          playerMilestoneInfo_ = null;
        } else {
          playerMilestoneInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLoginAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.PlayerLoginAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLoginAsk build() {
        com.yorha.proto.SsSceneDungeon.PlayerLoginAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLoginAsk buildPartial() {
        com.yorha.proto.SsSceneDungeon.PlayerLoginAsk result = new com.yorha.proto.SsSceneDungeon.PlayerLoginAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (sessionRefBuilder_ == null) {
            result.sessionRef_ = sessionRef_;
          } else {
            result.sessionRef_ = sessionRefBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.isNewbie_ = isNewbie_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lastLoginOutTsMs_ = lastLoginOutTsMs_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (buildInfoBuilder_ == null) {
            result.buildInfo_ = buildInfo_;
          } else {
            result.buildInfo_ = buildInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.intlNtfToken_ = intlNtfToken_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.language_ = language_;
        if (((bitField0_ & 0x00000100) != 0)) {
          skynetMonsterList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.skynetMonsterList_ = skynetMonsterList_;
        if (((from_bitField0_ & 0x00000200) != 0)) {
          to_bitField0_ |= 0x00000100;
        }
        result.dungeonType_ = dungeonType_;
        if (((from_bitField0_ & 0x00000400) != 0)) {
          if (playerMilestoneInfoBuilder_ == null) {
            result.playerMilestoneInfo_ = playerMilestoneInfo_;
          } else {
            result.playerMilestoneInfo_ = playerMilestoneInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000200;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.PlayerLoginAsk) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.PlayerLoginAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.PlayerLoginAsk other) {
        if (other == com.yorha.proto.SsSceneDungeon.PlayerLoginAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasSessionRef()) {
          mergeSessionRef(other.getSessionRef());
        }
        if (other.hasIsNewbie()) {
          setIsNewbie(other.getIsNewbie());
        }
        if (other.hasLastLoginOutTsMs()) {
          setLastLoginOutTsMs(other.getLastLoginOutTsMs());
        }
        if (other.hasBuildInfo()) {
          mergeBuildInfo(other.getBuildInfo());
        }
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000020;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasIntlNtfToken()) {
          bitField0_ |= 0x00000040;
          intlNtfToken_ = other.intlNtfToken_;
          onChanged();
        }
        if (other.hasLanguage()) {
          setLanguage(other.getLanguage());
        }
        if (!other.skynetMonsterList_.isEmpty()) {
          if (skynetMonsterList_.isEmpty()) {
            skynetMonsterList_ = other.skynetMonsterList_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureSkynetMonsterListIsMutable();
            skynetMonsterList_.addAll(other.skynetMonsterList_);
          }
          onChanged();
        }
        if (other.hasDungeonType()) {
          setDungeonType(other.getDungeonType());
        }
        if (other.hasPlayerMilestoneInfo()) {
          mergePlayerMilestoneInfo(other.getPlayerMilestoneInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.PlayerLoginAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.PlayerLoginAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.ActorRefData sessionRef_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> sessionRefBuilder_;
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       * @return Whether the sessionRef field is set.
       */
      public boolean hasSessionRef() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       * @return The sessionRef.
       */
      public com.yorha.proto.CommonMsg.ActorRefData getSessionRef() {
        if (sessionRefBuilder_ == null) {
          return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
        } else {
          return sessionRefBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public Builder setSessionRef(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (sessionRefBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sessionRef_ = value;
          onChanged();
        } else {
          sessionRefBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public Builder setSessionRef(
          com.yorha.proto.CommonMsg.ActorRefData.Builder builderForValue) {
        if (sessionRefBuilder_ == null) {
          sessionRef_ = builderForValue.build();
          onChanged();
        } else {
          sessionRefBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public Builder mergeSessionRef(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (sessionRefBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              sessionRef_ != null &&
              sessionRef_ != com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance()) {
            sessionRef_ =
              com.yorha.proto.CommonMsg.ActorRefData.newBuilder(sessionRef_).mergeFrom(value).buildPartial();
          } else {
            sessionRef_ = value;
          }
          onChanged();
        } else {
          sessionRefBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public Builder clearSessionRef() {
        if (sessionRefBuilder_ == null) {
          sessionRef_ = null;
          onChanged();
        } else {
          sessionRefBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData.Builder getSessionRefBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSessionRefFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder() {
        if (sessionRefBuilder_ != null) {
          return sessionRefBuilder_.getMessageOrBuilder();
        } else {
          return sessionRef_ == null ?
              com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
          getSessionRefFieldBuilder() {
        if (sessionRefBuilder_ == null) {
          sessionRefBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder>(
                  getSessionRef(),
                  getParentForChildren(),
                  isClean());
          sessionRef_ = null;
        }
        return sessionRefBuilder_;
      }

      private boolean isNewbie_ ;
      /**
       * <code>optional bool isNewbie = 3;</code>
       * @return Whether the isNewbie field is set.
       */
      @java.lang.Override
      public boolean hasIsNewbie() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool isNewbie = 3;</code>
       * @return The isNewbie.
       */
      @java.lang.Override
      public boolean getIsNewbie() {
        return isNewbie_;
      }
      /**
       * <code>optional bool isNewbie = 3;</code>
       * @param value The isNewbie to set.
       * @return This builder for chaining.
       */
      public Builder setIsNewbie(boolean value) {
        bitField0_ |= 0x00000004;
        isNewbie_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isNewbie = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsNewbie() {
        bitField0_ = (bitField0_ & ~0x00000004);
        isNewbie_ = false;
        onChanged();
        return this;
      }

      private long lastLoginOutTsMs_ ;
      /**
       * <pre>
       * 玩家上次登出时间
       * </pre>
       *
       * <code>optional int64 lastLoginOutTsMs = 7;</code>
       * @return Whether the lastLoginOutTsMs field is set.
       */
      @java.lang.Override
      public boolean hasLastLoginOutTsMs() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 玩家上次登出时间
       * </pre>
       *
       * <code>optional int64 lastLoginOutTsMs = 7;</code>
       * @return The lastLoginOutTsMs.
       */
      @java.lang.Override
      public long getLastLoginOutTsMs() {
        return lastLoginOutTsMs_;
      }
      /**
       * <pre>
       * 玩家上次登出时间
       * </pre>
       *
       * <code>optional int64 lastLoginOutTsMs = 7;</code>
       * @param value The lastLoginOutTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setLastLoginOutTsMs(long value) {
        bitField0_ |= 0x00000008;
        lastLoginOutTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家上次登出时间
       * </pre>
       *
       * <code>optional int64 lastLoginOutTsMs = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastLoginOutTsMs() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lastLoginOutTsMs_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.SceneInnerBuildInfo buildInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.SceneInnerBuildInfo, com.yorha.proto.CommonMsg.SceneInnerBuildInfo.Builder, com.yorha.proto.CommonMsg.SceneInnerBuildInfoOrBuilder> buildInfoBuilder_;
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       * @return Whether the buildInfo field is set.
       */
      public boolean hasBuildInfo() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       * @return The buildInfo.
       */
      public com.yorha.proto.CommonMsg.SceneInnerBuildInfo getBuildInfo() {
        if (buildInfoBuilder_ == null) {
          return buildInfo_ == null ? com.yorha.proto.CommonMsg.SceneInnerBuildInfo.getDefaultInstance() : buildInfo_;
        } else {
          return buildInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       */
      public Builder setBuildInfo(com.yorha.proto.CommonMsg.SceneInnerBuildInfo value) {
        if (buildInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buildInfo_ = value;
          onChanged();
        } else {
          buildInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       */
      public Builder setBuildInfo(
          com.yorha.proto.CommonMsg.SceneInnerBuildInfo.Builder builderForValue) {
        if (buildInfoBuilder_ == null) {
          buildInfo_ = builderForValue.build();
          onChanged();
        } else {
          buildInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       */
      public Builder mergeBuildInfo(com.yorha.proto.CommonMsg.SceneInnerBuildInfo value) {
        if (buildInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              buildInfo_ != null &&
              buildInfo_ != com.yorha.proto.CommonMsg.SceneInnerBuildInfo.getDefaultInstance()) {
            buildInfo_ =
              com.yorha.proto.CommonMsg.SceneInnerBuildInfo.newBuilder(buildInfo_).mergeFrom(value).buildPartial();
          } else {
            buildInfo_ = value;
          }
          onChanged();
        } else {
          buildInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       */
      public Builder clearBuildInfo() {
        if (buildInfoBuilder_ == null) {
          buildInfo_ = null;
          onChanged();
        } else {
          buildInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       */
      public com.yorha.proto.CommonMsg.SceneInnerBuildInfo.Builder getBuildInfoBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getBuildInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       */
      public com.yorha.proto.CommonMsg.SceneInnerBuildInfoOrBuilder getBuildInfoOrBuilder() {
        if (buildInfoBuilder_ != null) {
          return buildInfoBuilder_.getMessageOrBuilder();
        } else {
          return buildInfo_ == null ?
              com.yorha.proto.CommonMsg.SceneInnerBuildInfo.getDefaultInstance() : buildInfo_;
        }
      }
      /**
       * <pre>
       * 玩家内城信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneInnerBuildInfo buildInfo = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.SceneInnerBuildInfo, com.yorha.proto.CommonMsg.SceneInnerBuildInfo.Builder, com.yorha.proto.CommonMsg.SceneInnerBuildInfoOrBuilder> 
          getBuildInfoFieldBuilder() {
        if (buildInfoBuilder_ == null) {
          buildInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.SceneInnerBuildInfo, com.yorha.proto.CommonMsg.SceneInnerBuildInfo.Builder, com.yorha.proto.CommonMsg.SceneInnerBuildInfoOrBuilder>(
                  getBuildInfo(),
                  getParentForChildren(),
                  isClean());
          buildInfo_ = null;
        }
        return buildInfoBuilder_;
      }

      private java.lang.Object openId_ = "";
      /**
       * <pre>
       * 玩家openId（bigScene专属）
       * </pre>
       *
       * <code>optional string openId = 9;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 玩家openId（bigScene专属）
       * </pre>
       *
       * <code>optional string openId = 9;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 玩家openId（bigScene专属）
       * </pre>
       *
       * <code>optional string openId = 9;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 玩家openId（bigScene专属）
       * </pre>
       *
       * <code>optional string openId = 9;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家openId（bigScene专属）
       * </pre>
       *
       * <code>optional string openId = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家openId（bigScene专属）
       * </pre>
       *
       * <code>optional string openId = 9;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        openId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object intlNtfToken_ = "";
      /**
       * <pre>
       * Intl推送token
       * </pre>
       *
       * <code>optional string intlNtfToken = 10;</code>
       * @return Whether the intlNtfToken field is set.
       */
      public boolean hasIntlNtfToken() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * Intl推送token
       * </pre>
       *
       * <code>optional string intlNtfToken = 10;</code>
       * @return The intlNtfToken.
       */
      public java.lang.String getIntlNtfToken() {
        java.lang.Object ref = intlNtfToken_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            intlNtfToken_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * Intl推送token
       * </pre>
       *
       * <code>optional string intlNtfToken = 10;</code>
       * @return The bytes for intlNtfToken.
       */
      public com.google.protobuf.ByteString
          getIntlNtfTokenBytes() {
        java.lang.Object ref = intlNtfToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          intlNtfToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * Intl推送token
       * </pre>
       *
       * <code>optional string intlNtfToken = 10;</code>
       * @param value The intlNtfToken to set.
       * @return This builder for chaining.
       */
      public Builder setIntlNtfToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        intlNtfToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Intl推送token
       * </pre>
       *
       * <code>optional string intlNtfToken = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIntlNtfToken() {
        bitField0_ = (bitField0_ & ~0x00000040);
        intlNtfToken_ = getDefaultInstance().getIntlNtfToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * Intl推送token
       * </pre>
       *
       * <code>optional string intlNtfToken = 10;</code>
       * @param value The bytes for intlNtfToken to set.
       * @return This builder for chaining.
       */
      public Builder setIntlNtfTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        intlNtfToken_ = value;
        onChanged();
        return this;
      }

      private int language_ = 0;
      /**
       * <pre>
       * 玩家当前语言
       * </pre>
       *
       * <code>optional .com.yorha.proto.Language language = 11;</code>
       * @return Whether the language field is set.
       */
      @java.lang.Override public boolean hasLanguage() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 玩家当前语言
       * </pre>
       *
       * <code>optional .com.yorha.proto.Language language = 11;</code>
       * @return The language.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Language getLanguage() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Language result = com.yorha.proto.CommonEnum.Language.valueOf(language_);
        return result == null ? com.yorha.proto.CommonEnum.Language.L_NONE : result;
      }
      /**
       * <pre>
       * 玩家当前语言
       * </pre>
       *
       * <code>optional .com.yorha.proto.Language language = 11;</code>
       * @param value The language to set.
       * @return This builder for chaining.
       */
      public Builder setLanguage(com.yorha.proto.CommonEnum.Language value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000080;
        language_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家当前语言
       * </pre>
       *
       * <code>optional .com.yorha.proto.Language language = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearLanguage() {
        bitField0_ = (bitField0_ & ~0x00000080);
        language_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList skynetMonsterList_ = emptyLongList();
      private void ensureSkynetMonsterListIsMutable() {
        if (!((bitField0_ & 0x00000100) != 0)) {
          skynetMonsterList_ = mutableCopy(skynetMonsterList_);
          bitField0_ |= 0x00000100;
         }
      }
      /**
       * <pre>
       * 天网关注野怪
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 12;</code>
       * @return A list containing the skynetMonsterList.
       */
      public java.util.List<java.lang.Long>
          getSkynetMonsterListList() {
        return ((bitField0_ & 0x00000100) != 0) ?
                 java.util.Collections.unmodifiableList(skynetMonsterList_) : skynetMonsterList_;
      }
      /**
       * <pre>
       * 天网关注野怪
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 12;</code>
       * @return The count of skynetMonsterList.
       */
      public int getSkynetMonsterListCount() {
        return skynetMonsterList_.size();
      }
      /**
       * <pre>
       * 天网关注野怪
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 12;</code>
       * @param index The index of the element to return.
       * @return The skynetMonsterList at the given index.
       */
      public long getSkynetMonsterList(int index) {
        return skynetMonsterList_.getLong(index);
      }
      /**
       * <pre>
       * 天网关注野怪
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 12;</code>
       * @param index The index to set the value at.
       * @param value The skynetMonsterList to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetMonsterList(
          int index, long value) {
        ensureSkynetMonsterListIsMutable();
        skynetMonsterList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 天网关注野怪
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 12;</code>
       * @param value The skynetMonsterList to add.
       * @return This builder for chaining.
       */
      public Builder addSkynetMonsterList(long value) {
        ensureSkynetMonsterListIsMutable();
        skynetMonsterList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 天网关注野怪
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 12;</code>
       * @param values The skynetMonsterList to add.
       * @return This builder for chaining.
       */
      public Builder addAllSkynetMonsterList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureSkynetMonsterListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, skynetMonsterList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 天网关注野怪
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetMonsterList() {
        skynetMonsterList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }

      private int dungeonType_ = 0;
      /**
       * <pre>
       * 如果直接进副本 这个字段需要填充 告诉大世界登陆了 但是会在副本里
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonType dungeonType = 13;</code>
       * @return Whether the dungeonType field is set.
       */
      @java.lang.Override public boolean hasDungeonType() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 如果直接进副本 这个字段需要填充 告诉大世界登陆了 但是会在副本里
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonType dungeonType = 13;</code>
       * @return The dungeonType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.DungeonType getDungeonType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(dungeonType_);
        return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
      }
      /**
       * <pre>
       * 如果直接进副本 这个字段需要填充 告诉大世界登陆了 但是会在副本里
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonType dungeonType = 13;</code>
       * @param value The dungeonType to set.
       * @return This builder for chaining.
       */
      public Builder setDungeonType(com.yorha.proto.CommonEnum.DungeonType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000200;
        dungeonType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 如果直接进副本 这个字段需要填充 告诉大世界登陆了 但是会在副本里
       * </pre>
       *
       * <code>optional .com.yorha.proto.DungeonType dungeonType = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearDungeonType() {
        bitField0_ = (bitField0_ & ~0x00000200);
        dungeonType_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin playerMilestoneInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin, com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.Builder, com.yorha.proto.StructMsg.PlayerMilestoneInfoByLoginOrBuilder> playerMilestoneInfoBuilder_;
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       * @return Whether the playerMilestoneInfo field is set.
       */
      public boolean hasPlayerMilestoneInfo() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       * @return The playerMilestoneInfo.
       */
      public com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin getPlayerMilestoneInfo() {
        if (playerMilestoneInfoBuilder_ == null) {
          return playerMilestoneInfo_ == null ? com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.getDefaultInstance() : playerMilestoneInfo_;
        } else {
          return playerMilestoneInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       */
      public Builder setPlayerMilestoneInfo(com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin value) {
        if (playerMilestoneInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          playerMilestoneInfo_ = value;
          onChanged();
        } else {
          playerMilestoneInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       */
      public Builder setPlayerMilestoneInfo(
          com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.Builder builderForValue) {
        if (playerMilestoneInfoBuilder_ == null) {
          playerMilestoneInfo_ = builderForValue.build();
          onChanged();
        } else {
          playerMilestoneInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       */
      public Builder mergePlayerMilestoneInfo(com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin value) {
        if (playerMilestoneInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000400) != 0) &&
              playerMilestoneInfo_ != null &&
              playerMilestoneInfo_ != com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.getDefaultInstance()) {
            playerMilestoneInfo_ =
              com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.newBuilder(playerMilestoneInfo_).mergeFrom(value).buildPartial();
          } else {
            playerMilestoneInfo_ = value;
          }
          onChanged();
        } else {
          playerMilestoneInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       */
      public Builder clearPlayerMilestoneInfo() {
        if (playerMilestoneInfoBuilder_ == null) {
          playerMilestoneInfo_ = null;
          onChanged();
        } else {
          playerMilestoneInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       */
      public com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.Builder getPlayerMilestoneInfoBuilder() {
        bitField0_ |= 0x00000400;
        onChanged();
        return getPlayerMilestoneInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       */
      public com.yorha.proto.StructMsg.PlayerMilestoneInfoByLoginOrBuilder getPlayerMilestoneInfoOrBuilder() {
        if (playerMilestoneInfoBuilder_ != null) {
          return playerMilestoneInfoBuilder_.getMessageOrBuilder();
        } else {
          return playerMilestoneInfo_ == null ?
              com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.getDefaultInstance() : playerMilestoneInfo_;
        }
      }
      /**
       * <pre>
       * 玩家里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerMilestoneInfoByLogin playerMilestoneInfo = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin, com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.Builder, com.yorha.proto.StructMsg.PlayerMilestoneInfoByLoginOrBuilder> 
          getPlayerMilestoneInfoFieldBuilder() {
        if (playerMilestoneInfoBuilder_ == null) {
          playerMilestoneInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin, com.yorha.proto.StructMsg.PlayerMilestoneInfoByLogin.Builder, com.yorha.proto.StructMsg.PlayerMilestoneInfoByLoginOrBuilder>(
                  getPlayerMilestoneInfo(),
                  getParentForChildren(),
                  isClean());
          playerMilestoneInfo_ = null;
        }
        return playerMilestoneInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlayerLoginAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlayerLoginAsk)
    private static final com.yorha.proto.SsSceneDungeon.PlayerLoginAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.PlayerLoginAsk();
    }

    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlayerLoginAsk>
        PARSER = new com.google.protobuf.AbstractParser<PlayerLoginAsk>() {
      @java.lang.Override
      public PlayerLoginAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlayerLoginAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlayerLoginAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlayerLoginAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.PlayerLoginAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlayerLoginAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlayerLoginAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool isOk = 1;</code>
     * @return Whether the isOk field is set.
     */
    boolean hasIsOk();
    /**
     * <code>optional bool isOk = 1;</code>
     * @return The isOk.
     */
    boolean getIsOk();

    /**
     * <pre>
     * 在行军中的所有英雄
     * </pre>
     *
     * <code>repeated int32 heroList = 5;</code>
     * @return A list containing the heroList.
     */
    java.util.List<java.lang.Integer> getHeroListList();
    /**
     * <pre>
     * 在行军中的所有英雄
     * </pre>
     *
     * <code>repeated int32 heroList = 5;</code>
     * @return The count of heroList.
     */
    int getHeroListCount();
    /**
     * <pre>
     * 在行军中的所有英雄
     * </pre>
     *
     * <code>repeated int32 heroList = 5;</code>
     * @param index The index of the element to return.
     * @return The heroList at the given index.
     */
    int getHeroList(int index);

    /**
     * <pre>
     * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
     * </pre>
     *
     * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
     * @return Whether the devBuffs field is set.
     */
    boolean hasDevBuffs();
    /**
     * <pre>
     * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
     * </pre>
     *
     * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
     * @return The devBuffs.
     */
    com.yorha.proto.StructBattle.DevBuffSys getDevBuffs();
    /**
     * <pre>
     * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
     * </pre>
     *
     * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
     */
    com.yorha.proto.StructBattle.DevBuffSysOrBuilder getDevBuffsOrBuilder();

    /**
     * <pre>
     * scenePlayer上的addition，拿过去给player拼出来AdditionView
     * </pre>
     *
     * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
     * @return Whether the additionSys field is set.
     */
    boolean hasAdditionSys();
    /**
     * <pre>
     * scenePlayer上的addition，拿过去给player拼出来AdditionView
     * </pre>
     *
     * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
     * @return The additionSys.
     */
    com.yorha.proto.Struct.AdditionSys getAdditionSys();
    /**
     * <pre>
     * scenePlayer上的addition，拿过去给player拼出来AdditionView
     * </pre>
     *
     * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
     */
    com.yorha.proto.Struct.AdditionSysOrBuilder getAdditionSysOrBuilder();

    /**
     * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
     * @return Whether the zoneAdditionSys field is set.
     */
    boolean hasZoneAdditionSys();
    /**
     * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
     * @return The zoneAdditionSys.
     */
    com.yorha.proto.Struct.AdditionSys getZoneAdditionSys();
    /**
     * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
     */
    com.yorha.proto.Struct.AdditionSysOrBuilder getZoneAdditionSysOrBuilder();

    /**
     * <code>optional int64 clanId = 12;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 12;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <pre>
     * 大世界上的飞机列表
     * </pre>
     *
     * <code>repeated int64 planeList = 13;</code>
     * @return A list containing the planeList.
     */
    java.util.List<java.lang.Long> getPlaneListList();
    /**
     * <pre>
     * 大世界上的飞机列表
     * </pre>
     *
     * <code>repeated int64 planeList = 13;</code>
     * @return The count of planeList.
     */
    int getPlaneListCount();
    /**
     * <pre>
     * 大世界上的飞机列表
     * </pre>
     *
     * <code>repeated int64 planeList = 13;</code>
     * @param index The index of the element to return.
     * @return The planeList at the given index.
     */
    long getPlaneList(int index);

    /**
     * <pre>
     * 玩家关注的天网野怪确实存在的列表
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 15;</code>
     * @return A list containing the skynetMonsterList.
     */
    java.util.List<java.lang.Long> getSkynetMonsterListList();
    /**
     * <pre>
     * 玩家关注的天网野怪确实存在的列表
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 15;</code>
     * @return The count of skynetMonsterList.
     */
    int getSkynetMonsterListCount();
    /**
     * <pre>
     * 玩家关注的天网野怪确实存在的列表
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 15;</code>
     * @param index The index of the element to return.
     * @return The skynetMonsterList at the given index.
     */
    long getSkynetMonsterList(int index);

    /**
     * <pre>
     * 里程碑任务模板id
     * </pre>
     *
     * <code>optional int32 curMileStoneTemplateId = 16;</code>
     * @return Whether the curMileStoneTemplateId field is set.
     */
    boolean hasCurMileStoneTemplateId();
    /**
     * <pre>
     * 里程碑任务模板id
     * </pre>
     *
     * <code>optional int32 curMileStoneTemplateId = 16;</code>
     * @return The curMileStoneTemplateId.
     */
    int getCurMileStoneTemplateId();

    /**
     * <pre>
     * zone里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
     * @return Whether the zoneMilestoneInfo field is set.
     */
    boolean hasZoneMilestoneInfo();
    /**
     * <pre>
     * zone里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
     * @return The zoneMilestoneInfo.
     */
    com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin getZoneMilestoneInfo();
    /**
     * <pre>
     * zone里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
     */
    com.yorha.proto.StructMsg.ZoneMilestoneInfoByLoginOrBuilder getZoneMilestoneInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlayerLoginAns}
   */
  public static final class PlayerLoginAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlayerLoginAns)
      PlayerLoginAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlayerLoginAns.newBuilder() to construct.
    private PlayerLoginAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlayerLoginAns() {
      heroList_ = emptyIntList();
      planeList_ = emptyLongList();
      skynetMonsterList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlayerLoginAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlayerLoginAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isOk_ = input.readBool();
              break;
            }
            case 40: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                heroList_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              heroList_.addInt(input.readInt32());
              break;
            }
            case 42: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                heroList_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                heroList_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 74: {
              com.yorha.proto.StructBattle.DevBuffSys.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = devBuffs_.toBuilder();
              }
              devBuffs_ = input.readMessage(com.yorha.proto.StructBattle.DevBuffSys.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(devBuffs_);
                devBuffs_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 82: {
              com.yorha.proto.Struct.AdditionSys.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = additionSys_.toBuilder();
              }
              additionSys_ = input.readMessage(com.yorha.proto.Struct.AdditionSys.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(additionSys_);
                additionSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 90: {
              com.yorha.proto.Struct.AdditionSys.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = zoneAdditionSys_.toBuilder();
              }
              zoneAdditionSys_ = input.readMessage(com.yorha.proto.Struct.AdditionSys.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(zoneAdditionSys_);
                zoneAdditionSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 96: {
              bitField0_ |= 0x00000010;
              clanId_ = input.readInt64();
              break;
            }
            case 104: {
              if (!((mutable_bitField0_ & 0x00000040) != 0)) {
                planeList_ = newLongList();
                mutable_bitField0_ |= 0x00000040;
              }
              planeList_.addLong(input.readInt64());
              break;
            }
            case 106: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000040) != 0) && input.getBytesUntilLimit() > 0) {
                planeList_ = newLongList();
                mutable_bitField0_ |= 0x00000040;
              }
              while (input.getBytesUntilLimit() > 0) {
                planeList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 120: {
              if (!((mutable_bitField0_ & 0x00000080) != 0)) {
                skynetMonsterList_ = newLongList();
                mutable_bitField0_ |= 0x00000080;
              }
              skynetMonsterList_.addLong(input.readInt64());
              break;
            }
            case 122: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000080) != 0) && input.getBytesUntilLimit() > 0) {
                skynetMonsterList_ = newLongList();
                mutable_bitField0_ |= 0x00000080;
              }
              while (input.getBytesUntilLimit() > 0) {
                skynetMonsterList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 128: {
              bitField0_ |= 0x00000020;
              curMileStoneTemplateId_ = input.readInt32();
              break;
            }
            case 138: {
              com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = zoneMilestoneInfo_.toBuilder();
              }
              zoneMilestoneInfo_ = input.readMessage(com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(zoneMilestoneInfo_);
                zoneMilestoneInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          heroList_.makeImmutable(); // C
        }
        if (((mutable_bitField0_ & 0x00000040) != 0)) {
          planeList_.makeImmutable(); // C
        }
        if (((mutable_bitField0_ & 0x00000080) != 0)) {
          skynetMonsterList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.PlayerLoginAns.class, com.yorha.proto.SsSceneDungeon.PlayerLoginAns.Builder.class);
    }

    private int bitField0_;
    public static final int ISOK_FIELD_NUMBER = 1;
    private boolean isOk_;
    /**
     * <code>optional bool isOk = 1;</code>
     * @return Whether the isOk field is set.
     */
    @java.lang.Override
    public boolean hasIsOk() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool isOk = 1;</code>
     * @return The isOk.
     */
    @java.lang.Override
    public boolean getIsOk() {
      return isOk_;
    }

    public static final int HEROLIST_FIELD_NUMBER = 5;
    private com.google.protobuf.Internal.IntList heroList_;
    /**
     * <pre>
     * 在行军中的所有英雄
     * </pre>
     *
     * <code>repeated int32 heroList = 5;</code>
     * @return A list containing the heroList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getHeroListList() {
      return heroList_;
    }
    /**
     * <pre>
     * 在行军中的所有英雄
     * </pre>
     *
     * <code>repeated int32 heroList = 5;</code>
     * @return The count of heroList.
     */
    public int getHeroListCount() {
      return heroList_.size();
    }
    /**
     * <pre>
     * 在行军中的所有英雄
     * </pre>
     *
     * <code>repeated int32 heroList = 5;</code>
     * @param index The index of the element to return.
     * @return The heroList at the given index.
     */
    public int getHeroList(int index) {
      return heroList_.getInt(index);
    }

    public static final int DEVBUFFS_FIELD_NUMBER = 9;
    private com.yorha.proto.StructBattle.DevBuffSys devBuffs_;
    /**
     * <pre>
     * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
     * </pre>
     *
     * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
     * @return Whether the devBuffs field is set.
     */
    @java.lang.Override
    public boolean hasDevBuffs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
     * </pre>
     *
     * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
     * @return The devBuffs.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.DevBuffSys getDevBuffs() {
      return devBuffs_ == null ? com.yorha.proto.StructBattle.DevBuffSys.getDefaultInstance() : devBuffs_;
    }
    /**
     * <pre>
     * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
     * </pre>
     *
     * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.DevBuffSysOrBuilder getDevBuffsOrBuilder() {
      return devBuffs_ == null ? com.yorha.proto.StructBattle.DevBuffSys.getDefaultInstance() : devBuffs_;
    }

    public static final int ADDITIONSYS_FIELD_NUMBER = 10;
    private com.yorha.proto.Struct.AdditionSys additionSys_;
    /**
     * <pre>
     * scenePlayer上的addition，拿过去给player拼出来AdditionView
     * </pre>
     *
     * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
     * @return Whether the additionSys field is set.
     */
    @java.lang.Override
    public boolean hasAdditionSys() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * scenePlayer上的addition，拿过去给player拼出来AdditionView
     * </pre>
     *
     * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
     * @return The additionSys.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.AdditionSys getAdditionSys() {
      return additionSys_ == null ? com.yorha.proto.Struct.AdditionSys.getDefaultInstance() : additionSys_;
    }
    /**
     * <pre>
     * scenePlayer上的addition，拿过去给player拼出来AdditionView
     * </pre>
     *
     * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.AdditionSysOrBuilder getAdditionSysOrBuilder() {
      return additionSys_ == null ? com.yorha.proto.Struct.AdditionSys.getDefaultInstance() : additionSys_;
    }

    public static final int ZONEADDITIONSYS_FIELD_NUMBER = 11;
    private com.yorha.proto.Struct.AdditionSys zoneAdditionSys_;
    /**
     * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
     * @return Whether the zoneAdditionSys field is set.
     */
    @java.lang.Override
    public boolean hasZoneAdditionSys() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
     * @return The zoneAdditionSys.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.AdditionSys getZoneAdditionSys() {
      return zoneAdditionSys_ == null ? com.yorha.proto.Struct.AdditionSys.getDefaultInstance() : zoneAdditionSys_;
    }
    /**
     * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.AdditionSysOrBuilder getZoneAdditionSysOrBuilder() {
      return zoneAdditionSys_ == null ? com.yorha.proto.Struct.AdditionSys.getDefaultInstance() : zoneAdditionSys_;
    }

    public static final int CLANID_FIELD_NUMBER = 12;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 12;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 clanId = 12;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int PLANELIST_FIELD_NUMBER = 13;
    private com.google.protobuf.Internal.LongList planeList_;
    /**
     * <pre>
     * 大世界上的飞机列表
     * </pre>
     *
     * <code>repeated int64 planeList = 13;</code>
     * @return A list containing the planeList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getPlaneListList() {
      return planeList_;
    }
    /**
     * <pre>
     * 大世界上的飞机列表
     * </pre>
     *
     * <code>repeated int64 planeList = 13;</code>
     * @return The count of planeList.
     */
    public int getPlaneListCount() {
      return planeList_.size();
    }
    /**
     * <pre>
     * 大世界上的飞机列表
     * </pre>
     *
     * <code>repeated int64 planeList = 13;</code>
     * @param index The index of the element to return.
     * @return The planeList at the given index.
     */
    public long getPlaneList(int index) {
      return planeList_.getLong(index);
    }

    public static final int SKYNETMONSTERLIST_FIELD_NUMBER = 15;
    private com.google.protobuf.Internal.LongList skynetMonsterList_;
    /**
     * <pre>
     * 玩家关注的天网野怪确实存在的列表
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 15;</code>
     * @return A list containing the skynetMonsterList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getSkynetMonsterListList() {
      return skynetMonsterList_;
    }
    /**
     * <pre>
     * 玩家关注的天网野怪确实存在的列表
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 15;</code>
     * @return The count of skynetMonsterList.
     */
    public int getSkynetMonsterListCount() {
      return skynetMonsterList_.size();
    }
    /**
     * <pre>
     * 玩家关注的天网野怪确实存在的列表
     * </pre>
     *
     * <code>repeated int64 skynetMonsterList = 15;</code>
     * @param index The index of the element to return.
     * @return The skynetMonsterList at the given index.
     */
    public long getSkynetMonsterList(int index) {
      return skynetMonsterList_.getLong(index);
    }

    public static final int CURMILESTONETEMPLATEID_FIELD_NUMBER = 16;
    private int curMileStoneTemplateId_;
    /**
     * <pre>
     * 里程碑任务模板id
     * </pre>
     *
     * <code>optional int32 curMileStoneTemplateId = 16;</code>
     * @return Whether the curMileStoneTemplateId field is set.
     */
    @java.lang.Override
    public boolean hasCurMileStoneTemplateId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 里程碑任务模板id
     * </pre>
     *
     * <code>optional int32 curMileStoneTemplateId = 16;</code>
     * @return The curMileStoneTemplateId.
     */
    @java.lang.Override
    public int getCurMileStoneTemplateId() {
      return curMileStoneTemplateId_;
    }

    public static final int ZONEMILESTONEINFO_FIELD_NUMBER = 17;
    private com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin zoneMilestoneInfo_;
    /**
     * <pre>
     * zone里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
     * @return Whether the zoneMilestoneInfo field is set.
     */
    @java.lang.Override
    public boolean hasZoneMilestoneInfo() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * zone里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
     * @return The zoneMilestoneInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin getZoneMilestoneInfo() {
      return zoneMilestoneInfo_ == null ? com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.getDefaultInstance() : zoneMilestoneInfo_;
    }
    /**
     * <pre>
     * zone里程碑数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.ZoneMilestoneInfoByLoginOrBuilder getZoneMilestoneInfoOrBuilder() {
      return zoneMilestoneInfo_ == null ? com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.getDefaultInstance() : zoneMilestoneInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isOk_);
      }
      for (int i = 0; i < heroList_.size(); i++) {
        output.writeInt32(5, heroList_.getInt(i));
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(9, getDevBuffs());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(10, getAdditionSys());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(11, getZoneAdditionSys());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(12, clanId_);
      }
      for (int i = 0; i < planeList_.size(); i++) {
        output.writeInt64(13, planeList_.getLong(i));
      }
      for (int i = 0; i < skynetMonsterList_.size(); i++) {
        output.writeInt64(15, skynetMonsterList_.getLong(i));
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(16, curMileStoneTemplateId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(17, getZoneMilestoneInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isOk_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < heroList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(heroList_.getInt(i));
        }
        size += dataSize;
        size += 1 * getHeroListList().size();
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, getDevBuffs());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, getAdditionSys());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, getZoneAdditionSys());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(12, clanId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < planeList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(planeList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getPlaneListList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < skynetMonsterList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(skynetMonsterList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getSkynetMonsterListList().size();
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(16, curMileStoneTemplateId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(17, getZoneMilestoneInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.PlayerLoginAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.PlayerLoginAns other = (com.yorha.proto.SsSceneDungeon.PlayerLoginAns) obj;

      if (hasIsOk() != other.hasIsOk()) return false;
      if (hasIsOk()) {
        if (getIsOk()
            != other.getIsOk()) return false;
      }
      if (!getHeroListList()
          .equals(other.getHeroListList())) return false;
      if (hasDevBuffs() != other.hasDevBuffs()) return false;
      if (hasDevBuffs()) {
        if (!getDevBuffs()
            .equals(other.getDevBuffs())) return false;
      }
      if (hasAdditionSys() != other.hasAdditionSys()) return false;
      if (hasAdditionSys()) {
        if (!getAdditionSys()
            .equals(other.getAdditionSys())) return false;
      }
      if (hasZoneAdditionSys() != other.hasZoneAdditionSys()) return false;
      if (hasZoneAdditionSys()) {
        if (!getZoneAdditionSys()
            .equals(other.getZoneAdditionSys())) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (!getPlaneListList()
          .equals(other.getPlaneListList())) return false;
      if (!getSkynetMonsterListList()
          .equals(other.getSkynetMonsterListList())) return false;
      if (hasCurMileStoneTemplateId() != other.hasCurMileStoneTemplateId()) return false;
      if (hasCurMileStoneTemplateId()) {
        if (getCurMileStoneTemplateId()
            != other.getCurMileStoneTemplateId()) return false;
      }
      if (hasZoneMilestoneInfo() != other.hasZoneMilestoneInfo()) return false;
      if (hasZoneMilestoneInfo()) {
        if (!getZoneMilestoneInfo()
            .equals(other.getZoneMilestoneInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsOk()) {
        hash = (37 * hash) + ISOK_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsOk());
      }
      if (getHeroListCount() > 0) {
        hash = (37 * hash) + HEROLIST_FIELD_NUMBER;
        hash = (53 * hash) + getHeroListList().hashCode();
      }
      if (hasDevBuffs()) {
        hash = (37 * hash) + DEVBUFFS_FIELD_NUMBER;
        hash = (53 * hash) + getDevBuffs().hashCode();
      }
      if (hasAdditionSys()) {
        hash = (37 * hash) + ADDITIONSYS_FIELD_NUMBER;
        hash = (53 * hash) + getAdditionSys().hashCode();
      }
      if (hasZoneAdditionSys()) {
        hash = (37 * hash) + ZONEADDITIONSYS_FIELD_NUMBER;
        hash = (53 * hash) + getZoneAdditionSys().hashCode();
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (getPlaneListCount() > 0) {
        hash = (37 * hash) + PLANELIST_FIELD_NUMBER;
        hash = (53 * hash) + getPlaneListList().hashCode();
      }
      if (getSkynetMonsterListCount() > 0) {
        hash = (37 * hash) + SKYNETMONSTERLIST_FIELD_NUMBER;
        hash = (53 * hash) + getSkynetMonsterListList().hashCode();
      }
      if (hasCurMileStoneTemplateId()) {
        hash = (37 * hash) + CURMILESTONETEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getCurMileStoneTemplateId();
      }
      if (hasZoneMilestoneInfo()) {
        hash = (37 * hash) + ZONEMILESTONEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getZoneMilestoneInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.PlayerLoginAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlayerLoginAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlayerLoginAns)
        com.yorha.proto.SsSceneDungeon.PlayerLoginAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.PlayerLoginAns.class, com.yorha.proto.SsSceneDungeon.PlayerLoginAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.PlayerLoginAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDevBuffsFieldBuilder();
          getAdditionSysFieldBuilder();
          getZoneAdditionSysFieldBuilder();
          getZoneMilestoneInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isOk_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        heroList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        if (devBuffsBuilder_ == null) {
          devBuffs_ = null;
        } else {
          devBuffsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (additionSysBuilder_ == null) {
          additionSys_ = null;
        } else {
          additionSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (zoneAdditionSysBuilder_ == null) {
          zoneAdditionSys_ = null;
        } else {
          zoneAdditionSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        planeList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000040);
        skynetMonsterList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000080);
        curMileStoneTemplateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        if (zoneMilestoneInfoBuilder_ == null) {
          zoneMilestoneInfo_ = null;
        } else {
          zoneMilestoneInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLoginAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLoginAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.PlayerLoginAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLoginAns build() {
        com.yorha.proto.SsSceneDungeon.PlayerLoginAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLoginAns buildPartial() {
        com.yorha.proto.SsSceneDungeon.PlayerLoginAns result = new com.yorha.proto.SsSceneDungeon.PlayerLoginAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isOk_ = isOk_;
          to_bitField0_ |= 0x00000001;
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          heroList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.heroList_ = heroList_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (devBuffsBuilder_ == null) {
            result.devBuffs_ = devBuffs_;
          } else {
            result.devBuffs_ = devBuffsBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (additionSysBuilder_ == null) {
            result.additionSys_ = additionSys_;
          } else {
            result.additionSys_ = additionSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (zoneAdditionSysBuilder_ == null) {
            result.zoneAdditionSys_ = zoneAdditionSys_;
          } else {
            result.zoneAdditionSys_ = zoneAdditionSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000010;
        }
        if (((bitField0_ & 0x00000040) != 0)) {
          planeList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.planeList_ = planeList_;
        if (((bitField0_ & 0x00000080) != 0)) {
          skynetMonsterList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.skynetMonsterList_ = skynetMonsterList_;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.curMileStoneTemplateId_ = curMileStoneTemplateId_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          if (zoneMilestoneInfoBuilder_ == null) {
            result.zoneMilestoneInfo_ = zoneMilestoneInfo_;
          } else {
            result.zoneMilestoneInfo_ = zoneMilestoneInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.PlayerLoginAns) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.PlayerLoginAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.PlayerLoginAns other) {
        if (other == com.yorha.proto.SsSceneDungeon.PlayerLoginAns.getDefaultInstance()) return this;
        if (other.hasIsOk()) {
          setIsOk(other.getIsOk());
        }
        if (!other.heroList_.isEmpty()) {
          if (heroList_.isEmpty()) {
            heroList_ = other.heroList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureHeroListIsMutable();
            heroList_.addAll(other.heroList_);
          }
          onChanged();
        }
        if (other.hasDevBuffs()) {
          mergeDevBuffs(other.getDevBuffs());
        }
        if (other.hasAdditionSys()) {
          mergeAdditionSys(other.getAdditionSys());
        }
        if (other.hasZoneAdditionSys()) {
          mergeZoneAdditionSys(other.getZoneAdditionSys());
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (!other.planeList_.isEmpty()) {
          if (planeList_.isEmpty()) {
            planeList_ = other.planeList_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensurePlaneListIsMutable();
            planeList_.addAll(other.planeList_);
          }
          onChanged();
        }
        if (!other.skynetMonsterList_.isEmpty()) {
          if (skynetMonsterList_.isEmpty()) {
            skynetMonsterList_ = other.skynetMonsterList_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureSkynetMonsterListIsMutable();
            skynetMonsterList_.addAll(other.skynetMonsterList_);
          }
          onChanged();
        }
        if (other.hasCurMileStoneTemplateId()) {
          setCurMileStoneTemplateId(other.getCurMileStoneTemplateId());
        }
        if (other.hasZoneMilestoneInfo()) {
          mergeZoneMilestoneInfo(other.getZoneMilestoneInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.PlayerLoginAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.PlayerLoginAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isOk_ ;
      /**
       * <code>optional bool isOk = 1;</code>
       * @return Whether the isOk field is set.
       */
      @java.lang.Override
      public boolean hasIsOk() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool isOk = 1;</code>
       * @return The isOk.
       */
      @java.lang.Override
      public boolean getIsOk() {
        return isOk_;
      }
      /**
       * <code>optional bool isOk = 1;</code>
       * @param value The isOk to set.
       * @return This builder for chaining.
       */
      public Builder setIsOk(boolean value) {
        bitField0_ |= 0x00000001;
        isOk_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isOk = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsOk() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isOk_ = false;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList heroList_ = emptyIntList();
      private void ensureHeroListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          heroList_ = mutableCopy(heroList_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       * 在行军中的所有英雄
       * </pre>
       *
       * <code>repeated int32 heroList = 5;</code>
       * @return A list containing the heroList.
       */
      public java.util.List<java.lang.Integer>
          getHeroListList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(heroList_) : heroList_;
      }
      /**
       * <pre>
       * 在行军中的所有英雄
       * </pre>
       *
       * <code>repeated int32 heroList = 5;</code>
       * @return The count of heroList.
       */
      public int getHeroListCount() {
        return heroList_.size();
      }
      /**
       * <pre>
       * 在行军中的所有英雄
       * </pre>
       *
       * <code>repeated int32 heroList = 5;</code>
       * @param index The index of the element to return.
       * @return The heroList at the given index.
       */
      public int getHeroList(int index) {
        return heroList_.getInt(index);
      }
      /**
       * <pre>
       * 在行军中的所有英雄
       * </pre>
       *
       * <code>repeated int32 heroList = 5;</code>
       * @param index The index to set the value at.
       * @param value The heroList to set.
       * @return This builder for chaining.
       */
      public Builder setHeroList(
          int index, int value) {
        ensureHeroListIsMutable();
        heroList_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 在行军中的所有英雄
       * </pre>
       *
       * <code>repeated int32 heroList = 5;</code>
       * @param value The heroList to add.
       * @return This builder for chaining.
       */
      public Builder addHeroList(int value) {
        ensureHeroListIsMutable();
        heroList_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 在行军中的所有英雄
       * </pre>
       *
       * <code>repeated int32 heroList = 5;</code>
       * @param values The heroList to add.
       * @return This builder for chaining.
       */
      public Builder addAllHeroList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureHeroListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, heroList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 在行军中的所有英雄
       * </pre>
       *
       * <code>repeated int32 heroList = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroList() {
        heroList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private com.yorha.proto.StructBattle.DevBuffSys devBuffs_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.DevBuffSys, com.yorha.proto.StructBattle.DevBuffSys.Builder, com.yorha.proto.StructBattle.DevBuffSysOrBuilder> devBuffsBuilder_;
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       * @return Whether the devBuffs field is set.
       */
      public boolean hasDevBuffs() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       * @return The devBuffs.
       */
      public com.yorha.proto.StructBattle.DevBuffSys getDevBuffs() {
        if (devBuffsBuilder_ == null) {
          return devBuffs_ == null ? com.yorha.proto.StructBattle.DevBuffSys.getDefaultInstance() : devBuffs_;
        } else {
          return devBuffsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       */
      public Builder setDevBuffs(com.yorha.proto.StructBattle.DevBuffSys value) {
        if (devBuffsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          devBuffs_ = value;
          onChanged();
        } else {
          devBuffsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       */
      public Builder setDevBuffs(
          com.yorha.proto.StructBattle.DevBuffSys.Builder builderForValue) {
        if (devBuffsBuilder_ == null) {
          devBuffs_ = builderForValue.build();
          onChanged();
        } else {
          devBuffsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       */
      public Builder mergeDevBuffs(com.yorha.proto.StructBattle.DevBuffSys value) {
        if (devBuffsBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              devBuffs_ != null &&
              devBuffs_ != com.yorha.proto.StructBattle.DevBuffSys.getDefaultInstance()) {
            devBuffs_ =
              com.yorha.proto.StructBattle.DevBuffSys.newBuilder(devBuffs_).mergeFrom(value).buildPartial();
          } else {
            devBuffs_ = value;
          }
          onChanged();
        } else {
          devBuffsBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       */
      public Builder clearDevBuffs() {
        if (devBuffsBuilder_ == null) {
          devBuffs_ = null;
          onChanged();
        } else {
          devBuffsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       */
      public com.yorha.proto.StructBattle.DevBuffSys.Builder getDevBuffsBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getDevBuffsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       */
      public com.yorha.proto.StructBattle.DevBuffSysOrBuilder getDevBuffsOrBuilder() {
        if (devBuffsBuilder_ != null) {
          return devBuffsBuilder_.getMessageOrBuilder();
        } else {
          return devBuffs_ == null ?
              com.yorha.proto.StructBattle.DevBuffSys.getDefaultInstance() : devBuffs_;
        }
      }
      /**
       * <pre>
       * scenePlayer上的devBuff，拿过去给player拼出来DevBuffView
       * </pre>
       *
       * <code>optional .com.yorha.proto.DevBuffSys devBuffs = 9;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.DevBuffSys, com.yorha.proto.StructBattle.DevBuffSys.Builder, com.yorha.proto.StructBattle.DevBuffSysOrBuilder> 
          getDevBuffsFieldBuilder() {
        if (devBuffsBuilder_ == null) {
          devBuffsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattle.DevBuffSys, com.yorha.proto.StructBattle.DevBuffSys.Builder, com.yorha.proto.StructBattle.DevBuffSysOrBuilder>(
                  getDevBuffs(),
                  getParentForChildren(),
                  isClean());
          devBuffs_ = null;
        }
        return devBuffsBuilder_;
      }

      private com.yorha.proto.Struct.AdditionSys additionSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.AdditionSys, com.yorha.proto.Struct.AdditionSys.Builder, com.yorha.proto.Struct.AdditionSysOrBuilder> additionSysBuilder_;
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       * @return Whether the additionSys field is set.
       */
      public boolean hasAdditionSys() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       * @return The additionSys.
       */
      public com.yorha.proto.Struct.AdditionSys getAdditionSys() {
        if (additionSysBuilder_ == null) {
          return additionSys_ == null ? com.yorha.proto.Struct.AdditionSys.getDefaultInstance() : additionSys_;
        } else {
          return additionSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       */
      public Builder setAdditionSys(com.yorha.proto.Struct.AdditionSys value) {
        if (additionSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          additionSys_ = value;
          onChanged();
        } else {
          additionSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       */
      public Builder setAdditionSys(
          com.yorha.proto.Struct.AdditionSys.Builder builderForValue) {
        if (additionSysBuilder_ == null) {
          additionSys_ = builderForValue.build();
          onChanged();
        } else {
          additionSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       */
      public Builder mergeAdditionSys(com.yorha.proto.Struct.AdditionSys value) {
        if (additionSysBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              additionSys_ != null &&
              additionSys_ != com.yorha.proto.Struct.AdditionSys.getDefaultInstance()) {
            additionSys_ =
              com.yorha.proto.Struct.AdditionSys.newBuilder(additionSys_).mergeFrom(value).buildPartial();
          } else {
            additionSys_ = value;
          }
          onChanged();
        } else {
          additionSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       */
      public Builder clearAdditionSys() {
        if (additionSysBuilder_ == null) {
          additionSys_ = null;
          onChanged();
        } else {
          additionSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       */
      public com.yorha.proto.Struct.AdditionSys.Builder getAdditionSysBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getAdditionSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       */
      public com.yorha.proto.Struct.AdditionSysOrBuilder getAdditionSysOrBuilder() {
        if (additionSysBuilder_ != null) {
          return additionSysBuilder_.getMessageOrBuilder();
        } else {
          return additionSys_ == null ?
              com.yorha.proto.Struct.AdditionSys.getDefaultInstance() : additionSys_;
        }
      }
      /**
       * <pre>
       * scenePlayer上的addition，拿过去给player拼出来AdditionView
       * </pre>
       *
       * <code>optional .com.yorha.proto.AdditionSys additionSys = 10;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.AdditionSys, com.yorha.proto.Struct.AdditionSys.Builder, com.yorha.proto.Struct.AdditionSysOrBuilder> 
          getAdditionSysFieldBuilder() {
        if (additionSysBuilder_ == null) {
          additionSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.AdditionSys, com.yorha.proto.Struct.AdditionSys.Builder, com.yorha.proto.Struct.AdditionSysOrBuilder>(
                  getAdditionSys(),
                  getParentForChildren(),
                  isClean());
          additionSys_ = null;
        }
        return additionSysBuilder_;
      }

      private com.yorha.proto.Struct.AdditionSys zoneAdditionSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.AdditionSys, com.yorha.proto.Struct.AdditionSys.Builder, com.yorha.proto.Struct.AdditionSysOrBuilder> zoneAdditionSysBuilder_;
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       * @return Whether the zoneAdditionSys field is set.
       */
      public boolean hasZoneAdditionSys() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       * @return The zoneAdditionSys.
       */
      public com.yorha.proto.Struct.AdditionSys getZoneAdditionSys() {
        if (zoneAdditionSysBuilder_ == null) {
          return zoneAdditionSys_ == null ? com.yorha.proto.Struct.AdditionSys.getDefaultInstance() : zoneAdditionSys_;
        } else {
          return zoneAdditionSysBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       */
      public Builder setZoneAdditionSys(com.yorha.proto.Struct.AdditionSys value) {
        if (zoneAdditionSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          zoneAdditionSys_ = value;
          onChanged();
        } else {
          zoneAdditionSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       */
      public Builder setZoneAdditionSys(
          com.yorha.proto.Struct.AdditionSys.Builder builderForValue) {
        if (zoneAdditionSysBuilder_ == null) {
          zoneAdditionSys_ = builderForValue.build();
          onChanged();
        } else {
          zoneAdditionSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       */
      public Builder mergeZoneAdditionSys(com.yorha.proto.Struct.AdditionSys value) {
        if (zoneAdditionSysBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              zoneAdditionSys_ != null &&
              zoneAdditionSys_ != com.yorha.proto.Struct.AdditionSys.getDefaultInstance()) {
            zoneAdditionSys_ =
              com.yorha.proto.Struct.AdditionSys.newBuilder(zoneAdditionSys_).mergeFrom(value).buildPartial();
          } else {
            zoneAdditionSys_ = value;
          }
          onChanged();
        } else {
          zoneAdditionSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       */
      public Builder clearZoneAdditionSys() {
        if (zoneAdditionSysBuilder_ == null) {
          zoneAdditionSys_ = null;
          onChanged();
        } else {
          zoneAdditionSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       */
      public com.yorha.proto.Struct.AdditionSys.Builder getZoneAdditionSysBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getZoneAdditionSysFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       */
      public com.yorha.proto.Struct.AdditionSysOrBuilder getZoneAdditionSysOrBuilder() {
        if (zoneAdditionSysBuilder_ != null) {
          return zoneAdditionSysBuilder_.getMessageOrBuilder();
        } else {
          return zoneAdditionSys_ == null ?
              com.yorha.proto.Struct.AdditionSys.getDefaultInstance() : zoneAdditionSys_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSys zoneAdditionSys = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.AdditionSys, com.yorha.proto.Struct.AdditionSys.Builder, com.yorha.proto.Struct.AdditionSysOrBuilder> 
          getZoneAdditionSysFieldBuilder() {
        if (zoneAdditionSysBuilder_ == null) {
          zoneAdditionSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.AdditionSys, com.yorha.proto.Struct.AdditionSys.Builder, com.yorha.proto.Struct.AdditionSysOrBuilder>(
                  getZoneAdditionSys(),
                  getParentForChildren(),
                  isClean());
          zoneAdditionSys_ = null;
        }
        return zoneAdditionSysBuilder_;
      }

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 12;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional int64 clanId = 12;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 12;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000020;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList planeList_ = emptyLongList();
      private void ensurePlaneListIsMutable() {
        if (!((bitField0_ & 0x00000040) != 0)) {
          planeList_ = mutableCopy(planeList_);
          bitField0_ |= 0x00000040;
         }
      }
      /**
       * <pre>
       * 大世界上的飞机列表
       * </pre>
       *
       * <code>repeated int64 planeList = 13;</code>
       * @return A list containing the planeList.
       */
      public java.util.List<java.lang.Long>
          getPlaneListList() {
        return ((bitField0_ & 0x00000040) != 0) ?
                 java.util.Collections.unmodifiableList(planeList_) : planeList_;
      }
      /**
       * <pre>
       * 大世界上的飞机列表
       * </pre>
       *
       * <code>repeated int64 planeList = 13;</code>
       * @return The count of planeList.
       */
      public int getPlaneListCount() {
        return planeList_.size();
      }
      /**
       * <pre>
       * 大世界上的飞机列表
       * </pre>
       *
       * <code>repeated int64 planeList = 13;</code>
       * @param index The index of the element to return.
       * @return The planeList at the given index.
       */
      public long getPlaneList(int index) {
        return planeList_.getLong(index);
      }
      /**
       * <pre>
       * 大世界上的飞机列表
       * </pre>
       *
       * <code>repeated int64 planeList = 13;</code>
       * @param index The index to set the value at.
       * @param value The planeList to set.
       * @return This builder for chaining.
       */
      public Builder setPlaneList(
          int index, long value) {
        ensurePlaneListIsMutable();
        planeList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 大世界上的飞机列表
       * </pre>
       *
       * <code>repeated int64 planeList = 13;</code>
       * @param value The planeList to add.
       * @return This builder for chaining.
       */
      public Builder addPlaneList(long value) {
        ensurePlaneListIsMutable();
        planeList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 大世界上的飞机列表
       * </pre>
       *
       * <code>repeated int64 planeList = 13;</code>
       * @param values The planeList to add.
       * @return This builder for chaining.
       */
      public Builder addAllPlaneList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensurePlaneListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, planeList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 大世界上的飞机列表
       * </pre>
       *
       * <code>repeated int64 planeList = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlaneList() {
        planeList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList skynetMonsterList_ = emptyLongList();
      private void ensureSkynetMonsterListIsMutable() {
        if (!((bitField0_ & 0x00000080) != 0)) {
          skynetMonsterList_ = mutableCopy(skynetMonsterList_);
          bitField0_ |= 0x00000080;
         }
      }
      /**
       * <pre>
       * 玩家关注的天网野怪确实存在的列表
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 15;</code>
       * @return A list containing the skynetMonsterList.
       */
      public java.util.List<java.lang.Long>
          getSkynetMonsterListList() {
        return ((bitField0_ & 0x00000080) != 0) ?
                 java.util.Collections.unmodifiableList(skynetMonsterList_) : skynetMonsterList_;
      }
      /**
       * <pre>
       * 玩家关注的天网野怪确实存在的列表
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 15;</code>
       * @return The count of skynetMonsterList.
       */
      public int getSkynetMonsterListCount() {
        return skynetMonsterList_.size();
      }
      /**
       * <pre>
       * 玩家关注的天网野怪确实存在的列表
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 15;</code>
       * @param index The index of the element to return.
       * @return The skynetMonsterList at the given index.
       */
      public long getSkynetMonsterList(int index) {
        return skynetMonsterList_.getLong(index);
      }
      /**
       * <pre>
       * 玩家关注的天网野怪确实存在的列表
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 15;</code>
       * @param index The index to set the value at.
       * @param value The skynetMonsterList to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetMonsterList(
          int index, long value) {
        ensureSkynetMonsterListIsMutable();
        skynetMonsterList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家关注的天网野怪确实存在的列表
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 15;</code>
       * @param value The skynetMonsterList to add.
       * @return This builder for chaining.
       */
      public Builder addSkynetMonsterList(long value) {
        ensureSkynetMonsterListIsMutable();
        skynetMonsterList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家关注的天网野怪确实存在的列表
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 15;</code>
       * @param values The skynetMonsterList to add.
       * @return This builder for chaining.
       */
      public Builder addAllSkynetMonsterList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureSkynetMonsterListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, skynetMonsterList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家关注的天网野怪确实存在的列表
       * </pre>
       *
       * <code>repeated int64 skynetMonsterList = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetMonsterList() {
        skynetMonsterList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }

      private int curMileStoneTemplateId_ ;
      /**
       * <pre>
       * 里程碑任务模板id
       * </pre>
       *
       * <code>optional int32 curMileStoneTemplateId = 16;</code>
       * @return Whether the curMileStoneTemplateId field is set.
       */
      @java.lang.Override
      public boolean hasCurMileStoneTemplateId() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 里程碑任务模板id
       * </pre>
       *
       * <code>optional int32 curMileStoneTemplateId = 16;</code>
       * @return The curMileStoneTemplateId.
       */
      @java.lang.Override
      public int getCurMileStoneTemplateId() {
        return curMileStoneTemplateId_;
      }
      /**
       * <pre>
       * 里程碑任务模板id
       * </pre>
       *
       * <code>optional int32 curMileStoneTemplateId = 16;</code>
       * @param value The curMileStoneTemplateId to set.
       * @return This builder for chaining.
       */
      public Builder setCurMileStoneTemplateId(int value) {
        bitField0_ |= 0x00000100;
        curMileStoneTemplateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 里程碑任务模板id
       * </pre>
       *
       * <code>optional int32 curMileStoneTemplateId = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurMileStoneTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        curMileStoneTemplateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin zoneMilestoneInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin, com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.Builder, com.yorha.proto.StructMsg.ZoneMilestoneInfoByLoginOrBuilder> zoneMilestoneInfoBuilder_;
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       * @return Whether the zoneMilestoneInfo field is set.
       */
      public boolean hasZoneMilestoneInfo() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       * @return The zoneMilestoneInfo.
       */
      public com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin getZoneMilestoneInfo() {
        if (zoneMilestoneInfoBuilder_ == null) {
          return zoneMilestoneInfo_ == null ? com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.getDefaultInstance() : zoneMilestoneInfo_;
        } else {
          return zoneMilestoneInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       */
      public Builder setZoneMilestoneInfo(com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin value) {
        if (zoneMilestoneInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          zoneMilestoneInfo_ = value;
          onChanged();
        } else {
          zoneMilestoneInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       */
      public Builder setZoneMilestoneInfo(
          com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.Builder builderForValue) {
        if (zoneMilestoneInfoBuilder_ == null) {
          zoneMilestoneInfo_ = builderForValue.build();
          onChanged();
        } else {
          zoneMilestoneInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       */
      public Builder mergeZoneMilestoneInfo(com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin value) {
        if (zoneMilestoneInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000200) != 0) &&
              zoneMilestoneInfo_ != null &&
              zoneMilestoneInfo_ != com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.getDefaultInstance()) {
            zoneMilestoneInfo_ =
              com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.newBuilder(zoneMilestoneInfo_).mergeFrom(value).buildPartial();
          } else {
            zoneMilestoneInfo_ = value;
          }
          onChanged();
        } else {
          zoneMilestoneInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       */
      public Builder clearZoneMilestoneInfo() {
        if (zoneMilestoneInfoBuilder_ == null) {
          zoneMilestoneInfo_ = null;
          onChanged();
        } else {
          zoneMilestoneInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       */
      public com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.Builder getZoneMilestoneInfoBuilder() {
        bitField0_ |= 0x00000200;
        onChanged();
        return getZoneMilestoneInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       */
      public com.yorha.proto.StructMsg.ZoneMilestoneInfoByLoginOrBuilder getZoneMilestoneInfoOrBuilder() {
        if (zoneMilestoneInfoBuilder_ != null) {
          return zoneMilestoneInfoBuilder_.getMessageOrBuilder();
        } else {
          return zoneMilestoneInfo_ == null ?
              com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.getDefaultInstance() : zoneMilestoneInfo_;
        }
      }
      /**
       * <pre>
       * zone里程碑数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneMilestoneInfoByLogin zoneMilestoneInfo = 17;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin, com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.Builder, com.yorha.proto.StructMsg.ZoneMilestoneInfoByLoginOrBuilder> 
          getZoneMilestoneInfoFieldBuilder() {
        if (zoneMilestoneInfoBuilder_ == null) {
          zoneMilestoneInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin, com.yorha.proto.StructMsg.ZoneMilestoneInfoByLogin.Builder, com.yorha.proto.StructMsg.ZoneMilestoneInfoByLoginOrBuilder>(
                  getZoneMilestoneInfo(),
                  getParentForChildren(),
                  isClean());
          zoneMilestoneInfo_ = null;
        }
        return zoneMilestoneInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlayerLoginAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlayerLoginAns)
    private static final com.yorha.proto.SsSceneDungeon.PlayerLoginAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.PlayerLoginAns();
    }

    public static com.yorha.proto.SsSceneDungeon.PlayerLoginAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlayerLoginAns>
        PARSER = new com.google.protobuf.AbstractParser<PlayerLoginAns>() {
      @java.lang.Override
      public PlayerLoginAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlayerLoginAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlayerLoginAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlayerLoginAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.PlayerLoginAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlayerLogoutAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlayerLogoutAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlayerLogoutAsk}
   */
  public static final class PlayerLogoutAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlayerLogoutAsk)
      PlayerLogoutAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlayerLogoutAsk.newBuilder() to construct.
    private PlayerLogoutAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlayerLogoutAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlayerLogoutAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlayerLogoutAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk.class, com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk other = (com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlayerLogoutAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlayerLogoutAsk)
        com.yorha.proto.SsSceneDungeon.PlayerLogoutAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk.class, com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk build() {
        com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk buildPartial() {
        com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk result = new com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk other) {
        if (other == com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlayerLogoutAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlayerLogoutAsk)
    private static final com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk();
    }

    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlayerLogoutAsk>
        PARSER = new com.google.protobuf.AbstractParser<PlayerLogoutAsk>() {
      @java.lang.Override
      public PlayerLogoutAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlayerLogoutAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlayerLogoutAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlayerLogoutAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.PlayerLogoutAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlayerLogoutAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlayerLogoutAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool needSaveScene = 1;</code>
     * @return Whether the needSaveScene field is set.
     */
    boolean hasNeedSaveScene();
    /**
     * <code>optional bool needSaveScene = 1;</code>
     * @return The needSaveScene.
     */
    boolean getNeedSaveScene();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlayerLogoutAns}
   */
  public static final class PlayerLogoutAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlayerLogoutAns)
      PlayerLogoutAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlayerLogoutAns.newBuilder() to construct.
    private PlayerLogoutAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlayerLogoutAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlayerLogoutAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlayerLogoutAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              needSaveScene_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.PlayerLogoutAns.class, com.yorha.proto.SsSceneDungeon.PlayerLogoutAns.Builder.class);
    }

    private int bitField0_;
    public static final int NEEDSAVESCENE_FIELD_NUMBER = 1;
    private boolean needSaveScene_;
    /**
     * <code>optional bool needSaveScene = 1;</code>
     * @return Whether the needSaveScene field is set.
     */
    @java.lang.Override
    public boolean hasNeedSaveScene() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool needSaveScene = 1;</code>
     * @return The needSaveScene.
     */
    @java.lang.Override
    public boolean getNeedSaveScene() {
      return needSaveScene_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, needSaveScene_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, needSaveScene_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.PlayerLogoutAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.PlayerLogoutAns other = (com.yorha.proto.SsSceneDungeon.PlayerLogoutAns) obj;

      if (hasNeedSaveScene() != other.hasNeedSaveScene()) return false;
      if (hasNeedSaveScene()) {
        if (getNeedSaveScene()
            != other.getNeedSaveScene()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNeedSaveScene()) {
        hash = (37 * hash) + NEEDSAVESCENE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getNeedSaveScene());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.PlayerLogoutAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlayerLogoutAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlayerLogoutAns)
        com.yorha.proto.SsSceneDungeon.PlayerLogoutAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.PlayerLogoutAns.class, com.yorha.proto.SsSceneDungeon.PlayerLogoutAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.PlayerLogoutAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        needSaveScene_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PlayerLogoutAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLogoutAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.PlayerLogoutAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLogoutAns build() {
        com.yorha.proto.SsSceneDungeon.PlayerLogoutAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PlayerLogoutAns buildPartial() {
        com.yorha.proto.SsSceneDungeon.PlayerLogoutAns result = new com.yorha.proto.SsSceneDungeon.PlayerLogoutAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.needSaveScene_ = needSaveScene_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.PlayerLogoutAns) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.PlayerLogoutAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.PlayerLogoutAns other) {
        if (other == com.yorha.proto.SsSceneDungeon.PlayerLogoutAns.getDefaultInstance()) return this;
        if (other.hasNeedSaveScene()) {
          setNeedSaveScene(other.getNeedSaveScene());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.PlayerLogoutAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.PlayerLogoutAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean needSaveScene_ ;
      /**
       * <code>optional bool needSaveScene = 1;</code>
       * @return Whether the needSaveScene field is set.
       */
      @java.lang.Override
      public boolean hasNeedSaveScene() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool needSaveScene = 1;</code>
       * @return The needSaveScene.
       */
      @java.lang.Override
      public boolean getNeedSaveScene() {
        return needSaveScene_;
      }
      /**
       * <code>optional bool needSaveScene = 1;</code>
       * @param value The needSaveScene to set.
       * @return This builder for chaining.
       */
      public Builder setNeedSaveScene(boolean value) {
        bitField0_ |= 0x00000001;
        needSaveScene_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool needSaveScene = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNeedSaveScene() {
        bitField0_ = (bitField0_ & ~0x00000001);
        needSaveScene_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlayerLogoutAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlayerLogoutAns)
    private static final com.yorha.proto.SsSceneDungeon.PlayerLogoutAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.PlayerLogoutAns();
    }

    public static com.yorha.proto.SsSceneDungeon.PlayerLogoutAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlayerLogoutAns>
        PARSER = new com.google.protobuf.AbstractParser<PlayerLogoutAns>() {
      @java.lang.Override
      public PlayerLogoutAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlayerLogoutAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlayerLogoutAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlayerLogoutAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.PlayerLogoutAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PerformActionAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PerformActionAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 actionId = 1;</code>
     * @return Whether the actionId field is set.
     */
    boolean hasActionId();
    /**
     * <code>optional int32 actionId = 1;</code>
     * @return The actionId.
     */
    int getActionId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PerformActionAsk}
   */
  public static final class PerformActionAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PerformActionAsk)
      PerformActionAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PerformActionAsk.newBuilder() to construct.
    private PerformActionAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PerformActionAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PerformActionAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PerformActionAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              actionId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.PerformActionAsk.class, com.yorha.proto.SsSceneDungeon.PerformActionAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ACTIONID_FIELD_NUMBER = 1;
    private int actionId_;
    /**
     * <code>optional int32 actionId = 1;</code>
     * @return Whether the actionId field is set.
     */
    @java.lang.Override
    public boolean hasActionId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 actionId = 1;</code>
     * @return The actionId.
     */
    @java.lang.Override
    public int getActionId() {
      return actionId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, actionId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, actionId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.PerformActionAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.PerformActionAsk other = (com.yorha.proto.SsSceneDungeon.PerformActionAsk) obj;

      if (hasActionId() != other.hasActionId()) return false;
      if (hasActionId()) {
        if (getActionId()
            != other.getActionId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActionId()) {
        hash = (37 * hash) + ACTIONID_FIELD_NUMBER;
        hash = (53 * hash) + getActionId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.PerformActionAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PerformActionAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PerformActionAsk)
        com.yorha.proto.SsSceneDungeon.PerformActionAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.PerformActionAsk.class, com.yorha.proto.SsSceneDungeon.PerformActionAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.PerformActionAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actionId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PerformActionAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.PerformActionAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PerformActionAsk build() {
        com.yorha.proto.SsSceneDungeon.PerformActionAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PerformActionAsk buildPartial() {
        com.yorha.proto.SsSceneDungeon.PerformActionAsk result = new com.yorha.proto.SsSceneDungeon.PerformActionAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.actionId_ = actionId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.PerformActionAsk) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.PerformActionAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.PerformActionAsk other) {
        if (other == com.yorha.proto.SsSceneDungeon.PerformActionAsk.getDefaultInstance()) return this;
        if (other.hasActionId()) {
          setActionId(other.getActionId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.PerformActionAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.PerformActionAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int actionId_ ;
      /**
       * <code>optional int32 actionId = 1;</code>
       * @return Whether the actionId field is set.
       */
      @java.lang.Override
      public boolean hasActionId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 actionId = 1;</code>
       * @return The actionId.
       */
      @java.lang.Override
      public int getActionId() {
        return actionId_;
      }
      /**
       * <code>optional int32 actionId = 1;</code>
       * @param value The actionId to set.
       * @return This builder for chaining.
       */
      public Builder setActionId(int value) {
        bitField0_ |= 0x00000001;
        actionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 actionId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActionId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        actionId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PerformActionAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PerformActionAsk)
    private static final com.yorha.proto.SsSceneDungeon.PerformActionAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.PerformActionAsk();
    }

    public static com.yorha.proto.SsSceneDungeon.PerformActionAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PerformActionAsk>
        PARSER = new com.google.protobuf.AbstractParser<PerformActionAsk>() {
      @java.lang.Override
      public PerformActionAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PerformActionAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PerformActionAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PerformActionAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.PerformActionAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PerformActionAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PerformActionAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.PerformActionAns}
   */
  public static final class PerformActionAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PerformActionAns)
      PerformActionAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PerformActionAns.newBuilder() to construct.
    private PerformActionAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PerformActionAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PerformActionAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PerformActionAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneDungeon.PerformActionAns.class, com.yorha.proto.SsSceneDungeon.PerformActionAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneDungeon.PerformActionAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneDungeon.PerformActionAns other = (com.yorha.proto.SsSceneDungeon.PerformActionAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneDungeon.PerformActionAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneDungeon.PerformActionAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PerformActionAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PerformActionAns)
        com.yorha.proto.SsSceneDungeon.PerformActionAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneDungeon.PerformActionAns.class, com.yorha.proto.SsSceneDungeon.PerformActionAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneDungeon.PerformActionAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneDungeon.internal_static_com_yorha_proto_PerformActionAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PerformActionAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneDungeon.PerformActionAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PerformActionAns build() {
        com.yorha.proto.SsSceneDungeon.PerformActionAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneDungeon.PerformActionAns buildPartial() {
        com.yorha.proto.SsSceneDungeon.PerformActionAns result = new com.yorha.proto.SsSceneDungeon.PerformActionAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneDungeon.PerformActionAns) {
          return mergeFrom((com.yorha.proto.SsSceneDungeon.PerformActionAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneDungeon.PerformActionAns other) {
        if (other == com.yorha.proto.SsSceneDungeon.PerformActionAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneDungeon.PerformActionAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneDungeon.PerformActionAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PerformActionAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PerformActionAns)
    private static final com.yorha.proto.SsSceneDungeon.PerformActionAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneDungeon.PerformActionAns();
    }

    public static com.yorha.proto.SsSceneDungeon.PerformActionAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PerformActionAns>
        PARSER = new com.google.protobuf.AbstractParser<PerformActionAns>() {
      @java.lang.Override
      public PerformActionAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PerformActionAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PerformActionAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PerformActionAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneDungeon.PerformActionAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateDungeonAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateDungeonAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DungeonPlayerData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DungeonPlayerData_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DungeonPlayerData_ArmyEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DungeonPlayerData_ArmyEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateDungeonAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateDungeonAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_EnterDungeonAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_EnterDungeonAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_EnterDungeonAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_EnterDungeonAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_LeaveDungeonAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_LeaveDungeonAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_LeaveDungeonAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_LeaveDungeonAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlayerLoginAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlayerLoginAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlayerLoginAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlayerLoginAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlayerLogoutAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlayerLogoutAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlayerLogoutAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlayerLogoutAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PerformActionAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PerformActionAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PerformActionAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PerformActionAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)ss_proto/gen/scene/ss_scene_dungeon.pr" +
      "oto\022\017com.yorha.proto\032%ss_proto/gen/commo" +
      "n/common_enum.proto\032$ss_proto/gen/common" +
      "/common_msg.proto\032 ss_proto/gen/common/s" +
      "truct.proto\032\'ss_proto/gen/common/struct_" +
      "battle.proto\032$ss_proto/gen/common/struct" +
      "_msg.proto\032\'ss_proto/gen/common/struct_p" +
      "layer.proto\032 ss_proto/gen/player/player." +
      "proto\"k\n\020CreateDungeonAsk\022*\n\004type\030\001 \001(\0162" +
      "\034.com.yorha.proto.DungeonType\022\021\n\tdungeon" +
      "Id\030\002 \001(\005\022\030\n\020allowEnterPlayer\030\003 \003(\003\"\331\001\n\021D" +
      "ungeonPlayerData\022,\n\004info\030\001 \001(\0132\036.com.yor" +
      "ha.proto.DungeonPlayer\022:\n\004army\030\002 \003(\0132,.c" +
      "om.yorha.proto.DungeonPlayerData.ArmyEnt" +
      "ry\022\025\n\rmainCityLevel\030\003 \001(\005\032C\n\tArmyEntry\022\013" +
      "\n\003key\030\001 \001(\005\022%\n\005value\030\002 \001(\0132\026.com.yorha.p" +
      "roto.Troop:\0028\001\"\022\n\020CreateDungeonAns\"\264\001\n\017E" +
      "nterDungeonAsk\022\020\n\010playerId\030\001 \001(\003\0221\n\nsess" +
      "ionRef\030\002 \001(\0132\035.com.yorha.proto.ActorRefD" +
      "ata\022*\n\004type\030\003 \001(\0162\034.com.yorha.proto.Dung" +
      "eonType\0220\n\004data\030\004 \001(\0132\".com.yorha.proto." +
      "DungeonPlayerData\"\021\n\017EnterDungeonAns\"#\n\017" +
      "LeaveDungeonAsk\022\020\n\010playerId\030\001 \001(\003\"\021\n\017Lea" +
      "veDungeonAns\"\245\003\n\016PlayerLoginAsk\022\020\n\010playe" +
      "rId\030\001 \001(\003\0221\n\nsessionRef\030\002 \001(\0132\035.com.yorh" +
      "a.proto.ActorRefData\022\020\n\010isNewbie\030\003 \001(\010\022\030" +
      "\n\020lastLoginOutTsMs\030\007 \001(\003\0227\n\tbuildInfo\030\010 " +
      "\001(\0132$.com.yorha.proto.SceneInnerBuildInf" +
      "o\022\016\n\006openId\030\t \001(\t\022\024\n\014intlNtfToken\030\n \001(\t\022" +
      "+\n\010language\030\013 \001(\0162\031.com.yorha.proto.Lang" +
      "uage\022\031\n\021skynetMonsterList\030\014 \003(\003\0221\n\013dunge" +
      "onType\030\r \001(\0162\034.com.yorha.proto.DungeonTy" +
      "pe\022H\n\023playerMilestoneInfo\030\020 \001(\0132+.com.yo" +
      "rha.proto.PlayerMilestoneInfoByLogin\"\355\002\n" +
      "\016PlayerLoginAns\022\014\n\004isOk\030\001 \001(\010\022\020\n\010heroLis" +
      "t\030\005 \003(\005\022-\n\010devBuffs\030\t \001(\0132\033.com.yorha.pr" +
      "oto.DevBuffSys\0221\n\013additionSys\030\n \001(\0132\034.co" +
      "m.yorha.proto.AdditionSys\0225\n\017zoneAdditio" +
      "nSys\030\013 \001(\0132\034.com.yorha.proto.AdditionSys" +
      "\022\016\n\006clanId\030\014 \001(\003\022\021\n\tplaneList\030\r \003(\003\022\031\n\021s" +
      "kynetMonsterList\030\017 \003(\003\022\036\n\026curMileStoneTe" +
      "mplateId\030\020 \001(\005\022D\n\021zoneMilestoneInfo\030\021 \001(" +
      "\0132).com.yorha.proto.ZoneMilestoneInfoByL" +
      "ogin\"#\n\017PlayerLogoutAsk\022\020\n\010playerId\030\001 \001(" +
      "\003\"(\n\017PlayerLogoutAns\022\025\n\rneedSaveScene\030\001 " +
      "\001(\010\"$\n\020PerformActionAsk\022\020\n\010actionId\030\001 \001(" +
      "\005\"\022\n\020PerformActionAnsB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructBattle.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
          com.yorha.proto.StructPlayer.getDescriptor(),
          com.yorha.proto.Player.getDescriptor(),
        });
    internal_static_com_yorha_proto_CreateDungeonAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_CreateDungeonAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateDungeonAsk_descriptor,
        new java.lang.String[] { "Type", "DungeonId", "AllowEnterPlayer", });
    internal_static_com_yorha_proto_DungeonPlayerData_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_DungeonPlayerData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DungeonPlayerData_descriptor,
        new java.lang.String[] { "Info", "Army", "MainCityLevel", });
    internal_static_com_yorha_proto_DungeonPlayerData_ArmyEntry_descriptor =
      internal_static_com_yorha_proto_DungeonPlayerData_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_DungeonPlayerData_ArmyEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DungeonPlayerData_ArmyEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_CreateDungeonAns_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_CreateDungeonAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateDungeonAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_EnterDungeonAsk_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_EnterDungeonAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_EnterDungeonAsk_descriptor,
        new java.lang.String[] { "PlayerId", "SessionRef", "Type", "Data", });
    internal_static_com_yorha_proto_EnterDungeonAns_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_EnterDungeonAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_EnterDungeonAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_LeaveDungeonAsk_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_LeaveDungeonAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_LeaveDungeonAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_LeaveDungeonAns_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_LeaveDungeonAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_LeaveDungeonAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_PlayerLoginAsk_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_PlayerLoginAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlayerLoginAsk_descriptor,
        new java.lang.String[] { "PlayerId", "SessionRef", "IsNewbie", "LastLoginOutTsMs", "BuildInfo", "OpenId", "IntlNtfToken", "Language", "SkynetMonsterList", "DungeonType", "PlayerMilestoneInfo", });
    internal_static_com_yorha_proto_PlayerLoginAns_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_PlayerLoginAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlayerLoginAns_descriptor,
        new java.lang.String[] { "IsOk", "HeroList", "DevBuffs", "AdditionSys", "ZoneAdditionSys", "ClanId", "PlaneList", "SkynetMonsterList", "CurMileStoneTemplateId", "ZoneMilestoneInfo", });
    internal_static_com_yorha_proto_PlayerLogoutAsk_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_PlayerLogoutAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlayerLogoutAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_PlayerLogoutAns_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_PlayerLogoutAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlayerLogoutAns_descriptor,
        new java.lang.String[] { "NeedSaveScene", });
    internal_static_com_yorha_proto_PerformActionAsk_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_PerformActionAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PerformActionAsk_descriptor,
        new java.lang.String[] { "ActionId", });
    internal_static_com_yorha_proto_PerformActionAns_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_PerformActionAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PerformActionAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructBattle.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
    com.yorha.proto.StructPlayer.getDescriptor();
    com.yorha.proto.Player.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

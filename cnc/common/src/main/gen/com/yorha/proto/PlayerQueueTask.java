// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_queue_task.proto

package com.yorha.proto;

public final class PlayerQueueTask {
  private PlayerQueueTask() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_SpeedQueueTask_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SpeedQueueTask_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return Whether the queueType field is set.
     */
    boolean hasQueueType();
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return The queueType.
     */
    com.yorha.proto.CommonEnum.QueueTaskType getQueueType();

    /**
     * <code>optional int64 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    boolean hasTaskId();
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return The taskId.
     */
    long getTaskId();

    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     * @return Whether the itemList field is set.
     */
    boolean hasItemList();
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     * @return The itemList.
     */
    com.yorha.proto.StructPB.ItemListPB getItemList();
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     */
    com.yorha.proto.StructPB.ItemListPBOrBuilder getItemListOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SpeedQueueTask_C2S}
   */
  public static final class Player_SpeedQueueTask_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SpeedQueueTask_C2S)
      Player_SpeedQueueTask_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SpeedQueueTask_C2S.newBuilder() to construct.
    private Player_SpeedQueueTask_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SpeedQueueTask_C2S() {
      queueType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SpeedQueueTask_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SpeedQueueTask_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.QueueTaskType value = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                queueType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              taskId_ = input.readInt64();
              break;
            }
            case 26: {
              com.yorha.proto.StructPB.ItemListPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = itemList_.toBuilder();
              }
              itemList_ = input.readMessage(com.yorha.proto.StructPB.ItemListPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(itemList_);
                itemList_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S.class, com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int QUEUETYPE_FIELD_NUMBER = 1;
    private int queueType_;
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return Whether the queueType field is set.
     */
    @java.lang.Override public boolean hasQueueType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return The queueType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.QueueTaskType getQueueType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.QueueTaskType result = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(queueType_);
      return result == null ? com.yorha.proto.CommonEnum.QueueTaskType.DEFAULT_NONE : result;
    }

    public static final int TASKID_FIELD_NUMBER = 2;
    private long taskId_;
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    @java.lang.Override
    public boolean hasTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public long getTaskId() {
      return taskId_;
    }

    public static final int ITEMLIST_FIELD_NUMBER = 3;
    private com.yorha.proto.StructPB.ItemListPB itemList_;
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     * @return Whether the itemList field is set.
     */
    @java.lang.Override
    public boolean hasItemList() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     * @return The itemList.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ItemListPB getItemList() {
      return itemList_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemList_;
    }
    /**
     * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ItemListPBOrBuilder getItemListOrBuilder() {
      return itemList_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemList_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, queueType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, taskId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getItemList());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, queueType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, taskId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getItemList());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S other = (com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S) obj;

      if (hasQueueType() != other.hasQueueType()) return false;
      if (hasQueueType()) {
        if (queueType_ != other.queueType_) return false;
      }
      if (hasTaskId() != other.hasTaskId()) return false;
      if (hasTaskId()) {
        if (getTaskId()
            != other.getTaskId()) return false;
      }
      if (hasItemList() != other.hasItemList()) return false;
      if (hasItemList()) {
        if (!getItemList()
            .equals(other.getItemList())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasQueueType()) {
        hash = (37 * hash) + QUEUETYPE_FIELD_NUMBER;
        hash = (53 * hash) + queueType_;
      }
      if (hasTaskId()) {
        hash = (37 * hash) + TASKID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTaskId());
      }
      if (hasItemList()) {
        hash = (37 * hash) + ITEMLIST_FIELD_NUMBER;
        hash = (53 * hash) + getItemList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SpeedQueueTask_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SpeedQueueTask_C2S)
        com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S.class, com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getItemListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        queueType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        taskId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (itemListBuilder_ == null) {
          itemList_ = null;
        } else {
          itemListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S build() {
        com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S result = new com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.queueType_ = queueType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.taskId_ = taskId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (itemListBuilder_ == null) {
            result.itemList_ = itemList_;
          } else {
            result.itemList_ = itemListBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S.getDefaultInstance()) return this;
        if (other.hasQueueType()) {
          setQueueType(other.getQueueType());
        }
        if (other.hasTaskId()) {
          setTaskId(other.getTaskId());
        }
        if (other.hasItemList()) {
          mergeItemList(other.getItemList());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int queueType_ = 0;
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return Whether the queueType field is set.
       */
      @java.lang.Override public boolean hasQueueType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return The queueType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.QueueTaskType getQueueType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.QueueTaskType result = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(queueType_);
        return result == null ? com.yorha.proto.CommonEnum.QueueTaskType.DEFAULT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @param value The queueType to set.
       * @return This builder for chaining.
       */
      public Builder setQueueType(com.yorha.proto.CommonEnum.QueueTaskType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        queueType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueueType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        queueType_ = 0;
        onChanged();
        return this;
      }

      private long taskId_ ;
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return Whether the taskId field is set.
       */
      @java.lang.Override
      public boolean hasTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public long getTaskId() {
        return taskId_;
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(long value) {
        bitField0_ |= 0x00000002;
        taskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        taskId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.ItemListPB itemList_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder> itemListBuilder_;
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       * @return Whether the itemList field is set.
       */
      public boolean hasItemList() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       * @return The itemList.
       */
      public com.yorha.proto.StructPB.ItemListPB getItemList() {
        if (itemListBuilder_ == null) {
          return itemList_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemList_;
        } else {
          return itemListBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public Builder setItemList(com.yorha.proto.StructPB.ItemListPB value) {
        if (itemListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          itemList_ = value;
          onChanged();
        } else {
          itemListBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public Builder setItemList(
          com.yorha.proto.StructPB.ItemListPB.Builder builderForValue) {
        if (itemListBuilder_ == null) {
          itemList_ = builderForValue.build();
          onChanged();
        } else {
          itemListBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public Builder mergeItemList(com.yorha.proto.StructPB.ItemListPB value) {
        if (itemListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              itemList_ != null &&
              itemList_ != com.yorha.proto.StructPB.ItemListPB.getDefaultInstance()) {
            itemList_ =
              com.yorha.proto.StructPB.ItemListPB.newBuilder(itemList_).mergeFrom(value).buildPartial();
          } else {
            itemList_ = value;
          }
          onChanged();
        } else {
          itemListBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public Builder clearItemList() {
        if (itemListBuilder_ == null) {
          itemList_ = null;
          onChanged();
        } else {
          itemListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public com.yorha.proto.StructPB.ItemListPB.Builder getItemListBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getItemListFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      public com.yorha.proto.StructPB.ItemListPBOrBuilder getItemListOrBuilder() {
        if (itemListBuilder_ != null) {
          return itemListBuilder_.getMessageOrBuilder();
        } else {
          return itemList_ == null ?
              com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemList_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ItemListPB itemList = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder> 
          getItemListFieldBuilder() {
        if (itemListBuilder_ == null) {
          itemListBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder>(
                  getItemList(),
                  getParentForChildren(),
                  isClean());
          itemList_ = null;
        }
        return itemListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SpeedQueueTask_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SpeedQueueTask_C2S)
    private static final com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SpeedQueueTask_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SpeedQueueTask_C2S>() {
      @java.lang.Override
      public Player_SpeedQueueTask_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SpeedQueueTask_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SpeedQueueTask_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SpeedQueueTask_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SpeedQueueTask_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SpeedQueueTask_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SpeedQueueTask_S2C}
   */
  public static final class Player_SpeedQueueTask_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SpeedQueueTask_S2C)
      Player_SpeedQueueTask_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SpeedQueueTask_S2C.newBuilder() to construct.
    private Player_SpeedQueueTask_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SpeedQueueTask_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SpeedQueueTask_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SpeedQueueTask_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C.class, com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C other = (com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SpeedQueueTask_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SpeedQueueTask_S2C)
        com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C.class, com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C build() {
        com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C result = new com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SpeedQueueTask_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SpeedQueueTask_S2C)
    private static final com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SpeedQueueTask_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SpeedQueueTask_S2C>() {
      @java.lang.Override
      public Player_SpeedQueueTask_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SpeedQueueTask_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SpeedQueueTask_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SpeedQueueTask_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_SpeedQueueTask_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FinishQueueTask_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FinishQueueTask_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return Whether the queueType field is set.
     */
    boolean hasQueueType();
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return The queueType.
     */
    com.yorha.proto.CommonEnum.QueueTaskType getQueueType();

    /**
     * <code>optional int64 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    boolean hasTaskId();
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return The taskId.
     */
    long getTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FinishQueueTask_C2S}
   */
  public static final class Player_FinishQueueTask_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FinishQueueTask_C2S)
      Player_FinishQueueTask_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FinishQueueTask_C2S.newBuilder() to construct.
    private Player_FinishQueueTask_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FinishQueueTask_C2S() {
      queueType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FinishQueueTask_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FinishQueueTask_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.QueueTaskType value = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                queueType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              taskId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S.class, com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int QUEUETYPE_FIELD_NUMBER = 1;
    private int queueType_;
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return Whether the queueType field is set.
     */
    @java.lang.Override public boolean hasQueueType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return The queueType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.QueueTaskType getQueueType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.QueueTaskType result = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(queueType_);
      return result == null ? com.yorha.proto.CommonEnum.QueueTaskType.DEFAULT_NONE : result;
    }

    public static final int TASKID_FIELD_NUMBER = 2;
    private long taskId_;
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    @java.lang.Override
    public boolean hasTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public long getTaskId() {
      return taskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, queueType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, taskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, queueType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, taskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S other = (com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S) obj;

      if (hasQueueType() != other.hasQueueType()) return false;
      if (hasQueueType()) {
        if (queueType_ != other.queueType_) return false;
      }
      if (hasTaskId() != other.hasTaskId()) return false;
      if (hasTaskId()) {
        if (getTaskId()
            != other.getTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasQueueType()) {
        hash = (37 * hash) + QUEUETYPE_FIELD_NUMBER;
        hash = (53 * hash) + queueType_;
      }
      if (hasTaskId()) {
        hash = (37 * hash) + TASKID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTaskId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FinishQueueTask_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FinishQueueTask_C2S)
        com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S.class, com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        queueType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        taskId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S build() {
        com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S result = new com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.queueType_ = queueType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.taskId_ = taskId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S.getDefaultInstance()) return this;
        if (other.hasQueueType()) {
          setQueueType(other.getQueueType());
        }
        if (other.hasTaskId()) {
          setTaskId(other.getTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int queueType_ = 0;
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return Whether the queueType field is set.
       */
      @java.lang.Override public boolean hasQueueType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return The queueType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.QueueTaskType getQueueType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.QueueTaskType result = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(queueType_);
        return result == null ? com.yorha.proto.CommonEnum.QueueTaskType.DEFAULT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @param value The queueType to set.
       * @return This builder for chaining.
       */
      public Builder setQueueType(com.yorha.proto.CommonEnum.QueueTaskType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        queueType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueueType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        queueType_ = 0;
        onChanged();
        return this;
      }

      private long taskId_ ;
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return Whether the taskId field is set.
       */
      @java.lang.Override
      public boolean hasTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public long getTaskId() {
        return taskId_;
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(long value) {
        bitField0_ |= 0x00000002;
        taskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        taskId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FinishQueueTask_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FinishQueueTask_C2S)
    private static final com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FinishQueueTask_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FinishQueueTask_C2S>() {
      @java.lang.Override
      public Player_FinishQueueTask_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FinishQueueTask_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FinishQueueTask_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FinishQueueTask_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FinishQueueTask_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FinishQueueTask_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */
    int getSoldierId2NumCount();
    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */
    boolean containsSoldierId2Num(
        int key);
    /**
     * Use {@link #getSoldierId2NumMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getSoldierId2Num();
    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getSoldierId2NumMap();
    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */

    int getSoldierId2NumOrDefault(
        int key,
        int defaultValue);
    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */

    int getSoldierId2NumOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FinishQueueTask_S2C}
   */
  public static final class Player_FinishQueueTask_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FinishQueueTask_S2C)
      Player_FinishQueueTask_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FinishQueueTask_S2C.newBuilder() to construct.
    private Player_FinishQueueTask_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FinishQueueTask_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FinishQueueTask_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FinishQueueTask_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                soldierId2Num_ = com.google.protobuf.MapField.newMapField(
                    SoldierId2NumDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              soldierId2Num__ = input.readMessage(
                  SoldierId2NumDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              soldierId2Num_.getMutableMap().put(
                  soldierId2Num__.getKey(), soldierId2Num__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetSoldierId2Num();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C.class, com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C.Builder.class);
    }

    public static final int SOLDIERID2NUM_FIELD_NUMBER = 1;
    private static final class SoldierId2NumDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_SoldierId2NumEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> soldierId2Num_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetSoldierId2Num() {
      if (soldierId2Num_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            SoldierId2NumDefaultEntryHolder.defaultEntry);
      }
      return soldierId2Num_;
    }

    public int getSoldierId2NumCount() {
      return internalGetSoldierId2Num().getMap().size();
    }
    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */

    @java.lang.Override
    public boolean containsSoldierId2Num(
        int key) {
      
      return internalGetSoldierId2Num().getMap().containsKey(key);
    }
    /**
     * Use {@link #getSoldierId2NumMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getSoldierId2Num() {
      return getSoldierId2NumMap();
    }
    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Integer> getSoldierId2NumMap() {
      return internalGetSoldierId2Num().getMap();
    }
    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */
    @java.lang.Override

    public int getSoldierId2NumOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetSoldierId2Num().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
     */
    @java.lang.Override

    public int getSoldierId2NumOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetSoldierId2Num().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetSoldierId2Num(),
          SoldierId2NumDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetSoldierId2Num().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        soldierId2Num__ = SoldierId2NumDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, soldierId2Num__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C other = (com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C) obj;

      if (!internalGetSoldierId2Num().equals(
          other.internalGetSoldierId2Num())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetSoldierId2Num().getMap().isEmpty()) {
        hash = (37 * hash) + SOLDIERID2NUM_FIELD_NUMBER;
        hash = (53 * hash) + internalGetSoldierId2Num().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FinishQueueTask_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FinishQueueTask_S2C)
        com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetSoldierId2Num();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableSoldierId2Num();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C.class, com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableSoldierId2Num().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C build() {
        com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C result = new com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C(this);
        int from_bitField0_ = bitField0_;
        result.soldierId2Num_ = internalGetSoldierId2Num();
        result.soldierId2Num_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C.getDefaultInstance()) return this;
        internalGetMutableSoldierId2Num().mergeFrom(
            other.internalGetSoldierId2Num());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> soldierId2Num_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetSoldierId2Num() {
        if (soldierId2Num_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              SoldierId2NumDefaultEntryHolder.defaultEntry);
        }
        return soldierId2Num_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableSoldierId2Num() {
        onChanged();;
        if (soldierId2Num_ == null) {
          soldierId2Num_ = com.google.protobuf.MapField.newMapField(
              SoldierId2NumDefaultEntryHolder.defaultEntry);
        }
        if (!soldierId2Num_.isMutable()) {
          soldierId2Num_ = soldierId2Num_.copy();
        }
        return soldierId2Num_;
      }

      public int getSoldierId2NumCount() {
        return internalGetSoldierId2Num().getMap().size();
      }
      /**
       * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
       */

      @java.lang.Override
      public boolean containsSoldierId2Num(
          int key) {
        
        return internalGetSoldierId2Num().getMap().containsKey(key);
      }
      /**
       * Use {@link #getSoldierId2NumMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getSoldierId2Num() {
        return getSoldierId2NumMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Integer> getSoldierId2NumMap() {
        return internalGetSoldierId2Num().getMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
       */
      @java.lang.Override

      public int getSoldierId2NumOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetSoldierId2Num().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
       */
      @java.lang.Override

      public int getSoldierId2NumOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetSoldierId2Num().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearSoldierId2Num() {
        internalGetMutableSoldierId2Num().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
       */

      public Builder removeSoldierId2Num(
          int key) {
        
        internalGetMutableSoldierId2Num().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableSoldierId2Num() {
        return internalGetMutableSoldierId2Num().getMutableMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
       */
      public Builder putSoldierId2Num(
          int key,
          int value) {
        
        
        internalGetMutableSoldierId2Num().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, int32&gt; soldierId2Num = 1;</code>
       */

      public Builder putAllSoldierId2Num(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableSoldierId2Num().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FinishQueueTask_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FinishQueueTask_S2C)
    private static final com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FinishQueueTask_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FinishQueueTask_S2C>() {
      @java.lang.Override
      public Player_FinishQueueTask_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FinishQueueTask_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FinishQueueTask_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FinishQueueTask_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_FinishQueueTask_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CancelQueueTask_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CancelQueueTask_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return Whether the queueType field is set.
     */
    boolean hasQueueType();
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return The queueType.
     */
    com.yorha.proto.CommonEnum.QueueTaskType getQueueType();

    /**
     * <code>optional int64 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    boolean hasTaskId();
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return The taskId.
     */
    long getTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CancelQueueTask_C2S}
   */
  public static final class Player_CancelQueueTask_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CancelQueueTask_C2S)
      Player_CancelQueueTask_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CancelQueueTask_C2S.newBuilder() to construct.
    private Player_CancelQueueTask_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CancelQueueTask_C2S() {
      queueType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CancelQueueTask_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CancelQueueTask_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.QueueTaskType value = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                queueType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              taskId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S.class, com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int QUEUETYPE_FIELD_NUMBER = 1;
    private int queueType_;
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return Whether the queueType field is set.
     */
    @java.lang.Override public boolean hasQueueType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
     * @return The queueType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.QueueTaskType getQueueType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.QueueTaskType result = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(queueType_);
      return result == null ? com.yorha.proto.CommonEnum.QueueTaskType.DEFAULT_NONE : result;
    }

    public static final int TASKID_FIELD_NUMBER = 2;
    private long taskId_;
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    @java.lang.Override
    public boolean hasTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public long getTaskId() {
      return taskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, queueType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, taskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, queueType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, taskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S other = (com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S) obj;

      if (hasQueueType() != other.hasQueueType()) return false;
      if (hasQueueType()) {
        if (queueType_ != other.queueType_) return false;
      }
      if (hasTaskId() != other.hasTaskId()) return false;
      if (hasTaskId()) {
        if (getTaskId()
            != other.getTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasQueueType()) {
        hash = (37 * hash) + QUEUETYPE_FIELD_NUMBER;
        hash = (53 * hash) + queueType_;
      }
      if (hasTaskId()) {
        hash = (37 * hash) + TASKID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTaskId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CancelQueueTask_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CancelQueueTask_C2S)
        com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S.class, com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        queueType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        taskId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S build() {
        com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S result = new com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.queueType_ = queueType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.taskId_ = taskId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S.getDefaultInstance()) return this;
        if (other.hasQueueType()) {
          setQueueType(other.getQueueType());
        }
        if (other.hasTaskId()) {
          setTaskId(other.getTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int queueType_ = 0;
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return Whether the queueType field is set.
       */
      @java.lang.Override public boolean hasQueueType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return The queueType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.QueueTaskType getQueueType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.QueueTaskType result = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(queueType_);
        return result == null ? com.yorha.proto.CommonEnum.QueueTaskType.DEFAULT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @param value The queueType to set.
       * @return This builder for chaining.
       */
      public Builder setQueueType(com.yorha.proto.CommonEnum.QueueTaskType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        queueType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueueType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        queueType_ = 0;
        onChanged();
        return this;
      }

      private long taskId_ ;
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return Whether the taskId field is set.
       */
      @java.lang.Override
      public boolean hasTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public long getTaskId() {
        return taskId_;
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(long value) {
        bitField0_ |= 0x00000002;
        taskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        taskId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CancelQueueTask_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CancelQueueTask_C2S)
    private static final com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CancelQueueTask_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_CancelQueueTask_C2S>() {
      @java.lang.Override
      public Player_CancelQueueTask_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CancelQueueTask_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CancelQueueTask_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CancelQueueTask_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CancelQueueTask_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CancelQueueTask_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CancelQueueTask_S2C}
   */
  public static final class Player_CancelQueueTask_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CancelQueueTask_S2C)
      Player_CancelQueueTask_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CancelQueueTask_S2C.newBuilder() to construct.
    private Player_CancelQueueTask_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CancelQueueTask_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CancelQueueTask_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CancelQueueTask_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C.class, com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C other = (com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CancelQueueTask_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CancelQueueTask_S2C)
        com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C.class, com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C build() {
        com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C result = new com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CancelQueueTask_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CancelQueueTask_S2C)
    private static final com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CancelQueueTask_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_CancelQueueTask_S2C>() {
      @java.lang.Override
      public Player_CancelQueueTask_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CancelQueueTask_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CancelQueueTask_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CancelQueueTask_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_CancelQueueTask_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_QueueFreeSpeed_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_QueueFreeSpeed_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 queueId = 1;</code>
     * @return Whether the queueId field is set.
     */
    boolean hasQueueId();
    /**
     * <code>optional int64 queueId = 1;</code>
     * @return The queueId.
     */
    long getQueueId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_QueueFreeSpeed_C2S}
   */
  public static final class Player_QueueFreeSpeed_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_QueueFreeSpeed_C2S)
      Player_QueueFreeSpeed_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_QueueFreeSpeed_C2S.newBuilder() to construct.
    private Player_QueueFreeSpeed_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_QueueFreeSpeed_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_QueueFreeSpeed_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_QueueFreeSpeed_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              queueId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S.class, com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int QUEUEID_FIELD_NUMBER = 1;
    private long queueId_;
    /**
     * <code>optional int64 queueId = 1;</code>
     * @return Whether the queueId field is set.
     */
    @java.lang.Override
    public boolean hasQueueId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 queueId = 1;</code>
     * @return The queueId.
     */
    @java.lang.Override
    public long getQueueId() {
      return queueId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, queueId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, queueId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S other = (com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S) obj;

      if (hasQueueId() != other.hasQueueId()) return false;
      if (hasQueueId()) {
        if (getQueueId()
            != other.getQueueId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasQueueId()) {
        hash = (37 * hash) + QUEUEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getQueueId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_QueueFreeSpeed_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_QueueFreeSpeed_C2S)
        com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S.class, com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        queueId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S build() {
        com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S result = new com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.queueId_ = queueId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S.getDefaultInstance()) return this;
        if (other.hasQueueId()) {
          setQueueId(other.getQueueId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long queueId_ ;
      /**
       * <code>optional int64 queueId = 1;</code>
       * @return Whether the queueId field is set.
       */
      @java.lang.Override
      public boolean hasQueueId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 queueId = 1;</code>
       * @return The queueId.
       */
      @java.lang.Override
      public long getQueueId() {
        return queueId_;
      }
      /**
       * <code>optional int64 queueId = 1;</code>
       * @param value The queueId to set.
       * @return This builder for chaining.
       */
      public Builder setQueueId(long value) {
        bitField0_ |= 0x00000001;
        queueId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 queueId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueueId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        queueId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_QueueFreeSpeed_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_QueueFreeSpeed_C2S)
    private static final com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_QueueFreeSpeed_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_QueueFreeSpeed_C2S>() {
      @java.lang.Override
      public Player_QueueFreeSpeed_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_QueueFreeSpeed_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_QueueFreeSpeed_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_QueueFreeSpeed_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_QueueFreeSpeed_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_QueueFreeSpeed_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_QueueFreeSpeed_S2C}
   */
  public static final class Player_QueueFreeSpeed_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_QueueFreeSpeed_S2C)
      Player_QueueFreeSpeed_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_QueueFreeSpeed_S2C.newBuilder() to construct.
    private Player_QueueFreeSpeed_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_QueueFreeSpeed_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_QueueFreeSpeed_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_QueueFreeSpeed_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C.class, com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C other = (com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_QueueFreeSpeed_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_QueueFreeSpeed_S2C)
        com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C.class, com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C build() {
        com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C result = new com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_QueueFreeSpeed_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_QueueFreeSpeed_S2C)
    private static final com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_QueueFreeSpeed_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_QueueFreeSpeed_S2C>() {
      @java.lang.Override
      public Player_QueueFreeSpeed_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_QueueFreeSpeed_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_QueueFreeSpeed_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_QueueFreeSpeed_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_QueueFreeSpeed_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_QueueFinish_NTFOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_QueueFinish_NTF)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 taskId = 1;</code>
     * @return Whether the taskId field is set.
     */
    boolean hasTaskId();
    /**
     * <code>optional int64 taskId = 1;</code>
     * @return The taskId.
     */
    long getTaskId();

    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 2;</code>
     * @return Whether the queueType field is set.
     */
    boolean hasQueueType();
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 2;</code>
     * @return The queueType.
     */
    com.yorha.proto.CommonEnum.QueueTaskType getQueueType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_QueueFinish_NTF}
   */
  public static final class Player_QueueFinish_NTF extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_QueueFinish_NTF)
      Player_QueueFinish_NTFOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_QueueFinish_NTF.newBuilder() to construct.
    private Player_QueueFinish_NTF(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_QueueFinish_NTF() {
      queueType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_QueueFinish_NTF();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_QueueFinish_NTF(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              taskId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.QueueTaskType value = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                queueType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFinish_NTF_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFinish_NTF_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF.class, com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF.Builder.class);
    }

    private int bitField0_;
    public static final int TASKID_FIELD_NUMBER = 1;
    private long taskId_;
    /**
     * <code>optional int64 taskId = 1;</code>
     * @return Whether the taskId field is set.
     */
    @java.lang.Override
    public boolean hasTaskId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 taskId = 1;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public long getTaskId() {
      return taskId_;
    }

    public static final int QUEUETYPE_FIELD_NUMBER = 2;
    private int queueType_;
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 2;</code>
     * @return Whether the queueType field is set.
     */
    @java.lang.Override public boolean hasQueueType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.QueueTaskType queueType = 2;</code>
     * @return The queueType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.QueueTaskType getQueueType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.QueueTaskType result = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(queueType_);
      return result == null ? com.yorha.proto.CommonEnum.QueueTaskType.DEFAULT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, taskId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, queueType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, taskId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, queueType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF other = (com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF) obj;

      if (hasTaskId() != other.hasTaskId()) return false;
      if (hasTaskId()) {
        if (getTaskId()
            != other.getTaskId()) return false;
      }
      if (hasQueueType() != other.hasQueueType()) return false;
      if (hasQueueType()) {
        if (queueType_ != other.queueType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTaskId()) {
        hash = (37 * hash) + TASKID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTaskId());
      }
      if (hasQueueType()) {
        hash = (37 * hash) + QUEUETYPE_FIELD_NUMBER;
        hash = (53 * hash) + queueType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_QueueFinish_NTF}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_QueueFinish_NTF)
        com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTFOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFinish_NTF_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFinish_NTF_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF.class, com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        taskId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        queueType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerQueueTask.internal_static_com_yorha_proto_Player_QueueFinish_NTF_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF getDefaultInstanceForType() {
        return com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF build() {
        com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF buildPartial() {
        com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF result = new com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.taskId_ = taskId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.queueType_ = queueType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF) {
          return mergeFrom((com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF other) {
        if (other == com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF.getDefaultInstance()) return this;
        if (other.hasTaskId()) {
          setTaskId(other.getTaskId());
        }
        if (other.hasQueueType()) {
          setQueueType(other.getQueueType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long taskId_ ;
      /**
       * <code>optional int64 taskId = 1;</code>
       * @return Whether the taskId field is set.
       */
      @java.lang.Override
      public boolean hasTaskId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 taskId = 1;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public long getTaskId() {
        return taskId_;
      }
      /**
       * <code>optional int64 taskId = 1;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(long value) {
        bitField0_ |= 0x00000001;
        taskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 taskId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        taskId_ = 0L;
        onChanged();
        return this;
      }

      private int queueType_ = 0;
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 2;</code>
       * @return Whether the queueType field is set.
       */
      @java.lang.Override public boolean hasQueueType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 2;</code>
       * @return The queueType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.QueueTaskType getQueueType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.QueueTaskType result = com.yorha.proto.CommonEnum.QueueTaskType.valueOf(queueType_);
        return result == null ? com.yorha.proto.CommonEnum.QueueTaskType.DEFAULT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 2;</code>
       * @param value The queueType to set.
       * @return This builder for chaining.
       */
      public Builder setQueueType(com.yorha.proto.CommonEnum.QueueTaskType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        queueType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.QueueTaskType queueType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueueType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        queueType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_QueueFinish_NTF)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_QueueFinish_NTF)
    private static final com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF();
    }

    public static com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_QueueFinish_NTF>
        PARSER = new com.google.protobuf.AbstractParser<Player_QueueFinish_NTF>() {
      @java.lang.Override
      public Player_QueueFinish_NTF parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_QueueFinish_NTF(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_QueueFinish_NTF> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_QueueFinish_NTF> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerQueueTask.Player_QueueFinish_NTF getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_SoldierId2NumEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_SoldierId2NumEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_QueueFinish_NTF_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_QueueFinish_NTF_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/player/cs/player_queue_ta" +
      "sk.proto\022\017com.yorha.proto\032\"cs_proto/gen/" +
      "common/structPB.proto\032%ss_proto/gen/comm" +
      "on/common_enum.proto\"\215\001\n\031Player_SpeedQue" +
      "ueTask_C2S\0221\n\tqueueType\030\001 \001(\0162\036.com.yorh" +
      "a.proto.QueueTaskType\022\016\n\006taskId\030\002 \001(\003\022-\n" +
      "\010itemList\030\003 \001(\0132\033.com.yorha.proto.ItemLi" +
      "stPB\"\033\n\031Player_SpeedQueueTask_S2C\"_\n\032Pla" +
      "yer_FinishQueueTask_C2S\0221\n\tqueueType\030\001 \001" +
      "(\0162\036.com.yorha.proto.QueueTaskType\022\016\n\006ta" +
      "skId\030\002 \001(\003\"\251\001\n\032Player_FinishQueueTask_S2" +
      "C\022U\n\rsoldierId2Num\030\001 \003(\0132>.com.yorha.pro" +
      "to.Player_FinishQueueTask_S2C.SoldierId2" +
      "NumEntry\0324\n\022SoldierId2NumEntry\022\013\n\003key\030\001 " +
      "\001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"_\n\032Player_CancelQ" +
      "ueueTask_C2S\0221\n\tqueueType\030\001 \001(\0162\036.com.yo" +
      "rha.proto.QueueTaskType\022\016\n\006taskId\030\002 \001(\003\"" +
      "\034\n\032Player_CancelQueueTask_S2C\",\n\031Player_" +
      "QueueFreeSpeed_C2S\022\017\n\007queueId\030\001 \001(\003\"\033\n\031P" +
      "layer_QueueFreeSpeed_S2C\"[\n\026Player_Queue" +
      "Finish_NTF\022\016\n\006taskId\030\001 \001(\003\0221\n\tqueueType\030" +
      "\002 \001(\0162\036.com.yorha.proto.QueueTaskTypeB\002H" +
      "\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SpeedQueueTask_C2S_descriptor,
        new java.lang.String[] { "QueueType", "TaskId", "ItemList", });
    internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SpeedQueueTask_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FinishQueueTask_C2S_descriptor,
        new java.lang.String[] { "QueueType", "TaskId", });
    internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_descriptor,
        new java.lang.String[] { "SoldierId2Num", });
    internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_SoldierId2NumEntry_descriptor =
      internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_SoldierId2NumEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FinishQueueTask_S2C_SoldierId2NumEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CancelQueueTask_C2S_descriptor,
        new java.lang.String[] { "QueueType", "TaskId", });
    internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CancelQueueTask_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_QueueFreeSpeed_C2S_descriptor,
        new java.lang.String[] { "QueueId", });
    internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_QueueFreeSpeed_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_QueueFinish_NTF_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_QueueFinish_NTF_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_QueueFinish_NTF_descriptor,
        new java.lang.String[] { "TaskId", "QueueType", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

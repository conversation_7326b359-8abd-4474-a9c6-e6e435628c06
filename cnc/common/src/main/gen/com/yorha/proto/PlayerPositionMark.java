// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_position_mark.proto

package com.yorha.proto;

public final class PlayerPositionMark {
  private PlayerPositionMark() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_PositionMarck_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PositionMarck_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 操作类型（收藏，编辑，删除，分享）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkActionType actionType = 1;</code>
     * @return Whether the actionType field is set.
     */
    boolean hasActionType();
    /**
     * <pre>
     * 操作类型（收藏，编辑，删除，分享）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkActionType actionType = 1;</code>
     * @return The actionType.
     */
    com.yorha.proto.CommonEnum.PositionMarkActionType getActionType();

    /**
     * <pre>
     * 标记类型（个人，军团）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkType positionMarkType = 2;</code>
     * @return Whether the positionMarkType field is set.
     */
    boolean hasPositionMarkType();
    /**
     * <pre>
     * 标记类型（个人，军团）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkType positionMarkType = 2;</code>
     * @return The positionMarkType.
     */
    com.yorha.proto.CommonEnum.PositionMarkType getPositionMarkType();

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     * @return The point.
     */
    com.yorha.proto.StructPB.PointPB getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 标记唯一id
     * </pre>
     *
     * <code>optional int64 markId = 4;</code>
     * @return Whether the markId field is set.
     */
    boolean hasMarkId();
    /**
     * <pre>
     * 标记唯一id
     * </pre>
     *
     * <code>optional int64 markId = 4;</code>
     * @return The markId.
     */
    long getMarkId();

    /**
     * <pre>
     * 标记名
     * </pre>
     *
     * <code>optional string markName = 5;</code>
     * @return Whether the markName field is set.
     */
    boolean hasMarkName();
    /**
     * <pre>
     * 标记名
     * </pre>
     *
     * <code>optional string markName = 5;</code>
     * @return The markName.
     */
    java.lang.String getMarkName();
    /**
     * <pre>
     * 标记名
     * </pre>
     *
     * <code>optional string markName = 5;</code>
     * @return The bytes for markName.
     */
    com.google.protobuf.ByteString
        getMarkNameBytes();

    /**
     * <pre>
     * 标记图标id
     * </pre>
     *
     * <code>optional int32 markPic = 6;</code>
     * @return Whether the markPic field is set.
     */
    boolean hasMarkPic();
    /**
     * <pre>
     * 标记图标id
     * </pre>
     *
     * <code>optional int32 markPic = 6;</code>
     * @return The markPic.
     */
    int getMarkPic();

    /**
     * <pre>
     * 标记坐标的服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 7;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 标记坐标的服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 7;</code>
     * @return The zoneId.
     */
    int getZoneId();

    /**
     * <pre>
     * 分享类型（王国，军团）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkShareType shareType = 8;</code>
     * @return Whether the shareType field is set.
     */
    boolean hasShareType();
    /**
     * <pre>
     * 分享类型（王国，军团）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkShareType shareType = 8;</code>
     * @return The shareType.
     */
    com.yorha.proto.CommonEnum.PositionMarkShareType getShareType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PositionMarck_C2S}
   */
  public static final class Player_PositionMarck_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PositionMarck_C2S)
      Player_PositionMarck_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PositionMarck_C2S.newBuilder() to construct.
    private Player_PositionMarck_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PositionMarck_C2S() {
      actionType_ = 0;
      positionMarkType_ = 0;
      markName_ = "";
      shareType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PositionMarck_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PositionMarck_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.PositionMarkActionType value = com.yorha.proto.CommonEnum.PositionMarkActionType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                actionType_ = rawValue;
              }
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.PositionMarkType value = com.yorha.proto.CommonEnum.PositionMarkType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                positionMarkType_ = rawValue;
              }
              break;
            }
            case 26: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              markId_ = input.readInt64();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              markName_ = bs;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              markPic_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              zoneId_ = input.readInt32();
              break;
            }
            case 64: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.PositionMarkShareType value = com.yorha.proto.CommonEnum.PositionMarkShareType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(8, rawValue);
              } else {
                bitField0_ |= 0x00000080;
                shareType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S.class, com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ACTIONTYPE_FIELD_NUMBER = 1;
    private int actionType_;
    /**
     * <pre>
     * 操作类型（收藏，编辑，删除，分享）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkActionType actionType = 1;</code>
     * @return Whether the actionType field is set.
     */
    @java.lang.Override public boolean hasActionType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 操作类型（收藏，编辑，删除，分享）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkActionType actionType = 1;</code>
     * @return The actionType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.PositionMarkActionType getActionType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.PositionMarkActionType result = com.yorha.proto.CommonEnum.PositionMarkActionType.valueOf(actionType_);
      return result == null ? com.yorha.proto.CommonEnum.PositionMarkActionType.PMAT_NONE : result;
    }

    public static final int POSITIONMARKTYPE_FIELD_NUMBER = 2;
    private int positionMarkType_;
    /**
     * <pre>
     * 标记类型（个人，军团）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkType positionMarkType = 2;</code>
     * @return Whether the positionMarkType field is set.
     */
    @java.lang.Override public boolean hasPositionMarkType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 标记类型（个人，军团）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkType positionMarkType = 2;</code>
     * @return The positionMarkType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.PositionMarkType getPositionMarkType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.PositionMarkType result = com.yorha.proto.CommonEnum.PositionMarkType.valueOf(positionMarkType_);
      return result == null ? com.yorha.proto.CommonEnum.PositionMarkType.PMT_NONE : result;
    }

    public static final int POINT_FIELD_NUMBER = 3;
    private com.yorha.proto.StructPB.PointPB point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getPoint() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }

    public static final int MARKID_FIELD_NUMBER = 4;
    private long markId_;
    /**
     * <pre>
     * 标记唯一id
     * </pre>
     *
     * <code>optional int64 markId = 4;</code>
     * @return Whether the markId field is set.
     */
    @java.lang.Override
    public boolean hasMarkId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 标记唯一id
     * </pre>
     *
     * <code>optional int64 markId = 4;</code>
     * @return The markId.
     */
    @java.lang.Override
    public long getMarkId() {
      return markId_;
    }

    public static final int MARKNAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object markName_;
    /**
     * <pre>
     * 标记名
     * </pre>
     *
     * <code>optional string markName = 5;</code>
     * @return Whether the markName field is set.
     */
    @java.lang.Override
    public boolean hasMarkName() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 标记名
     * </pre>
     *
     * <code>optional string markName = 5;</code>
     * @return The markName.
     */
    @java.lang.Override
    public java.lang.String getMarkName() {
      java.lang.Object ref = markName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          markName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 标记名
     * </pre>
     *
     * <code>optional string markName = 5;</code>
     * @return The bytes for markName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMarkNameBytes() {
      java.lang.Object ref = markName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        markName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MARKPIC_FIELD_NUMBER = 6;
    private int markPic_;
    /**
     * <pre>
     * 标记图标id
     * </pre>
     *
     * <code>optional int32 markPic = 6;</code>
     * @return Whether the markPic field is set.
     */
    @java.lang.Override
    public boolean hasMarkPic() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 标记图标id
     * </pre>
     *
     * <code>optional int32 markPic = 6;</code>
     * @return The markPic.
     */
    @java.lang.Override
    public int getMarkPic() {
      return markPic_;
    }

    public static final int ZONEID_FIELD_NUMBER = 7;
    private int zoneId_;
    /**
     * <pre>
     * 标记坐标的服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 7;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 标记坐标的服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 7;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    public static final int SHARETYPE_FIELD_NUMBER = 8;
    private int shareType_;
    /**
     * <pre>
     * 分享类型（王国，军团）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkShareType shareType = 8;</code>
     * @return Whether the shareType field is set.
     */
    @java.lang.Override public boolean hasShareType() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 分享类型（王国，军团）
     * </pre>
     *
     * <code>optional .com.yorha.proto.PositionMarkShareType shareType = 8;</code>
     * @return The shareType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.PositionMarkShareType getShareType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.PositionMarkShareType result = com.yorha.proto.CommonEnum.PositionMarkShareType.valueOf(shareType_);
      return result == null ? com.yorha.proto.CommonEnum.PositionMarkShareType.PMST_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, actionType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, positionMarkType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getPoint());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, markId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, markName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, markPic_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(7, zoneId_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeEnum(8, shareType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, actionType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, positionMarkType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getPoint());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, markId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, markName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, markPic_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, zoneId_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(8, shareType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S other = (com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S) obj;

      if (hasActionType() != other.hasActionType()) return false;
      if (hasActionType()) {
        if (actionType_ != other.actionType_) return false;
      }
      if (hasPositionMarkType() != other.hasPositionMarkType()) return false;
      if (hasPositionMarkType()) {
        if (positionMarkType_ != other.positionMarkType_) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasMarkId() != other.hasMarkId()) return false;
      if (hasMarkId()) {
        if (getMarkId()
            != other.getMarkId()) return false;
      }
      if (hasMarkName() != other.hasMarkName()) return false;
      if (hasMarkName()) {
        if (!getMarkName()
            .equals(other.getMarkName())) return false;
      }
      if (hasMarkPic() != other.hasMarkPic()) return false;
      if (hasMarkPic()) {
        if (getMarkPic()
            != other.getMarkPic()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (hasShareType() != other.hasShareType()) return false;
      if (hasShareType()) {
        if (shareType_ != other.shareType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActionType()) {
        hash = (37 * hash) + ACTIONTYPE_FIELD_NUMBER;
        hash = (53 * hash) + actionType_;
      }
      if (hasPositionMarkType()) {
        hash = (37 * hash) + POSITIONMARKTYPE_FIELD_NUMBER;
        hash = (53 * hash) + positionMarkType_;
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasMarkId()) {
        hash = (37 * hash) + MARKID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMarkId());
      }
      if (hasMarkName()) {
        hash = (37 * hash) + MARKNAME_FIELD_NUMBER;
        hash = (53 * hash) + getMarkName().hashCode();
      }
      if (hasMarkPic()) {
        hash = (37 * hash) + MARKPIC_FIELD_NUMBER;
        hash = (53 * hash) + getMarkPic();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      if (hasShareType()) {
        hash = (37 * hash) + SHARETYPE_FIELD_NUMBER;
        hash = (53 * hash) + shareType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PositionMarck_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PositionMarck_C2S)
        com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S.class, com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actionType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        positionMarkType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        markId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        markName_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        markPic_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        shareType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S build() {
        com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S buildPartial() {
        com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S result = new com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.actionType_ = actionType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.positionMarkType_ = positionMarkType_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.markId_ = markId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.markName_ = markName_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.markPic_ = markPic_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.shareType_ = shareType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S) {
          return mergeFrom((com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S other) {
        if (other == com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S.getDefaultInstance()) return this;
        if (other.hasActionType()) {
          setActionType(other.getActionType());
        }
        if (other.hasPositionMarkType()) {
          setPositionMarkType(other.getPositionMarkType());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasMarkId()) {
          setMarkId(other.getMarkId());
        }
        if (other.hasMarkName()) {
          bitField0_ |= 0x00000010;
          markName_ = other.markName_;
          onChanged();
        }
        if (other.hasMarkPic()) {
          setMarkPic(other.getMarkPic());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        if (other.hasShareType()) {
          setShareType(other.getShareType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int actionType_ = 0;
      /**
       * <pre>
       * 操作类型（收藏，编辑，删除，分享）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkActionType actionType = 1;</code>
       * @return Whether the actionType field is set.
       */
      @java.lang.Override public boolean hasActionType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 操作类型（收藏，编辑，删除，分享）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkActionType actionType = 1;</code>
       * @return The actionType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.PositionMarkActionType getActionType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.PositionMarkActionType result = com.yorha.proto.CommonEnum.PositionMarkActionType.valueOf(actionType_);
        return result == null ? com.yorha.proto.CommonEnum.PositionMarkActionType.PMAT_NONE : result;
      }
      /**
       * <pre>
       * 操作类型（收藏，编辑，删除，分享）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkActionType actionType = 1;</code>
       * @param value The actionType to set.
       * @return This builder for chaining.
       */
      public Builder setActionType(com.yorha.proto.CommonEnum.PositionMarkActionType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        actionType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作类型（收藏，编辑，删除，分享）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkActionType actionType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActionType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        actionType_ = 0;
        onChanged();
        return this;
      }

      private int positionMarkType_ = 0;
      /**
       * <pre>
       * 标记类型（个人，军团）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkType positionMarkType = 2;</code>
       * @return Whether the positionMarkType field is set.
       */
      @java.lang.Override public boolean hasPositionMarkType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 标记类型（个人，军团）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkType positionMarkType = 2;</code>
       * @return The positionMarkType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.PositionMarkType getPositionMarkType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.PositionMarkType result = com.yorha.proto.CommonEnum.PositionMarkType.valueOf(positionMarkType_);
        return result == null ? com.yorha.proto.CommonEnum.PositionMarkType.PMT_NONE : result;
      }
      /**
       * <pre>
       * 标记类型（个人，军团）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkType positionMarkType = 2;</code>
       * @param value The positionMarkType to set.
       * @return This builder for chaining.
       */
      public Builder setPositionMarkType(com.yorha.proto.CommonEnum.PositionMarkType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        positionMarkType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记类型（个人，军团）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkType positionMarkType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPositionMarkType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        positionMarkType_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       * @return The point.
       */
      public com.yorha.proto.StructPB.PointPB getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public Builder setPoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public Builder setPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public Builder mergePoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            point_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPointBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private long markId_ ;
      /**
       * <pre>
       * 标记唯一id
       * </pre>
       *
       * <code>optional int64 markId = 4;</code>
       * @return Whether the markId field is set.
       */
      @java.lang.Override
      public boolean hasMarkId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 标记唯一id
       * </pre>
       *
       * <code>optional int64 markId = 4;</code>
       * @return The markId.
       */
      @java.lang.Override
      public long getMarkId() {
        return markId_;
      }
      /**
       * <pre>
       * 标记唯一id
       * </pre>
       *
       * <code>optional int64 markId = 4;</code>
       * @param value The markId to set.
       * @return This builder for chaining.
       */
      public Builder setMarkId(long value) {
        bitField0_ |= 0x00000008;
        markId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记唯一id
       * </pre>
       *
       * <code>optional int64 markId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearMarkId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        markId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object markName_ = "";
      /**
       * <pre>
       * 标记名
       * </pre>
       *
       * <code>optional string markName = 5;</code>
       * @return Whether the markName field is set.
       */
      public boolean hasMarkName() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 标记名
       * </pre>
       *
       * <code>optional string markName = 5;</code>
       * @return The markName.
       */
      public java.lang.String getMarkName() {
        java.lang.Object ref = markName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            markName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 标记名
       * </pre>
       *
       * <code>optional string markName = 5;</code>
       * @return The bytes for markName.
       */
      public com.google.protobuf.ByteString
          getMarkNameBytes() {
        java.lang.Object ref = markName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          markName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 标记名
       * </pre>
       *
       * <code>optional string markName = 5;</code>
       * @param value The markName to set.
       * @return This builder for chaining.
       */
      public Builder setMarkName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        markName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记名
       * </pre>
       *
       * <code>optional string markName = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearMarkName() {
        bitField0_ = (bitField0_ & ~0x00000010);
        markName_ = getDefaultInstance().getMarkName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记名
       * </pre>
       *
       * <code>optional string markName = 5;</code>
       * @param value The bytes for markName to set.
       * @return This builder for chaining.
       */
      public Builder setMarkNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        markName_ = value;
        onChanged();
        return this;
      }

      private int markPic_ ;
      /**
       * <pre>
       * 标记图标id
       * </pre>
       *
       * <code>optional int32 markPic = 6;</code>
       * @return Whether the markPic field is set.
       */
      @java.lang.Override
      public boolean hasMarkPic() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 标记图标id
       * </pre>
       *
       * <code>optional int32 markPic = 6;</code>
       * @return The markPic.
       */
      @java.lang.Override
      public int getMarkPic() {
        return markPic_;
      }
      /**
       * <pre>
       * 标记图标id
       * </pre>
       *
       * <code>optional int32 markPic = 6;</code>
       * @param value The markPic to set.
       * @return This builder for chaining.
       */
      public Builder setMarkPic(int value) {
        bitField0_ |= 0x00000020;
        markPic_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记图标id
       * </pre>
       *
       * <code>optional int32 markPic = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearMarkPic() {
        bitField0_ = (bitField0_ & ~0x00000020);
        markPic_ = 0;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 标记坐标的服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 7;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 标记坐标的服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 7;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 标记坐标的服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 7;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000040;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记坐标的服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000040);
        zoneId_ = 0;
        onChanged();
        return this;
      }

      private int shareType_ = 0;
      /**
       * <pre>
       * 分享类型（王国，军团）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkShareType shareType = 8;</code>
       * @return Whether the shareType field is set.
       */
      @java.lang.Override public boolean hasShareType() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 分享类型（王国，军团）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkShareType shareType = 8;</code>
       * @return The shareType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.PositionMarkShareType getShareType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.PositionMarkShareType result = com.yorha.proto.CommonEnum.PositionMarkShareType.valueOf(shareType_);
        return result == null ? com.yorha.proto.CommonEnum.PositionMarkShareType.PMST_NONE : result;
      }
      /**
       * <pre>
       * 分享类型（王国，军团）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkShareType shareType = 8;</code>
       * @param value The shareType to set.
       * @return This builder for chaining.
       */
      public Builder setShareType(com.yorha.proto.CommonEnum.PositionMarkShareType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000080;
        shareType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 分享类型（王国，军团）
       * </pre>
       *
       * <code>optional .com.yorha.proto.PositionMarkShareType shareType = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearShareType() {
        bitField0_ = (bitField0_ & ~0x00000080);
        shareType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PositionMarck_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PositionMarck_C2S)
    private static final com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S();
    }

    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PositionMarck_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_PositionMarck_C2S>() {
      @java.lang.Override
      public Player_PositionMarck_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PositionMarck_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PositionMarck_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PositionMarck_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPositionMark.Player_PositionMarck_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_PositionMarck_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PositionMarck_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PositionMarck_S2C}
   */
  public static final class Player_PositionMarck_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PositionMarck_S2C)
      Player_PositionMarck_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PositionMarck_S2C.newBuilder() to construct.
    private Player_PositionMarck_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PositionMarck_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PositionMarck_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PositionMarck_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C.class, com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C other = (com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PositionMarck_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PositionMarck_S2C)
        com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C.class, com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_PositionMarck_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C build() {
        com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C buildPartial() {
        com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C result = new com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C) {
          return mergeFrom((com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C other) {
        if (other == com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PositionMarck_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PositionMarck_S2C)
    private static final com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C();
    }

    public static com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PositionMarck_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_PositionMarck_S2C>() {
      @java.lang.Override
      public Player_PositionMarck_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PositionMarck_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PositionMarck_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PositionMarck_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPositionMark.Player_PositionMarck_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchClanPositionMark_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanPositionMark_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanPositionMark_C2S}
   */
  public static final class Player_FetchClanPositionMark_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanPositionMark_C2S)
      Player_FetchClanPositionMark_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanPositionMark_C2S.newBuilder() to construct.
    private Player_FetchClanPositionMark_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanPositionMark_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanPositionMark_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanPositionMark_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S.class, com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S other = (com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanPositionMark_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanPositionMark_C2S)
        com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S.class, com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S build() {
        com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S buildPartial() {
        com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S result = new com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S) {
          return mergeFrom((com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S other) {
        if (other == com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanPositionMark_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanPositionMark_C2S)
    private static final com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S();
    }

    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanPositionMark_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanPositionMark_C2S>() {
      @java.lang.Override
      public Player_FetchClanPositionMark_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanPositionMark_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanPositionMark_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanPositionMark_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchClanPositionMark_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchClanPositionMark_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    java.util.List<com.yorha.proto.StructPB.PositionMarkInfoPB> 
        getInfosList();
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    com.yorha.proto.StructPB.PositionMarkInfoPB getInfos(int index);
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    int getInfosCount();
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder> 
        getInfosOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder getInfosOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchClanPositionMark_S2C}
   */
  public static final class Player_FetchClanPositionMark_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchClanPositionMark_S2C)
      Player_FetchClanPositionMark_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchClanPositionMark_S2C.newBuilder() to construct.
    private Player_FetchClanPositionMark_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchClanPositionMark_S2C() {
      infos_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchClanPositionMark_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchClanPositionMark_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                infos_ = new java.util.ArrayList<com.yorha.proto.StructPB.PositionMarkInfoPB>();
                mutable_bitField0_ |= 0x00000001;
              }
              infos_.add(
                  input.readMessage(com.yorha.proto.StructPB.PositionMarkInfoPB.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          infos_ = java.util.Collections.unmodifiableList(infos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C.class, com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C.Builder.class);
    }

    public static final int INFOS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.StructPB.PositionMarkInfoPB> infos_;
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructPB.PositionMarkInfoPB> getInfosList() {
      return infos_;
    }
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder> 
        getInfosOrBuilderList() {
      return infos_;
    }
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    @java.lang.Override
    public int getInfosCount() {
      return infos_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PositionMarkInfoPB getInfos(int index) {
      return infos_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder getInfosOrBuilder(
        int index) {
      return infos_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < infos_.size(); i++) {
        output.writeMessage(1, infos_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < infos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, infos_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C other = (com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C) obj;

      if (!getInfosList()
          .equals(other.getInfosList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfosCount() > 0) {
        hash = (37 * hash) + INFOS_FIELD_NUMBER;
        hash = (53 * hash) + getInfosList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchClanPositionMark_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchClanPositionMark_S2C)
        com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C.class, com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfosFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infosBuilder_ == null) {
          infos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infosBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPositionMark.internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C build() {
        com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C buildPartial() {
        com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C result = new com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C(this);
        int from_bitField0_ = bitField0_;
        if (infosBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            infos_ = java.util.Collections.unmodifiableList(infos_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.infos_ = infos_;
        } else {
          result.infos_ = infosBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C) {
          return mergeFrom((com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C other) {
        if (other == com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C.getDefaultInstance()) return this;
        if (infosBuilder_ == null) {
          if (!other.infos_.isEmpty()) {
            if (infos_.isEmpty()) {
              infos_ = other.infos_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfosIsMutable();
              infos_.addAll(other.infos_);
            }
            onChanged();
          }
        } else {
          if (!other.infos_.isEmpty()) {
            if (infosBuilder_.isEmpty()) {
              infosBuilder_.dispose();
              infosBuilder_ = null;
              infos_ = other.infos_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infosBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfosFieldBuilder() : null;
            } else {
              infosBuilder_.addAllMessages(other.infos_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.StructPB.PositionMarkInfoPB> infos_ =
        java.util.Collections.emptyList();
      private void ensureInfosIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          infos_ = new java.util.ArrayList<com.yorha.proto.StructPB.PositionMarkInfoPB>(infos_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructPB.PositionMarkInfoPB, com.yorha.proto.StructPB.PositionMarkInfoPB.Builder, com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder> infosBuilder_;

      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructPB.PositionMarkInfoPB> getInfosList() {
        if (infosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(infos_);
        } else {
          return infosBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public int getInfosCount() {
        if (infosBuilder_ == null) {
          return infos_.size();
        } else {
          return infosBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public com.yorha.proto.StructPB.PositionMarkInfoPB getInfos(int index) {
        if (infosBuilder_ == null) {
          return infos_.get(index);
        } else {
          return infosBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder setInfos(
          int index, com.yorha.proto.StructPB.PositionMarkInfoPB value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.set(index, value);
          onChanged();
        } else {
          infosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder setInfos(
          int index, com.yorha.proto.StructPB.PositionMarkInfoPB.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.set(index, builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder addInfos(com.yorha.proto.StructPB.PositionMarkInfoPB value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.add(value);
          onChanged();
        } else {
          infosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder addInfos(
          int index, com.yorha.proto.StructPB.PositionMarkInfoPB value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.add(index, value);
          onChanged();
        } else {
          infosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder addInfos(
          com.yorha.proto.StructPB.PositionMarkInfoPB.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.add(builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder addInfos(
          int index, com.yorha.proto.StructPB.PositionMarkInfoPB.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.add(index, builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder addAllInfos(
          java.lang.Iterable<? extends com.yorha.proto.StructPB.PositionMarkInfoPB> values) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, infos_);
          onChanged();
        } else {
          infosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder clearInfos() {
        if (infosBuilder_ == null) {
          infos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infosBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public Builder removeInfos(int index) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.remove(index);
          onChanged();
        } else {
          infosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public com.yorha.proto.StructPB.PositionMarkInfoPB.Builder getInfosBuilder(
          int index) {
        return getInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder getInfosOrBuilder(
          int index) {
        if (infosBuilder_ == null) {
          return infos_.get(index);  } else {
          return infosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder> 
           getInfosOrBuilderList() {
        if (infosBuilder_ != null) {
          return infosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(infos_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public com.yorha.proto.StructPB.PositionMarkInfoPB.Builder addInfosBuilder() {
        return getInfosFieldBuilder().addBuilder(
            com.yorha.proto.StructPB.PositionMarkInfoPB.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public com.yorha.proto.StructPB.PositionMarkInfoPB.Builder addInfosBuilder(
          int index) {
        return getInfosFieldBuilder().addBuilder(
            index, com.yorha.proto.StructPB.PositionMarkInfoPB.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.PositionMarkInfoPB infos = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructPB.PositionMarkInfoPB.Builder> 
           getInfosBuilderList() {
        return getInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructPB.PositionMarkInfoPB, com.yorha.proto.StructPB.PositionMarkInfoPB.Builder, com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder> 
          getInfosFieldBuilder() {
        if (infosBuilder_ == null) {
          infosBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructPB.PositionMarkInfoPB, com.yorha.proto.StructPB.PositionMarkInfoPB.Builder, com.yorha.proto.StructPB.PositionMarkInfoPBOrBuilder>(
                  infos_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          infos_ = null;
        }
        return infosBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchClanPositionMark_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchClanPositionMark_S2C)
    private static final com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C();
    }

    public static com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchClanPositionMark_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchClanPositionMark_S2C>() {
      @java.lang.Override
      public Player_FetchClanPositionMark_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchClanPositionMark_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchClanPositionMark_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchClanPositionMark_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPositionMark.Player_FetchClanPositionMark_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PositionMarck_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PositionMarck_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PositionMarck_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PositionMarck_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n1ss_proto/gen/player/cs/player_position" +
      "_mark.proto\022\017com.yorha.proto\032\"cs_proto/g" +
      "en/common/structPB.proto\032%ss_proto/gen/c" +
      "ommon/common_enum.proto\"\273\002\n\030Player_Posit" +
      "ionMarck_C2S\022;\n\nactionType\030\001 \001(\0162\'.com.y" +
      "orha.proto.PositionMarkActionType\022;\n\020pos" +
      "itionMarkType\030\002 \001(\0162!.com.yorha.proto.Po" +
      "sitionMarkType\022\'\n\005point\030\003 \001(\0132\030.com.yorh" +
      "a.proto.PointPB\022\016\n\006markId\030\004 \001(\003\022\020\n\010markN" +
      "ame\030\005 \001(\t\022\017\n\007markPic\030\006 \001(\005\022\016\n\006zoneId\030\007 \001" +
      "(\005\0229\n\tshareType\030\010 \001(\0162&.com.yorha.proto." +
      "PositionMarkShareType\"\032\n\030Player_Position" +
      "Marck_S2C\"\"\n Player_FetchClanPositionMar" +
      "k_C2S\"V\n Player_FetchClanPositionMark_S2" +
      "C\0222\n\005infos\030\001 \003(\0132#.com.yorha.proto.Posit" +
      "ionMarkInfoPBB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_PositionMarck_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_PositionMarck_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PositionMarck_C2S_descriptor,
        new java.lang.String[] { "ActionType", "PositionMarkType", "Point", "MarkId", "MarkName", "MarkPic", "ZoneId", "ShareType", });
    internal_static_com_yorha_proto_Player_PositionMarck_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_PositionMarck_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PositionMarck_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanPositionMark_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchClanPositionMark_S2C_descriptor,
        new java.lang.String[] { "Infos", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

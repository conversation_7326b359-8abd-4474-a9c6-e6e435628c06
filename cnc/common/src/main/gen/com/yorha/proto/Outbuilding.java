// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/outbuilding/outbuilding.proto

package com.yorha.proto;

public final class Outbuilding {
  private Outbuilding() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface OutbuildingEntityOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OutbuildingEntity)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return Whether the partId field is set.
     */
    boolean hasPartId();
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return The point.
     */
    com.yorha.proto.Struct.Point getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 4;</code>
     * @return Whether the troop field is set.
     */
    boolean hasTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 4;</code>
     * @return The troop.
     */
    com.yorha.proto.StructPlayer.Troop getTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 4;</code>
     */
    com.yorha.proto.StructPlayer.TroopOrBuilder getTroopOrBuilder();

    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 5;</code>
     * @return Whether the battle field is set.
     */
    boolean hasBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 5;</code>
     * @return The battle.
     */
    com.yorha.proto.StructBattle.Battle getBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 5;</code>
     */
    com.yorha.proto.StructBattle.BattleOrBuilder getBattleOrBuilder();

    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OutbuildingState state = 6;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OutbuildingState state = 6;</code>
     * @return The state.
     */
    com.yorha.proto.CommonEnum.OutbuildingState getState();

    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
     * @return Whether the buffSys field is set.
     */
    boolean hasBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
     * @return The buffSys.
     */
    com.yorha.proto.StructBattle.BuffSys getBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
     */
    com.yorha.proto.StructBattle.BuffSysOrBuilder getBuffSysOrBuilder();
  }
  /**
   * <pre>
   * 超武附属建筑
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.OutbuildingEntity}
   */
  public static final class OutbuildingEntity extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OutbuildingEntity)
      OutbuildingEntityOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OutbuildingEntity.newBuilder() to construct.
    private OutbuildingEntity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OutbuildingEntity() {
      state_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OutbuildingEntity();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OutbuildingEntity(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              templateId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              partId_ = input.readInt32();
              break;
            }
            case 26: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 34: {
              com.yorha.proto.StructPlayer.Troop.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = troop_.toBuilder();
              }
              troop_ = input.readMessage(com.yorha.proto.StructPlayer.Troop.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(troop_);
                troop_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 42: {
              com.yorha.proto.StructBattle.Battle.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = battle_.toBuilder();
              }
              battle_ = input.readMessage(com.yorha.proto.StructBattle.Battle.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battle_);
                battle_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 48: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.OutbuildingState value = com.yorha.proto.CommonEnum.OutbuildingState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(6, rawValue);
              } else {
                bitField0_ |= 0x00000020;
                state_ = rawValue;
              }
              break;
            }
            case 58: {
              com.yorha.proto.StructBattle.BuffSys.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = buffSys_.toBuilder();
              }
              buffSys_ = input.readMessage(com.yorha.proto.StructBattle.BuffSys.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buffSys_);
                buffSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Outbuilding.internal_static_com_yorha_proto_OutbuildingEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Outbuilding.internal_static_com_yorha_proto_OutbuildingEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Outbuilding.OutbuildingEntity.class, com.yorha.proto.Outbuilding.OutbuildingEntity.Builder.class);
    }

    private int bitField0_;
    public static final int TEMPLATEID_FIELD_NUMBER = 1;
    private int templateId_;
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int PARTID_FIELD_NUMBER = 2;
    private int partId_;
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return Whether the partId field is set.
     */
    @java.lang.Override
    public boolean hasPartId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int POINT_FIELD_NUMBER = 3;
    private com.yorha.proto.Struct.Point point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }

    public static final int TROOP_FIELD_NUMBER = 4;
    private com.yorha.proto.StructPlayer.Troop troop_;
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 4;</code>
     * @return Whether the troop field is set.
     */
    @java.lang.Override
    public boolean hasTroop() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 4;</code>
     * @return The troop.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.Troop getTroop() {
      return troop_ == null ? com.yorha.proto.StructPlayer.Troop.getDefaultInstance() : troop_;
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.TroopOrBuilder getTroopOrBuilder() {
      return troop_ == null ? com.yorha.proto.StructPlayer.Troop.getDefaultInstance() : troop_;
    }

    public static final int BATTLE_FIELD_NUMBER = 5;
    private com.yorha.proto.StructBattle.Battle battle_;
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 5;</code>
     * @return Whether the battle field is set.
     */
    @java.lang.Override
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 5;</code>
     * @return The battle.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.Battle getBattle() {
      return battle_ == null ? com.yorha.proto.StructBattle.Battle.getDefaultInstance() : battle_;
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.BattleOrBuilder getBattleOrBuilder() {
      return battle_ == null ? com.yorha.proto.StructBattle.Battle.getDefaultInstance() : battle_;
    }

    public static final int STATE_FIELD_NUMBER = 6;
    private int state_;
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OutbuildingState state = 6;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override public boolean hasState() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OutbuildingState state = 6;</code>
     * @return The state.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.OutbuildingState getState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.OutbuildingState result = com.yorha.proto.CommonEnum.OutbuildingState.valueOf(state_);
      return result == null ? com.yorha.proto.CommonEnum.OutbuildingState.OS_NONE : result;
    }

    public static final int BUFFSYS_FIELD_NUMBER = 7;
    private com.yorha.proto.StructBattle.BuffSys buffSys_;
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
     * @return Whether the buffSys field is set.
     */
    @java.lang.Override
    public boolean hasBuffSys() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
     * @return The buffSys.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.BuffSys getBuffSys() {
      return buffSys_ == null ? com.yorha.proto.StructBattle.BuffSys.getDefaultInstance() : buffSys_;
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.BuffSysOrBuilder getBuffSysOrBuilder() {
      return buffSys_ == null ? com.yorha.proto.StructBattle.BuffSys.getDefaultInstance() : buffSys_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, partId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getPoint());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getTroop());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getBattle());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeEnum(6, state_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(7, getBuffSys());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, partId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getPoint());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getTroop());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getBattle());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(6, state_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getBuffSys());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Outbuilding.OutbuildingEntity)) {
        return super.equals(obj);
      }
      com.yorha.proto.Outbuilding.OutbuildingEntity other = (com.yorha.proto.Outbuilding.OutbuildingEntity) obj;

      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasPartId() != other.hasPartId()) return false;
      if (hasPartId()) {
        if (getPartId()
            != other.getPartId()) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasTroop() != other.hasTroop()) return false;
      if (hasTroop()) {
        if (!getTroop()
            .equals(other.getTroop())) return false;
      }
      if (hasBattle() != other.hasBattle()) return false;
      if (hasBattle()) {
        if (!getBattle()
            .equals(other.getBattle())) return false;
      }
      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (state_ != other.state_) return false;
      }
      if (hasBuffSys() != other.hasBuffSys()) return false;
      if (hasBuffSys()) {
        if (!getBuffSys()
            .equals(other.getBuffSys())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasPartId()) {
        hash = (37 * hash) + PARTID_FIELD_NUMBER;
        hash = (53 * hash) + getPartId();
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasTroop()) {
        hash = (37 * hash) + TROOP_FIELD_NUMBER;
        hash = (53 * hash) + getTroop().hashCode();
      }
      if (hasBattle()) {
        hash = (37 * hash) + BATTLE_FIELD_NUMBER;
        hash = (53 * hash) + getBattle().hashCode();
      }
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + state_;
      }
      if (hasBuffSys()) {
        hash = (37 * hash) + BUFFSYS_FIELD_NUMBER;
        hash = (53 * hash) + getBuffSys().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Outbuilding.OutbuildingEntity parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Outbuilding.OutbuildingEntity prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 超武附属建筑
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.OutbuildingEntity}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OutbuildingEntity)
        com.yorha.proto.Outbuilding.OutbuildingEntityOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Outbuilding.internal_static_com_yorha_proto_OutbuildingEntity_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Outbuilding.internal_static_com_yorha_proto_OutbuildingEntity_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Outbuilding.OutbuildingEntity.class, com.yorha.proto.Outbuilding.OutbuildingEntity.Builder.class);
      }

      // Construct using com.yorha.proto.Outbuilding.OutbuildingEntity.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getTroopFieldBuilder();
          getBattleFieldBuilder();
          getBuffSysFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (troopBuilder_ == null) {
          troop_ = null;
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (battleBuilder_ == null) {
          battle_ = null;
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        state_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Outbuilding.internal_static_com_yorha_proto_OutbuildingEntity_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Outbuilding.OutbuildingEntity getDefaultInstanceForType() {
        return com.yorha.proto.Outbuilding.OutbuildingEntity.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Outbuilding.OutbuildingEntity build() {
        com.yorha.proto.Outbuilding.OutbuildingEntity result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Outbuilding.OutbuildingEntity buildPartial() {
        com.yorha.proto.Outbuilding.OutbuildingEntity result = new com.yorha.proto.Outbuilding.OutbuildingEntity(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.partId_ = partId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (troopBuilder_ == null) {
            result.troop_ = troop_;
          } else {
            result.troop_ = troopBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (battleBuilder_ == null) {
            result.battle_ = battle_;
          } else {
            result.battle_ = battleBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.state_ = state_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          if (buffSysBuilder_ == null) {
            result.buffSys_ = buffSys_;
          } else {
            result.buffSys_ = buffSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Outbuilding.OutbuildingEntity) {
          return mergeFrom((com.yorha.proto.Outbuilding.OutbuildingEntity)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Outbuilding.OutbuildingEntity other) {
        if (other == com.yorha.proto.Outbuilding.OutbuildingEntity.getDefaultInstance()) return this;
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasPartId()) {
          setPartId(other.getPartId());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasTroop()) {
          mergeTroop(other.getTroop());
        }
        if (other.hasBattle()) {
          mergeBattle(other.getBattle());
        }
        if (other.hasState()) {
          setState(other.getState());
        }
        if (other.hasBuffSys()) {
          mergeBuffSys(other.getBuffSys());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Outbuilding.OutbuildingEntity parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Outbuilding.OutbuildingEntity) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int templateId_ ;
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000001;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private int partId_ ;
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return Whether the partId field is set.
       */
      @java.lang.Override
      public boolean hasPartId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {
        bitField0_ |= 0x00000002;
        partId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        partId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Point point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       * @return The point.
       */
      public com.yorha.proto.Struct.Point getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder setPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder setPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder mergePoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            point_ =
              com.yorha.proto.Struct.Point.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private com.yorha.proto.StructPlayer.Troop troop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.Troop, com.yorha.proto.StructPlayer.Troop.Builder, com.yorha.proto.StructPlayer.TroopOrBuilder> troopBuilder_;
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       * @return Whether the troop field is set.
       */
      public boolean hasTroop() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       * @return The troop.
       */
      public com.yorha.proto.StructPlayer.Troop getTroop() {
        if (troopBuilder_ == null) {
          return troop_ == null ? com.yorha.proto.StructPlayer.Troop.getDefaultInstance() : troop_;
        } else {
          return troopBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       */
      public Builder setTroop(com.yorha.proto.StructPlayer.Troop value) {
        if (troopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          troop_ = value;
          onChanged();
        } else {
          troopBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       */
      public Builder setTroop(
          com.yorha.proto.StructPlayer.Troop.Builder builderForValue) {
        if (troopBuilder_ == null) {
          troop_ = builderForValue.build();
          onChanged();
        } else {
          troopBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       */
      public Builder mergeTroop(com.yorha.proto.StructPlayer.Troop value) {
        if (troopBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              troop_ != null &&
              troop_ != com.yorha.proto.StructPlayer.Troop.getDefaultInstance()) {
            troop_ =
              com.yorha.proto.StructPlayer.Troop.newBuilder(troop_).mergeFrom(value).buildPartial();
          } else {
            troop_ = value;
          }
          onChanged();
        } else {
          troopBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       */
      public Builder clearTroop() {
        if (troopBuilder_ == null) {
          troop_ = null;
          onChanged();
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       */
      public com.yorha.proto.StructPlayer.Troop.Builder getTroopBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getTroopFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       */
      public com.yorha.proto.StructPlayer.TroopOrBuilder getTroopOrBuilder() {
        if (troopBuilder_ != null) {
          return troopBuilder_.getMessageOrBuilder();
        } else {
          return troop_ == null ?
              com.yorha.proto.StructPlayer.Troop.getDefaultInstance() : troop_;
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.Troop, com.yorha.proto.StructPlayer.Troop.Builder, com.yorha.proto.StructPlayer.TroopOrBuilder> 
          getTroopFieldBuilder() {
        if (troopBuilder_ == null) {
          troopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayer.Troop, com.yorha.proto.StructPlayer.Troop.Builder, com.yorha.proto.StructPlayer.TroopOrBuilder>(
                  getTroop(),
                  getParentForChildren(),
                  isClean());
          troop_ = null;
        }
        return troopBuilder_;
      }

      private com.yorha.proto.StructBattle.Battle battle_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.Battle, com.yorha.proto.StructBattle.Battle.Builder, com.yorha.proto.StructBattle.BattleOrBuilder> battleBuilder_;
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       * @return Whether the battle field is set.
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       * @return The battle.
       */
      public com.yorha.proto.StructBattle.Battle getBattle() {
        if (battleBuilder_ == null) {
          return battle_ == null ? com.yorha.proto.StructBattle.Battle.getDefaultInstance() : battle_;
        } else {
          return battleBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       */
      public Builder setBattle(com.yorha.proto.StructBattle.Battle value) {
        if (battleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battle_ = value;
          onChanged();
        } else {
          battleBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       */
      public Builder setBattle(
          com.yorha.proto.StructBattle.Battle.Builder builderForValue) {
        if (battleBuilder_ == null) {
          battle_ = builderForValue.build();
          onChanged();
        } else {
          battleBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       */
      public Builder mergeBattle(com.yorha.proto.StructBattle.Battle value) {
        if (battleBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              battle_ != null &&
              battle_ != com.yorha.proto.StructBattle.Battle.getDefaultInstance()) {
            battle_ =
              com.yorha.proto.StructBattle.Battle.newBuilder(battle_).mergeFrom(value).buildPartial();
          } else {
            battle_ = value;
          }
          onChanged();
        } else {
          battleBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       */
      public Builder clearBattle() {
        if (battleBuilder_ == null) {
          battle_ = null;
          onChanged();
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       */
      public com.yorha.proto.StructBattle.Battle.Builder getBattleBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getBattleFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       */
      public com.yorha.proto.StructBattle.BattleOrBuilder getBattleOrBuilder() {
        if (battleBuilder_ != null) {
          return battleBuilder_.getMessageOrBuilder();
        } else {
          return battle_ == null ?
              com.yorha.proto.StructBattle.Battle.getDefaultInstance() : battle_;
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.Battle, com.yorha.proto.StructBattle.Battle.Builder, com.yorha.proto.StructBattle.BattleOrBuilder> 
          getBattleFieldBuilder() {
        if (battleBuilder_ == null) {
          battleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattle.Battle, com.yorha.proto.StructBattle.Battle.Builder, com.yorha.proto.StructBattle.BattleOrBuilder>(
                  getBattle(),
                  getParentForChildren(),
                  isClean());
          battle_ = null;
        }
        return battleBuilder_;
      }

      private int state_ = 0;
      /**
       * <pre>
       * 状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OutbuildingState state = 6;</code>
       * @return Whether the state field is set.
       */
      @java.lang.Override public boolean hasState() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OutbuildingState state = 6;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.OutbuildingState getState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.OutbuildingState result = com.yorha.proto.CommonEnum.OutbuildingState.valueOf(state_);
        return result == null ? com.yorha.proto.CommonEnum.OutbuildingState.OS_NONE : result;
      }
      /**
       * <pre>
       * 状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OutbuildingState state = 6;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(com.yorha.proto.CommonEnum.OutbuildingState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000020;
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OutbuildingState state = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        bitField0_ = (bitField0_ & ~0x00000020);
        state_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructBattle.BuffSys buffSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.BuffSys, com.yorha.proto.StructBattle.BuffSys.Builder, com.yorha.proto.StructBattle.BuffSysOrBuilder> buffSysBuilder_;
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       * @return Whether the buffSys field is set.
       */
      public boolean hasBuffSys() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       * @return The buffSys.
       */
      public com.yorha.proto.StructBattle.BuffSys getBuffSys() {
        if (buffSysBuilder_ == null) {
          return buffSys_ == null ? com.yorha.proto.StructBattle.BuffSys.getDefaultInstance() : buffSys_;
        } else {
          return buffSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       */
      public Builder setBuffSys(com.yorha.proto.StructBattle.BuffSys value) {
        if (buffSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buffSys_ = value;
          onChanged();
        } else {
          buffSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       */
      public Builder setBuffSys(
          com.yorha.proto.StructBattle.BuffSys.Builder builderForValue) {
        if (buffSysBuilder_ == null) {
          buffSys_ = builderForValue.build();
          onChanged();
        } else {
          buffSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       */
      public Builder mergeBuffSys(com.yorha.proto.StructBattle.BuffSys value) {
        if (buffSysBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
              buffSys_ != null &&
              buffSys_ != com.yorha.proto.StructBattle.BuffSys.getDefaultInstance()) {
            buffSys_ =
              com.yorha.proto.StructBattle.BuffSys.newBuilder(buffSys_).mergeFrom(value).buildPartial();
          } else {
            buffSys_ = value;
          }
          onChanged();
        } else {
          buffSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       */
      public Builder clearBuffSys() {
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
          onChanged();
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       */
      public com.yorha.proto.StructBattle.BuffSys.Builder getBuffSysBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getBuffSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       */
      public com.yorha.proto.StructBattle.BuffSysOrBuilder getBuffSysOrBuilder() {
        if (buffSysBuilder_ != null) {
          return buffSysBuilder_.getMessageOrBuilder();
        } else {
          return buffSys_ == null ?
              com.yorha.proto.StructBattle.BuffSys.getDefaultInstance() : buffSys_;
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.BuffSys, com.yorha.proto.StructBattle.BuffSys.Builder, com.yorha.proto.StructBattle.BuffSysOrBuilder> 
          getBuffSysFieldBuilder() {
        if (buffSysBuilder_ == null) {
          buffSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattle.BuffSys, com.yorha.proto.StructBattle.BuffSys.Builder, com.yorha.proto.StructBattle.BuffSysOrBuilder>(
                  getBuffSys(),
                  getParentForChildren(),
                  isClean());
          buffSys_ = null;
        }
        return buffSysBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OutbuildingEntity)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OutbuildingEntity)
    private static final com.yorha.proto.Outbuilding.OutbuildingEntity DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Outbuilding.OutbuildingEntity();
    }

    public static com.yorha.proto.Outbuilding.OutbuildingEntity getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OutbuildingEntity>
        PARSER = new com.google.protobuf.AbstractParser<OutbuildingEntity>() {
      @java.lang.Override
      public OutbuildingEntity parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OutbuildingEntity(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OutbuildingEntity> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OutbuildingEntity> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Outbuilding.OutbuildingEntity getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OutbuildingEntity_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OutbuildingEntity_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n*ss_proto/gen/outbuilding/outbuilding.p" +
      "roto\022\017com.yorha.proto\032%ss_proto/gen/comm" +
      "on/common_enum.proto\032 ss_proto/gen/commo" +
      "n/struct.proto\032\'ss_proto/gen/common/stru" +
      "ct_battle.proto\032\'ss_proto/gen/common/str" +
      "uct_player.proto\"\213\002\n\021OutbuildingEntity\022\022" +
      "\n\ntemplateId\030\001 \001(\005\022\016\n\006partId\030\002 \001(\005\022%\n\005po" +
      "int\030\003 \001(\0132\026.com.yorha.proto.Point\022%\n\005tro" +
      "op\030\004 \001(\0132\026.com.yorha.proto.Troop\022\'\n\006batt" +
      "le\030\005 \001(\0132\027.com.yorha.proto.Battle\0220\n\005sta" +
      "te\030\006 \001(\0162!.com.yorha.proto.OutbuildingSt" +
      "ate\022)\n\007buffSys\030\007 \001(\0132\030.com.yorha.proto.B" +
      "uffSysB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructBattle.getDescriptor(),
          com.yorha.proto.StructPlayer.getDescriptor(),
        });
    internal_static_com_yorha_proto_OutbuildingEntity_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_OutbuildingEntity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OutbuildingEntity_descriptor,
        new java.lang.String[] { "TemplateId", "PartId", "Point", "Troop", "Battle", "State", "BuffSys", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructBattle.getDescriptor();
    com.yorha.proto.StructPlayer.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

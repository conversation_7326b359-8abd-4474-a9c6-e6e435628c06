// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/common/struct_skynetPB.proto

package com.yorha.proto;

public final class StructSkynetPB {
  private StructSkynetPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SkynetNormalTaskPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SkynetNormalTaskPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 已接取的任务。key：唯一id
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
     * @return Whether the normalTask field is set.
     */
    boolean hasNormalTask();
    /**
     * <pre>
     * 已接取的任务。key：唯一id
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
     * @return The normalTask.
     */
    com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB getNormalTask();
    /**
     * <pre>
     * 已接取的任务。key：唯一id
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
     */
    com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPBOrBuilder getNormalTaskOrBuilder();

    /**
     * <pre>
     * 上次刷新的时间戳
     * </pre>
     *
     * <code>optional int64 lastRefreshTsMs = 2;</code>
     * @return Whether the lastRefreshTsMs field is set.
     */
    boolean hasLastRefreshTsMs();
    /**
     * <pre>
     * 上次刷新的时间戳
     * </pre>
     *
     * <code>optional int64 lastRefreshTsMs = 2;</code>
     * @return The lastRefreshTsMs.
     */
    long getLastRefreshTsMs();

    /**
     * <pre>
     * 任务池数量
     * </pre>
     *
     * <code>optional int32 taskPoolNum = 3;</code>
     * @return Whether the taskPoolNum field is set.
     */
    boolean hasTaskPoolNum();
    /**
     * <pre>
     * 任务池数量
     * </pre>
     *
     * <code>optional int32 taskPoolNum = 3;</code>
     * @return The taskPoolNum.
     */
    int getTaskPoolNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SkynetNormalTaskPB}
   */
  public static final class SkynetNormalTaskPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SkynetNormalTaskPB)
      SkynetNormalTaskPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SkynetNormalTaskPB.newBuilder() to construct.
    private SkynetNormalTaskPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SkynetNormalTaskPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SkynetNormalTaskPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SkynetNormalTaskPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = normalTask_.toBuilder();
              }
              normalTask_ = input.readMessage(com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(normalTask_);
                normalTask_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              lastRefreshTsMs_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              taskPoolNum_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetNormalTaskPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetNormalTaskPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB.class, com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB.Builder.class);
    }

    private int bitField0_;
    public static final int NORMALTASK_FIELD_NUMBER = 1;
    private com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB normalTask_;
    /**
     * <pre>
     * 已接取的任务。key：唯一id
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
     * @return Whether the normalTask field is set.
     */
    @java.lang.Override
    public boolean hasNormalTask() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 已接取的任务。key：唯一id
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
     * @return The normalTask.
     */
    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB getNormalTask() {
      return normalTask_ == null ? com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.getDefaultInstance() : normalTask_;
    }
    /**
     * <pre>
     * 已接取的任务。key：唯一id
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPBOrBuilder getNormalTaskOrBuilder() {
      return normalTask_ == null ? com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.getDefaultInstance() : normalTask_;
    }

    public static final int LASTREFRESHTSMS_FIELD_NUMBER = 2;
    private long lastRefreshTsMs_;
    /**
     * <pre>
     * 上次刷新的时间戳
     * </pre>
     *
     * <code>optional int64 lastRefreshTsMs = 2;</code>
     * @return Whether the lastRefreshTsMs field is set.
     */
    @java.lang.Override
    public boolean hasLastRefreshTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 上次刷新的时间戳
     * </pre>
     *
     * <code>optional int64 lastRefreshTsMs = 2;</code>
     * @return The lastRefreshTsMs.
     */
    @java.lang.Override
    public long getLastRefreshTsMs() {
      return lastRefreshTsMs_;
    }

    public static final int TASKPOOLNUM_FIELD_NUMBER = 3;
    private int taskPoolNum_;
    /**
     * <pre>
     * 任务池数量
     * </pre>
     *
     * <code>optional int32 taskPoolNum = 3;</code>
     * @return Whether the taskPoolNum field is set.
     */
    @java.lang.Override
    public boolean hasTaskPoolNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 任务池数量
     * </pre>
     *
     * <code>optional int32 taskPoolNum = 3;</code>
     * @return The taskPoolNum.
     */
    @java.lang.Override
    public int getTaskPoolNum() {
      return taskPoolNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getNormalTask());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, lastRefreshTsMs_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, taskPoolNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getNormalTask());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, lastRefreshTsMs_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, taskPoolNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB other = (com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB) obj;

      if (hasNormalTask() != other.hasNormalTask()) return false;
      if (hasNormalTask()) {
        if (!getNormalTask()
            .equals(other.getNormalTask())) return false;
      }
      if (hasLastRefreshTsMs() != other.hasLastRefreshTsMs()) return false;
      if (hasLastRefreshTsMs()) {
        if (getLastRefreshTsMs()
            != other.getLastRefreshTsMs()) return false;
      }
      if (hasTaskPoolNum() != other.hasTaskPoolNum()) return false;
      if (hasTaskPoolNum()) {
        if (getTaskPoolNum()
            != other.getTaskPoolNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNormalTask()) {
        hash = (37 * hash) + NORMALTASK_FIELD_NUMBER;
        hash = (53 * hash) + getNormalTask().hashCode();
      }
      if (hasLastRefreshTsMs()) {
        hash = (37 * hash) + LASTREFRESHTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLastRefreshTsMs());
      }
      if (hasTaskPoolNum()) {
        hash = (37 * hash) + TASKPOOLNUM_FIELD_NUMBER;
        hash = (53 * hash) + getTaskPoolNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SkynetNormalTaskPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SkynetNormalTaskPB)
        com.yorha.proto.StructSkynetPB.SkynetNormalTaskPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetNormalTaskPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetNormalTaskPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB.class, com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getNormalTaskFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (normalTaskBuilder_ == null) {
          normalTask_ = null;
        } else {
          normalTaskBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        lastRefreshTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        taskPoolNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetNormalTaskPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB build() {
        com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB buildPartial() {
        com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB result = new com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (normalTaskBuilder_ == null) {
            result.normalTask_ = normalTask_;
          } else {
            result.normalTask_ = normalTaskBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.lastRefreshTsMs_ = lastRefreshTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.taskPoolNum_ = taskPoolNum_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB other) {
        if (other == com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB.getDefaultInstance()) return this;
        if (other.hasNormalTask()) {
          mergeNormalTask(other.getNormalTask());
        }
        if (other.hasLastRefreshTsMs()) {
          setLastRefreshTsMs(other.getLastRefreshTsMs());
        }
        if (other.hasTaskPoolNum()) {
          setTaskPoolNum(other.getTaskPoolNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB normalTask_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB, com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.Builder, com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPBOrBuilder> normalTaskBuilder_;
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       * @return Whether the normalTask field is set.
       */
      public boolean hasNormalTask() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       * @return The normalTask.
       */
      public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB getNormalTask() {
        if (normalTaskBuilder_ == null) {
          return normalTask_ == null ? com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.getDefaultInstance() : normalTask_;
        } else {
          return normalTaskBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       */
      public Builder setNormalTask(com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB value) {
        if (normalTaskBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          normalTask_ = value;
          onChanged();
        } else {
          normalTaskBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       */
      public Builder setNormalTask(
          com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.Builder builderForValue) {
        if (normalTaskBuilder_ == null) {
          normalTask_ = builderForValue.build();
          onChanged();
        } else {
          normalTaskBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       */
      public Builder mergeNormalTask(com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB value) {
        if (normalTaskBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              normalTask_ != null &&
              normalTask_ != com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.getDefaultInstance()) {
            normalTask_ =
              com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.newBuilder(normalTask_).mergeFrom(value).buildPartial();
          } else {
            normalTask_ = value;
          }
          onChanged();
        } else {
          normalTaskBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       */
      public Builder clearNormalTask() {
        if (normalTaskBuilder_ == null) {
          normalTask_ = null;
          onChanged();
        } else {
          normalTaskBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       */
      public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.Builder getNormalTaskBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getNormalTaskFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       */
      public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPBOrBuilder getNormalTaskOrBuilder() {
        if (normalTaskBuilder_ != null) {
          return normalTaskBuilder_.getMessageOrBuilder();
        } else {
          return normalTask_ == null ?
              com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.getDefaultInstance() : normalTask_;
        }
      }
      /**
       * <pre>
       * 已接取的任务。key：唯一id
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32SkynetTaskMapPB normalTask = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB, com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.Builder, com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPBOrBuilder> 
          getNormalTaskFieldBuilder() {
        if (normalTaskBuilder_ == null) {
          normalTaskBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB, com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.Builder, com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPBOrBuilder>(
                  getNormalTask(),
                  getParentForChildren(),
                  isClean());
          normalTask_ = null;
        }
        return normalTaskBuilder_;
      }

      private long lastRefreshTsMs_ ;
      /**
       * <pre>
       * 上次刷新的时间戳
       * </pre>
       *
       * <code>optional int64 lastRefreshTsMs = 2;</code>
       * @return Whether the lastRefreshTsMs field is set.
       */
      @java.lang.Override
      public boolean hasLastRefreshTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 上次刷新的时间戳
       * </pre>
       *
       * <code>optional int64 lastRefreshTsMs = 2;</code>
       * @return The lastRefreshTsMs.
       */
      @java.lang.Override
      public long getLastRefreshTsMs() {
        return lastRefreshTsMs_;
      }
      /**
       * <pre>
       * 上次刷新的时间戳
       * </pre>
       *
       * <code>optional int64 lastRefreshTsMs = 2;</code>
       * @param value The lastRefreshTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setLastRefreshTsMs(long value) {
        bitField0_ |= 0x00000002;
        lastRefreshTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 上次刷新的时间戳
       * </pre>
       *
       * <code>optional int64 lastRefreshTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastRefreshTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lastRefreshTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int taskPoolNum_ ;
      /**
       * <pre>
       * 任务池数量
       * </pre>
       *
       * <code>optional int32 taskPoolNum = 3;</code>
       * @return Whether the taskPoolNum field is set.
       */
      @java.lang.Override
      public boolean hasTaskPoolNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 任务池数量
       * </pre>
       *
       * <code>optional int32 taskPoolNum = 3;</code>
       * @return The taskPoolNum.
       */
      @java.lang.Override
      public int getTaskPoolNum() {
        return taskPoolNum_;
      }
      /**
       * <pre>
       * 任务池数量
       * </pre>
       *
       * <code>optional int32 taskPoolNum = 3;</code>
       * @param value The taskPoolNum to set.
       * @return This builder for chaining.
       */
      public Builder setTaskPoolNum(int value) {
        bitField0_ |= 0x00000004;
        taskPoolNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务池数量
       * </pre>
       *
       * <code>optional int32 taskPoolNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskPoolNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        taskPoolNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SkynetNormalTaskPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SkynetNormalTaskPB)
    private static final com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB();
    }

    public static com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SkynetNormalTaskPB>
        PARSER = new com.google.protobuf.AbstractParser<SkynetNormalTaskPB>() {
      @java.lang.Override
      public SkynetNormalTaskPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SkynetNormalTaskPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SkynetNormalTaskPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SkynetNormalTaskPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SkynetBossTaskPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SkynetBossTaskPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * boss任务
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
     * @return Whether the bossTask field is set.
     */
    boolean hasBossTask();
    /**
     * <pre>
     * boss任务
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
     * @return The bossTask.
     */
    com.yorha.proto.StructSkynetPB.SkynetTaskPB getBossTask();
    /**
     * <pre>
     * boss任务
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
     */
    com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder getBossTaskOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SkynetBossTaskPB}
   */
  public static final class SkynetBossTaskPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SkynetBossTaskPB)
      SkynetBossTaskPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SkynetBossTaskPB.newBuilder() to construct.
    private SkynetBossTaskPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SkynetBossTaskPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SkynetBossTaskPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SkynetBossTaskPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = bossTask_.toBuilder();
              }
              bossTask_ = input.readMessage(com.yorha.proto.StructSkynetPB.SkynetTaskPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(bossTask_);
                bossTask_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBossTaskPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBossTaskPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.SkynetBossTaskPB.class, com.yorha.proto.StructSkynetPB.SkynetBossTaskPB.Builder.class);
    }

    private int bitField0_;
    public static final int BOSSTASK_FIELD_NUMBER = 1;
    private com.yorha.proto.StructSkynetPB.SkynetTaskPB bossTask_;
    /**
     * <pre>
     * boss任务
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
     * @return Whether the bossTask field is set.
     */
    @java.lang.Override
    public boolean hasBossTask() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * boss任务
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
     * @return The bossTask.
     */
    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetTaskPB getBossTask() {
      return bossTask_ == null ? com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance() : bossTask_;
    }
    /**
     * <pre>
     * boss任务
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder getBossTaskOrBuilder() {
      return bossTask_ == null ? com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance() : bossTask_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getBossTask());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getBossTask());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.SkynetBossTaskPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.SkynetBossTaskPB other = (com.yorha.proto.StructSkynetPB.SkynetBossTaskPB) obj;

      if (hasBossTask() != other.hasBossTask()) return false;
      if (hasBossTask()) {
        if (!getBossTask()
            .equals(other.getBossTask())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBossTask()) {
        hash = (37 * hash) + BOSSTASK_FIELD_NUMBER;
        hash = (53 * hash) + getBossTask().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.SkynetBossTaskPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SkynetBossTaskPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SkynetBossTaskPB)
        com.yorha.proto.StructSkynetPB.SkynetBossTaskPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBossTaskPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBossTaskPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.SkynetBossTaskPB.class, com.yorha.proto.StructSkynetPB.SkynetBossTaskPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.SkynetBossTaskPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getBossTaskFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (bossTaskBuilder_ == null) {
          bossTask_ = null;
        } else {
          bossTaskBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBossTaskPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetBossTaskPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.SkynetBossTaskPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetBossTaskPB build() {
        com.yorha.proto.StructSkynetPB.SkynetBossTaskPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetBossTaskPB buildPartial() {
        com.yorha.proto.StructSkynetPB.SkynetBossTaskPB result = new com.yorha.proto.StructSkynetPB.SkynetBossTaskPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (bossTaskBuilder_ == null) {
            result.bossTask_ = bossTask_;
          } else {
            result.bossTask_ = bossTaskBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.SkynetBossTaskPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.SkynetBossTaskPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.SkynetBossTaskPB other) {
        if (other == com.yorha.proto.StructSkynetPB.SkynetBossTaskPB.getDefaultInstance()) return this;
        if (other.hasBossTask()) {
          mergeBossTask(other.getBossTask());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.SkynetBossTaskPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.SkynetBossTaskPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructSkynetPB.SkynetTaskPB bossTask_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructSkynetPB.SkynetTaskPB, com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder, com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder> bossTaskBuilder_;
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       * @return Whether the bossTask field is set.
       */
      public boolean hasBossTask() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       * @return The bossTask.
       */
      public com.yorha.proto.StructSkynetPB.SkynetTaskPB getBossTask() {
        if (bossTaskBuilder_ == null) {
          return bossTask_ == null ? com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance() : bossTask_;
        } else {
          return bossTaskBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       */
      public Builder setBossTask(com.yorha.proto.StructSkynetPB.SkynetTaskPB value) {
        if (bossTaskBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          bossTask_ = value;
          onChanged();
        } else {
          bossTaskBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       */
      public Builder setBossTask(
          com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder builderForValue) {
        if (bossTaskBuilder_ == null) {
          bossTask_ = builderForValue.build();
          onChanged();
        } else {
          bossTaskBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       */
      public Builder mergeBossTask(com.yorha.proto.StructSkynetPB.SkynetTaskPB value) {
        if (bossTaskBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              bossTask_ != null &&
              bossTask_ != com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance()) {
            bossTask_ =
              com.yorha.proto.StructSkynetPB.SkynetTaskPB.newBuilder(bossTask_).mergeFrom(value).buildPartial();
          } else {
            bossTask_ = value;
          }
          onChanged();
        } else {
          bossTaskBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       */
      public Builder clearBossTask() {
        if (bossTaskBuilder_ == null) {
          bossTask_ = null;
          onChanged();
        } else {
          bossTaskBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       */
      public com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder getBossTaskBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getBossTaskFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       */
      public com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder getBossTaskOrBuilder() {
        if (bossTaskBuilder_ != null) {
          return bossTaskBuilder_.getMessageOrBuilder();
        } else {
          return bossTask_ == null ?
              com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance() : bossTask_;
        }
      }
      /**
       * <pre>
       * boss任务
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskPB bossTask = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructSkynetPB.SkynetTaskPB, com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder, com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder> 
          getBossTaskFieldBuilder() {
        if (bossTaskBuilder_ == null) {
          bossTaskBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructSkynetPB.SkynetTaskPB, com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder, com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder>(
                  getBossTask(),
                  getParentForChildren(),
                  isClean());
          bossTask_ = null;
        }
        return bossTaskBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SkynetBossTaskPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SkynetBossTaskPB)
    private static final com.yorha.proto.StructSkynetPB.SkynetBossTaskPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.SkynetBossTaskPB();
    }

    public static com.yorha.proto.StructSkynetPB.SkynetBossTaskPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SkynetBossTaskPB>
        PARSER = new com.google.protobuf.AbstractParser<SkynetBossTaskPB>() {
      @java.lang.Override
      public SkynetBossTaskPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SkynetBossTaskPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SkynetBossTaskPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SkynetBossTaskPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetBossTaskPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SkynetGuardTaskPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SkynetGuardTaskPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
     * @return Whether the guardTask field is set.
     */
    boolean hasGuardTask();
    /**
     * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
     * @return The guardTask.
     */
    com.yorha.proto.StructSkynetPB.SkynetTaskPB getGuardTask();
    /**
     * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
     */
    com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder getGuardTaskOrBuilder();

    /**
     * <pre>
     * 上次刷新的时间戳
     * </pre>
     *
     * <code>optional int64 lastRefreshTsMs = 2;</code>
     * @return Whether the lastRefreshTsMs field is set.
     */
    boolean hasLastRefreshTsMs();
    /**
     * <pre>
     * 上次刷新的时间戳
     * </pre>
     *
     * <code>optional int64 lastRefreshTsMs = 2;</code>
     * @return The lastRefreshTsMs.
     */
    long getLastRefreshTsMs();

    /**
     * <pre>
     * 任务池数量
     * </pre>
     *
     * <code>optional int32 taskPoolNum = 3;</code>
     * @return Whether the taskPoolNum field is set.
     */
    boolean hasTaskPoolNum();
    /**
     * <pre>
     * 任务池数量
     * </pre>
     *
     * <code>optional int32 taskPoolNum = 3;</code>
     * @return The taskPoolNum.
     */
    int getTaskPoolNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SkynetGuardTaskPB}
   */
  public static final class SkynetGuardTaskPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SkynetGuardTaskPB)
      SkynetGuardTaskPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SkynetGuardTaskPB.newBuilder() to construct.
    private SkynetGuardTaskPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SkynetGuardTaskPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SkynetGuardTaskPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SkynetGuardTaskPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = guardTask_.toBuilder();
              }
              guardTask_ = input.readMessage(com.yorha.proto.StructSkynetPB.SkynetTaskPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(guardTask_);
                guardTask_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              lastRefreshTsMs_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              taskPoolNum_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetGuardTaskPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetGuardTaskPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB.class, com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB.Builder.class);
    }

    private int bitField0_;
    public static final int GUARDTASK_FIELD_NUMBER = 1;
    private com.yorha.proto.StructSkynetPB.SkynetTaskPB guardTask_;
    /**
     * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
     * @return Whether the guardTask field is set.
     */
    @java.lang.Override
    public boolean hasGuardTask() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
     * @return The guardTask.
     */
    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetTaskPB getGuardTask() {
      return guardTask_ == null ? com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance() : guardTask_;
    }
    /**
     * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder getGuardTaskOrBuilder() {
      return guardTask_ == null ? com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance() : guardTask_;
    }

    public static final int LASTREFRESHTSMS_FIELD_NUMBER = 2;
    private long lastRefreshTsMs_;
    /**
     * <pre>
     * 上次刷新的时间戳
     * </pre>
     *
     * <code>optional int64 lastRefreshTsMs = 2;</code>
     * @return Whether the lastRefreshTsMs field is set.
     */
    @java.lang.Override
    public boolean hasLastRefreshTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 上次刷新的时间戳
     * </pre>
     *
     * <code>optional int64 lastRefreshTsMs = 2;</code>
     * @return The lastRefreshTsMs.
     */
    @java.lang.Override
    public long getLastRefreshTsMs() {
      return lastRefreshTsMs_;
    }

    public static final int TASKPOOLNUM_FIELD_NUMBER = 3;
    private int taskPoolNum_;
    /**
     * <pre>
     * 任务池数量
     * </pre>
     *
     * <code>optional int32 taskPoolNum = 3;</code>
     * @return Whether the taskPoolNum field is set.
     */
    @java.lang.Override
    public boolean hasTaskPoolNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 任务池数量
     * </pre>
     *
     * <code>optional int32 taskPoolNum = 3;</code>
     * @return The taskPoolNum.
     */
    @java.lang.Override
    public int getTaskPoolNum() {
      return taskPoolNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getGuardTask());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, lastRefreshTsMs_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, taskPoolNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getGuardTask());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, lastRefreshTsMs_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, taskPoolNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB other = (com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB) obj;

      if (hasGuardTask() != other.hasGuardTask()) return false;
      if (hasGuardTask()) {
        if (!getGuardTask()
            .equals(other.getGuardTask())) return false;
      }
      if (hasLastRefreshTsMs() != other.hasLastRefreshTsMs()) return false;
      if (hasLastRefreshTsMs()) {
        if (getLastRefreshTsMs()
            != other.getLastRefreshTsMs()) return false;
      }
      if (hasTaskPoolNum() != other.hasTaskPoolNum()) return false;
      if (hasTaskPoolNum()) {
        if (getTaskPoolNum()
            != other.getTaskPoolNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGuardTask()) {
        hash = (37 * hash) + GUARDTASK_FIELD_NUMBER;
        hash = (53 * hash) + getGuardTask().hashCode();
      }
      if (hasLastRefreshTsMs()) {
        hash = (37 * hash) + LASTREFRESHTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLastRefreshTsMs());
      }
      if (hasTaskPoolNum()) {
        hash = (37 * hash) + TASKPOOLNUM_FIELD_NUMBER;
        hash = (53 * hash) + getTaskPoolNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SkynetGuardTaskPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SkynetGuardTaskPB)
        com.yorha.proto.StructSkynetPB.SkynetGuardTaskPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetGuardTaskPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetGuardTaskPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB.class, com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getGuardTaskFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (guardTaskBuilder_ == null) {
          guardTask_ = null;
        } else {
          guardTaskBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        lastRefreshTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        taskPoolNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetGuardTaskPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB build() {
        com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB buildPartial() {
        com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB result = new com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (guardTaskBuilder_ == null) {
            result.guardTask_ = guardTask_;
          } else {
            result.guardTask_ = guardTaskBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.lastRefreshTsMs_ = lastRefreshTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.taskPoolNum_ = taskPoolNum_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB other) {
        if (other == com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB.getDefaultInstance()) return this;
        if (other.hasGuardTask()) {
          mergeGuardTask(other.getGuardTask());
        }
        if (other.hasLastRefreshTsMs()) {
          setLastRefreshTsMs(other.getLastRefreshTsMs());
        }
        if (other.hasTaskPoolNum()) {
          setTaskPoolNum(other.getTaskPoolNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructSkynetPB.SkynetTaskPB guardTask_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructSkynetPB.SkynetTaskPB, com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder, com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder> guardTaskBuilder_;
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       * @return Whether the guardTask field is set.
       */
      public boolean hasGuardTask() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       * @return The guardTask.
       */
      public com.yorha.proto.StructSkynetPB.SkynetTaskPB getGuardTask() {
        if (guardTaskBuilder_ == null) {
          return guardTask_ == null ? com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance() : guardTask_;
        } else {
          return guardTaskBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       */
      public Builder setGuardTask(com.yorha.proto.StructSkynetPB.SkynetTaskPB value) {
        if (guardTaskBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          guardTask_ = value;
          onChanged();
        } else {
          guardTaskBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       */
      public Builder setGuardTask(
          com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder builderForValue) {
        if (guardTaskBuilder_ == null) {
          guardTask_ = builderForValue.build();
          onChanged();
        } else {
          guardTaskBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       */
      public Builder mergeGuardTask(com.yorha.proto.StructSkynetPB.SkynetTaskPB value) {
        if (guardTaskBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              guardTask_ != null &&
              guardTask_ != com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance()) {
            guardTask_ =
              com.yorha.proto.StructSkynetPB.SkynetTaskPB.newBuilder(guardTask_).mergeFrom(value).buildPartial();
          } else {
            guardTask_ = value;
          }
          onChanged();
        } else {
          guardTaskBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       */
      public Builder clearGuardTask() {
        if (guardTaskBuilder_ == null) {
          guardTask_ = null;
          onChanged();
        } else {
          guardTaskBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       */
      public com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder getGuardTaskBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getGuardTaskFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       */
      public com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder getGuardTaskOrBuilder() {
        if (guardTaskBuilder_ != null) {
          return guardTaskBuilder_.getMessageOrBuilder();
        } else {
          return guardTask_ == null ?
              com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance() : guardTask_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.SkynetTaskPB guardTask = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructSkynetPB.SkynetTaskPB, com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder, com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder> 
          getGuardTaskFieldBuilder() {
        if (guardTaskBuilder_ == null) {
          guardTaskBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructSkynetPB.SkynetTaskPB, com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder, com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder>(
                  getGuardTask(),
                  getParentForChildren(),
                  isClean());
          guardTask_ = null;
        }
        return guardTaskBuilder_;
      }

      private long lastRefreshTsMs_ ;
      /**
       * <pre>
       * 上次刷新的时间戳
       * </pre>
       *
       * <code>optional int64 lastRefreshTsMs = 2;</code>
       * @return Whether the lastRefreshTsMs field is set.
       */
      @java.lang.Override
      public boolean hasLastRefreshTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 上次刷新的时间戳
       * </pre>
       *
       * <code>optional int64 lastRefreshTsMs = 2;</code>
       * @return The lastRefreshTsMs.
       */
      @java.lang.Override
      public long getLastRefreshTsMs() {
        return lastRefreshTsMs_;
      }
      /**
       * <pre>
       * 上次刷新的时间戳
       * </pre>
       *
       * <code>optional int64 lastRefreshTsMs = 2;</code>
       * @param value The lastRefreshTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setLastRefreshTsMs(long value) {
        bitField0_ |= 0x00000002;
        lastRefreshTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 上次刷新的时间戳
       * </pre>
       *
       * <code>optional int64 lastRefreshTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastRefreshTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lastRefreshTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int taskPoolNum_ ;
      /**
       * <pre>
       * 任务池数量
       * </pre>
       *
       * <code>optional int32 taskPoolNum = 3;</code>
       * @return Whether the taskPoolNum field is set.
       */
      @java.lang.Override
      public boolean hasTaskPoolNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 任务池数量
       * </pre>
       *
       * <code>optional int32 taskPoolNum = 3;</code>
       * @return The taskPoolNum.
       */
      @java.lang.Override
      public int getTaskPoolNum() {
        return taskPoolNum_;
      }
      /**
       * <pre>
       * 任务池数量
       * </pre>
       *
       * <code>optional int32 taskPoolNum = 3;</code>
       * @param value The taskPoolNum to set.
       * @return This builder for chaining.
       */
      public Builder setTaskPoolNum(int value) {
        bitField0_ |= 0x00000004;
        taskPoolNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务池数量
       * </pre>
       *
       * <code>optional int32 taskPoolNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskPoolNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        taskPoolNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SkynetGuardTaskPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SkynetGuardTaskPB)
    private static final com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB();
    }

    public static com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SkynetGuardTaskPB>
        PARSER = new com.google.protobuf.AbstractParser<SkynetGuardTaskPB>() {
      @java.lang.Override
      public SkynetGuardTaskPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SkynetGuardTaskPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SkynetGuardTaskPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SkynetGuardTaskPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SkynetTaskPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SkynetTaskPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 模块内的唯一id
     * </pre>
     *
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <pre>
     * 模块内的唯一id
     * </pre>
     *
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <pre>
     * 配置任务id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return Whether the skynetTaskId field is set.
     */
    boolean hasSkynetTaskId();
    /**
     * <pre>
     * 配置任务id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return The skynetTaskId.
     */
    int getSkynetTaskId();

    /**
     * <pre>
     * 任务状态 0：初始  1：已接取  2：进行中  3：待领奖  4：完结
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskStatus skynetTaskStatus = 4;</code>
     * @return Whether the skynetTaskStatus field is set.
     */
    boolean hasSkynetTaskStatus();
    /**
     * <pre>
     * 任务状态 0：初始  1：已接取  2：进行中  3：待领奖  4：完结
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskStatus skynetTaskStatus = 4;</code>
     * @return The skynetTaskStatus.
     */
    com.yorha.proto.CommonEnum.SkynetTaskStatus getSkynetTaskStatus();

    /**
     * <pre>
     * 任务接取时间
     * </pre>
     *
     * <code>optional int64 receiveTsMs = 5;</code>
     * @return Whether the receiveTsMs field is set.
     */
    boolean hasReceiveTsMs();
    /**
     * <pre>
     * 任务接取时间
     * </pre>
     *
     * <code>optional int64 receiveTsMs = 5;</code>
     * @return The receiveTsMs.
     */
    long getReceiveTsMs();

    /**
     * <pre>
     * 进行中的过期时间
     * </pre>
     *
     * <code>optional int64 doingTsMs = 6;</code>
     * @return Whether the doingTsMs field is set.
     */
    boolean hasDoingTsMs();
    /**
     * <pre>
     * 进行中的过期时间
     * </pre>
     *
     * <code>optional int64 doingTsMs = 6;</code>
     * @return The doingTsMs.
     */
    long getDoingTsMs();

    /**
     * <pre>
     * 关联野怪id
     * </pre>
     *
     * <code>optional int64 monsterId = 7;</code>
     * @return Whether the monsterId field is set.
     */
    boolean hasMonsterId();
    /**
     * <pre>
     * 关联野怪id
     * </pre>
     *
     * <code>optional int64 monsterId = 7;</code>
     * @return The monsterId.
     */
    long getMonsterId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SkynetTaskPB}
   */
  public static final class SkynetTaskPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SkynetTaskPB)
      SkynetTaskPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SkynetTaskPB.newBuilder() to construct.
    private SkynetTaskPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SkynetTaskPB() {
      skynetTaskStatus_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SkynetTaskPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SkynetTaskPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              skynetTaskId_ = input.readInt32();
              break;
            }
            case 32: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SkynetTaskStatus value = com.yorha.proto.CommonEnum.SkynetTaskStatus.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(4, rawValue);
              } else {
                bitField0_ |= 0x00000004;
                skynetTaskStatus_ = rawValue;
              }
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              receiveTsMs_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000010;
              doingTsMs_ = input.readInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000020;
              monsterId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetTaskPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetTaskPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.SkynetTaskPB.class, com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder.class);
    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <pre>
     * 模块内的唯一id
     * </pre>
     *
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 模块内的唯一id
     * </pre>
     *
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int SKYNETTASKID_FIELD_NUMBER = 2;
    private int skynetTaskId_;
    /**
     * <pre>
     * 配置任务id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return Whether the skynetTaskId field is set.
     */
    @java.lang.Override
    public boolean hasSkynetTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 配置任务id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return The skynetTaskId.
     */
    @java.lang.Override
    public int getSkynetTaskId() {
      return skynetTaskId_;
    }

    public static final int SKYNETTASKSTATUS_FIELD_NUMBER = 4;
    private int skynetTaskStatus_;
    /**
     * <pre>
     * 任务状态 0：初始  1：已接取  2：进行中  3：待领奖  4：完结
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskStatus skynetTaskStatus = 4;</code>
     * @return Whether the skynetTaskStatus field is set.
     */
    @java.lang.Override public boolean hasSkynetTaskStatus() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 任务状态 0：初始  1：已接取  2：进行中  3：待领奖  4：完结
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetTaskStatus skynetTaskStatus = 4;</code>
     * @return The skynetTaskStatus.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SkynetTaskStatus getSkynetTaskStatus() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SkynetTaskStatus result = com.yorha.proto.CommonEnum.SkynetTaskStatus.valueOf(skynetTaskStatus_);
      return result == null ? com.yorha.proto.CommonEnum.SkynetTaskStatus.SNTS_NONE : result;
    }

    public static final int RECEIVETSMS_FIELD_NUMBER = 5;
    private long receiveTsMs_;
    /**
     * <pre>
     * 任务接取时间
     * </pre>
     *
     * <code>optional int64 receiveTsMs = 5;</code>
     * @return Whether the receiveTsMs field is set.
     */
    @java.lang.Override
    public boolean hasReceiveTsMs() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 任务接取时间
     * </pre>
     *
     * <code>optional int64 receiveTsMs = 5;</code>
     * @return The receiveTsMs.
     */
    @java.lang.Override
    public long getReceiveTsMs() {
      return receiveTsMs_;
    }

    public static final int DOINGTSMS_FIELD_NUMBER = 6;
    private long doingTsMs_;
    /**
     * <pre>
     * 进行中的过期时间
     * </pre>
     *
     * <code>optional int64 doingTsMs = 6;</code>
     * @return Whether the doingTsMs field is set.
     */
    @java.lang.Override
    public boolean hasDoingTsMs() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 进行中的过期时间
     * </pre>
     *
     * <code>optional int64 doingTsMs = 6;</code>
     * @return The doingTsMs.
     */
    @java.lang.Override
    public long getDoingTsMs() {
      return doingTsMs_;
    }

    public static final int MONSTERID_FIELD_NUMBER = 7;
    private long monsterId_;
    /**
     * <pre>
     * 关联野怪id
     * </pre>
     *
     * <code>optional int64 monsterId = 7;</code>
     * @return Whether the monsterId field is set.
     */
    @java.lang.Override
    public boolean hasMonsterId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 关联野怪id
     * </pre>
     *
     * <code>optional int64 monsterId = 7;</code>
     * @return The monsterId.
     */
    @java.lang.Override
    public long getMonsterId() {
      return monsterId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, skynetTaskId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeEnum(4, skynetTaskStatus_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(5, receiveTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(6, doingTsMs_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt64(7, monsterId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, skynetTaskId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, skynetTaskStatus_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, receiveTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, doingTsMs_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, monsterId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.SkynetTaskPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.SkynetTaskPB other = (com.yorha.proto.StructSkynetPB.SkynetTaskPB) obj;

      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (hasSkynetTaskId() != other.hasSkynetTaskId()) return false;
      if (hasSkynetTaskId()) {
        if (getSkynetTaskId()
            != other.getSkynetTaskId()) return false;
      }
      if (hasSkynetTaskStatus() != other.hasSkynetTaskStatus()) return false;
      if (hasSkynetTaskStatus()) {
        if (skynetTaskStatus_ != other.skynetTaskStatus_) return false;
      }
      if (hasReceiveTsMs() != other.hasReceiveTsMs()) return false;
      if (hasReceiveTsMs()) {
        if (getReceiveTsMs()
            != other.getReceiveTsMs()) return false;
      }
      if (hasDoingTsMs() != other.hasDoingTsMs()) return false;
      if (hasDoingTsMs()) {
        if (getDoingTsMs()
            != other.getDoingTsMs()) return false;
      }
      if (hasMonsterId() != other.hasMonsterId()) return false;
      if (hasMonsterId()) {
        if (getMonsterId()
            != other.getMonsterId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getId();
      }
      if (hasSkynetTaskId()) {
        hash = (37 * hash) + SKYNETTASKID_FIELD_NUMBER;
        hash = (53 * hash) + getSkynetTaskId();
      }
      if (hasSkynetTaskStatus()) {
        hash = (37 * hash) + SKYNETTASKSTATUS_FIELD_NUMBER;
        hash = (53 * hash) + skynetTaskStatus_;
      }
      if (hasReceiveTsMs()) {
        hash = (37 * hash) + RECEIVETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getReceiveTsMs());
      }
      if (hasDoingTsMs()) {
        hash = (37 * hash) + DOINGTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getDoingTsMs());
      }
      if (hasMonsterId()) {
        hash = (37 * hash) + MONSTERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMonsterId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.SkynetTaskPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SkynetTaskPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SkynetTaskPB)
        com.yorha.proto.StructSkynetPB.SkynetTaskPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetTaskPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetTaskPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.SkynetTaskPB.class, com.yorha.proto.StructSkynetPB.SkynetTaskPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.SkynetTaskPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        skynetTaskId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        skynetTaskStatus_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        receiveTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        doingTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        monsterId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetTaskPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetTaskPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetTaskPB build() {
        com.yorha.proto.StructSkynetPB.SkynetTaskPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetTaskPB buildPartial() {
        com.yorha.proto.StructSkynetPB.SkynetTaskPB result = new com.yorha.proto.StructSkynetPB.SkynetTaskPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.skynetTaskId_ = skynetTaskId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.skynetTaskStatus_ = skynetTaskStatus_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.receiveTsMs_ = receiveTsMs_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.doingTsMs_ = doingTsMs_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.monsterId_ = monsterId_;
          to_bitField0_ |= 0x00000020;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.SkynetTaskPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.SkynetTaskPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.SkynetTaskPB other) {
        if (other == com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasSkynetTaskId()) {
          setSkynetTaskId(other.getSkynetTaskId());
        }
        if (other.hasSkynetTaskStatus()) {
          setSkynetTaskStatus(other.getSkynetTaskStatus());
        }
        if (other.hasReceiveTsMs()) {
          setReceiveTsMs(other.getReceiveTsMs());
        }
        if (other.hasDoingTsMs()) {
          setDoingTsMs(other.getDoingTsMs());
        }
        if (other.hasMonsterId()) {
          setMonsterId(other.getMonsterId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.SkynetTaskPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.SkynetTaskPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <pre>
       * 模块内的唯一id
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 模块内的唯一id
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       * 模块内的唯一id
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模块内的唯一id
       * </pre>
       *
       * <code>optional int32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int skynetTaskId_ ;
      /**
       * <pre>
       * 配置任务id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return Whether the skynetTaskId field is set.
       */
      @java.lang.Override
      public boolean hasSkynetTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 配置任务id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return The skynetTaskId.
       */
      @java.lang.Override
      public int getSkynetTaskId() {
        return skynetTaskId_;
      }
      /**
       * <pre>
       * 配置任务id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @param value The skynetTaskId to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetTaskId(int value) {
        bitField0_ |= 0x00000002;
        skynetTaskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 配置任务id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        skynetTaskId_ = 0;
        onChanged();
        return this;
      }

      private int skynetTaskStatus_ = 0;
      /**
       * <pre>
       * 任务状态 0：初始  1：已接取  2：进行中  3：待领奖  4：完结
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskStatus skynetTaskStatus = 4;</code>
       * @return Whether the skynetTaskStatus field is set.
       */
      @java.lang.Override public boolean hasSkynetTaskStatus() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 任务状态 0：初始  1：已接取  2：进行中  3：待领奖  4：完结
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskStatus skynetTaskStatus = 4;</code>
       * @return The skynetTaskStatus.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SkynetTaskStatus getSkynetTaskStatus() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SkynetTaskStatus result = com.yorha.proto.CommonEnum.SkynetTaskStatus.valueOf(skynetTaskStatus_);
        return result == null ? com.yorha.proto.CommonEnum.SkynetTaskStatus.SNTS_NONE : result;
      }
      /**
       * <pre>
       * 任务状态 0：初始  1：已接取  2：进行中  3：待领奖  4：完结
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskStatus skynetTaskStatus = 4;</code>
       * @param value The skynetTaskStatus to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetTaskStatus(com.yorha.proto.CommonEnum.SkynetTaskStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        skynetTaskStatus_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务状态 0：初始  1：已接取  2：进行中  3：待领奖  4：完结
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetTaskStatus skynetTaskStatus = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetTaskStatus() {
        bitField0_ = (bitField0_ & ~0x00000004);
        skynetTaskStatus_ = 0;
        onChanged();
        return this;
      }

      private long receiveTsMs_ ;
      /**
       * <pre>
       * 任务接取时间
       * </pre>
       *
       * <code>optional int64 receiveTsMs = 5;</code>
       * @return Whether the receiveTsMs field is set.
       */
      @java.lang.Override
      public boolean hasReceiveTsMs() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 任务接取时间
       * </pre>
       *
       * <code>optional int64 receiveTsMs = 5;</code>
       * @return The receiveTsMs.
       */
      @java.lang.Override
      public long getReceiveTsMs() {
        return receiveTsMs_;
      }
      /**
       * <pre>
       * 任务接取时间
       * </pre>
       *
       * <code>optional int64 receiveTsMs = 5;</code>
       * @param value The receiveTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setReceiveTsMs(long value) {
        bitField0_ |= 0x00000008;
        receiveTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务接取时间
       * </pre>
       *
       * <code>optional int64 receiveTsMs = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearReceiveTsMs() {
        bitField0_ = (bitField0_ & ~0x00000008);
        receiveTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long doingTsMs_ ;
      /**
       * <pre>
       * 进行中的过期时间
       * </pre>
       *
       * <code>optional int64 doingTsMs = 6;</code>
       * @return Whether the doingTsMs field is set.
       */
      @java.lang.Override
      public boolean hasDoingTsMs() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 进行中的过期时间
       * </pre>
       *
       * <code>optional int64 doingTsMs = 6;</code>
       * @return The doingTsMs.
       */
      @java.lang.Override
      public long getDoingTsMs() {
        return doingTsMs_;
      }
      /**
       * <pre>
       * 进行中的过期时间
       * </pre>
       *
       * <code>optional int64 doingTsMs = 6;</code>
       * @param value The doingTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setDoingTsMs(long value) {
        bitField0_ |= 0x00000010;
        doingTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 进行中的过期时间
       * </pre>
       *
       * <code>optional int64 doingTsMs = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearDoingTsMs() {
        bitField0_ = (bitField0_ & ~0x00000010);
        doingTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long monsterId_ ;
      /**
       * <pre>
       * 关联野怪id
       * </pre>
       *
       * <code>optional int64 monsterId = 7;</code>
       * @return Whether the monsterId field is set.
       */
      @java.lang.Override
      public boolean hasMonsterId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 关联野怪id
       * </pre>
       *
       * <code>optional int64 monsterId = 7;</code>
       * @return The monsterId.
       */
      @java.lang.Override
      public long getMonsterId() {
        return monsterId_;
      }
      /**
       * <pre>
       * 关联野怪id
       * </pre>
       *
       * <code>optional int64 monsterId = 7;</code>
       * @param value The monsterId to set.
       * @return This builder for chaining.
       */
      public Builder setMonsterId(long value) {
        bitField0_ |= 0x00000020;
        monsterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关联野怪id
       * </pre>
       *
       * <code>optional int64 monsterId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonsterId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        monsterId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SkynetTaskPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SkynetTaskPB)
    private static final com.yorha.proto.StructSkynetPB.SkynetTaskPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.SkynetTaskPB();
    }

    public static com.yorha.proto.StructSkynetPB.SkynetTaskPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SkynetTaskPB>
        PARSER = new com.google.protobuf.AbstractParser<SkynetTaskPB>() {
      @java.lang.Override
      public SkynetTaskPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SkynetTaskPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SkynetTaskPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SkynetTaskPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetTaskPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SkynetBaseDataPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SkynetBaseDataPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 一点一点加的，膨胀不了一点
     * </pre>
     *
     * <code>optional int32 skynetExp = 1;</code>
     * @return Whether the skynetExp field is set.
     */
    boolean hasSkynetExp();
    /**
     * <pre>
     * 一点一点加的，膨胀不了一点
     * </pre>
     *
     * <code>optional int32 skynetExp = 1;</code>
     * @return The skynetExp.
     */
    int getSkynetExp();

    /**
     * <pre>
     * 等级
     * </pre>
     *
     * <code>optional int32 curLevel = 2;</code>
     * @return Whether the curLevel field is set.
     */
    boolean hasCurLevel();
    /**
     * <pre>
     * 等级
     * </pre>
     *
     * <code>optional int32 curLevel = 2;</code>
     * @return The curLevel.
     */
    int getCurLevel();

    /**
     * <pre>
     * 情报点
     * </pre>
     *
     * <code>optional int32 totalInfoPoint = 3;</code>
     * @return Whether the totalInfoPoint field is set.
     */
    boolean hasTotalInfoPoint();
    /**
     * <pre>
     * 情报点
     * </pre>
     *
     * <code>optional int32 totalInfoPoint = 3;</code>
     * @return The totalInfoPoint.
     */
    int getTotalInfoPoint();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SkynetBaseDataPB}
   */
  public static final class SkynetBaseDataPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SkynetBaseDataPB)
      SkynetBaseDataPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SkynetBaseDataPB.newBuilder() to construct.
    private SkynetBaseDataPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SkynetBaseDataPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SkynetBaseDataPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SkynetBaseDataPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              skynetExp_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              curLevel_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              totalInfoPoint_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBaseDataPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBaseDataPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.SkynetBaseDataPB.class, com.yorha.proto.StructSkynetPB.SkynetBaseDataPB.Builder.class);
    }

    private int bitField0_;
    public static final int SKYNETEXP_FIELD_NUMBER = 1;
    private int skynetExp_;
    /**
     * <pre>
     * 一点一点加的，膨胀不了一点
     * </pre>
     *
     * <code>optional int32 skynetExp = 1;</code>
     * @return Whether the skynetExp field is set.
     */
    @java.lang.Override
    public boolean hasSkynetExp() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 一点一点加的，膨胀不了一点
     * </pre>
     *
     * <code>optional int32 skynetExp = 1;</code>
     * @return The skynetExp.
     */
    @java.lang.Override
    public int getSkynetExp() {
      return skynetExp_;
    }

    public static final int CURLEVEL_FIELD_NUMBER = 2;
    private int curLevel_;
    /**
     * <pre>
     * 等级
     * </pre>
     *
     * <code>optional int32 curLevel = 2;</code>
     * @return Whether the curLevel field is set.
     */
    @java.lang.Override
    public boolean hasCurLevel() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 等级
     * </pre>
     *
     * <code>optional int32 curLevel = 2;</code>
     * @return The curLevel.
     */
    @java.lang.Override
    public int getCurLevel() {
      return curLevel_;
    }

    public static final int TOTALINFOPOINT_FIELD_NUMBER = 3;
    private int totalInfoPoint_;
    /**
     * <pre>
     * 情报点
     * </pre>
     *
     * <code>optional int32 totalInfoPoint = 3;</code>
     * @return Whether the totalInfoPoint field is set.
     */
    @java.lang.Override
    public boolean hasTotalInfoPoint() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 情报点
     * </pre>
     *
     * <code>optional int32 totalInfoPoint = 3;</code>
     * @return The totalInfoPoint.
     */
    @java.lang.Override
    public int getTotalInfoPoint() {
      return totalInfoPoint_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, skynetExp_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, curLevel_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, totalInfoPoint_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, skynetExp_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, curLevel_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, totalInfoPoint_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.SkynetBaseDataPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.SkynetBaseDataPB other = (com.yorha.proto.StructSkynetPB.SkynetBaseDataPB) obj;

      if (hasSkynetExp() != other.hasSkynetExp()) return false;
      if (hasSkynetExp()) {
        if (getSkynetExp()
            != other.getSkynetExp()) return false;
      }
      if (hasCurLevel() != other.hasCurLevel()) return false;
      if (hasCurLevel()) {
        if (getCurLevel()
            != other.getCurLevel()) return false;
      }
      if (hasTotalInfoPoint() != other.hasTotalInfoPoint()) return false;
      if (hasTotalInfoPoint()) {
        if (getTotalInfoPoint()
            != other.getTotalInfoPoint()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkynetExp()) {
        hash = (37 * hash) + SKYNETEXP_FIELD_NUMBER;
        hash = (53 * hash) + getSkynetExp();
      }
      if (hasCurLevel()) {
        hash = (37 * hash) + CURLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getCurLevel();
      }
      if (hasTotalInfoPoint()) {
        hash = (37 * hash) + TOTALINFOPOINT_FIELD_NUMBER;
        hash = (53 * hash) + getTotalInfoPoint();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.SkynetBaseDataPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SkynetBaseDataPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SkynetBaseDataPB)
        com.yorha.proto.StructSkynetPB.SkynetBaseDataPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBaseDataPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBaseDataPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.SkynetBaseDataPB.class, com.yorha.proto.StructSkynetPB.SkynetBaseDataPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.SkynetBaseDataPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skynetExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        curLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        totalInfoPoint_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetBaseDataPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetBaseDataPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.SkynetBaseDataPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetBaseDataPB build() {
        com.yorha.proto.StructSkynetPB.SkynetBaseDataPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetBaseDataPB buildPartial() {
        com.yorha.proto.StructSkynetPB.SkynetBaseDataPB result = new com.yorha.proto.StructSkynetPB.SkynetBaseDataPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.skynetExp_ = skynetExp_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.curLevel_ = curLevel_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.totalInfoPoint_ = totalInfoPoint_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.SkynetBaseDataPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.SkynetBaseDataPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.SkynetBaseDataPB other) {
        if (other == com.yorha.proto.StructSkynetPB.SkynetBaseDataPB.getDefaultInstance()) return this;
        if (other.hasSkynetExp()) {
          setSkynetExp(other.getSkynetExp());
        }
        if (other.hasCurLevel()) {
          setCurLevel(other.getCurLevel());
        }
        if (other.hasTotalInfoPoint()) {
          setTotalInfoPoint(other.getTotalInfoPoint());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.SkynetBaseDataPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.SkynetBaseDataPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int skynetExp_ ;
      /**
       * <pre>
       * 一点一点加的，膨胀不了一点
       * </pre>
       *
       * <code>optional int32 skynetExp = 1;</code>
       * @return Whether the skynetExp field is set.
       */
      @java.lang.Override
      public boolean hasSkynetExp() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 一点一点加的，膨胀不了一点
       * </pre>
       *
       * <code>optional int32 skynetExp = 1;</code>
       * @return The skynetExp.
       */
      @java.lang.Override
      public int getSkynetExp() {
        return skynetExp_;
      }
      /**
       * <pre>
       * 一点一点加的，膨胀不了一点
       * </pre>
       *
       * <code>optional int32 skynetExp = 1;</code>
       * @param value The skynetExp to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetExp(int value) {
        bitField0_ |= 0x00000001;
        skynetExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 一点一点加的，膨胀不了一点
       * </pre>
       *
       * <code>optional int32 skynetExp = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetExp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        skynetExp_ = 0;
        onChanged();
        return this;
      }

      private int curLevel_ ;
      /**
       * <pre>
       * 等级
       * </pre>
       *
       * <code>optional int32 curLevel = 2;</code>
       * @return Whether the curLevel field is set.
       */
      @java.lang.Override
      public boolean hasCurLevel() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 等级
       * </pre>
       *
       * <code>optional int32 curLevel = 2;</code>
       * @return The curLevel.
       */
      @java.lang.Override
      public int getCurLevel() {
        return curLevel_;
      }
      /**
       * <pre>
       * 等级
       * </pre>
       *
       * <code>optional int32 curLevel = 2;</code>
       * @param value The curLevel to set.
       * @return This builder for chaining.
       */
      public Builder setCurLevel(int value) {
        bitField0_ |= 0x00000002;
        curLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 等级
       * </pre>
       *
       * <code>optional int32 curLevel = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurLevel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        curLevel_ = 0;
        onChanged();
        return this;
      }

      private int totalInfoPoint_ ;
      /**
       * <pre>
       * 情报点
       * </pre>
       *
       * <code>optional int32 totalInfoPoint = 3;</code>
       * @return Whether the totalInfoPoint field is set.
       */
      @java.lang.Override
      public boolean hasTotalInfoPoint() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 情报点
       * </pre>
       *
       * <code>optional int32 totalInfoPoint = 3;</code>
       * @return The totalInfoPoint.
       */
      @java.lang.Override
      public int getTotalInfoPoint() {
        return totalInfoPoint_;
      }
      /**
       * <pre>
       * 情报点
       * </pre>
       *
       * <code>optional int32 totalInfoPoint = 3;</code>
       * @param value The totalInfoPoint to set.
       * @return This builder for chaining.
       */
      public Builder setTotalInfoPoint(int value) {
        bitField0_ |= 0x00000004;
        totalInfoPoint_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 情报点
       * </pre>
       *
       * <code>optional int32 totalInfoPoint = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalInfoPoint() {
        bitField0_ = (bitField0_ & ~0x00000004);
        totalInfoPoint_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SkynetBaseDataPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SkynetBaseDataPB)
    private static final com.yorha.proto.StructSkynetPB.SkynetBaseDataPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.SkynetBaseDataPB();
    }

    public static com.yorha.proto.StructSkynetPB.SkynetBaseDataPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SkynetBaseDataPB>
        PARSER = new com.google.protobuf.AbstractParser<SkynetBaseDataPB>() {
      @java.lang.Override
      public SkynetBaseDataPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SkynetBaseDataPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SkynetBaseDataPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SkynetBaseDataPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetBaseDataPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SkynetShopPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SkynetShopPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
     * @return Whether the goodsHistory field is set.
     */
    boolean hasGoodsHistory();
    /**
     * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
     * @return The goodsHistory.
     */
    com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB getGoodsHistory();
    /**
     * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
     */
    com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPBOrBuilder getGoodsHistoryOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SkynetShopPB}
   */
  public static final class SkynetShopPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SkynetShopPB)
      SkynetShopPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SkynetShopPB.newBuilder() to construct.
    private SkynetShopPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SkynetShopPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SkynetShopPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SkynetShopPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = goodsHistory_.toBuilder();
              }
              goodsHistory_ = input.readMessage(com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(goodsHistory_);
                goodsHistory_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetShopPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetShopPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.SkynetShopPB.class, com.yorha.proto.StructSkynetPB.SkynetShopPB.Builder.class);
    }

    private int bitField0_;
    public static final int GOODSHISTORY_FIELD_NUMBER = 1;
    private com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB goodsHistory_;
    /**
     * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
     * @return Whether the goodsHistory field is set.
     */
    @java.lang.Override
    public boolean hasGoodsHistory() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
     * @return The goodsHistory.
     */
    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB getGoodsHistory() {
      return goodsHistory_ == null ? com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.getDefaultInstance() : goodsHistory_;
    }
    /**
     * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPBOrBuilder getGoodsHistoryOrBuilder() {
      return goodsHistory_ == null ? com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.getDefaultInstance() : goodsHistory_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getGoodsHistory());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getGoodsHistory());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.SkynetShopPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.SkynetShopPB other = (com.yorha.proto.StructSkynetPB.SkynetShopPB) obj;

      if (hasGoodsHistory() != other.hasGoodsHistory()) return false;
      if (hasGoodsHistory()) {
        if (!getGoodsHistory()
            .equals(other.getGoodsHistory())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGoodsHistory()) {
        hash = (37 * hash) + GOODSHISTORY_FIELD_NUMBER;
        hash = (53 * hash) + getGoodsHistory().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.SkynetShopPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.SkynetShopPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SkynetShopPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SkynetShopPB)
        com.yorha.proto.StructSkynetPB.SkynetShopPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetShopPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetShopPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.SkynetShopPB.class, com.yorha.proto.StructSkynetPB.SkynetShopPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.SkynetShopPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getGoodsHistoryFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (goodsHistoryBuilder_ == null) {
          goodsHistory_ = null;
        } else {
          goodsHistoryBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_SkynetShopPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetShopPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.SkynetShopPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetShopPB build() {
        com.yorha.proto.StructSkynetPB.SkynetShopPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.SkynetShopPB buildPartial() {
        com.yorha.proto.StructSkynetPB.SkynetShopPB result = new com.yorha.proto.StructSkynetPB.SkynetShopPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (goodsHistoryBuilder_ == null) {
            result.goodsHistory_ = goodsHistory_;
          } else {
            result.goodsHistory_ = goodsHistoryBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.SkynetShopPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.SkynetShopPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.SkynetShopPB other) {
        if (other == com.yorha.proto.StructSkynetPB.SkynetShopPB.getDefaultInstance()) return this;
        if (other.hasGoodsHistory()) {
          mergeGoodsHistory(other.getGoodsHistory());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.SkynetShopPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.SkynetShopPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB goodsHistory_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB, com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.Builder, com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPBOrBuilder> goodsHistoryBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       * @return Whether the goodsHistory field is set.
       */
      public boolean hasGoodsHistory() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       * @return The goodsHistory.
       */
      public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB getGoodsHistory() {
        if (goodsHistoryBuilder_ == null) {
          return goodsHistory_ == null ? com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.getDefaultInstance() : goodsHistory_;
        } else {
          return goodsHistoryBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       */
      public Builder setGoodsHistory(com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB value) {
        if (goodsHistoryBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          goodsHistory_ = value;
          onChanged();
        } else {
          goodsHistoryBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       */
      public Builder setGoodsHistory(
          com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.Builder builderForValue) {
        if (goodsHistoryBuilder_ == null) {
          goodsHistory_ = builderForValue.build();
          onChanged();
        } else {
          goodsHistoryBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       */
      public Builder mergeGoodsHistory(com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB value) {
        if (goodsHistoryBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              goodsHistory_ != null &&
              goodsHistory_ != com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.getDefaultInstance()) {
            goodsHistory_ =
              com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.newBuilder(goodsHistory_).mergeFrom(value).buildPartial();
          } else {
            goodsHistory_ = value;
          }
          onChanged();
        } else {
          goodsHistoryBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       */
      public Builder clearGoodsHistory() {
        if (goodsHistoryBuilder_ == null) {
          goodsHistory_ = null;
          onChanged();
        } else {
          goodsHistoryBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       */
      public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.Builder getGoodsHistoryBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getGoodsHistoryFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       */
      public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPBOrBuilder getGoodsHistoryOrBuilder() {
        if (goodsHistoryBuilder_ != null) {
          return goodsHistoryBuilder_.getMessageOrBuilder();
        } else {
          return goodsHistory_ == null ?
              com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.getDefaultInstance() : goodsHistory_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32ShopHistoryMapPB goodsHistory = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB, com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.Builder, com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPBOrBuilder> 
          getGoodsHistoryFieldBuilder() {
        if (goodsHistoryBuilder_ == null) {
          goodsHistoryBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB, com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.Builder, com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPBOrBuilder>(
                  getGoodsHistory(),
                  getParentForChildren(),
                  isClean());
          goodsHistory_ = null;
        }
        return goodsHistoryBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SkynetShopPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SkynetShopPB)
    private static final com.yorha.proto.StructSkynetPB.SkynetShopPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.SkynetShopPB();
    }

    public static com.yorha.proto.StructSkynetPB.SkynetShopPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SkynetShopPB>
        PARSER = new com.google.protobuf.AbstractParser<SkynetShopPB>() {
      @java.lang.Override
      public SkynetShopPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SkynetShopPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SkynetShopPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SkynetShopPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.SkynetShopPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ShopHistoryPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ShopHistoryPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 shopId = 1;</code>
     * @return Whether the shopId field is set.
     */
    boolean hasShopId();
    /**
     * <code>optional int32 shopId = 1;</code>
     * @return The shopId.
     */
    int getShopId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ShopHistoryPB}
   */
  public static final class ShopHistoryPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ShopHistoryPB)
      ShopHistoryPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ShopHistoryPB.newBuilder() to construct.
    private ShopHistoryPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ShopHistoryPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ShopHistoryPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ShopHistoryPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              shopId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_ShopHistoryPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_ShopHistoryPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.ShopHistoryPB.class, com.yorha.proto.StructSkynetPB.ShopHistoryPB.Builder.class);
    }

    private int bitField0_;
    public static final int SHOPID_FIELD_NUMBER = 1;
    private int shopId_;
    /**
     * <code>optional int32 shopId = 1;</code>
     * @return Whether the shopId field is set.
     */
    @java.lang.Override
    public boolean hasShopId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 shopId = 1;</code>
     * @return The shopId.
     */
    @java.lang.Override
    public int getShopId() {
      return shopId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, shopId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, shopId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.ShopHistoryPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.ShopHistoryPB other = (com.yorha.proto.StructSkynetPB.ShopHistoryPB) obj;

      if (hasShopId() != other.hasShopId()) return false;
      if (hasShopId()) {
        if (getShopId()
            != other.getShopId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasShopId()) {
        hash = (37 * hash) + SHOPID_FIELD_NUMBER;
        hash = (53 * hash) + getShopId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.ShopHistoryPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ShopHistoryPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ShopHistoryPB)
        com.yorha.proto.StructSkynetPB.ShopHistoryPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_ShopHistoryPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_ShopHistoryPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.ShopHistoryPB.class, com.yorha.proto.StructSkynetPB.ShopHistoryPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.ShopHistoryPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        shopId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_ShopHistoryPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.ShopHistoryPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.ShopHistoryPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.ShopHistoryPB build() {
        com.yorha.proto.StructSkynetPB.ShopHistoryPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.ShopHistoryPB buildPartial() {
        com.yorha.proto.StructSkynetPB.ShopHistoryPB result = new com.yorha.proto.StructSkynetPB.ShopHistoryPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.shopId_ = shopId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.ShopHistoryPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.ShopHistoryPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.ShopHistoryPB other) {
        if (other == com.yorha.proto.StructSkynetPB.ShopHistoryPB.getDefaultInstance()) return this;
        if (other.hasShopId()) {
          setShopId(other.getShopId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.ShopHistoryPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.ShopHistoryPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int shopId_ ;
      /**
       * <code>optional int32 shopId = 1;</code>
       * @return Whether the shopId field is set.
       */
      @java.lang.Override
      public boolean hasShopId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 shopId = 1;</code>
       * @return The shopId.
       */
      @java.lang.Override
      public int getShopId() {
        return shopId_;
      }
      /**
       * <code>optional int32 shopId = 1;</code>
       * @param value The shopId to set.
       * @return This builder for chaining.
       */
      public Builder setShopId(int value) {
        bitField0_ |= 0x00000001;
        shopId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 shopId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearShopId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        shopId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ShopHistoryPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ShopHistoryPB)
    private static final com.yorha.proto.StructSkynetPB.ShopHistoryPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.ShopHistoryPB();
    }

    public static com.yorha.proto.StructSkynetPB.ShopHistoryPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ShopHistoryPB>
        PARSER = new com.google.protobuf.AbstractParser<ShopHistoryPB>() {
      @java.lang.Override
      public ShopHistoryPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ShopHistoryPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ShopHistoryPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ShopHistoryPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.ShopHistoryPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int32SkynetTaskMapPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32SkynetTaskMapPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */
    boolean containsDatas(
        int key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>
    getDatas();
    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>
    getDatasMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructSkynetPB.SkynetTaskPB getDatasOrDefault(
        int key,
        com.yorha.proto.StructSkynetPB.SkynetTaskPB defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructSkynetPB.SkynetTaskPB getDatasOrThrow(
        int key);

    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Integer> getDeleteKeysList();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    int getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32SkynetTaskMapPB}
   */
  public static final class Int32SkynetTaskMapPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32SkynetTaskMapPB)
      Int32SkynetTaskMapPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32SkynetTaskMapPB.newBuilder() to construct.
    private Int32SkynetTaskMapPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32SkynetTaskMapPB() {
      deleteKeys_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32SkynetTaskMapPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32SkynetTaskMapPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32SkynetTaskMapPB_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32SkynetTaskMapPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.class, com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>newDefaultInstance(
                  com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32SkynetTaskMapPB_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructSkynetPB.SkynetTaskPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> datas_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        int key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructSkynetPB.SkynetTaskPB getDatasOrDefault(
        int key,
        com.yorha.proto.StructSkynetPB.SkynetTaskPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructSkynetPB.SkynetTaskPB getDatasOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList deleteKeys_;
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public int getDeleteKeys(int index) {
      return deleteKeys_.getInt(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt32(2, deleteKeys_.getInt(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(deleteKeys_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB other = (com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32SkynetTaskMapPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32SkynetTaskMapPB)
        com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32SkynetTaskMapPB_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32SkynetTaskMapPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.class, com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32SkynetTaskMapPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB build() {
        com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB buildPartial() {
        com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB result = new com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB other) {
        if (other == com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> datas_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          int key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructSkynetPB.SkynetTaskPB getDatasOrDefault(
          int key,
          com.yorha.proto.StructSkynetPB.SkynetTaskPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructSkynetPB.SkynetTaskPB getDatasOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          int key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
       */
      public Builder putDatas(
          int key,
          com.yorha.proto.StructSkynetPB.SkynetTaskPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.SkynetTaskPB&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.SkynetTaskPB> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.IntList deleteKeys_ = emptyIntList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Integer>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public int getDeleteKeys(int index) {
        return deleteKeys_.getInt(index);
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32SkynetTaskMapPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32SkynetTaskMapPB)
    private static final com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB();
    }

    public static com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32SkynetTaskMapPB>
        PARSER = new com.google.protobuf.AbstractParser<Int32SkynetTaskMapPB>() {
      @java.lang.Override
      public Int32SkynetTaskMapPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32SkynetTaskMapPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32SkynetTaskMapPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32SkynetTaskMapPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.Int32SkynetTaskMapPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int32ShopHistoryMapPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32ShopHistoryMapPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */
    boolean containsDatas(
        int key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>
    getDatas();
    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>
    getDatasMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructSkynetPB.ShopHistoryPB getDatasOrDefault(
        int key,
        com.yorha.proto.StructSkynetPB.ShopHistoryPB defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructSkynetPB.ShopHistoryPB getDatasOrThrow(
        int key);

    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Integer> getDeleteKeysList();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    int getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32ShopHistoryMapPB}
   */
  public static final class Int32ShopHistoryMapPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32ShopHistoryMapPB)
      Int32ShopHistoryMapPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32ShopHistoryMapPB.newBuilder() to construct.
    private Int32ShopHistoryMapPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32ShopHistoryMapPB() {
      deleteKeys_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32ShopHistoryMapPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32ShopHistoryMapPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32ShopHistoryMapPB_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32ShopHistoryMapPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.class, com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>newDefaultInstance(
                  com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32ShopHistoryMapPB_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructSkynetPB.ShopHistoryPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> datas_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        int key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructSkynetPB.ShopHistoryPB getDatasOrDefault(
        int key,
        com.yorha.proto.StructSkynetPB.ShopHistoryPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructSkynetPB.ShopHistoryPB getDatasOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList deleteKeys_;
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public int getDeleteKeys(int index) {
      return deleteKeys_.getInt(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt32(2, deleteKeys_.getInt(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(deleteKeys_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB other = (com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32ShopHistoryMapPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32ShopHistoryMapPB)
        com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32ShopHistoryMapPB_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32ShopHistoryMapPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.class, com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructSkynetPB.internal_static_com_yorha_proto_Int32ShopHistoryMapPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB getDefaultInstanceForType() {
        return com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB build() {
        com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB buildPartial() {
        com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB result = new com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB) {
          return mergeFrom((com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB other) {
        if (other == com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> datas_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          int key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructSkynetPB.ShopHistoryPB getDatasOrDefault(
          int key,
          com.yorha.proto.StructSkynetPB.ShopHistoryPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructSkynetPB.ShopHistoryPB getDatasOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          int key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
       */
      public Builder putDatas(
          int key,
          com.yorha.proto.StructSkynetPB.ShopHistoryPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ShopHistoryPB&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructSkynetPB.ShopHistoryPB> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.IntList deleteKeys_ = emptyIntList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Integer>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public int getDeleteKeys(int index) {
        return deleteKeys_.getInt(index);
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32ShopHistoryMapPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32ShopHistoryMapPB)
    private static final com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB();
    }

    public static com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32ShopHistoryMapPB>
        PARSER = new com.google.protobuf.AbstractParser<Int32ShopHistoryMapPB>() {
      @java.lang.Override
      public Int32ShopHistoryMapPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32ShopHistoryMapPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32ShopHistoryMapPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32ShopHistoryMapPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructSkynetPB.Int32ShopHistoryMapPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SkynetNormalTaskPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SkynetNormalTaskPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SkynetBossTaskPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SkynetBossTaskPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SkynetGuardTaskPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SkynetGuardTaskPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SkynetTaskPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SkynetTaskPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SkynetBaseDataPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SkynetBaseDataPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SkynetShopPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SkynetShopPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ShopHistoryPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ShopHistoryPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32SkynetTaskMapPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32SkynetTaskMapPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32SkynetTaskMapPB_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32SkynetTaskMapPB_DatasEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32ShopHistoryMapPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32ShopHistoryMapPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32ShopHistoryMapPB_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32ShopHistoryMapPB_DatasEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)cs_proto/gen/common/struct_skynetPB.pr" +
      "oto\022\017com.yorha.proto\032%ss_proto/gen/commo" +
      "n/common_enum.proto\"}\n\022SkynetNormalTaskP" +
      "B\0229\n\nnormalTask\030\001 \001(\0132%.com.yorha.proto." +
      "Int32SkynetTaskMapPB\022\027\n\017lastRefreshTsMs\030" +
      "\002 \001(\003\022\023\n\013taskPoolNum\030\003 \001(\005\"C\n\020SkynetBoss" +
      "TaskPB\022/\n\010bossTask\030\001 \001(\0132\035.com.yorha.pro" +
      "to.SkynetTaskPB\"s\n\021SkynetGuardTaskPB\0220\n\t" +
      "guardTask\030\001 \001(\0132\035.com.yorha.proto.Skynet" +
      "TaskPB\022\027\n\017lastRefreshTsMs\030\002 \001(\003\022\023\n\013taskP" +
      "oolNum\030\003 \001(\005\"\250\001\n\014SkynetTaskPB\022\n\n\002id\030\001 \001(" +
      "\005\022\024\n\014skynetTaskId\030\002 \001(\005\022;\n\020skynetTaskSta" +
      "tus\030\004 \001(\0162!.com.yorha.proto.SkynetTaskSt" +
      "atus\022\023\n\013receiveTsMs\030\005 \001(\003\022\021\n\tdoingTsMs\030\006" +
      " \001(\003\022\021\n\tmonsterId\030\007 \001(\003\"O\n\020SkynetBaseDat" +
      "aPB\022\021\n\tskynetExp\030\001 \001(\005\022\020\n\010curLevel\030\002 \001(\005" +
      "\022\026\n\016totalInfoPoint\030\003 \001(\005\"L\n\014SkynetShopPB" +
      "\022<\n\014goodsHistory\030\001 \001(\0132&.com.yorha.proto" +
      ".Int32ShopHistoryMapPB\",\n\rShopHistoryPB\022" +
      "\016\n\006shopId\030\001 \001(\005\022\013\n\003num\030\002 \001(\005\"\313\001\n\024Int32Sk" +
      "ynetTaskMapPB\022?\n\005datas\030\001 \003(\01320.com.yorha" +
      ".proto.Int32SkynetTaskMapPB.DatasEntry\022\022" +
      "\n\ndeleteKeys\030\002 \003(\005\022\021\n\tclearFlag\030\003 \001(\010\032K\n" +
      "\nDatasEntry\022\013\n\003key\030\001 \001(\005\022,\n\005value\030\002 \001(\0132" +
      "\035.com.yorha.proto.SkynetTaskPB:\0028\001\"\316\001\n\025I" +
      "nt32ShopHistoryMapPB\022@\n\005datas\030\001 \003(\01321.co" +
      "m.yorha.proto.Int32ShopHistoryMapPB.Data" +
      "sEntry\022\022\n\ndeleteKeys\030\002 \003(\005\022\021\n\tclearFlag\030" +
      "\003 \001(\010\032L\n\nDatasEntry\022\013\n\003key\030\001 \001(\005\022-\n\005valu" +
      "e\030\002 \001(\0132\036.com.yorha.proto.ShopHistoryPB:" +
      "\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_SkynetNormalTaskPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SkynetNormalTaskPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SkynetNormalTaskPB_descriptor,
        new java.lang.String[] { "NormalTask", "LastRefreshTsMs", "TaskPoolNum", });
    internal_static_com_yorha_proto_SkynetBossTaskPB_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_SkynetBossTaskPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SkynetBossTaskPB_descriptor,
        new java.lang.String[] { "BossTask", });
    internal_static_com_yorha_proto_SkynetGuardTaskPB_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SkynetGuardTaskPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SkynetGuardTaskPB_descriptor,
        new java.lang.String[] { "GuardTask", "LastRefreshTsMs", "TaskPoolNum", });
    internal_static_com_yorha_proto_SkynetTaskPB_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_SkynetTaskPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SkynetTaskPB_descriptor,
        new java.lang.String[] { "Id", "SkynetTaskId", "SkynetTaskStatus", "ReceiveTsMs", "DoingTsMs", "MonsterId", });
    internal_static_com_yorha_proto_SkynetBaseDataPB_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_SkynetBaseDataPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SkynetBaseDataPB_descriptor,
        new java.lang.String[] { "SkynetExp", "CurLevel", "TotalInfoPoint", });
    internal_static_com_yorha_proto_SkynetShopPB_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_SkynetShopPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SkynetShopPB_descriptor,
        new java.lang.String[] { "GoodsHistory", });
    internal_static_com_yorha_proto_ShopHistoryPB_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_ShopHistoryPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ShopHistoryPB_descriptor,
        new java.lang.String[] { "ShopId", "Num", });
    internal_static_com_yorha_proto_Int32SkynetTaskMapPB_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Int32SkynetTaskMapPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32SkynetTaskMapPB_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int32SkynetTaskMapPB_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int32SkynetTaskMapPB_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int32SkynetTaskMapPB_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32SkynetTaskMapPB_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Int32ShopHistoryMapPB_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Int32ShopHistoryMapPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32ShopHistoryMapPB_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int32ShopHistoryMapPB_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int32ShopHistoryMapPB_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int32ShopHistoryMapPB_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32ShopHistoryMapPB_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_collect.proto

package com.yorha.proto;

public final class SsSceneCollect {
  private SsSceneCollect() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SearchResourceAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchResourceAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 level = 1;</code>
     * @return Whether the level field is set.
     */
    boolean hasLevel();
    /**
     * <code>optional int32 level = 1;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
     * @return Whether the currencyType field is set.
     */
    boolean hasCurrencyType();
    /**
     * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
     * @return The currencyType.
     */
    com.yorha.proto.CommonEnum.CurrencyType getCurrencyType();

    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchResourceAsk}
   */
  public static final class SearchResourceAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchResourceAsk)
      SearchResourceAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchResourceAsk.newBuilder() to construct.
    private SearchResourceAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchResourceAsk() {
      currencyType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchResourceAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchResourceAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              level_ = input.readInt32();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.CurrencyType value = com.yorha.proto.CommonEnum.CurrencyType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                currencyType_ = rawValue;
              }
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCollect.SearchResourceAsk.class, com.yorha.proto.SsSceneCollect.SearchResourceAsk.Builder.class);
    }

    private int bitField0_;
    public static final int LEVEL_FIELD_NUMBER = 1;
    private int level_;
    /**
     * <code>optional int32 level = 1;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 level = 1;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int CURRENCYTYPE_FIELD_NUMBER = 2;
    private int currencyType_;
    /**
     * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
     * @return Whether the currencyType field is set.
     */
    @java.lang.Override public boolean hasCurrencyType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
     * @return The currencyType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.CurrencyType getCurrencyType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.CurrencyType result = com.yorha.proto.CommonEnum.CurrencyType.valueOf(currencyType_);
      return result == null ? com.yorha.proto.CommonEnum.CurrencyType.CT_None : result;
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, level_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, currencyType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, level_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, currencyType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCollect.SearchResourceAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCollect.SearchResourceAsk other = (com.yorha.proto.SsSceneCollect.SearchResourceAsk) obj;

      if (hasLevel() != other.hasLevel()) return false;
      if (hasLevel()) {
        if (getLevel()
            != other.getLevel()) return false;
      }
      if (hasCurrencyType() != other.hasCurrencyType()) return false;
      if (hasCurrencyType()) {
        if (currencyType_ != other.currencyType_) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLevel()) {
        hash = (37 * hash) + LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getLevel();
      }
      if (hasCurrencyType()) {
        hash = (37 * hash) + CURRENCYTYPE_FIELD_NUMBER;
        hash = (53 * hash) + currencyType_;
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCollect.SearchResourceAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchResourceAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchResourceAsk)
        com.yorha.proto.SsSceneCollect.SearchResourceAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCollect.SearchResourceAsk.class, com.yorha.proto.SsSceneCollect.SearchResourceAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCollect.SearchResourceAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        level_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        currencyType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCollect.SearchResourceAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCollect.SearchResourceAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCollect.SearchResourceAsk build() {
        com.yorha.proto.SsSceneCollect.SearchResourceAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCollect.SearchResourceAsk buildPartial() {
        com.yorha.proto.SsSceneCollect.SearchResourceAsk result = new com.yorha.proto.SsSceneCollect.SearchResourceAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.level_ = level_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.currencyType_ = currencyType_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCollect.SearchResourceAsk) {
          return mergeFrom((com.yorha.proto.SsSceneCollect.SearchResourceAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCollect.SearchResourceAsk other) {
        if (other == com.yorha.proto.SsSceneCollect.SearchResourceAsk.getDefaultInstance()) return this;
        if (other.hasLevel()) {
          setLevel(other.getLevel());
        }
        if (other.hasCurrencyType()) {
          setCurrencyType(other.getCurrencyType());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCollect.SearchResourceAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCollect.SearchResourceAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int level_ ;
      /**
       * <code>optional int32 level = 1;</code>
       * @return Whether the level field is set.
       */
      @java.lang.Override
      public boolean hasLevel() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 level = 1;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <code>optional int32 level = 1;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        bitField0_ |= 0x00000001;
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 level = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        level_ = 0;
        onChanged();
        return this;
      }

      private int currencyType_ = 0;
      /**
       * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
       * @return Whether the currencyType field is set.
       */
      @java.lang.Override public boolean hasCurrencyType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
       * @return The currencyType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.CurrencyType getCurrencyType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.CurrencyType result = com.yorha.proto.CommonEnum.CurrencyType.valueOf(currencyType_);
        return result == null ? com.yorha.proto.CommonEnum.CurrencyType.CT_None : result;
      }
      /**
       * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
       * @param value The currencyType to set.
       * @return This builder for chaining.
       */
      public Builder setCurrencyType(com.yorha.proto.CommonEnum.CurrencyType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        currencyType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrencyType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        currencyType_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchResourceAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchResourceAsk)
    private static final com.yorha.proto.SsSceneCollect.SearchResourceAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCollect.SearchResourceAsk();
    }

    public static com.yorha.proto.SsSceneCollect.SearchResourceAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchResourceAsk>
        PARSER = new com.google.protobuf.AbstractParser<SearchResourceAsk>() {
      @java.lang.Override
      public SearchResourceAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchResourceAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchResourceAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchResourceAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCollect.SearchResourceAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchResourceAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchResourceAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return The point.
     */
    com.yorha.proto.Struct.Point getPoint();
    /**
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder();

    /**
     * <code>optional int64 entityId = 2;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <code>optional int64 entityId = 2;</code>
     * @return The entityId.
     */
    long getEntityId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchResourceAns}
   */
  public static final class SearchResourceAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchResourceAns)
      SearchResourceAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchResourceAns.newBuilder() to construct.
    private SearchResourceAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchResourceAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchResourceAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchResourceAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              entityId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneCollect.SearchResourceAns.class, com.yorha.proto.SsSceneCollect.SearchResourceAns.Builder.class);
    }

    private int bitField0_;
    public static final int POINT_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.Point point_;
    /**
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }
    /**
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }

    public static final int ENTITYID_FIELD_NUMBER = 2;
    private long entityId_;
    /**
     * <code>optional int64 entityId = 2;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 entityId = 2;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, entityId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, entityId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneCollect.SearchResourceAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneCollect.SearchResourceAns other = (com.yorha.proto.SsSceneCollect.SearchResourceAns) obj;

      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneCollect.SearchResourceAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneCollect.SearchResourceAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchResourceAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchResourceAns)
        com.yorha.proto.SsSceneCollect.SearchResourceAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneCollect.SearchResourceAns.class, com.yorha.proto.SsSceneCollect.SearchResourceAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneCollect.SearchResourceAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneCollect.internal_static_com_yorha_proto_SearchResourceAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCollect.SearchResourceAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneCollect.SearchResourceAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCollect.SearchResourceAns build() {
        com.yorha.proto.SsSceneCollect.SearchResourceAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneCollect.SearchResourceAns buildPartial() {
        com.yorha.proto.SsSceneCollect.SearchResourceAns result = new com.yorha.proto.SsSceneCollect.SearchResourceAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneCollect.SearchResourceAns) {
          return mergeFrom((com.yorha.proto.SsSceneCollect.SearchResourceAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneCollect.SearchResourceAns other) {
        if (other == com.yorha.proto.SsSceneCollect.SearchResourceAns.getDefaultInstance()) return this;
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneCollect.SearchResourceAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneCollect.SearchResourceAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.Point point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       * @return The point.
       */
      public com.yorha.proto.Struct.Point getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder setPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder setPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder mergePoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            point_ =
              com.yorha.proto.Struct.Point.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private long entityId_ ;
      /**
       * <code>optional int64 entityId = 2;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 entityId = 2;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>optional int64 entityId = 2;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000002;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        entityId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchResourceAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchResourceAns)
    private static final com.yorha.proto.SsSceneCollect.SearchResourceAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneCollect.SearchResourceAns();
    }

    public static com.yorha.proto.SsSceneCollect.SearchResourceAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchResourceAns>
        PARSER = new com.google.protobuf.AbstractParser<SearchResourceAns>() {
      @java.lang.Override
      public SearchResourceAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchResourceAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchResourceAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchResourceAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneCollect.SearchResourceAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchResourceAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchResourceAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchResourceAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchResourceAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)ss_proto/gen/scene/ss_scene_collect.pr" +
      "oto\022\017com.yorha.proto\032%ss_proto/gen/commo" +
      "n/common_enum.proto\032 ss_proto/gen/common" +
      "/struct.proto\"i\n\021SearchResourceAsk\022\r\n\005le" +
      "vel\030\001 \001(\005\0223\n\014currencyType\030\002 \001(\0162\035.com.yo" +
      "rha.proto.CurrencyType\022\020\n\010playerId\030\003 \001(\003" +
      "\"L\n\021SearchResourceAns\022%\n\005point\030\001 \001(\0132\026.c" +
      "om.yorha.proto.Point\022\020\n\010entityId\030\002 \001(\003B\002" +
      "H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_SearchResourceAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SearchResourceAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchResourceAsk_descriptor,
        new java.lang.String[] { "Level", "CurrencyType", "PlayerId", });
    internal_static_com_yorha_proto_SearchResourceAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_SearchResourceAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchResourceAns_descriptor,
        new java.lang.String[] { "Point", "EntityId", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/activity/ss_activity.proto

package com.yorha.proto;

public final class SsActivity {
  private SsActivity() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TakeActivityRewardAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.TakeActivityRewardAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    boolean hasActivityId();
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    int getActivityId();

    /**
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.TakeActivityRewardAsk}
   */
  public static final class TakeActivityRewardAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.TakeActivityRewardAsk)
      TakeActivityRewardAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TakeActivityRewardAsk.newBuilder() to construct.
    private TakeActivityRewardAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TakeActivityRewardAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TakeActivityRewardAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TakeActivityRewardAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              activityId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsActivity.TakeActivityRewardAsk.class, com.yorha.proto.SsActivity.TakeActivityRewardAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ACTIVITYID_FIELD_NUMBER = 1;
    private int activityId_;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 2;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, activityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, activityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsActivity.TakeActivityRewardAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsActivity.TakeActivityRewardAsk other = (com.yorha.proto.SsActivity.TakeActivityRewardAsk) obj;

      if (hasActivityId() != other.hasActivityId()) return false;
      if (hasActivityId()) {
        if (getActivityId()
            != other.getActivityId()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActivityId()) {
        hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
        hash = (53 * hash) + getActivityId();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsActivity.TakeActivityRewardAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.TakeActivityRewardAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.TakeActivityRewardAsk)
        com.yorha.proto.SsActivity.TakeActivityRewardAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsActivity.TakeActivityRewardAsk.class, com.yorha.proto.SsActivity.TakeActivityRewardAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsActivity.TakeActivityRewardAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        activityId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.TakeActivityRewardAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsActivity.TakeActivityRewardAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.TakeActivityRewardAsk build() {
        com.yorha.proto.SsActivity.TakeActivityRewardAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.TakeActivityRewardAsk buildPartial() {
        com.yorha.proto.SsActivity.TakeActivityRewardAsk result = new com.yorha.proto.SsActivity.TakeActivityRewardAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.activityId_ = activityId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsActivity.TakeActivityRewardAsk) {
          return mergeFrom((com.yorha.proto.SsActivity.TakeActivityRewardAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsActivity.TakeActivityRewardAsk other) {
        if (other == com.yorha.proto.SsActivity.TakeActivityRewardAsk.getDefaultInstance()) return this;
        if (other.hasActivityId()) {
          setActivityId(other.getActivityId());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsActivity.TakeActivityRewardAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsActivity.TakeActivityRewardAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int activityId_ ;
      /**
       * <code>optional int32 activityId = 1;</code>
       * @return Whether the activityId field is set.
       */
      @java.lang.Override
      public boolean hasActivityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 activityId = 1;</code>
       * @return The activityId.
       */
      @java.lang.Override
      public int getActivityId() {
        return activityId_;
      }
      /**
       * <code>optional int32 activityId = 1;</code>
       * @param value The activityId to set.
       * @return This builder for chaining.
       */
      public Builder setActivityId(int value) {
        bitField0_ |= 0x00000001;
        activityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 activityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActivityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        activityId_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000002;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.TakeActivityRewardAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.TakeActivityRewardAsk)
    private static final com.yorha.proto.SsActivity.TakeActivityRewardAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsActivity.TakeActivityRewardAsk();
    }

    public static com.yorha.proto.SsActivity.TakeActivityRewardAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TakeActivityRewardAsk>
        PARSER = new com.google.protobuf.AbstractParser<TakeActivityRewardAsk>() {
      @java.lang.Override
      public TakeActivityRewardAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TakeActivityRewardAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TakeActivityRewardAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TakeActivityRewardAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsActivity.TakeActivityRewardAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TakeActivityRewardAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.TakeActivityRewardAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.TakeActivityRewardAns}
   */
  public static final class TakeActivityRewardAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.TakeActivityRewardAns)
      TakeActivityRewardAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TakeActivityRewardAns.newBuilder() to construct.
    private TakeActivityRewardAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TakeActivityRewardAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TakeActivityRewardAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TakeActivityRewardAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsActivity.TakeActivityRewardAns.class, com.yorha.proto.SsActivity.TakeActivityRewardAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsActivity.TakeActivityRewardAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsActivity.TakeActivityRewardAns other = (com.yorha.proto.SsActivity.TakeActivityRewardAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.TakeActivityRewardAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsActivity.TakeActivityRewardAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.TakeActivityRewardAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.TakeActivityRewardAns)
        com.yorha.proto.SsActivity.TakeActivityRewardAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsActivity.TakeActivityRewardAns.class, com.yorha.proto.SsActivity.TakeActivityRewardAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsActivity.TakeActivityRewardAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_TakeActivityRewardAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.TakeActivityRewardAns getDefaultInstanceForType() {
        return com.yorha.proto.SsActivity.TakeActivityRewardAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.TakeActivityRewardAns build() {
        com.yorha.proto.SsActivity.TakeActivityRewardAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.TakeActivityRewardAns buildPartial() {
        com.yorha.proto.SsActivity.TakeActivityRewardAns result = new com.yorha.proto.SsActivity.TakeActivityRewardAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsActivity.TakeActivityRewardAns) {
          return mergeFrom((com.yorha.proto.SsActivity.TakeActivityRewardAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsActivity.TakeActivityRewardAns other) {
        if (other == com.yorha.proto.SsActivity.TakeActivityRewardAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsActivity.TakeActivityRewardAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsActivity.TakeActivityRewardAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.TakeActivityRewardAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.TakeActivityRewardAns)
    private static final com.yorha.proto.SsActivity.TakeActivityRewardAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsActivity.TakeActivityRewardAns();
    }

    public static com.yorha.proto.SsActivity.TakeActivityRewardAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TakeActivityRewardAns>
        PARSER = new com.google.protobuf.AbstractParser<TakeActivityRewardAns>() {
      @java.lang.Override
      public TakeActivityRewardAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TakeActivityRewardAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TakeActivityRewardAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TakeActivityRewardAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsActivity.TakeActivityRewardAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetAllActivityStatusAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetAllActivityStatusAsk)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetAllActivityStatusAsk}
   */
  public static final class GetAllActivityStatusAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetAllActivityStatusAsk)
      GetAllActivityStatusAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetAllActivityStatusAsk.newBuilder() to construct.
    private GetAllActivityStatusAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetAllActivityStatusAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetAllActivityStatusAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetAllActivityStatusAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsActivity.GetAllActivityStatusAsk.class, com.yorha.proto.SsActivity.GetAllActivityStatusAsk.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsActivity.GetAllActivityStatusAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsActivity.GetAllActivityStatusAsk other = (com.yorha.proto.SsActivity.GetAllActivityStatusAsk) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsActivity.GetAllActivityStatusAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetAllActivityStatusAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetAllActivityStatusAsk)
        com.yorha.proto.SsActivity.GetAllActivityStatusAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsActivity.GetAllActivityStatusAsk.class, com.yorha.proto.SsActivity.GetAllActivityStatusAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsActivity.GetAllActivityStatusAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.GetAllActivityStatusAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsActivity.GetAllActivityStatusAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.GetAllActivityStatusAsk build() {
        com.yorha.proto.SsActivity.GetAllActivityStatusAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.GetAllActivityStatusAsk buildPartial() {
        com.yorha.proto.SsActivity.GetAllActivityStatusAsk result = new com.yorha.proto.SsActivity.GetAllActivityStatusAsk(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsActivity.GetAllActivityStatusAsk) {
          return mergeFrom((com.yorha.proto.SsActivity.GetAllActivityStatusAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsActivity.GetAllActivityStatusAsk other) {
        if (other == com.yorha.proto.SsActivity.GetAllActivityStatusAsk.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsActivity.GetAllActivityStatusAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsActivity.GetAllActivityStatusAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetAllActivityStatusAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetAllActivityStatusAsk)
    private static final com.yorha.proto.SsActivity.GetAllActivityStatusAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsActivity.GetAllActivityStatusAsk();
    }

    public static com.yorha.proto.SsActivity.GetAllActivityStatusAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetAllActivityStatusAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetAllActivityStatusAsk>() {
      @java.lang.Override
      public GetAllActivityStatusAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetAllActivityStatusAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetAllActivityStatusAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetAllActivityStatusAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsActivity.GetAllActivityStatusAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetAllActivityStatusAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetAllActivityStatusAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */
    int getRetCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */
    boolean containsRet(
        int key);
    /**
     * Use {@link #getRetMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus>
    getRet();
    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus>
    getRetMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */
    com.yorha.proto.CommonEnum.ActivityStatus getRetOrDefault(
        int key,
        com.yorha.proto.CommonEnum.ActivityStatus defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */
    com.yorha.proto.CommonEnum.ActivityStatus getRetOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetAllActivityStatusAns}
   */
  public static final class GetAllActivityStatusAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetAllActivityStatusAns)
      GetAllActivityStatusAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetAllActivityStatusAns.newBuilder() to construct.
    private GetAllActivityStatusAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetAllActivityStatusAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetAllActivityStatusAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetAllActivityStatusAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                ret_ = com.google.protobuf.MapField.newMapField(
                    RetDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.ByteString bytes = input.readBytes();
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              ret__ = RetDefaultEntryHolder.defaultEntry.getParserForType().parseFrom(bytes);
              if (com.yorha.proto.CommonEnum.ActivityStatus.forNumber(ret__.getValue()) == null) {
                unknownFields.mergeLengthDelimitedField(1, bytes);
              } else {
                ret_.getMutableMap().put(
                    ret__.getKey(), ret__.getValue());
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetRet();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsActivity.GetAllActivityStatusAns.class, com.yorha.proto.SsActivity.GetAllActivityStatusAns.Builder.class);
    }

    public static final int RET_FIELD_NUMBER = 1;
    private static final class RetDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAns_RetEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.ENUM,
                  com.yorha.proto.CommonEnum.ActivityStatus.ACS_NONE.getNumber());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> ret_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetRet() {
      if (ret_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            RetDefaultEntryHolder.defaultEntry);
      }
      return ret_;
    }
    private static final
    com.google.protobuf.Internal.MapAdapter.Converter<
        java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus> retValueConverter =
            com.google.protobuf.Internal.MapAdapter.newEnumConverter(
                com.yorha.proto.CommonEnum.ActivityStatus.internalGetValueMap(),
                com.yorha.proto.CommonEnum.ActivityStatus.ACS_NONE);
    private static final java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus>
    internalGetAdaptedRetMap(
        java.util.Map<java.lang.Integer, java.lang.Integer> map) {
      return new com.google.protobuf.Internal.MapAdapter<
          java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus, java.lang.Integer>(
              map, retValueConverter);
    }

    public int getRetCount() {
      return internalGetRet().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */

    @java.lang.Override
    public boolean containsRet(
        int key) {
      
      return internalGetRet().getMap().containsKey(key);
    }
    /**
     * Use {@link #getRetMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus>
    getRet() {
      return getRetMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus>
    getRetMap() {
      return internalGetAdaptedRetMap(
          internalGetRet().getMap());}
    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonEnum.ActivityStatus getRetOrDefault(
        int key,
        com.yorha.proto.CommonEnum.ActivityStatus defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetRet().getMap();
      return map.containsKey(key)
             ? retValueConverter.doForward(map.get(key))
             : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonEnum.ActivityStatus getRetOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetRet().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return retValueConverter.doForward(map.get(key));
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetRet(),
          RetDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetRet().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        ret__ = RetDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, ret__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsActivity.GetAllActivityStatusAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsActivity.GetAllActivityStatusAns other = (com.yorha.proto.SsActivity.GetAllActivityStatusAns) obj;

      if (!internalGetRet().equals(
          other.internalGetRet())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetRet().getMap().isEmpty()) {
        hash = (37 * hash) + RET_FIELD_NUMBER;
        hash = (53 * hash) + internalGetRet().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsActivity.GetAllActivityStatusAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetAllActivityStatusAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetAllActivityStatusAns)
        com.yorha.proto.SsActivity.GetAllActivityStatusAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetRet();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableRet();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsActivity.GetAllActivityStatusAns.class, com.yorha.proto.SsActivity.GetAllActivityStatusAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsActivity.GetAllActivityStatusAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableRet().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_GetAllActivityStatusAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.GetAllActivityStatusAns getDefaultInstanceForType() {
        return com.yorha.proto.SsActivity.GetAllActivityStatusAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.GetAllActivityStatusAns build() {
        com.yorha.proto.SsActivity.GetAllActivityStatusAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.GetAllActivityStatusAns buildPartial() {
        com.yorha.proto.SsActivity.GetAllActivityStatusAns result = new com.yorha.proto.SsActivity.GetAllActivityStatusAns(this);
        int from_bitField0_ = bitField0_;
        result.ret_ = internalGetRet();
        result.ret_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsActivity.GetAllActivityStatusAns) {
          return mergeFrom((com.yorha.proto.SsActivity.GetAllActivityStatusAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsActivity.GetAllActivityStatusAns other) {
        if (other == com.yorha.proto.SsActivity.GetAllActivityStatusAns.getDefaultInstance()) return this;
        internalGetMutableRet().mergeFrom(
            other.internalGetRet());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsActivity.GetAllActivityStatusAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsActivity.GetAllActivityStatusAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> ret_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetRet() {
        if (ret_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              RetDefaultEntryHolder.defaultEntry);
        }
        return ret_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableRet() {
        onChanged();;
        if (ret_ == null) {
          ret_ = com.google.protobuf.MapField.newMapField(
              RetDefaultEntryHolder.defaultEntry);
        }
        if (!ret_.isMutable()) {
          ret_ = ret_.copy();
        }
        return ret_;
      }

      public int getRetCount() {
        return internalGetRet().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
       */

      @java.lang.Override
      public boolean containsRet(
          int key) {
        
        return internalGetRet().getMap().containsKey(key);
      }
      /**
       * Use {@link #getRetMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus>
      getRet() {
        return getRetMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus>
      getRetMap() {
        return internalGetAdaptedRetMap(
            internalGetRet().getMap());}
      /**
       * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonEnum.ActivityStatus getRetOrDefault(
          int key,
          com.yorha.proto.CommonEnum.ActivityStatus defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetRet().getMap();
        return map.containsKey(key)
               ? retValueConverter.doForward(map.get(key))
               : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonEnum.ActivityStatus getRetOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetRet().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return retValueConverter.doForward(map.get(key));
      }

      public Builder clearRet() {
        internalGetMutableRet().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
       */

      public Builder removeRet(
          int key) {
        
        internalGetMutableRet().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus>
      getMutableRet() {
        return internalGetAdaptedRetMap(
             internalGetMutableRet().getMutableMap());
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
       */
      public Builder putRet(
          int key,
          com.yorha.proto.CommonEnum.ActivityStatus value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableRet().getMutableMap()
            .put(key, retValueConverter.doBackward(value));
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.ActivityStatus&gt; ret = 1;</code>
       */
      public Builder putAllRet(
          java.util.Map<java.lang.Integer, com.yorha.proto.CommonEnum.ActivityStatus> values) {
        internalGetAdaptedRetMap(
            internalGetMutableRet().getMutableMap())
                .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetAllActivityStatusAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetAllActivityStatusAns)
    private static final com.yorha.proto.SsActivity.GetAllActivityStatusAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsActivity.GetAllActivityStatusAns();
    }

    public static com.yorha.proto.SsActivity.GetAllActivityStatusAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetAllActivityStatusAns>
        PARSER = new com.google.protobuf.AbstractParser<GetAllActivityStatusAns>() {
      @java.lang.Override
      public GetAllActivityStatusAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetAllActivityStatusAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetAllActivityStatusAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetAllActivityStatusAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsActivity.GetAllActivityStatusAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface InitActivityActorAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.InitActivityActorAsk)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.InitActivityActorAsk}
   */
  public static final class InitActivityActorAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.InitActivityActorAsk)
      InitActivityActorAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use InitActivityActorAsk.newBuilder() to construct.
    private InitActivityActorAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private InitActivityActorAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new InitActivityActorAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private InitActivityActorAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsActivity.InitActivityActorAsk.class, com.yorha.proto.SsActivity.InitActivityActorAsk.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsActivity.InitActivityActorAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsActivity.InitActivityActorAsk other = (com.yorha.proto.SsActivity.InitActivityActorAsk) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsActivity.InitActivityActorAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.InitActivityActorAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.InitActivityActorAsk)
        com.yorha.proto.SsActivity.InitActivityActorAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsActivity.InitActivityActorAsk.class, com.yorha.proto.SsActivity.InitActivityActorAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsActivity.InitActivityActorAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.InitActivityActorAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsActivity.InitActivityActorAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.InitActivityActorAsk build() {
        com.yorha.proto.SsActivity.InitActivityActorAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.InitActivityActorAsk buildPartial() {
        com.yorha.proto.SsActivity.InitActivityActorAsk result = new com.yorha.proto.SsActivity.InitActivityActorAsk(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsActivity.InitActivityActorAsk) {
          return mergeFrom((com.yorha.proto.SsActivity.InitActivityActorAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsActivity.InitActivityActorAsk other) {
        if (other == com.yorha.proto.SsActivity.InitActivityActorAsk.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsActivity.InitActivityActorAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsActivity.InitActivityActorAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.InitActivityActorAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.InitActivityActorAsk)
    private static final com.yorha.proto.SsActivity.InitActivityActorAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsActivity.InitActivityActorAsk();
    }

    public static com.yorha.proto.SsActivity.InitActivityActorAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<InitActivityActorAsk>
        PARSER = new com.google.protobuf.AbstractParser<InitActivityActorAsk>() {
      @java.lang.Override
      public InitActivityActorAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new InitActivityActorAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<InitActivityActorAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<InitActivityActorAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsActivity.InitActivityActorAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface InitActivityActorAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.InitActivityActorAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.InitActivityActorAns}
   */
  public static final class InitActivityActorAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.InitActivityActorAns)
      InitActivityActorAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use InitActivityActorAns.newBuilder() to construct.
    private InitActivityActorAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private InitActivityActorAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new InitActivityActorAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private InitActivityActorAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsActivity.InitActivityActorAns.class, com.yorha.proto.SsActivity.InitActivityActorAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsActivity.InitActivityActorAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsActivity.InitActivityActorAns other = (com.yorha.proto.SsActivity.InitActivityActorAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsActivity.InitActivityActorAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsActivity.InitActivityActorAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.InitActivityActorAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.InitActivityActorAns)
        com.yorha.proto.SsActivity.InitActivityActorAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsActivity.InitActivityActorAns.class, com.yorha.proto.SsActivity.InitActivityActorAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsActivity.InitActivityActorAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsActivity.internal_static_com_yorha_proto_InitActivityActorAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.InitActivityActorAns getDefaultInstanceForType() {
        return com.yorha.proto.SsActivity.InitActivityActorAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.InitActivityActorAns build() {
        com.yorha.proto.SsActivity.InitActivityActorAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsActivity.InitActivityActorAns buildPartial() {
        com.yorha.proto.SsActivity.InitActivityActorAns result = new com.yorha.proto.SsActivity.InitActivityActorAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsActivity.InitActivityActorAns) {
          return mergeFrom((com.yorha.proto.SsActivity.InitActivityActorAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsActivity.InitActivityActorAns other) {
        if (other == com.yorha.proto.SsActivity.InitActivityActorAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsActivity.InitActivityActorAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsActivity.InitActivityActorAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.InitActivityActorAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.InitActivityActorAns)
    private static final com.yorha.proto.SsActivity.InitActivityActorAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsActivity.InitActivityActorAns();
    }

    public static com.yorha.proto.SsActivity.InitActivityActorAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<InitActivityActorAns>
        PARSER = new com.google.protobuf.AbstractParser<InitActivityActorAns>() {
      @java.lang.Override
      public InitActivityActorAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new InitActivityActorAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<InitActivityActorAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<InitActivityActorAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsActivity.InitActivityActorAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_TakeActivityRewardAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_TakeActivityRewardAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_TakeActivityRewardAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_TakeActivityRewardAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetAllActivityStatusAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetAllActivityStatusAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetAllActivityStatusAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetAllActivityStatusAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetAllActivityStatusAns_RetEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetAllActivityStatusAns_RetEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_InitActivityActorAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_InitActivityActorAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_InitActivityActorAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_InitActivityActorAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'ss_proto/gen/activity/ss_activity.prot" +
      "o\022\017com.yorha.proto\032%ss_proto/gen/common/" +
      "common_enum.proto\"=\n\025TakeActivityRewardA" +
      "sk\022\022\n\nactivityId\030\001 \001(\005\022\020\n\010playerId\030\002 \001(\003" +
      "\"\027\n\025TakeActivityRewardAns\"\031\n\027GetAllActiv" +
      "ityStatusAsk\"\246\001\n\027GetAllActivityStatusAns" +
      "\022>\n\003ret\030\001 \003(\01321.com.yorha.proto.GetAllAc" +
      "tivityStatusAns.RetEntry\032K\n\010RetEntry\022\013\n\003" +
      "key\030\001 \001(\005\022.\n\005value\030\002 \001(\0162\037.com.yorha.pro" +
      "to.ActivityStatus:\0028\001\"\026\n\024InitActivityAct" +
      "orAsk\"\026\n\024InitActivityActorAnsB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_TakeActivityRewardAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_TakeActivityRewardAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_TakeActivityRewardAsk_descriptor,
        new java.lang.String[] { "ActivityId", "PlayerId", });
    internal_static_com_yorha_proto_TakeActivityRewardAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_TakeActivityRewardAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_TakeActivityRewardAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_GetAllActivityStatusAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_GetAllActivityStatusAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetAllActivityStatusAsk_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_GetAllActivityStatusAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_GetAllActivityStatusAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetAllActivityStatusAns_descriptor,
        new java.lang.String[] { "Ret", });
    internal_static_com_yorha_proto_GetAllActivityStatusAns_RetEntry_descriptor =
      internal_static_com_yorha_proto_GetAllActivityStatusAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_GetAllActivityStatusAns_RetEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetAllActivityStatusAns_RetEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_InitActivityActorAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_InitActivityActorAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_InitActivityActorAsk_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_InitActivityActorAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_InitActivityActorAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_InitActivityActorAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

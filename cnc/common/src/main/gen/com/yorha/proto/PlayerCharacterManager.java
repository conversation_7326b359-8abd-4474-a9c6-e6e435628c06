// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_character_manager.proto

package com.yorha.proto;

public final class PlayerCharacterManager {
  private PlayerCharacterManager() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_GetCharacterList_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetCharacterList_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetCharacterList_C2S}
   */
  public static final class Player_GetCharacterList_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetCharacterList_C2S)
      Player_GetCharacterList_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetCharacterList_C2S.newBuilder() to construct.
    private Player_GetCharacterList_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetCharacterList_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetCharacterList_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetCharacterList_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S other = (com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetCharacterList_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetCharacterList_C2S)
        com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S build() {
        com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S result = new com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetCharacterList_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetCharacterList_C2S)
    private static final com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetCharacterList_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetCharacterList_C2S>() {
      @java.lang.Override
      public Player_GetCharacterList_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetCharacterList_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetCharacterList_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetCharacterList_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetCharacterList_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetCharacterList_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    java.util.List<com.yorha.proto.CommonMsg.CharacterInfo> 
        getCharacterListList();
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    com.yorha.proto.CommonMsg.CharacterInfo getCharacterList(int index);
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    int getCharacterListCount();
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.CommonMsg.CharacterInfoOrBuilder> 
        getCharacterListOrBuilderList();
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    com.yorha.proto.CommonMsg.CharacterInfoOrBuilder getCharacterListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetCharacterList_S2C}
   */
  public static final class Player_GetCharacterList_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetCharacterList_S2C)
      Player_GetCharacterList_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetCharacterList_S2C.newBuilder() to construct.
    private Player_GetCharacterList_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetCharacterList_S2C() {
      characterList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetCharacterList_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetCharacterList_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                characterList_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.CharacterInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              characterList_.add(
                  input.readMessage(com.yorha.proto.CommonMsg.CharacterInfo.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          characterList_ = java.util.Collections.unmodifiableList(characterList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C.Builder.class);
    }

    public static final int CHARACTERLIST_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.CommonMsg.CharacterInfo> characterList_;
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.CommonMsg.CharacterInfo> getCharacterListList() {
      return characterList_;
    }
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.CommonMsg.CharacterInfoOrBuilder> 
        getCharacterListOrBuilderList() {
      return characterList_;
    }
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    @java.lang.Override
    public int getCharacterListCount() {
      return characterList_.size();
    }
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.CharacterInfo getCharacterList(int index) {
      return characterList_.get(index);
    }
    /**
     * <pre>
     * 账号下各角色信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.CharacterInfoOrBuilder getCharacterListOrBuilder(
        int index) {
      return characterList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < characterList_.size(); i++) {
        output.writeMessage(1, characterList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < characterList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, characterList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C other = (com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C) obj;

      if (!getCharacterListList()
          .equals(other.getCharacterListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getCharacterListCount() > 0) {
        hash = (37 * hash) + CHARACTERLIST_FIELD_NUMBER;
        hash = (53 * hash) + getCharacterListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetCharacterList_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetCharacterList_S2C)
        com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCharacterListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (characterListBuilder_ == null) {
          characterList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          characterListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetCharacterList_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C build() {
        com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C result = new com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C(this);
        int from_bitField0_ = bitField0_;
        if (characterListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            characterList_ = java.util.Collections.unmodifiableList(characterList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.characterList_ = characterList_;
        } else {
          result.characterList_ = characterListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C.getDefaultInstance()) return this;
        if (characterListBuilder_ == null) {
          if (!other.characterList_.isEmpty()) {
            if (characterList_.isEmpty()) {
              characterList_ = other.characterList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureCharacterListIsMutable();
              characterList_.addAll(other.characterList_);
            }
            onChanged();
          }
        } else {
          if (!other.characterList_.isEmpty()) {
            if (characterListBuilder_.isEmpty()) {
              characterListBuilder_.dispose();
              characterListBuilder_ = null;
              characterList_ = other.characterList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              characterListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getCharacterListFieldBuilder() : null;
            } else {
              characterListBuilder_.addAllMessages(other.characterList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.CommonMsg.CharacterInfo> characterList_ =
        java.util.Collections.emptyList();
      private void ensureCharacterListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          characterList_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.CharacterInfo>(characterList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.CharacterInfo, com.yorha.proto.CommonMsg.CharacterInfo.Builder, com.yorha.proto.CommonMsg.CharacterInfoOrBuilder> characterListBuilder_;

      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.CharacterInfo> getCharacterListList() {
        if (characterListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(characterList_);
        } else {
          return characterListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public int getCharacterListCount() {
        if (characterListBuilder_ == null) {
          return characterList_.size();
        } else {
          return characterListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.CharacterInfo getCharacterList(int index) {
        if (characterListBuilder_ == null) {
          return characterList_.get(index);
        } else {
          return characterListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder setCharacterList(
          int index, com.yorha.proto.CommonMsg.CharacterInfo value) {
        if (characterListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCharacterListIsMutable();
          characterList_.set(index, value);
          onChanged();
        } else {
          characterListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder setCharacterList(
          int index, com.yorha.proto.CommonMsg.CharacterInfo.Builder builderForValue) {
        if (characterListBuilder_ == null) {
          ensureCharacterListIsMutable();
          characterList_.set(index, builderForValue.build());
          onChanged();
        } else {
          characterListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder addCharacterList(com.yorha.proto.CommonMsg.CharacterInfo value) {
        if (characterListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCharacterListIsMutable();
          characterList_.add(value);
          onChanged();
        } else {
          characterListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder addCharacterList(
          int index, com.yorha.proto.CommonMsg.CharacterInfo value) {
        if (characterListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCharacterListIsMutable();
          characterList_.add(index, value);
          onChanged();
        } else {
          characterListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder addCharacterList(
          com.yorha.proto.CommonMsg.CharacterInfo.Builder builderForValue) {
        if (characterListBuilder_ == null) {
          ensureCharacterListIsMutable();
          characterList_.add(builderForValue.build());
          onChanged();
        } else {
          characterListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder addCharacterList(
          int index, com.yorha.proto.CommonMsg.CharacterInfo.Builder builderForValue) {
        if (characterListBuilder_ == null) {
          ensureCharacterListIsMutable();
          characterList_.add(index, builderForValue.build());
          onChanged();
        } else {
          characterListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder addAllCharacterList(
          java.lang.Iterable<? extends com.yorha.proto.CommonMsg.CharacterInfo> values) {
        if (characterListBuilder_ == null) {
          ensureCharacterListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, characterList_);
          onChanged();
        } else {
          characterListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder clearCharacterList() {
        if (characterListBuilder_ == null) {
          characterList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          characterListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public Builder removeCharacterList(int index) {
        if (characterListBuilder_ == null) {
          ensureCharacterListIsMutable();
          characterList_.remove(index);
          onChanged();
        } else {
          characterListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.CharacterInfo.Builder getCharacterListBuilder(
          int index) {
        return getCharacterListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.CharacterInfoOrBuilder getCharacterListOrBuilder(
          int index) {
        if (characterListBuilder_ == null) {
          return characterList_.get(index);  } else {
          return characterListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.CommonMsg.CharacterInfoOrBuilder> 
           getCharacterListOrBuilderList() {
        if (characterListBuilder_ != null) {
          return characterListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(characterList_);
        }
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.CharacterInfo.Builder addCharacterListBuilder() {
        return getCharacterListFieldBuilder().addBuilder(
            com.yorha.proto.CommonMsg.CharacterInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.CharacterInfo.Builder addCharacterListBuilder(
          int index) {
        return getCharacterListFieldBuilder().addBuilder(
            index, com.yorha.proto.CommonMsg.CharacterInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 账号下各角色信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.CharacterInfo characterList = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.CharacterInfo.Builder> 
           getCharacterListBuilderList() {
        return getCharacterListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.CharacterInfo, com.yorha.proto.CommonMsg.CharacterInfo.Builder, com.yorha.proto.CommonMsg.CharacterInfoOrBuilder> 
          getCharacterListFieldBuilder() {
        if (characterListBuilder_ == null) {
          characterListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.CommonMsg.CharacterInfo, com.yorha.proto.CommonMsg.CharacterInfo.Builder, com.yorha.proto.CommonMsg.CharacterInfoOrBuilder>(
                  characterList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          characterList_ = null;
        }
        return characterListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetCharacterList_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetCharacterList_S2C)
    private static final com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetCharacterList_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetCharacterList_S2C>() {
      @java.lang.Override
      public Player_GetCharacterList_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetCharacterList_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetCharacterList_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetCharacterList_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_GetCharacterList_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetZoneServerList_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetZoneServerList_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetZoneServerList_C2S}
   */
  public static final class Player_GetZoneServerList_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetZoneServerList_C2S)
      Player_GetZoneServerList_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetZoneServerList_C2S.newBuilder() to construct.
    private Player_GetZoneServerList_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetZoneServerList_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetZoneServerList_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetZoneServerList_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S other = (com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetZoneServerList_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetZoneServerList_C2S)
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S build() {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S result = new com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetZoneServerList_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetZoneServerList_C2S)
    private static final com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetZoneServerList_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetZoneServerList_C2S>() {
      @java.lang.Override
      public Player_GetZoneServerList_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetZoneServerList_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetZoneServerList_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetZoneServerList_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetZoneServerList_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetZoneServerList_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> 
        getZoneServerListList();
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    com.yorha.proto.CommonMsg.ZoneServerInfo getZoneServerList(int index);
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    int getZoneServerListCount();
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> 
        getZoneServerListOrBuilderList();
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder getZoneServerListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetZoneServerList_S2C}
   */
  public static final class Player_GetZoneServerList_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetZoneServerList_S2C)
      Player_GetZoneServerList_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetZoneServerList_S2C.newBuilder() to construct.
    private Player_GetZoneServerList_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetZoneServerList_S2C() {
      zoneServerList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetZoneServerList_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetZoneServerList_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                zoneServerList_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ZoneServerInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              zoneServerList_.add(
                  input.readMessage(com.yorha.proto.CommonMsg.ZoneServerInfo.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          zoneServerList_ = java.util.Collections.unmodifiableList(zoneServerList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C.Builder.class);
    }

    public static final int ZONESERVERLIST_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> zoneServerList_;
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> getZoneServerListList() {
      return zoneServerList_;
    }
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> 
        getZoneServerListOrBuilderList() {
      return zoneServerList_;
    }
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public int getZoneServerListCount() {
      return zoneServerList_.size();
    }
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ZoneServerInfo getZoneServerList(int index) {
      return zoneServerList_.get(index);
    }
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder getZoneServerListOrBuilder(
        int index) {
      return zoneServerList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < zoneServerList_.size(); i++) {
        output.writeMessage(1, zoneServerList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < zoneServerList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, zoneServerList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C other = (com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C) obj;

      if (!getZoneServerListList()
          .equals(other.getZoneServerListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getZoneServerListCount() > 0) {
        hash = (37 * hash) + ZONESERVERLIST_FIELD_NUMBER;
        hash = (53 * hash) + getZoneServerListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetZoneServerList_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetZoneServerList_S2C)
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getZoneServerListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (zoneServerListBuilder_ == null) {
          zoneServerList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          zoneServerListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C build() {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C result = new com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C(this);
        int from_bitField0_ = bitField0_;
        if (zoneServerListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            zoneServerList_ = java.util.Collections.unmodifiableList(zoneServerList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.zoneServerList_ = zoneServerList_;
        } else {
          result.zoneServerList_ = zoneServerListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C.getDefaultInstance()) return this;
        if (zoneServerListBuilder_ == null) {
          if (!other.zoneServerList_.isEmpty()) {
            if (zoneServerList_.isEmpty()) {
              zoneServerList_ = other.zoneServerList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureZoneServerListIsMutable();
              zoneServerList_.addAll(other.zoneServerList_);
            }
            onChanged();
          }
        } else {
          if (!other.zoneServerList_.isEmpty()) {
            if (zoneServerListBuilder_.isEmpty()) {
              zoneServerListBuilder_.dispose();
              zoneServerListBuilder_ = null;
              zoneServerList_ = other.zoneServerList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              zoneServerListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getZoneServerListFieldBuilder() : null;
            } else {
              zoneServerListBuilder_.addAllMessages(other.zoneServerList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> zoneServerList_ =
        java.util.Collections.emptyList();
      private void ensureZoneServerListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          zoneServerList_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ZoneServerInfo>(zoneServerList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ZoneServerInfo, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder, com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> zoneServerListBuilder_;

      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> getZoneServerListList() {
        if (zoneServerListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(zoneServerList_);
        } else {
          return zoneServerListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public int getZoneServerListCount() {
        if (zoneServerListBuilder_ == null) {
          return zoneServerList_.size();
        } else {
          return zoneServerListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfo getZoneServerList(int index) {
        if (zoneServerListBuilder_ == null) {
          return zoneServerList_.get(index);
        } else {
          return zoneServerListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder setZoneServerList(
          int index, com.yorha.proto.CommonMsg.ZoneServerInfo value) {
        if (zoneServerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureZoneServerListIsMutable();
          zoneServerList_.set(index, value);
          onChanged();
        } else {
          zoneServerListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder setZoneServerList(
          int index, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder builderForValue) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          zoneServerList_.set(index, builderForValue.build());
          onChanged();
        } else {
          zoneServerListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addZoneServerList(com.yorha.proto.CommonMsg.ZoneServerInfo value) {
        if (zoneServerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureZoneServerListIsMutable();
          zoneServerList_.add(value);
          onChanged();
        } else {
          zoneServerListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addZoneServerList(
          int index, com.yorha.proto.CommonMsg.ZoneServerInfo value) {
        if (zoneServerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureZoneServerListIsMutable();
          zoneServerList_.add(index, value);
          onChanged();
        } else {
          zoneServerListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addZoneServerList(
          com.yorha.proto.CommonMsg.ZoneServerInfo.Builder builderForValue) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          zoneServerList_.add(builderForValue.build());
          onChanged();
        } else {
          zoneServerListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addZoneServerList(
          int index, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder builderForValue) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          zoneServerList_.add(index, builderForValue.build());
          onChanged();
        } else {
          zoneServerListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addAllZoneServerList(
          java.lang.Iterable<? extends com.yorha.proto.CommonMsg.ZoneServerInfo> values) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, zoneServerList_);
          onChanged();
        } else {
          zoneServerListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder clearZoneServerList() {
        if (zoneServerListBuilder_ == null) {
          zoneServerList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          zoneServerListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder removeZoneServerList(int index) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          zoneServerList_.remove(index);
          onChanged();
        } else {
          zoneServerListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfo.Builder getZoneServerListBuilder(
          int index) {
        return getZoneServerListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder getZoneServerListOrBuilder(
          int index) {
        if (zoneServerListBuilder_ == null) {
          return zoneServerList_.get(index);  } else {
          return zoneServerListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> 
           getZoneServerListOrBuilderList() {
        if (zoneServerListBuilder_ != null) {
          return zoneServerListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(zoneServerList_);
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfo.Builder addZoneServerListBuilder() {
        return getZoneServerListFieldBuilder().addBuilder(
            com.yorha.proto.CommonMsg.ZoneServerInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfo.Builder addZoneServerListBuilder(
          int index) {
        return getZoneServerListFieldBuilder().addBuilder(
            index, com.yorha.proto.CommonMsg.ZoneServerInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo.Builder> 
           getZoneServerListBuilderList() {
        return getZoneServerListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ZoneServerInfo, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder, com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> 
          getZoneServerListFieldBuilder() {
        if (zoneServerListBuilder_ == null) {
          zoneServerListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.CommonMsg.ZoneServerInfo, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder, com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder>(
                  zoneServerList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          zoneServerList_ = null;
        }
        return zoneServerListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetZoneServerList_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetZoneServerList_S2C)
    private static final com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetZoneServerList_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetZoneServerList_S2C>() {
      @java.lang.Override
      public Player_GetZoneServerList_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetZoneServerList_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetZoneServerList_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetZoneServerList_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_GetZoneServerList_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ManageStarForCharacter_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ManageStarForCharacter_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标角色id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 目标角色id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 标记 or 取消星标
     * </pre>
     *
     * <code>optional bool setStar = 2;</code>
     * @return Whether the setStar field is set.
     */
    boolean hasSetStar();
    /**
     * <pre>
     * 标记 or 取消星标
     * </pre>
     *
     * <code>optional bool setStar = 2;</code>
     * @return The setStar.
     */
    boolean getSetStar();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ManageStarForCharacter_C2S}
   */
  public static final class Player_ManageStarForCharacter_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ManageStarForCharacter_C2S)
      Player_ManageStarForCharacter_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ManageStarForCharacter_C2S.newBuilder() to construct.
    private Player_ManageStarForCharacter_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ManageStarForCharacter_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ManageStarForCharacter_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ManageStarForCharacter_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              setStar_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 目标角色id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 目标角色id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int SETSTAR_FIELD_NUMBER = 2;
    private boolean setStar_;
    /**
     * <pre>
     * 标记 or 取消星标
     * </pre>
     *
     * <code>optional bool setStar = 2;</code>
     * @return Whether the setStar field is set.
     */
    @java.lang.Override
    public boolean hasSetStar() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 标记 or 取消星标
     * </pre>
     *
     * <code>optional bool setStar = 2;</code>
     * @return The setStar.
     */
    @java.lang.Override
    public boolean getSetStar() {
      return setStar_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, setStar_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, setStar_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S other = (com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasSetStar() != other.hasSetStar()) return false;
      if (hasSetStar()) {
        if (getSetStar()
            != other.getSetStar()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasSetStar()) {
        hash = (37 * hash) + SETSTAR_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getSetStar());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ManageStarForCharacter_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ManageStarForCharacter_C2S)
        com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        setStar_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S build() {
        com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S result = new com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.setStar_ = setStar_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasSetStar()) {
          setSetStar(other.getSetStar());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 目标角色id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 目标角色id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 目标角色id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标角色id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private boolean setStar_ ;
      /**
       * <pre>
       * 标记 or 取消星标
       * </pre>
       *
       * <code>optional bool setStar = 2;</code>
       * @return Whether the setStar field is set.
       */
      @java.lang.Override
      public boolean hasSetStar() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 标记 or 取消星标
       * </pre>
       *
       * <code>optional bool setStar = 2;</code>
       * @return The setStar.
       */
      @java.lang.Override
      public boolean getSetStar() {
        return setStar_;
      }
      /**
       * <pre>
       * 标记 or 取消星标
       * </pre>
       *
       * <code>optional bool setStar = 2;</code>
       * @param value The setStar to set.
       * @return This builder for chaining.
       */
      public Builder setSetStar(boolean value) {
        bitField0_ |= 0x00000002;
        setStar_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记 or 取消星标
       * </pre>
       *
       * <code>optional bool setStar = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSetStar() {
        bitField0_ = (bitField0_ & ~0x00000002);
        setStar_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ManageStarForCharacter_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ManageStarForCharacter_C2S)
    private static final com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ManageStarForCharacter_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ManageStarForCharacter_C2S>() {
      @java.lang.Override
      public Player_ManageStarForCharacter_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ManageStarForCharacter_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ManageStarForCharacter_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ManageStarForCharacter_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ManageStarForCharacter_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ManageStarForCharacter_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ManageStarForCharacter_S2C}
   */
  public static final class Player_ManageStarForCharacter_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ManageStarForCharacter_S2C)
      Player_ManageStarForCharacter_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ManageStarForCharacter_S2C.newBuilder() to construct.
    private Player_ManageStarForCharacter_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ManageStarForCharacter_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ManageStarForCharacter_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ManageStarForCharacter_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C other = (com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ManageStarForCharacter_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ManageStarForCharacter_S2C)
        com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C build() {
        com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C result = new com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ManageStarForCharacter_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ManageStarForCharacter_S2C)
    private static final com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ManageStarForCharacter_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ManageStarForCharacter_S2C>() {
      @java.lang.Override
      public Player_ManageStarForCharacter_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ManageStarForCharacter_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ManageStarForCharacter_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ManageStarForCharacter_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_ManageStarForCharacter_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CheckRegisterNewCharacter_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CheckRegisterNewCharacter_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CheckRegisterNewCharacter_C2S}
   */
  public static final class Player_CheckRegisterNewCharacter_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CheckRegisterNewCharacter_C2S)
      Player_CheckRegisterNewCharacter_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CheckRegisterNewCharacter_C2S.newBuilder() to construct.
    private Player_CheckRegisterNewCharacter_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CheckRegisterNewCharacter_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CheckRegisterNewCharacter_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CheckRegisterNewCharacter_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ZONEID_FIELD_NUMBER = 1;
    private int zoneId_;
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S other = (com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S) obj;

      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CheckRegisterNewCharacter_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CheckRegisterNewCharacter_C2S)
        com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S build() {
        com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S result = new com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.getDefaultInstance()) return this;
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int zoneId_ ;
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000001;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CheckRegisterNewCharacter_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CheckRegisterNewCharacter_C2S)
    private static final com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CheckRegisterNewCharacter_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_CheckRegisterNewCharacter_C2S>() {
      @java.lang.Override
      public Player_CheckRegisterNewCharacter_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CheckRegisterNewCharacter_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CheckRegisterNewCharacter_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CheckRegisterNewCharacter_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CheckRegisterNewCharacter_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CheckRegisterNewCharacter_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return Whether the ip field is set.
     */
    boolean hasIp();
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return The ip.
     */
    java.lang.String getIp();
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return The bytes for ip.
     */
    com.google.protobuf.ByteString
        getIpBytes();

    /**
     * <pre>
     * 目标服端口
     * </pre>
     *
     * <code>optional int32 port = 2;</code>
     * @return Whether the port field is set.
     */
    boolean hasPort();
    /**
     * <pre>
     * 目标服端口
     * </pre>
     *
     * <code>optional int32 port = 2;</code>
     * @return The port.
     */
    int getPort();

    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return Whether the channelName field is set.
     */
    boolean hasChannelName();
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The channelName.
     */
    java.lang.String getChannelName();
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The bytes for channelName.
     */
    com.google.protobuf.ByteString
        getChannelNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CheckRegisterNewCharacter_S2C}
   */
  public static final class Player_CheckRegisterNewCharacter_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CheckRegisterNewCharacter_S2C)
      Player_CheckRegisterNewCharacter_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CheckRegisterNewCharacter_S2C.newBuilder() to construct.
    private Player_CheckRegisterNewCharacter_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CheckRegisterNewCharacter_S2C() {
      ip_ = "";
      channelName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CheckRegisterNewCharacter_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CheckRegisterNewCharacter_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              ip_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              port_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              channelName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int IP_FIELD_NUMBER = 1;
    private volatile java.lang.Object ip_;
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return Whether the ip field is set.
     */
    @java.lang.Override
    public boolean hasIp() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return The ip.
     */
    @java.lang.Override
    public java.lang.String getIp() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ip_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return The bytes for ip.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIpBytes() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ip_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PORT_FIELD_NUMBER = 2;
    private int port_;
    /**
     * <pre>
     * 目标服端口
     * </pre>
     *
     * <code>optional int32 port = 2;</code>
     * @return Whether the port field is set.
     */
    @java.lang.Override
    public boolean hasPort() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 目标服端口
     * </pre>
     *
     * <code>optional int32 port = 2;</code>
     * @return The port.
     */
    @java.lang.Override
    public int getPort() {
      return port_;
    }

    public static final int CHANNELNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object channelName_;
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return Whether the channelName field is set.
     */
    @java.lang.Override
    public boolean hasChannelName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The channelName.
     */
    @java.lang.Override
    public java.lang.String getChannelName() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The bytes for channelName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelNameBytes() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, ip_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, port_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, channelName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, ip_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, port_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, channelName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C other = (com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C) obj;

      if (hasIp() != other.hasIp()) return false;
      if (hasIp()) {
        if (!getIp()
            .equals(other.getIp())) return false;
      }
      if (hasPort() != other.hasPort()) return false;
      if (hasPort()) {
        if (getPort()
            != other.getPort()) return false;
      }
      if (hasChannelName() != other.hasChannelName()) return false;
      if (hasChannelName()) {
        if (!getChannelName()
            .equals(other.getChannelName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIp()) {
        hash = (37 * hash) + IP_FIELD_NUMBER;
        hash = (53 * hash) + getIp().hashCode();
      }
      if (hasPort()) {
        hash = (37 * hash) + PORT_FIELD_NUMBER;
        hash = (53 * hash) + getPort();
      }
      if (hasChannelName()) {
        hash = (37 * hash) + CHANNELNAME_FIELD_NUMBER;
        hash = (53 * hash) + getChannelName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CheckRegisterNewCharacter_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CheckRegisterNewCharacter_S2C)
        com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ip_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        port_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        channelName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C build() {
        com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C result = new com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.ip_ = ip_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.port_ = port_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channelName_ = channelName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.getDefaultInstance()) return this;
        if (other.hasIp()) {
          bitField0_ |= 0x00000001;
          ip_ = other.ip_;
          onChanged();
        }
        if (other.hasPort()) {
          setPort(other.getPort());
        }
        if (other.hasChannelName()) {
          bitField0_ |= 0x00000004;
          channelName_ = other.channelName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object ip_ = "";
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @return Whether the ip field is set.
       */
      public boolean hasIp() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @return The ip.
       */
      public java.lang.String getIp() {
        java.lang.Object ref = ip_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ip_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @return The bytes for ip.
       */
      public com.google.protobuf.ByteString
          getIpBytes() {
        java.lang.Object ref = ip_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ip_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @param value The ip to set.
       * @return This builder for chaining.
       */
      public Builder setIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ip_ = getDefaultInstance().getIp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @param value The bytes for ip to set.
       * @return This builder for chaining.
       */
      public Builder setIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }

      private int port_ ;
      /**
       * <pre>
       * 目标服端口
       * </pre>
       *
       * <code>optional int32 port = 2;</code>
       * @return Whether the port field is set.
       */
      @java.lang.Override
      public boolean hasPort() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 目标服端口
       * </pre>
       *
       * <code>optional int32 port = 2;</code>
       * @return The port.
       */
      @java.lang.Override
      public int getPort() {
        return port_;
      }
      /**
       * <pre>
       * 目标服端口
       * </pre>
       *
       * <code>optional int32 port = 2;</code>
       * @param value The port to set.
       * @return This builder for chaining.
       */
      public Builder setPort(int value) {
        bitField0_ |= 0x00000002;
        port_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标服端口
       * </pre>
       *
       * <code>optional int32 port = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPort() {
        bitField0_ = (bitField0_ & ~0x00000002);
        port_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object channelName_ = "";
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return Whether the channelName field is set.
       */
      public boolean hasChannelName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return The channelName.
       */
      public java.lang.String getChannelName() {
        java.lang.Object ref = channelName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return The bytes for channelName.
       */
      public com.google.protobuf.ByteString
          getChannelNameBytes() {
        java.lang.Object ref = channelName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @param value The channelName to set.
       * @return This builder for chaining.
       */
      public Builder setChannelName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channelName_ = getDefaultInstance().getChannelName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @param value The bytes for channelName to set.
       * @return This builder for chaining.
       */
      public Builder setChannelNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CheckRegisterNewCharacter_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CheckRegisterNewCharacter_S2C)
    private static final com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CheckRegisterNewCharacter_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_CheckRegisterNewCharacter_S2C>() {
      @java.lang.Override
      public Player_CheckRegisterNewCharacter_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CheckRegisterNewCharacter_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CheckRegisterNewCharacter_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CheckRegisterNewCharacter_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetZoneIpPort_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetZoneIpPort_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetZoneIpPort_C2S}
   */
  public static final class Player_GetZoneIpPort_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetZoneIpPort_C2S)
      Player_GetZoneIpPort_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetZoneIpPort_C2S.newBuilder() to construct.
    private Player_GetZoneIpPort_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetZoneIpPort_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetZoneIpPort_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetZoneIpPort_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ZONEID_FIELD_NUMBER = 1;
    private int zoneId_;
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S other = (com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S) obj;

      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetZoneIpPort_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetZoneIpPort_C2S)
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S.class, com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S build() {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S result = new com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S.getDefaultInstance()) return this;
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int zoneId_ ;
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000001;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetZoneIpPort_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetZoneIpPort_C2S)
    private static final com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetZoneIpPort_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetZoneIpPort_C2S>() {
      @java.lang.Override
      public Player_GetZoneIpPort_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetZoneIpPort_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetZoneIpPort_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetZoneIpPort_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetZoneIpPort_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetZoneIpPort_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return Whether the ip field is set.
     */
    boolean hasIp();
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return The ip.
     */
    java.lang.String getIp();
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return The bytes for ip.
     */
    com.google.protobuf.ByteString
        getIpBytes();

    /**
     * <pre>
     * 目标服端口
     * </pre>
     *
     * <code>optional int32 port = 2;</code>
     * @return Whether the port field is set.
     */
    boolean hasPort();
    /**
     * <pre>
     * 目标服端口
     * </pre>
     *
     * <code>optional int32 port = 2;</code>
     * @return The port.
     */
    int getPort();

    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return Whether the channelName field is set.
     */
    boolean hasChannelName();
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The channelName.
     */
    java.lang.String getChannelName();
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The bytes for channelName.
     */
    com.google.protobuf.ByteString
        getChannelNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetZoneIpPort_S2C}
   */
  public static final class Player_GetZoneIpPort_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetZoneIpPort_S2C)
      Player_GetZoneIpPort_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetZoneIpPort_S2C.newBuilder() to construct.
    private Player_GetZoneIpPort_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetZoneIpPort_S2C() {
      ip_ = "";
      channelName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetZoneIpPort_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetZoneIpPort_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              ip_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              port_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              channelName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int IP_FIELD_NUMBER = 1;
    private volatile java.lang.Object ip_;
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return Whether the ip field is set.
     */
    @java.lang.Override
    public boolean hasIp() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return The ip.
     */
    @java.lang.Override
    public java.lang.String getIp() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ip_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 目标服ip
     * </pre>
     *
     * <code>optional string ip = 1;</code>
     * @return The bytes for ip.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIpBytes() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ip_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PORT_FIELD_NUMBER = 2;
    private int port_;
    /**
     * <pre>
     * 目标服端口
     * </pre>
     *
     * <code>optional int32 port = 2;</code>
     * @return Whether the port field is set.
     */
    @java.lang.Override
    public boolean hasPort() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 目标服端口
     * </pre>
     *
     * <code>optional int32 port = 2;</code>
     * @return The port.
     */
    @java.lang.Override
    public int getPort() {
      return port_;
    }

    public static final int CHANNELNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object channelName_;
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return Whether the channelName field is set.
     */
    @java.lang.Override
    public boolean hasChannelName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The channelName.
     */
    @java.lang.Override
    public java.lang.String getChannelName() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The bytes for channelName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelNameBytes() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, ip_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, port_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, channelName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, ip_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, port_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, channelName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C other = (com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C) obj;

      if (hasIp() != other.hasIp()) return false;
      if (hasIp()) {
        if (!getIp()
            .equals(other.getIp())) return false;
      }
      if (hasPort() != other.hasPort()) return false;
      if (hasPort()) {
        if (getPort()
            != other.getPort()) return false;
      }
      if (hasChannelName() != other.hasChannelName()) return false;
      if (hasChannelName()) {
        if (!getChannelName()
            .equals(other.getChannelName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIp()) {
        hash = (37 * hash) + IP_FIELD_NUMBER;
        hash = (53 * hash) + getIp().hashCode();
      }
      if (hasPort()) {
        hash = (37 * hash) + PORT_FIELD_NUMBER;
        hash = (53 * hash) + getPort();
      }
      if (hasChannelName()) {
        hash = (37 * hash) + CHANNELNAME_FIELD_NUMBER;
        hash = (53 * hash) + getChannelName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetZoneIpPort_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetZoneIpPort_S2C)
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C.class, com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ip_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        port_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        channelName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerCharacterManager.internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C build() {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C buildPartial() {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C result = new com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.ip_ = ip_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.port_ = port_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channelName_ = channelName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C) {
          return mergeFrom((com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C other) {
        if (other == com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C.getDefaultInstance()) return this;
        if (other.hasIp()) {
          bitField0_ |= 0x00000001;
          ip_ = other.ip_;
          onChanged();
        }
        if (other.hasPort()) {
          setPort(other.getPort());
        }
        if (other.hasChannelName()) {
          bitField0_ |= 0x00000004;
          channelName_ = other.channelName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object ip_ = "";
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @return Whether the ip field is set.
       */
      public boolean hasIp() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @return The ip.
       */
      public java.lang.String getIp() {
        java.lang.Object ref = ip_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ip_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @return The bytes for ip.
       */
      public com.google.protobuf.ByteString
          getIpBytes() {
        java.lang.Object ref = ip_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ip_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @param value The ip to set.
       * @return This builder for chaining.
       */
      public Builder setIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ip_ = getDefaultInstance().getIp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标服ip
       * </pre>
       *
       * <code>optional string ip = 1;</code>
       * @param value The bytes for ip to set.
       * @return This builder for chaining.
       */
      public Builder setIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }

      private int port_ ;
      /**
       * <pre>
       * 目标服端口
       * </pre>
       *
       * <code>optional int32 port = 2;</code>
       * @return Whether the port field is set.
       */
      @java.lang.Override
      public boolean hasPort() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 目标服端口
       * </pre>
       *
       * <code>optional int32 port = 2;</code>
       * @return The port.
       */
      @java.lang.Override
      public int getPort() {
        return port_;
      }
      /**
       * <pre>
       * 目标服端口
       * </pre>
       *
       * <code>optional int32 port = 2;</code>
       * @param value The port to set.
       * @return This builder for chaining.
       */
      public Builder setPort(int value) {
        bitField0_ |= 0x00000002;
        port_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标服端口
       * </pre>
       *
       * <code>optional int32 port = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPort() {
        bitField0_ = (bitField0_ & ~0x00000002);
        port_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object channelName_ = "";
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return Whether the channelName field is set.
       */
      public boolean hasChannelName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return The channelName.
       */
      public java.lang.String getChannelName() {
        java.lang.Object ref = channelName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return The bytes for channelName.
       */
      public com.google.protobuf.ByteString
          getChannelNameBytes() {
        java.lang.Object ref = channelName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @param value The channelName to set.
       * @return This builder for chaining.
       */
      public Builder setChannelName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channelName_ = getDefaultInstance().getChannelName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @param value The bytes for channelName to set.
       * @return This builder for chaining.
       */
      public Builder setChannelNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetZoneIpPort_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetZoneIpPort_S2C)
    private static final com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C();
    }

    public static com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetZoneIpPort_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetZoneIpPort_S2C>() {
      @java.lang.Override
      public Player_GetZoneIpPort_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetZoneIpPort_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetZoneIpPort_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetZoneIpPort_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerCharacterManager.Player_GetZoneIpPort_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetCharacterList_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetCharacterList_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetCharacterList_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetCharacterList_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n5ss_proto/gen/player/cs/player_characte" +
      "r_manager.proto\022\017com.yorha.proto\032$ss_pro" +
      "to/gen/common/common_msg.proto\"\035\n\033Player" +
      "_GetCharacterList_C2S\"T\n\033Player_GetChara" +
      "cterList_S2C\0225\n\rcharacterList\030\001 \003(\0132\036.co" +
      "m.yorha.proto.CharacterInfo\"\036\n\034Player_Ge" +
      "tZoneServerList_C2S\"W\n\034Player_GetZoneSer" +
      "verList_S2C\0227\n\016zoneServerList\030\001 \003(\0132\037.co" +
      "m.yorha.proto.ZoneServerInfo\"F\n!Player_M" +
      "anageStarForCharacter_C2S\022\020\n\010playerId\030\001 " +
      "\001(\003\022\017\n\007setStar\030\002 \001(\010\"#\n!Player_ManageSta" +
      "rForCharacter_S2C\"6\n$Player_CheckRegiste" +
      "rNewCharacter_C2S\022\016\n\006zoneId\030\001 \001(\005\"U\n$Pla" +
      "yer_CheckRegisterNewCharacter_S2C\022\n\n\002ip\030" +
      "\001 \001(\t\022\014\n\004port\030\002 \001(\005\022\023\n\013channelName\030\003 \001(\t" +
      "\"*\n\030Player_GetZoneIpPort_C2S\022\016\n\006zoneId\030\001" +
      " \001(\005\"I\n\030Player_GetZoneIpPort_S2C\022\n\n\002ip\030\001" +
      " \001(\t\022\014\n\004port\030\002 \001(\005\022\023\n\013channelName\030\003 \001(\tB" +
      "\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_GetCharacterList_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_GetCharacterList_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetCharacterList_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetCharacterList_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_GetCharacterList_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetCharacterList_S2C_descriptor,
        new java.lang.String[] { "CharacterList", });
    internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZoneServerList_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZoneServerList_S2C_descriptor,
        new java.lang.String[] { "ZoneServerList", });
    internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ManageStarForCharacter_C2S_descriptor,
        new java.lang.String[] { "PlayerId", "SetStar", });
    internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ManageStarForCharacter_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_C2S_descriptor,
        new java.lang.String[] { "ZoneId", });
    internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CheckRegisterNewCharacter_S2C_descriptor,
        new java.lang.String[] { "Ip", "Port", "ChannelName", });
    internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZoneIpPort_C2S_descriptor,
        new java.lang.String[] { "ZoneId", });
    internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZoneIpPort_S2C_descriptor,
        new java.lang.String[] { "Ip", "Port", "ChannelName", });
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

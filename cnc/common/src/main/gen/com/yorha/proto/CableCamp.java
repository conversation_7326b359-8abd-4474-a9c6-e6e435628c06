// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/cableCamp/cable_camp.proto

package com.yorha.proto;

public final class CableCamp {
  private CableCamp() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CableCampEntityOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CableCampEntity)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     * @return The point.
     */
    com.yorha.proto.Struct.Point getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 3;</code>
     * @return Whether the partId field is set.
     */
    boolean hasPartId();
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 3;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <pre>
     * 等级   大小营地是刷新难度  墓碑是野怪templateId
     * </pre>
     *
     * <code>optional int32 level = 4;</code>
     * @return Whether the level field is set.
     */
    boolean hasLevel();
    /**
     * <pre>
     * 等级   大小营地是刷新难度  墓碑是野怪templateId
     * </pre>
     *
     * <code>optional int32 level = 4;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <pre>
     * 下次刷新时间    大营地是激活时间/刷boss的时间(小于当前时间说明在激活状态)   小营地是下次刷boss的时间(小于当前时间说明可攻打)
     * </pre>
     *
     * <code>optional int64 nextRefreshTsMs = 5;</code>
     * @return Whether the nextRefreshTsMs field is set.
     */
    boolean hasNextRefreshTsMs();
    /**
     * <pre>
     * 下次刷新时间    大营地是激活时间/刷boss的时间(小于当前时间说明在激活状态)   小营地是下次刷boss的时间(小于当前时间说明可攻打)
     * </pre>
     *
     * <code>optional int64 nextRefreshTsMs = 5;</code>
     * @return The nextRefreshTsMs.
     */
    long getNextRefreshTsMs();

    /**
     * <pre>
     * 当前警戒值  大型卡巴尔营地专用
     * </pre>
     *
     * <code>optional int32 curWarnV = 6;</code>
     * @return Whether the curWarnV field is set.
     */
    boolean hasCurWarnV();
    /**
     * <pre>
     * 当前警戒值  大型卡巴尔营地专用
     * </pre>
     *
     * <code>optional int32 curWarnV = 6;</code>
     * @return The curWarnV.
     */
    int getCurWarnV();

    /**
     * <pre>
     * 最后一击联盟简称  墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatClanName = 7;</code>
     * @return Whether the lastDefeatClanName field is set.
     */
    boolean hasLastDefeatClanName();
    /**
     * <pre>
     * 最后一击联盟简称  墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatClanName = 7;</code>
     * @return The lastDefeatClanName.
     */
    java.lang.String getLastDefeatClanName();
    /**
     * <pre>
     * 最后一击联盟简称  墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatClanName = 7;</code>
     * @return The bytes for lastDefeatClanName.
     */
    com.google.protobuf.ByteString
        getLastDefeatClanNameBytes();

    /**
     * <pre>
     * 最后一击玩家名字 墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatPlayerName = 8;</code>
     * @return Whether the lastDefeatPlayerName field is set.
     */
    boolean hasLastDefeatPlayerName();
    /**
     * <pre>
     * 最后一击玩家名字 墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatPlayerName = 8;</code>
     * @return The lastDefeatPlayerName.
     */
    java.lang.String getLastDefeatPlayerName();
    /**
     * <pre>
     * 最后一击玩家名字 墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatPlayerName = 8;</code>
     * @return The bytes for lastDefeatPlayerName.
     */
    com.google.protobuf.ByteString
        getLastDefeatPlayerNameBytes();

    /**
     * <pre>
     * 成功次数
     * </pre>
     *
     * <code>optional int32 succeedCount = 11;</code>
     * @return Whether the succeedCount field is set.
     */
    boolean hasSucceedCount();
    /**
     * <pre>
     * 成功次数
     * </pre>
     *
     * <code>optional int32 succeedCount = 11;</code>
     * @return The succeedCount.
     */
    int getSucceedCount();

    /**
     * <pre>
     * 下一次的等级  1升级 0 不升级不降级  -1降级
     * </pre>
     *
     * <code>optional int32 nextLevel = 12;</code>
     * @return Whether the nextLevel field is set.
     */
    boolean hasNextLevel();
    /**
     * <pre>
     * 下一次的等级  1升级 0 不升级不降级  -1降级
     * </pre>
     *
     * <code>optional int32 nextLevel = 12;</code>
     * @return The nextLevel.
     */
    int getNextLevel();
  }
  /**
   * <pre>
   * 卡巴尔营地
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.CableCampEntity}
   */
  public static final class CableCampEntity extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CableCampEntity)
      CableCampEntityOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CableCampEntity.newBuilder() to construct.
    private CableCampEntity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CableCampEntity() {
      lastDefeatClanName_ = "";
      lastDefeatPlayerName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CableCampEntity();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CableCampEntity(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              templateId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              partId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              level_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              nextRefreshTsMs_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              curWarnV_ = input.readInt32();
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              lastDefeatClanName_ = bs;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              lastDefeatPlayerName_ = bs;
              break;
            }
            case 88: {
              bitField0_ |= 0x00000100;
              succeedCount_ = input.readInt32();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000200;
              nextLevel_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.CableCamp.internal_static_com_yorha_proto_CableCampEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.CableCamp.internal_static_com_yorha_proto_CableCampEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.CableCamp.CableCampEntity.class, com.yorha.proto.CableCamp.CableCampEntity.Builder.class);
    }

    private int bitField0_;
    public static final int TEMPLATEID_FIELD_NUMBER = 1;
    private int templateId_;
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int POINT_FIELD_NUMBER = 2;
    private com.yorha.proto.Struct.Point point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }

    public static final int PARTID_FIELD_NUMBER = 3;
    private int partId_;
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 3;</code>
     * @return Whether the partId field is set.
     */
    @java.lang.Override
    public boolean hasPartId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 3;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int LEVEL_FIELD_NUMBER = 4;
    private int level_;
    /**
     * <pre>
     * 等级   大小营地是刷新难度  墓碑是野怪templateId
     * </pre>
     *
     * <code>optional int32 level = 4;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 等级   大小营地是刷新难度  墓碑是野怪templateId
     * </pre>
     *
     * <code>optional int32 level = 4;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int NEXTREFRESHTSMS_FIELD_NUMBER = 5;
    private long nextRefreshTsMs_;
    /**
     * <pre>
     * 下次刷新时间    大营地是激活时间/刷boss的时间(小于当前时间说明在激活状态)   小营地是下次刷boss的时间(小于当前时间说明可攻打)
     * </pre>
     *
     * <code>optional int64 nextRefreshTsMs = 5;</code>
     * @return Whether the nextRefreshTsMs field is set.
     */
    @java.lang.Override
    public boolean hasNextRefreshTsMs() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 下次刷新时间    大营地是激活时间/刷boss的时间(小于当前时间说明在激活状态)   小营地是下次刷boss的时间(小于当前时间说明可攻打)
     * </pre>
     *
     * <code>optional int64 nextRefreshTsMs = 5;</code>
     * @return The nextRefreshTsMs.
     */
    @java.lang.Override
    public long getNextRefreshTsMs() {
      return nextRefreshTsMs_;
    }

    public static final int CURWARNV_FIELD_NUMBER = 6;
    private int curWarnV_;
    /**
     * <pre>
     * 当前警戒值  大型卡巴尔营地专用
     * </pre>
     *
     * <code>optional int32 curWarnV = 6;</code>
     * @return Whether the curWarnV field is set.
     */
    @java.lang.Override
    public boolean hasCurWarnV() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 当前警戒值  大型卡巴尔营地专用
     * </pre>
     *
     * <code>optional int32 curWarnV = 6;</code>
     * @return The curWarnV.
     */
    @java.lang.Override
    public int getCurWarnV() {
      return curWarnV_;
    }

    public static final int LASTDEFEATCLANNAME_FIELD_NUMBER = 7;
    private volatile java.lang.Object lastDefeatClanName_;
    /**
     * <pre>
     * 最后一击联盟简称  墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatClanName = 7;</code>
     * @return Whether the lastDefeatClanName field is set.
     */
    @java.lang.Override
    public boolean hasLastDefeatClanName() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 最后一击联盟简称  墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatClanName = 7;</code>
     * @return The lastDefeatClanName.
     */
    @java.lang.Override
    public java.lang.String getLastDefeatClanName() {
      java.lang.Object ref = lastDefeatClanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          lastDefeatClanName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 最后一击联盟简称  墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatClanName = 7;</code>
     * @return The bytes for lastDefeatClanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLastDefeatClanNameBytes() {
      java.lang.Object ref = lastDefeatClanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lastDefeatClanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LASTDEFEATPLAYERNAME_FIELD_NUMBER = 8;
    private volatile java.lang.Object lastDefeatPlayerName_;
    /**
     * <pre>
     * 最后一击玩家名字 墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatPlayerName = 8;</code>
     * @return Whether the lastDefeatPlayerName field is set.
     */
    @java.lang.Override
    public boolean hasLastDefeatPlayerName() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 最后一击玩家名字 墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatPlayerName = 8;</code>
     * @return The lastDefeatPlayerName.
     */
    @java.lang.Override
    public java.lang.String getLastDefeatPlayerName() {
      java.lang.Object ref = lastDefeatPlayerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          lastDefeatPlayerName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 最后一击玩家名字 墓碑专用字段
     * </pre>
     *
     * <code>optional string lastDefeatPlayerName = 8;</code>
     * @return The bytes for lastDefeatPlayerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLastDefeatPlayerNameBytes() {
      java.lang.Object ref = lastDefeatPlayerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lastDefeatPlayerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SUCCEEDCOUNT_FIELD_NUMBER = 11;
    private int succeedCount_;
    /**
     * <pre>
     * 成功次数
     * </pre>
     *
     * <code>optional int32 succeedCount = 11;</code>
     * @return Whether the succeedCount field is set.
     */
    @java.lang.Override
    public boolean hasSucceedCount() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 成功次数
     * </pre>
     *
     * <code>optional int32 succeedCount = 11;</code>
     * @return The succeedCount.
     */
    @java.lang.Override
    public int getSucceedCount() {
      return succeedCount_;
    }

    public static final int NEXTLEVEL_FIELD_NUMBER = 12;
    private int nextLevel_;
    /**
     * <pre>
     * 下一次的等级  1升级 0 不升级不降级  -1降级
     * </pre>
     *
     * <code>optional int32 nextLevel = 12;</code>
     * @return Whether the nextLevel field is set.
     */
    @java.lang.Override
    public boolean hasNextLevel() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 下一次的等级  1升级 0 不升级不降级  -1降级
     * </pre>
     *
     * <code>optional int32 nextLevel = 12;</code>
     * @return The nextLevel.
     */
    @java.lang.Override
    public int getNextLevel() {
      return nextLevel_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, partId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, level_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, nextRefreshTsMs_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, curWarnV_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, lastDefeatClanName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, lastDefeatPlayerName_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt32(11, succeedCount_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt32(12, nextLevel_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, partId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, level_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, nextRefreshTsMs_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, curWarnV_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, lastDefeatClanName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, lastDefeatPlayerName_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, succeedCount_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, nextLevel_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.CableCamp.CableCampEntity)) {
        return super.equals(obj);
      }
      com.yorha.proto.CableCamp.CableCampEntity other = (com.yorha.proto.CableCamp.CableCampEntity) obj;

      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasPartId() != other.hasPartId()) return false;
      if (hasPartId()) {
        if (getPartId()
            != other.getPartId()) return false;
      }
      if (hasLevel() != other.hasLevel()) return false;
      if (hasLevel()) {
        if (getLevel()
            != other.getLevel()) return false;
      }
      if (hasNextRefreshTsMs() != other.hasNextRefreshTsMs()) return false;
      if (hasNextRefreshTsMs()) {
        if (getNextRefreshTsMs()
            != other.getNextRefreshTsMs()) return false;
      }
      if (hasCurWarnV() != other.hasCurWarnV()) return false;
      if (hasCurWarnV()) {
        if (getCurWarnV()
            != other.getCurWarnV()) return false;
      }
      if (hasLastDefeatClanName() != other.hasLastDefeatClanName()) return false;
      if (hasLastDefeatClanName()) {
        if (!getLastDefeatClanName()
            .equals(other.getLastDefeatClanName())) return false;
      }
      if (hasLastDefeatPlayerName() != other.hasLastDefeatPlayerName()) return false;
      if (hasLastDefeatPlayerName()) {
        if (!getLastDefeatPlayerName()
            .equals(other.getLastDefeatPlayerName())) return false;
      }
      if (hasSucceedCount() != other.hasSucceedCount()) return false;
      if (hasSucceedCount()) {
        if (getSucceedCount()
            != other.getSucceedCount()) return false;
      }
      if (hasNextLevel() != other.hasNextLevel()) return false;
      if (hasNextLevel()) {
        if (getNextLevel()
            != other.getNextLevel()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasPartId()) {
        hash = (37 * hash) + PARTID_FIELD_NUMBER;
        hash = (53 * hash) + getPartId();
      }
      if (hasLevel()) {
        hash = (37 * hash) + LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getLevel();
      }
      if (hasNextRefreshTsMs()) {
        hash = (37 * hash) + NEXTREFRESHTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getNextRefreshTsMs());
      }
      if (hasCurWarnV()) {
        hash = (37 * hash) + CURWARNV_FIELD_NUMBER;
        hash = (53 * hash) + getCurWarnV();
      }
      if (hasLastDefeatClanName()) {
        hash = (37 * hash) + LASTDEFEATCLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getLastDefeatClanName().hashCode();
      }
      if (hasLastDefeatPlayerName()) {
        hash = (37 * hash) + LASTDEFEATPLAYERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getLastDefeatPlayerName().hashCode();
      }
      if (hasSucceedCount()) {
        hash = (37 * hash) + SUCCEEDCOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getSucceedCount();
      }
      if (hasNextLevel()) {
        hash = (37 * hash) + NEXTLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getNextLevel();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.CableCamp.CableCampEntity parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.CableCamp.CableCampEntity prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 卡巴尔营地
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.CableCampEntity}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CableCampEntity)
        com.yorha.proto.CableCamp.CableCampEntityOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.CableCamp.internal_static_com_yorha_proto_CableCampEntity_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.CableCamp.internal_static_com_yorha_proto_CableCampEntity_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.CableCamp.CableCampEntity.class, com.yorha.proto.CableCamp.CableCampEntity.Builder.class);
      }

      // Construct using com.yorha.proto.CableCamp.CableCampEntity.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        partId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        level_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        nextRefreshTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        curWarnV_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        lastDefeatClanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        lastDefeatPlayerName_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        succeedCount_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        nextLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.CableCamp.internal_static_com_yorha_proto_CableCampEntity_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.CableCamp.CableCampEntity getDefaultInstanceForType() {
        return com.yorha.proto.CableCamp.CableCampEntity.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.CableCamp.CableCampEntity build() {
        com.yorha.proto.CableCamp.CableCampEntity result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.CableCamp.CableCampEntity buildPartial() {
        com.yorha.proto.CableCamp.CableCampEntity result = new com.yorha.proto.CableCamp.CableCampEntity(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.partId_ = partId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.level_ = level_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.nextRefreshTsMs_ = nextRefreshTsMs_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.curWarnV_ = curWarnV_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.lastDefeatClanName_ = lastDefeatClanName_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.lastDefeatPlayerName_ = lastDefeatPlayerName_;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.succeedCount_ = succeedCount_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.nextLevel_ = nextLevel_;
          to_bitField0_ |= 0x00000200;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.CableCamp.CableCampEntity) {
          return mergeFrom((com.yorha.proto.CableCamp.CableCampEntity)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.CableCamp.CableCampEntity other) {
        if (other == com.yorha.proto.CableCamp.CableCampEntity.getDefaultInstance()) return this;
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasPartId()) {
          setPartId(other.getPartId());
        }
        if (other.hasLevel()) {
          setLevel(other.getLevel());
        }
        if (other.hasNextRefreshTsMs()) {
          setNextRefreshTsMs(other.getNextRefreshTsMs());
        }
        if (other.hasCurWarnV()) {
          setCurWarnV(other.getCurWarnV());
        }
        if (other.hasLastDefeatClanName()) {
          bitField0_ |= 0x00000040;
          lastDefeatClanName_ = other.lastDefeatClanName_;
          onChanged();
        }
        if (other.hasLastDefeatPlayerName()) {
          bitField0_ |= 0x00000080;
          lastDefeatPlayerName_ = other.lastDefeatPlayerName_;
          onChanged();
        }
        if (other.hasSucceedCount()) {
          setSucceedCount(other.getSucceedCount());
        }
        if (other.hasNextLevel()) {
          setNextLevel(other.getNextLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.CableCamp.CableCampEntity parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.CableCamp.CableCampEntity) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int templateId_ ;
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000001;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Point point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       * @return The point.
       */
      public com.yorha.proto.Struct.Point getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public Builder setPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public Builder setPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public Builder mergePoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            point_ =
              com.yorha.proto.Struct.Point.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private int partId_ ;
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 3;</code>
       * @return Whether the partId field is set.
       */
      @java.lang.Override
      public boolean hasPartId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 3;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 3;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {
        bitField0_ |= 0x00000004;
        partId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       * 等级   大小营地是刷新难度  墓碑是野怪templateId
       * </pre>
       *
       * <code>optional int32 level = 4;</code>
       * @return Whether the level field is set.
       */
      @java.lang.Override
      public boolean hasLevel() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 等级   大小营地是刷新难度  墓碑是野怪templateId
       * </pre>
       *
       * <code>optional int32 level = 4;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       * 等级   大小营地是刷新难度  墓碑是野怪templateId
       * </pre>
       *
       * <code>optional int32 level = 4;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        bitField0_ |= 0x00000008;
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 等级   大小营地是刷新难度  墓碑是野怪templateId
       * </pre>
       *
       * <code>optional int32 level = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000008);
        level_ = 0;
        onChanged();
        return this;
      }

      private long nextRefreshTsMs_ ;
      /**
       * <pre>
       * 下次刷新时间    大营地是激活时间/刷boss的时间(小于当前时间说明在激活状态)   小营地是下次刷boss的时间(小于当前时间说明可攻打)
       * </pre>
       *
       * <code>optional int64 nextRefreshTsMs = 5;</code>
       * @return Whether the nextRefreshTsMs field is set.
       */
      @java.lang.Override
      public boolean hasNextRefreshTsMs() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 下次刷新时间    大营地是激活时间/刷boss的时间(小于当前时间说明在激活状态)   小营地是下次刷boss的时间(小于当前时间说明可攻打)
       * </pre>
       *
       * <code>optional int64 nextRefreshTsMs = 5;</code>
       * @return The nextRefreshTsMs.
       */
      @java.lang.Override
      public long getNextRefreshTsMs() {
        return nextRefreshTsMs_;
      }
      /**
       * <pre>
       * 下次刷新时间    大营地是激活时间/刷boss的时间(小于当前时间说明在激活状态)   小营地是下次刷boss的时间(小于当前时间说明可攻打)
       * </pre>
       *
       * <code>optional int64 nextRefreshTsMs = 5;</code>
       * @param value The nextRefreshTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setNextRefreshTsMs(long value) {
        bitField0_ |= 0x00000010;
        nextRefreshTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下次刷新时间    大营地是激活时间/刷boss的时间(小于当前时间说明在激活状态)   小营地是下次刷boss的时间(小于当前时间说明可攻打)
       * </pre>
       *
       * <code>optional int64 nextRefreshTsMs = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextRefreshTsMs() {
        bitField0_ = (bitField0_ & ~0x00000010);
        nextRefreshTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int curWarnV_ ;
      /**
       * <pre>
       * 当前警戒值  大型卡巴尔营地专用
       * </pre>
       *
       * <code>optional int32 curWarnV = 6;</code>
       * @return Whether the curWarnV field is set.
       */
      @java.lang.Override
      public boolean hasCurWarnV() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 当前警戒值  大型卡巴尔营地专用
       * </pre>
       *
       * <code>optional int32 curWarnV = 6;</code>
       * @return The curWarnV.
       */
      @java.lang.Override
      public int getCurWarnV() {
        return curWarnV_;
      }
      /**
       * <pre>
       * 当前警戒值  大型卡巴尔营地专用
       * </pre>
       *
       * <code>optional int32 curWarnV = 6;</code>
       * @param value The curWarnV to set.
       * @return This builder for chaining.
       */
      public Builder setCurWarnV(int value) {
        bitField0_ |= 0x00000020;
        curWarnV_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前警戒值  大型卡巴尔营地专用
       * </pre>
       *
       * <code>optional int32 curWarnV = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurWarnV() {
        bitField0_ = (bitField0_ & ~0x00000020);
        curWarnV_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object lastDefeatClanName_ = "";
      /**
       * <pre>
       * 最后一击联盟简称  墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatClanName = 7;</code>
       * @return Whether the lastDefeatClanName field is set.
       */
      public boolean hasLastDefeatClanName() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 最后一击联盟简称  墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatClanName = 7;</code>
       * @return The lastDefeatClanName.
       */
      public java.lang.String getLastDefeatClanName() {
        java.lang.Object ref = lastDefeatClanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            lastDefeatClanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 最后一击联盟简称  墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatClanName = 7;</code>
       * @return The bytes for lastDefeatClanName.
       */
      public com.google.protobuf.ByteString
          getLastDefeatClanNameBytes() {
        java.lang.Object ref = lastDefeatClanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          lastDefeatClanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 最后一击联盟简称  墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatClanName = 7;</code>
       * @param value The lastDefeatClanName to set.
       * @return This builder for chaining.
       */
      public Builder setLastDefeatClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        lastDefeatClanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后一击联盟简称  墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatClanName = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastDefeatClanName() {
        bitField0_ = (bitField0_ & ~0x00000040);
        lastDefeatClanName_ = getDefaultInstance().getLastDefeatClanName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后一击联盟简称  墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatClanName = 7;</code>
       * @param value The bytes for lastDefeatClanName to set.
       * @return This builder for chaining.
       */
      public Builder setLastDefeatClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        lastDefeatClanName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object lastDefeatPlayerName_ = "";
      /**
       * <pre>
       * 最后一击玩家名字 墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatPlayerName = 8;</code>
       * @return Whether the lastDefeatPlayerName field is set.
       */
      public boolean hasLastDefeatPlayerName() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 最后一击玩家名字 墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatPlayerName = 8;</code>
       * @return The lastDefeatPlayerName.
       */
      public java.lang.String getLastDefeatPlayerName() {
        java.lang.Object ref = lastDefeatPlayerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            lastDefeatPlayerName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 最后一击玩家名字 墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatPlayerName = 8;</code>
       * @return The bytes for lastDefeatPlayerName.
       */
      public com.google.protobuf.ByteString
          getLastDefeatPlayerNameBytes() {
        java.lang.Object ref = lastDefeatPlayerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          lastDefeatPlayerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 最后一击玩家名字 墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatPlayerName = 8;</code>
       * @param value The lastDefeatPlayerName to set.
       * @return This builder for chaining.
       */
      public Builder setLastDefeatPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        lastDefeatPlayerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后一击玩家名字 墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatPlayerName = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastDefeatPlayerName() {
        bitField0_ = (bitField0_ & ~0x00000080);
        lastDefeatPlayerName_ = getDefaultInstance().getLastDefeatPlayerName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后一击玩家名字 墓碑专用字段
       * </pre>
       *
       * <code>optional string lastDefeatPlayerName = 8;</code>
       * @param value The bytes for lastDefeatPlayerName to set.
       * @return This builder for chaining.
       */
      public Builder setLastDefeatPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        lastDefeatPlayerName_ = value;
        onChanged();
        return this;
      }

      private int succeedCount_ ;
      /**
       * <pre>
       * 成功次数
       * </pre>
       *
       * <code>optional int32 succeedCount = 11;</code>
       * @return Whether the succeedCount field is set.
       */
      @java.lang.Override
      public boolean hasSucceedCount() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 成功次数
       * </pre>
       *
       * <code>optional int32 succeedCount = 11;</code>
       * @return The succeedCount.
       */
      @java.lang.Override
      public int getSucceedCount() {
        return succeedCount_;
      }
      /**
       * <pre>
       * 成功次数
       * </pre>
       *
       * <code>optional int32 succeedCount = 11;</code>
       * @param value The succeedCount to set.
       * @return This builder for chaining.
       */
      public Builder setSucceedCount(int value) {
        bitField0_ |= 0x00000100;
        succeedCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成功次数
       * </pre>
       *
       * <code>optional int32 succeedCount = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearSucceedCount() {
        bitField0_ = (bitField0_ & ~0x00000100);
        succeedCount_ = 0;
        onChanged();
        return this;
      }

      private int nextLevel_ ;
      /**
       * <pre>
       * 下一次的等级  1升级 0 不升级不降级  -1降级
       * </pre>
       *
       * <code>optional int32 nextLevel = 12;</code>
       * @return Whether the nextLevel field is set.
       */
      @java.lang.Override
      public boolean hasNextLevel() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 下一次的等级  1升级 0 不升级不降级  -1降级
       * </pre>
       *
       * <code>optional int32 nextLevel = 12;</code>
       * @return The nextLevel.
       */
      @java.lang.Override
      public int getNextLevel() {
        return nextLevel_;
      }
      /**
       * <pre>
       * 下一次的等级  1升级 0 不升级不降级  -1降级
       * </pre>
       *
       * <code>optional int32 nextLevel = 12;</code>
       * @param value The nextLevel to set.
       * @return This builder for chaining.
       */
      public Builder setNextLevel(int value) {
        bitField0_ |= 0x00000200;
        nextLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下一次的等级  1升级 0 不升级不降级  -1降级
       * </pre>
       *
       * <code>optional int32 nextLevel = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextLevel() {
        bitField0_ = (bitField0_ & ~0x00000200);
        nextLevel_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CableCampEntity)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CableCampEntity)
    private static final com.yorha.proto.CableCamp.CableCampEntity DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.CableCamp.CableCampEntity();
    }

    public static com.yorha.proto.CableCamp.CableCampEntity getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CableCampEntity>
        PARSER = new com.google.protobuf.AbstractParser<CableCampEntity>() {
      @java.lang.Override
      public CableCampEntity parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CableCampEntity(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CableCampEntity> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CableCampEntity> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.CableCamp.CableCampEntity getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CableCampEntity_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CableCampEntity_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'ss_proto/gen/cableCamp/cable_camp.prot" +
      "o\022\017com.yorha.proto\032 ss_proto/gen/common/" +
      "struct.proto\"\371\001\n\017CableCampEntity\022\022\n\ntemp" +
      "lateId\030\001 \001(\005\022%\n\005point\030\002 \001(\0132\026.com.yorha." +
      "proto.Point\022\016\n\006partId\030\003 \001(\005\022\r\n\005level\030\004 \001" +
      "(\005\022\027\n\017nextRefreshTsMs\030\005 \001(\003\022\020\n\010curWarnV\030" +
      "\006 \001(\005\022\032\n\022lastDefeatClanName\030\007 \001(\t\022\034\n\024las" +
      "tDefeatPlayerName\030\010 \001(\t\022\024\n\014succeedCount\030" +
      "\013 \001(\005\022\021\n\tnextLevel\030\014 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_CableCampEntity_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_CableCampEntity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CableCampEntity_descriptor,
        new java.lang.String[] { "TemplateId", "Point", "PartId", "Level", "NextRefreshTsMs", "CurWarnV", "LastDefeatClanName", "LastDefeatPlayerName", "SucceedCount", "NextLevel", });
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

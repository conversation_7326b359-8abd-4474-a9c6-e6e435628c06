// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_inner_build_logic.proto

package com.yorha.proto;

public final class PlayerInnerBuildLogic {
  private PlayerInnerBuildLogic() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_PromptForPlaneUnlock_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PromptForPlaneUnlock_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 飞机id（运输机，战斗机，侦察机）
     * </pre>
     *
     * <code>optional int64 battlePlaneId = 1;</code>
     * @return Whether the battlePlaneId field is set.
     */
    boolean hasBattlePlaneId();
    /**
     * <pre>
     * 飞机id（运输机，战斗机，侦察机）
     * </pre>
     *
     * <code>optional int64 battlePlaneId = 1;</code>
     * @return The battlePlaneId.
     */
    long getBattlePlaneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PromptForPlaneUnlock_C2S}
   */
  public static final class Player_PromptForPlaneUnlock_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PromptForPlaneUnlock_C2S)
      Player_PromptForPlaneUnlock_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PromptForPlaneUnlock_C2S.newBuilder() to construct.
    private Player_PromptForPlaneUnlock_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PromptForPlaneUnlock_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PromptForPlaneUnlock_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PromptForPlaneUnlock_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              battlePlaneId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int BATTLEPLANEID_FIELD_NUMBER = 1;
    private long battlePlaneId_;
    /**
     * <pre>
     * 飞机id（运输机，战斗机，侦察机）
     * </pre>
     *
     * <code>optional int64 battlePlaneId = 1;</code>
     * @return Whether the battlePlaneId field is set.
     */
    @java.lang.Override
    public boolean hasBattlePlaneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 飞机id（运输机，战斗机，侦察机）
     * </pre>
     *
     * <code>optional int64 battlePlaneId = 1;</code>
     * @return The battlePlaneId.
     */
    @java.lang.Override
    public long getBattlePlaneId() {
      return battlePlaneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, battlePlaneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, battlePlaneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S other = (com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S) obj;

      if (hasBattlePlaneId() != other.hasBattlePlaneId()) return false;
      if (hasBattlePlaneId()) {
        if (getBattlePlaneId()
            != other.getBattlePlaneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBattlePlaneId()) {
        hash = (37 * hash) + BATTLEPLANEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBattlePlaneId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PromptForPlaneUnlock_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PromptForPlaneUnlock_C2S)
        com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        battlePlaneId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S result = new com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.battlePlaneId_ = battlePlaneId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S.getDefaultInstance()) return this;
        if (other.hasBattlePlaneId()) {
          setBattlePlaneId(other.getBattlePlaneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long battlePlaneId_ ;
      /**
       * <pre>
       * 飞机id（运输机，战斗机，侦察机）
       * </pre>
       *
       * <code>optional int64 battlePlaneId = 1;</code>
       * @return Whether the battlePlaneId field is set.
       */
      @java.lang.Override
      public boolean hasBattlePlaneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 飞机id（运输机，战斗机，侦察机）
       * </pre>
       *
       * <code>optional int64 battlePlaneId = 1;</code>
       * @return The battlePlaneId.
       */
      @java.lang.Override
      public long getBattlePlaneId() {
        return battlePlaneId_;
      }
      /**
       * <pre>
       * 飞机id（运输机，战斗机，侦察机）
       * </pre>
       *
       * <code>optional int64 battlePlaneId = 1;</code>
       * @param value The battlePlaneId to set.
       * @return This builder for chaining.
       */
      public Builder setBattlePlaneId(long value) {
        bitField0_ |= 0x00000001;
        battlePlaneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 飞机id（运输机，战斗机，侦察机）
       * </pre>
       *
       * <code>optional int64 battlePlaneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBattlePlaneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        battlePlaneId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PromptForPlaneUnlock_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PromptForPlaneUnlock_C2S)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PromptForPlaneUnlock_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_PromptForPlaneUnlock_C2S>() {
      @java.lang.Override
      public Player_PromptForPlaneUnlock_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PromptForPlaneUnlock_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PromptForPlaneUnlock_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PromptForPlaneUnlock_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_PromptForPlaneUnlock_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PromptForPlaneUnlock_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PromptForPlaneUnlock_S2C}
   */
  public static final class Player_PromptForPlaneUnlock_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PromptForPlaneUnlock_S2C)
      Player_PromptForPlaneUnlock_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PromptForPlaneUnlock_S2C.newBuilder() to construct.
    private Player_PromptForPlaneUnlock_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PromptForPlaneUnlock_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PromptForPlaneUnlock_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PromptForPlaneUnlock_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C other = (com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PromptForPlaneUnlock_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PromptForPlaneUnlock_S2C)
        com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C result = new com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PromptForPlaneUnlock_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PromptForPlaneUnlock_S2C)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PromptForPlaneUnlock_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_PromptForPlaneUnlock_S2C>() {
      @java.lang.Override
      public Player_PromptForPlaneUnlock_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PromptForPlaneUnlock_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PromptForPlaneUnlock_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PromptForPlaneUnlock_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetNoticeBoard_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetNoticeBoard_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetNoticeBoard_C2S}
   */
  public static final class Player_GetNoticeBoard_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetNoticeBoard_C2S)
      Player_GetNoticeBoard_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetNoticeBoard_C2S.newBuilder() to construct.
    private Player_GetNoticeBoard_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetNoticeBoard_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetNoticeBoard_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetNoticeBoard_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S other = (com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetNoticeBoard_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetNoticeBoard_C2S)
        com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S result = new com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetNoticeBoard_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetNoticeBoard_C2S)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetNoticeBoard_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetNoticeBoard_C2S>() {
      @java.lang.Override
      public Player_GetNoticeBoard_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetNoticeBoard_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetNoticeBoard_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetNoticeBoard_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetNoticeBoard_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetNoticeBoard_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> 
        getInfoList();
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getInfo(int index);
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    int getInfoCount();
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> 
        getInfoOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder getInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetNoticeBoard_S2C}
   */
  public static final class Player_GetNoticeBoard_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetNoticeBoard_S2C)
      Player_GetNoticeBoard_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetNoticeBoard_S2C.newBuilder() to construct.
    private Player_GetNoticeBoard_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetNoticeBoard_S2C() {
      info_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetNoticeBoard_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetNoticeBoard_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = new java.util.ArrayList<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              info_.add(
                  input.readMessage(com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          info_ = java.util.Collections.unmodifiableList(info_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C.Builder.class);
    }

    public static final int INFO_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> info_;
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> getInfoList() {
      return info_;
    }
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> 
        getInfoOrBuilderList() {
      return info_;
    }
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public int getInfoCount() {
      return info_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getInfo(int index) {
      return info_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder getInfoOrBuilder(
        int index) {
      return info_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < info_.size(); i++) {
        output.writeMessage(1, info_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < info_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, info_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C other = (com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C) obj;

      if (!getInfoList()
          .equals(other.getInfoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfoCount() > 0) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetNoticeBoard_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetNoticeBoard_S2C)
        com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C result = new com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C(this);
        int from_bitField0_ = bitField0_;
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            info_ = java.util.Collections.unmodifiableList(info_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.info_ = info_;
        } else {
          result.info_ = infoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C.getDefaultInstance()) return this;
        if (infoBuilder_ == null) {
          if (!other.info_.isEmpty()) {
            if (info_.isEmpty()) {
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfoIsMutable();
              info_.addAll(other.info_);
            }
            onChanged();
          }
        } else {
          if (!other.info_.isEmpty()) {
            if (infoBuilder_.isEmpty()) {
              infoBuilder_.dispose();
              infoBuilder_ = null;
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfoFieldBuilder() : null;
            } else {
              infoBuilder_.addAllMessages(other.info_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> info_ =
        java.util.Collections.emptyList();
      private void ensureInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          info_ = new java.util.ArrayList<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo>(info_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> infoBuilder_;

      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> getInfoList() {
        if (infoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(info_);
        } else {
          return infoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public int getInfoCount() {
        if (infoBuilder_ == null) {
          return info_.size();
        } else {
          return infoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getInfo(int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);
        } else {
          return infoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.set(index, value);
          onChanged();
        } else {
          infoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.set(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addInfo(com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(value);
          onChanged();
        } else {
          infoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(index, value);
          onChanged();
        } else {
          infoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addInfo(
          com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addAllInfo(
          java.lang.Iterable<? extends com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> values) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, info_);
          onChanged();
        } else {
          infoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder removeInfo(int index) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.remove(index);
          onChanged();
        } else {
          infoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder getInfoBuilder(
          int index) {
        return getInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder getInfoOrBuilder(
          int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);  } else {
          return infoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> 
           getInfoOrBuilderList() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(info_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder addInfoBuilder() {
        return getInfoFieldBuilder().addBuilder(
            com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder addInfoBuilder(
          int index) {
        return getInfoFieldBuilder().addBuilder(
            index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder> 
           getInfoBuilderList() {
        return getInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder>(
                  info_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetNoticeBoard_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetNoticeBoard_S2C)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetNoticeBoard_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetNoticeBoard_S2C>() {
      @java.lang.Override
      public Player_GetNoticeBoard_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetNoticeBoard_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetNoticeBoard_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetNoticeBoard_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_NoticeBoard_NTFOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_NoticeBoard_NTF)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> 
        getInfoList();
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getInfo(int index);
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    int getInfoCount();
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> 
        getInfoOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder getInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_NoticeBoard_NTF}
   */
  public static final class Player_NoticeBoard_NTF extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_NoticeBoard_NTF)
      Player_NoticeBoard_NTFOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_NoticeBoard_NTF.newBuilder() to construct.
    private Player_NoticeBoard_NTF(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_NoticeBoard_NTF() {
      info_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_NoticeBoard_NTF();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_NoticeBoard_NTF(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = new java.util.ArrayList<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              info_.add(
                  input.readMessage(com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          info_ = java.util.Collections.unmodifiableList(info_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_NoticeBoard_NTF_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_NoticeBoard_NTF_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF.class, com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF.Builder.class);
    }

    public static final int INFO_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> info_;
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> getInfoList() {
      return info_;
    }
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> 
        getInfoOrBuilderList() {
      return info_;
    }
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public int getInfoCount() {
      return info_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getInfo(int index) {
      return info_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder getInfoOrBuilder(
        int index) {
      return info_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < info_.size(); i++) {
        output.writeMessage(1, info_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < info_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, info_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF other = (com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF) obj;

      if (!getInfoList()
          .equals(other.getInfoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfoCount() > 0) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_NoticeBoard_NTF}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_NoticeBoard_NTF)
        com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTFOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_NoticeBoard_NTF_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_NoticeBoard_NTF_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF.class, com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_NoticeBoard_NTF_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF result = new com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF(this);
        int from_bitField0_ = bitField0_;
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            info_ = java.util.Collections.unmodifiableList(info_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.info_ = info_;
        } else {
          result.info_ = infoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF.getDefaultInstance()) return this;
        if (infoBuilder_ == null) {
          if (!other.info_.isEmpty()) {
            if (info_.isEmpty()) {
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfoIsMutable();
              info_.addAll(other.info_);
            }
            onChanged();
          }
        } else {
          if (!other.info_.isEmpty()) {
            if (infoBuilder_.isEmpty()) {
              infoBuilder_.dispose();
              infoBuilder_ = null;
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfoFieldBuilder() : null;
            } else {
              infoBuilder_.addAllMessages(other.info_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> info_ =
        java.util.Collections.emptyList();
      private void ensureInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          info_ = new java.util.ArrayList<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo>(info_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> infoBuilder_;

      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> getInfoList() {
        if (infoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(info_);
        } else {
          return infoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public int getInfoCount() {
        if (infoBuilder_ == null) {
          return info_.size();
        } else {
          return infoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getInfo(int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);
        } else {
          return infoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.set(index, value);
          onChanged();
        } else {
          infoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.set(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addInfo(com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(value);
          onChanged();
        } else {
          infoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(index, value);
          onChanged();
        } else {
          infoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addInfo(
          com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder addAllInfo(
          java.lang.Iterable<? extends com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo> values) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, info_);
          onChanged();
        } else {
          infoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public Builder removeInfo(int index) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.remove(index);
          onChanged();
        } else {
          infoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder getInfoBuilder(
          int index) {
        return getInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder getInfoOrBuilder(
          int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);  } else {
          return infoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> 
           getInfoOrBuilderList() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(info_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder addInfoBuilder() {
        return getInfoFieldBuilder().addBuilder(
            com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder addInfoBuilder(
          int index) {
        return getInfoFieldBuilder().addBuilder(
            index, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.NoticeInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder> 
           getInfoBuilderList() {
        return getInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder>(
                  info_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_NoticeBoard_NTF)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_NoticeBoard_NTF)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_NoticeBoard_NTF>
        PARSER = new com.google.protobuf.AbstractParser<Player_NoticeBoard_NTF>() {
      @java.lang.Override
      public Player_NoticeBoard_NTF parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_NoticeBoard_NTF(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_NoticeBoard_NTF> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_NoticeBoard_NTF> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_NoticeBoard_NTF getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface NoticeInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.NoticeInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string tab = 1;</code>
     * @return Whether the tab field is set.
     */
    boolean hasTab();
    /**
     * <code>optional string tab = 1;</code>
     * @return The tab.
     */
    java.lang.String getTab();
    /**
     * <code>optional string tab = 1;</code>
     * @return The bytes for tab.
     */
    com.google.protobuf.ByteString
        getTabBytes();

    /**
     * <code>optional string title = 2;</code>
     * @return Whether the title field is set.
     */
    boolean hasTitle();
    /**
     * <code>optional string title = 2;</code>
     * @return The title.
     */
    java.lang.String getTitle();
    /**
     * <code>optional string title = 2;</code>
     * @return The bytes for title.
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    /**
     * <code>optional string desc = 3;</code>
     * @return Whether the desc field is set.
     */
    boolean hasDesc();
    /**
     * <code>optional string desc = 3;</code>
     * @return The desc.
     */
    java.lang.String getDesc();
    /**
     * <code>optional string desc = 3;</code>
     * @return The bytes for desc.
     */
    com.google.protobuf.ByteString
        getDescBytes();

    /**
     * <code>optional string image = 4;</code>
     * @return Whether the image field is set.
     */
    boolean hasImage();
    /**
     * <code>optional string image = 4;</code>
     * @return The image.
     */
    java.lang.String getImage();
    /**
     * <code>optional string image = 4;</code>
     * @return The bytes for image.
     */
    com.google.protobuf.ByteString
        getImageBytes();

    /**
     * <code>optional int64 startTimeMs = 5;</code>
     * @return Whether the startTimeMs field is set.
     */
    boolean hasStartTimeMs();
    /**
     * <code>optional int64 startTimeMs = 5;</code>
     * @return The startTimeMs.
     */
    long getStartTimeMs();

    /**
     * <code>optional int64 offTimeMs = 6;</code>
     * @return Whether the offTimeMs field is set.
     */
    boolean hasOffTimeMs();
    /**
     * <code>optional int64 offTimeMs = 6;</code>
     * @return The offTimeMs.
     */
    long getOffTimeMs();

    /**
     * <code>optional int32 weight = 7;</code>
     * @return Whether the weight field is set.
     */
    boolean hasWeight();
    /**
     * <code>optional int32 weight = 7;</code>
     * @return The weight.
     */
    int getWeight();

    /**
     * <code>optional int32 activePush = 8;</code>
     * @return Whether the activePush field is set.
     */
    boolean hasActivePush();
    /**
     * <code>optional int32 activePush = 8;</code>
     * @return The activePush.
     */
    int getActivePush();

    /**
     * <code>optional int32 displayTime = 9;</code>
     * @return Whether the displayTime field is set.
     */
    boolean hasDisplayTime();
    /**
     * <code>optional int32 displayTime = 9;</code>
     * @return The displayTime.
     */
    int getDisplayTime();

    /**
     * <code>optional int32 id = 10;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <code>optional int32 id = 10;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.NoticeInfo}
   */
  public static final class NoticeInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.NoticeInfo)
      NoticeInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NoticeInfo.newBuilder() to construct.
    private NoticeInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NoticeInfo() {
      tab_ = "";
      title_ = "";
      desc_ = "";
      image_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NoticeInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private NoticeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              tab_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              title_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              desc_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              image_ = bs;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              startTimeMs_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              offTimeMs_ = input.readInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              weight_ = input.readInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              activePush_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              displayTime_ = input.readInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              id_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_NoticeInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_NoticeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.class, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder.class);
    }

    private int bitField0_;
    public static final int TAB_FIELD_NUMBER = 1;
    private volatile java.lang.Object tab_;
    /**
     * <code>optional string tab = 1;</code>
     * @return Whether the tab field is set.
     */
    @java.lang.Override
    public boolean hasTab() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public java.lang.String getTab() {
      java.lang.Object ref = tab_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          tab_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string tab = 1;</code>
     * @return The bytes for tab.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTabBytes() {
      java.lang.Object ref = tab_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tab_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TITLE_FIELD_NUMBER = 2;
    private volatile java.lang.Object title_;
    /**
     * <code>optional string title = 2;</code>
     * @return Whether the title field is set.
     */
    @java.lang.Override
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string title = 2;</code>
     * @return The title.
     */
    @java.lang.Override
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          title_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string title = 2;</code>
     * @return The bytes for title.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DESC_FIELD_NUMBER = 3;
    private volatile java.lang.Object desc_;
    /**
     * <code>optional string desc = 3;</code>
     * @return Whether the desc field is set.
     */
    @java.lang.Override
    public boolean hasDesc() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string desc = 3;</code>
     * @return The desc.
     */
    @java.lang.Override
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          desc_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string desc = 3;</code>
     * @return The bytes for desc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IMAGE_FIELD_NUMBER = 4;
    private volatile java.lang.Object image_;
    /**
     * <code>optional string image = 4;</code>
     * @return Whether the image field is set.
     */
    @java.lang.Override
    public boolean hasImage() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string image = 4;</code>
     * @return The image.
     */
    @java.lang.Override
    public java.lang.String getImage() {
      java.lang.Object ref = image_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          image_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string image = 4;</code>
     * @return The bytes for image.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getImageBytes() {
      java.lang.Object ref = image_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        image_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int STARTTIMEMS_FIELD_NUMBER = 5;
    private long startTimeMs_;
    /**
     * <code>optional int64 startTimeMs = 5;</code>
     * @return Whether the startTimeMs field is set.
     */
    @java.lang.Override
    public boolean hasStartTimeMs() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 startTimeMs = 5;</code>
     * @return The startTimeMs.
     */
    @java.lang.Override
    public long getStartTimeMs() {
      return startTimeMs_;
    }

    public static final int OFFTIMEMS_FIELD_NUMBER = 6;
    private long offTimeMs_;
    /**
     * <code>optional int64 offTimeMs = 6;</code>
     * @return Whether the offTimeMs field is set.
     */
    @java.lang.Override
    public boolean hasOffTimeMs() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int64 offTimeMs = 6;</code>
     * @return The offTimeMs.
     */
    @java.lang.Override
    public long getOffTimeMs() {
      return offTimeMs_;
    }

    public static final int WEIGHT_FIELD_NUMBER = 7;
    private int weight_;
    /**
     * <code>optional int32 weight = 7;</code>
     * @return Whether the weight field is set.
     */
    @java.lang.Override
    public boolean hasWeight() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional int32 weight = 7;</code>
     * @return The weight.
     */
    @java.lang.Override
    public int getWeight() {
      return weight_;
    }

    public static final int ACTIVEPUSH_FIELD_NUMBER = 8;
    private int activePush_;
    /**
     * <code>optional int32 activePush = 8;</code>
     * @return Whether the activePush field is set.
     */
    @java.lang.Override
    public boolean hasActivePush() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional int32 activePush = 8;</code>
     * @return The activePush.
     */
    @java.lang.Override
    public int getActivePush() {
      return activePush_;
    }

    public static final int DISPLAYTIME_FIELD_NUMBER = 9;
    private int displayTime_;
    /**
     * <code>optional int32 displayTime = 9;</code>
     * @return Whether the displayTime field is set.
     */
    @java.lang.Override
    public boolean hasDisplayTime() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional int32 displayTime = 9;</code>
     * @return The displayTime.
     */
    @java.lang.Override
    public int getDisplayTime() {
      return displayTime_;
    }

    public static final int ID_FIELD_NUMBER = 10;
    private int id_;
    /**
     * <code>optional int32 id = 10;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional int32 id = 10;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, tab_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, title_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, desc_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, image_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, startTimeMs_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt64(6, offTimeMs_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(7, weight_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt32(8, activePush_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt32(9, displayTime_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt32(10, id_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, tab_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, title_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, desc_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, image_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, startTimeMs_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, offTimeMs_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, weight_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, activePush_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, displayTime_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, id_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo other = (com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo) obj;

      if (hasTab() != other.hasTab()) return false;
      if (hasTab()) {
        if (!getTab()
            .equals(other.getTab())) return false;
      }
      if (hasTitle() != other.hasTitle()) return false;
      if (hasTitle()) {
        if (!getTitle()
            .equals(other.getTitle())) return false;
      }
      if (hasDesc() != other.hasDesc()) return false;
      if (hasDesc()) {
        if (!getDesc()
            .equals(other.getDesc())) return false;
      }
      if (hasImage() != other.hasImage()) return false;
      if (hasImage()) {
        if (!getImage()
            .equals(other.getImage())) return false;
      }
      if (hasStartTimeMs() != other.hasStartTimeMs()) return false;
      if (hasStartTimeMs()) {
        if (getStartTimeMs()
            != other.getStartTimeMs()) return false;
      }
      if (hasOffTimeMs() != other.hasOffTimeMs()) return false;
      if (hasOffTimeMs()) {
        if (getOffTimeMs()
            != other.getOffTimeMs()) return false;
      }
      if (hasWeight() != other.hasWeight()) return false;
      if (hasWeight()) {
        if (getWeight()
            != other.getWeight()) return false;
      }
      if (hasActivePush() != other.hasActivePush()) return false;
      if (hasActivePush()) {
        if (getActivePush()
            != other.getActivePush()) return false;
      }
      if (hasDisplayTime() != other.hasDisplayTime()) return false;
      if (hasDisplayTime()) {
        if (getDisplayTime()
            != other.getDisplayTime()) return false;
      }
      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTab()) {
        hash = (37 * hash) + TAB_FIELD_NUMBER;
        hash = (53 * hash) + getTab().hashCode();
      }
      if (hasTitle()) {
        hash = (37 * hash) + TITLE_FIELD_NUMBER;
        hash = (53 * hash) + getTitle().hashCode();
      }
      if (hasDesc()) {
        hash = (37 * hash) + DESC_FIELD_NUMBER;
        hash = (53 * hash) + getDesc().hashCode();
      }
      if (hasImage()) {
        hash = (37 * hash) + IMAGE_FIELD_NUMBER;
        hash = (53 * hash) + getImage().hashCode();
      }
      if (hasStartTimeMs()) {
        hash = (37 * hash) + STARTTIMEMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStartTimeMs());
      }
      if (hasOffTimeMs()) {
        hash = (37 * hash) + OFFTIMEMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOffTimeMs());
      }
      if (hasWeight()) {
        hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
        hash = (53 * hash) + getWeight();
      }
      if (hasActivePush()) {
        hash = (37 * hash) + ACTIVEPUSH_FIELD_NUMBER;
        hash = (53 * hash) + getActivePush();
      }
      if (hasDisplayTime()) {
        hash = (37 * hash) + DISPLAYTIME_FIELD_NUMBER;
        hash = (53 * hash) + getDisplayTime();
      }
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.NoticeInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.NoticeInfo)
        com.yorha.proto.PlayerInnerBuildLogic.NoticeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_NoticeInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_NoticeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.class, com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        tab_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        title_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        desc_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        image_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        startTimeMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        offTimeMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        weight_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        activePush_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        displayTime_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_NoticeInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo build() {
        com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo result = new com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.tab_ = tab_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.title_ = title_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.desc_ = desc_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.image_ = image_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.startTimeMs_ = startTimeMs_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.offTimeMs_ = offTimeMs_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.weight_ = weight_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.activePush_ = activePush_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.displayTime_ = displayTime_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000200;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo.getDefaultInstance()) return this;
        if (other.hasTab()) {
          bitField0_ |= 0x00000001;
          tab_ = other.tab_;
          onChanged();
        }
        if (other.hasTitle()) {
          bitField0_ |= 0x00000002;
          title_ = other.title_;
          onChanged();
        }
        if (other.hasDesc()) {
          bitField0_ |= 0x00000004;
          desc_ = other.desc_;
          onChanged();
        }
        if (other.hasImage()) {
          bitField0_ |= 0x00000008;
          image_ = other.image_;
          onChanged();
        }
        if (other.hasStartTimeMs()) {
          setStartTimeMs(other.getStartTimeMs());
        }
        if (other.hasOffTimeMs()) {
          setOffTimeMs(other.getOffTimeMs());
        }
        if (other.hasWeight()) {
          setWeight(other.getWeight());
        }
        if (other.hasActivePush()) {
          setActivePush(other.getActivePush());
        }
        if (other.hasDisplayTime()) {
          setDisplayTime(other.getDisplayTime());
        }
        if (other.hasId()) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object tab_ = "";
      /**
       * <code>optional string tab = 1;</code>
       * @return Whether the tab field is set.
       */
      public boolean hasTab() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string tab = 1;</code>
       * @return The tab.
       */
      public java.lang.String getTab() {
        java.lang.Object ref = tab_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            tab_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string tab = 1;</code>
       * @return The bytes for tab.
       */
      public com.google.protobuf.ByteString
          getTabBytes() {
        java.lang.Object ref = tab_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tab_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        tab_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = getDefaultInstance().getTab();
        onChanged();
        return this;
      }
      /**
       * <code>optional string tab = 1;</code>
       * @param value The bytes for tab to set.
       * @return This builder for chaining.
       */
      public Builder setTabBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        tab_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object title_ = "";
      /**
       * <code>optional string title = 2;</code>
       * @return Whether the title field is set.
       */
      public boolean hasTitle() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string title = 2;</code>
       * @return The title.
       */
      public java.lang.String getTitle() {
        java.lang.Object ref = title_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            title_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string title = 2;</code>
       * @return The bytes for title.
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        java.lang.Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string title = 2;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string title = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        bitField0_ = (bitField0_ & ~0x00000002);
        title_ = getDefaultInstance().getTitle();
        onChanged();
        return this;
      }
      /**
       * <code>optional string title = 2;</code>
       * @param value The bytes for title to set.
       * @return This builder for chaining.
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        title_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object desc_ = "";
      /**
       * <code>optional string desc = 3;</code>
       * @return Whether the desc field is set.
       */
      public boolean hasDesc() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string desc = 3;</code>
       * @return The desc.
       */
      public java.lang.String getDesc() {
        java.lang.Object ref = desc_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            desc_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string desc = 3;</code>
       * @return The bytes for desc.
       */
      public com.google.protobuf.ByteString
          getDescBytes() {
        java.lang.Object ref = desc_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          desc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string desc = 3;</code>
       * @param value The desc to set.
       * @return This builder for chaining.
       */
      public Builder setDesc(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        desc_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string desc = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDesc() {
        bitField0_ = (bitField0_ & ~0x00000004);
        desc_ = getDefaultInstance().getDesc();
        onChanged();
        return this;
      }
      /**
       * <code>optional string desc = 3;</code>
       * @param value The bytes for desc to set.
       * @return This builder for chaining.
       */
      public Builder setDescBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        desc_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object image_ = "";
      /**
       * <code>optional string image = 4;</code>
       * @return Whether the image field is set.
       */
      public boolean hasImage() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string image = 4;</code>
       * @return The image.
       */
      public java.lang.String getImage() {
        java.lang.Object ref = image_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            image_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string image = 4;</code>
       * @return The bytes for image.
       */
      public com.google.protobuf.ByteString
          getImageBytes() {
        java.lang.Object ref = image_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          image_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string image = 4;</code>
       * @param value The image to set.
       * @return This builder for chaining.
       */
      public Builder setImage(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        image_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string image = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearImage() {
        bitField0_ = (bitField0_ & ~0x00000008);
        image_ = getDefaultInstance().getImage();
        onChanged();
        return this;
      }
      /**
       * <code>optional string image = 4;</code>
       * @param value The bytes for image to set.
       * @return This builder for chaining.
       */
      public Builder setImageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        image_ = value;
        onChanged();
        return this;
      }

      private long startTimeMs_ ;
      /**
       * <code>optional int64 startTimeMs = 5;</code>
       * @return Whether the startTimeMs field is set.
       */
      @java.lang.Override
      public boolean hasStartTimeMs() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int64 startTimeMs = 5;</code>
       * @return The startTimeMs.
       */
      @java.lang.Override
      public long getStartTimeMs() {
        return startTimeMs_;
      }
      /**
       * <code>optional int64 startTimeMs = 5;</code>
       * @param value The startTimeMs to set.
       * @return This builder for chaining.
       */
      public Builder setStartTimeMs(long value) {
        bitField0_ |= 0x00000010;
        startTimeMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 startTimeMs = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartTimeMs() {
        bitField0_ = (bitField0_ & ~0x00000010);
        startTimeMs_ = 0L;
        onChanged();
        return this;
      }

      private long offTimeMs_ ;
      /**
       * <code>optional int64 offTimeMs = 6;</code>
       * @return Whether the offTimeMs field is set.
       */
      @java.lang.Override
      public boolean hasOffTimeMs() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional int64 offTimeMs = 6;</code>
       * @return The offTimeMs.
       */
      @java.lang.Override
      public long getOffTimeMs() {
        return offTimeMs_;
      }
      /**
       * <code>optional int64 offTimeMs = 6;</code>
       * @param value The offTimeMs to set.
       * @return This builder for chaining.
       */
      public Builder setOffTimeMs(long value) {
        bitField0_ |= 0x00000020;
        offTimeMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 offTimeMs = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearOffTimeMs() {
        bitField0_ = (bitField0_ & ~0x00000020);
        offTimeMs_ = 0L;
        onChanged();
        return this;
      }

      private int weight_ ;
      /**
       * <code>optional int32 weight = 7;</code>
       * @return Whether the weight field is set.
       */
      @java.lang.Override
      public boolean hasWeight() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional int32 weight = 7;</code>
       * @return The weight.
       */
      @java.lang.Override
      public int getWeight() {
        return weight_;
      }
      /**
       * <code>optional int32 weight = 7;</code>
       * @param value The weight to set.
       * @return This builder for chaining.
       */
      public Builder setWeight(int value) {
        bitField0_ |= 0x00000040;
        weight_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 weight = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeight() {
        bitField0_ = (bitField0_ & ~0x00000040);
        weight_ = 0;
        onChanged();
        return this;
      }

      private int activePush_ ;
      /**
       * <code>optional int32 activePush = 8;</code>
       * @return Whether the activePush field is set.
       */
      @java.lang.Override
      public boolean hasActivePush() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional int32 activePush = 8;</code>
       * @return The activePush.
       */
      @java.lang.Override
      public int getActivePush() {
        return activePush_;
      }
      /**
       * <code>optional int32 activePush = 8;</code>
       * @param value The activePush to set.
       * @return This builder for chaining.
       */
      public Builder setActivePush(int value) {
        bitField0_ |= 0x00000080;
        activePush_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 activePush = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearActivePush() {
        bitField0_ = (bitField0_ & ~0x00000080);
        activePush_ = 0;
        onChanged();
        return this;
      }

      private int displayTime_ ;
      /**
       * <code>optional int32 displayTime = 9;</code>
       * @return Whether the displayTime field is set.
       */
      @java.lang.Override
      public boolean hasDisplayTime() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional int32 displayTime = 9;</code>
       * @return The displayTime.
       */
      @java.lang.Override
      public int getDisplayTime() {
        return displayTime_;
      }
      /**
       * <code>optional int32 displayTime = 9;</code>
       * @param value The displayTime to set.
       * @return This builder for chaining.
       */
      public Builder setDisplayTime(int value) {
        bitField0_ |= 0x00000100;
        displayTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 displayTime = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearDisplayTime() {
        bitField0_ = (bitField0_ & ~0x00000100);
        displayTime_ = 0;
        onChanged();
        return this;
      }

      private int id_ ;
      /**
       * <code>optional int32 id = 10;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional int32 id = 10;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>optional int32 id = 10;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000200;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 id = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000200);
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.NoticeInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.NoticeInfo)
    private static final com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<NoticeInfo>
        PARSER = new com.google.protobuf.AbstractParser<NoticeInfo>() {
      @java.lang.Override
      public NoticeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new NoticeInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<NoticeInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NoticeInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.NoticeInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReceiveInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ReceiveInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 资源类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.CurrencyType type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <pre>
     * 资源类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.CurrencyType type = 1;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.CurrencyType getType();

    /**
     * <pre>
     * 资源数量
     * </pre>
     *
     * <code>optional int64 value = 3;</code>
     * @return Whether the value field is set.
     */
    boolean hasValue();
    /**
     * <pre>
     * 资源数量
     * </pre>
     *
     * <code>optional int64 value = 3;</code>
     * @return The value.
     */
    long getValue();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ReceiveInfo}
   */
  public static final class ReceiveInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ReceiveInfo)
      ReceiveInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReceiveInfo.newBuilder() to construct.
    private ReceiveInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReceiveInfo() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReceiveInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReceiveInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.CurrencyType value = com.yorha.proto.CommonEnum.CurrencyType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                type_ = rawValue;
              }
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              value_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_ReceiveInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_ReceiveInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo.class, com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     * 资源类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.CurrencyType type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 资源类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.CurrencyType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.CurrencyType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.CurrencyType result = com.yorha.proto.CommonEnum.CurrencyType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.CurrencyType.CT_None : result;
    }

    public static final int VALUE_FIELD_NUMBER = 3;
    private long value_;
    /**
     * <pre>
     * 资源数量
     * </pre>
     *
     * <code>optional int64 value = 3;</code>
     * @return Whether the value field is set.
     */
    @java.lang.Override
    public boolean hasValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 资源数量
     * </pre>
     *
     * <code>optional int64 value = 3;</code>
     * @return The value.
     */
    @java.lang.Override
    public long getValue() {
      return value_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(3, value_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, value_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo other = (com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasValue() != other.hasValue()) return false;
      if (hasValue()) {
        if (getValue()
            != other.getValue()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasValue()) {
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getValue());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ReceiveInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ReceiveInfo)
        com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_ReceiveInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_ReceiveInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo.class, com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        value_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_ReceiveInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo build() {
        com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo result = new com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.value_ = value_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasValue()) {
          setValue(other.getValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <pre>
       * 资源类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.CurrencyType type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 资源类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.CurrencyType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.CurrencyType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.CurrencyType result = com.yorha.proto.CommonEnum.CurrencyType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.CurrencyType.CT_None : result;
      }
      /**
       * <pre>
       * 资源类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.CurrencyType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.CurrencyType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 资源类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.CurrencyType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private long value_ ;
      /**
       * <pre>
       * 资源数量
       * </pre>
       *
       * <code>optional int64 value = 3;</code>
       * @return Whether the value field is set.
       */
      @java.lang.Override
      public boolean hasValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 资源数量
       * </pre>
       *
       * <code>optional int64 value = 3;</code>
       * @return The value.
       */
      @java.lang.Override
      public long getValue() {
        return value_;
      }
      /**
       * <pre>
       * 资源数量
       * </pre>
       *
       * <code>optional int64 value = 3;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(long value) {
        bitField0_ |= 0x00000002;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 资源数量
       * </pre>
       *
       * <code>optional int64 value = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        value_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ReceiveInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ReceiveInfo)
    private static final com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ReceiveInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReceiveInfo>() {
      @java.lang.Override
      public ReceiveInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReceiveInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReceiveInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReceiveInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.ReceiveInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_TakeProsperityReward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TakeProsperityReward_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 configId = 1;</code>
     * @return Whether the configId field is set.
     */
    boolean hasConfigId();
    /**
     * <code>optional int32 configId = 1;</code>
     * @return The configId.
     */
    int getConfigId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TakeProsperityReward_C2S}
   */
  public static final class Player_TakeProsperityReward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TakeProsperityReward_C2S)
      Player_TakeProsperityReward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TakeProsperityReward_C2S.newBuilder() to construct.
    private Player_TakeProsperityReward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TakeProsperityReward_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TakeProsperityReward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TakeProsperityReward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              configId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CONFIGID_FIELD_NUMBER = 1;
    private int configId_;
    /**
     * <code>optional int32 configId = 1;</code>
     * @return Whether the configId field is set.
     */
    @java.lang.Override
    public boolean hasConfigId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 configId = 1;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, configId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S other = (com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S) obj;

      if (hasConfigId() != other.hasConfigId()) return false;
      if (hasConfigId()) {
        if (getConfigId()
            != other.getConfigId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasConfigId()) {
        hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
        hash = (53 * hash) + getConfigId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TakeProsperityReward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TakeProsperityReward_C2S)
        com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        configId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S result = new com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.configId_ = configId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S.getDefaultInstance()) return this;
        if (other.hasConfigId()) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int configId_ ;
      /**
       * <code>optional int32 configId = 1;</code>
       * @return Whether the configId field is set.
       */
      @java.lang.Override
      public boolean hasConfigId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        bitField0_ |= 0x00000001;
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        configId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TakeProsperityReward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TakeProsperityReward_C2S)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TakeProsperityReward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_TakeProsperityReward_C2S>() {
      @java.lang.Override
      public Player_TakeProsperityReward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TakeProsperityReward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TakeProsperityReward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TakeProsperityReward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_TakeProsperityReward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TakeProsperityReward_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    boolean hasReward();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    com.yorha.proto.StructPB.YoAssetPackagePB getReward();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TakeProsperityReward_S2C}
   */
  public static final class Player_TakeProsperityReward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TakeProsperityReward_S2C)
      Player_TakeProsperityReward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TakeProsperityReward_S2C.newBuilder() to construct.
    private Player_TakeProsperityReward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TakeProsperityReward_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TakeProsperityReward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TakeProsperityReward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.YoAssetPackagePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = reward_.toBuilder();
              }
              reward_ = input.readMessage(com.yorha.proto.StructPB.YoAssetPackagePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(reward_);
                reward_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int REWARD_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    @java.lang.Override
    public boolean hasReward() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getReward());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getReward());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C other = (com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C) obj;

      if (hasReward() != other.hasReward()) return false;
      if (hasReward()) {
        if (!getReward()
            .equals(other.getReward())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReward()) {
        hash = (37 * hash) + REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getReward().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TakeProsperityReward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TakeProsperityReward_S2C)
        com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardBuilder_ == null) {
          reward_ = null;
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C result = new com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (rewardBuilder_ == null) {
            result.reward_ = reward_;
          } else {
            result.reward_ = rewardBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C.getDefaultInstance()) return this;
        if (other.hasReward()) {
          mergeReward(other.getReward());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> rewardBuilder_;
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return Whether the reward field is set.
       */
      public boolean hasReward() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return The reward.
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
        if (rewardBuilder_ == null) {
          return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        } else {
          return rewardBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          reward_ = value;
          onChanged();
        } else {
          rewardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(
          com.yorha.proto.StructPB.YoAssetPackagePB.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          reward_ = builderForValue.build();
          onChanged();
        } else {
          rewardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder mergeReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              reward_ != null &&
              reward_ != com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance()) {
            reward_ =
              com.yorha.proto.StructPB.YoAssetPackagePB.newBuilder(reward_).mergeFrom(value).buildPartial();
          } else {
            reward_ = value;
          }
          onChanged();
        } else {
          rewardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder clearReward() {
        if (rewardBuilder_ == null) {
          reward_ = null;
          onChanged();
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB.Builder getRewardBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRewardFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
        if (rewardBuilder_ != null) {
          return rewardBuilder_.getMessageOrBuilder();
        } else {
          return reward_ == null ?
              com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> 
          getRewardFieldBuilder() {
        if (rewardBuilder_ == null) {
          rewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder>(
                  getReward(),
                  getParentForChildren(),
                  isClean());
          reward_ = null;
        }
        return rewardBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TakeProsperityReward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TakeProsperityReward_S2C)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TakeProsperityReward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_TakeProsperityReward_S2C>() {
      @java.lang.Override
      public Player_TakeProsperityReward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TakeProsperityReward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TakeProsperityReward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TakeProsperityReward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CompleteQuestionnaireC2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CompleteQuestionnaireC2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 configId = 1;</code>
     * @return Whether the configId field is set.
     */
    boolean hasConfigId();
    /**
     * <code>optional int32 configId = 1;</code>
     * @return The configId.
     */
    int getConfigId();

    /**
     * <code>optional int32 answerId = 2;</code>
     * @return Whether the answerId field is set.
     */
    boolean hasAnswerId();
    /**
     * <code>optional int32 answerId = 2;</code>
     * @return The answerId.
     */
    int getAnswerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CompleteQuestionnaireC2S}
   */
  public static final class CompleteQuestionnaireC2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CompleteQuestionnaireC2S)
      CompleteQuestionnaireC2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CompleteQuestionnaireC2S.newBuilder() to construct.
    private CompleteQuestionnaireC2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CompleteQuestionnaireC2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CompleteQuestionnaireC2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CompleteQuestionnaireC2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              configId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              answerId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireC2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireC2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S.class, com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S.Builder.class);
    }

    private int bitField0_;
    public static final int CONFIGID_FIELD_NUMBER = 1;
    private int configId_;
    /**
     * <code>optional int32 configId = 1;</code>
     * @return Whether the configId field is set.
     */
    @java.lang.Override
    public boolean hasConfigId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 configId = 1;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    public static final int ANSWERID_FIELD_NUMBER = 2;
    private int answerId_;
    /**
     * <code>optional int32 answerId = 2;</code>
     * @return Whether the answerId field is set.
     */
    @java.lang.Override
    public boolean hasAnswerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 answerId = 2;</code>
     * @return The answerId.
     */
    @java.lang.Override
    public int getAnswerId() {
      return answerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, configId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, answerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, answerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S other = (com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S) obj;

      if (hasConfigId() != other.hasConfigId()) return false;
      if (hasConfigId()) {
        if (getConfigId()
            != other.getConfigId()) return false;
      }
      if (hasAnswerId() != other.hasAnswerId()) return false;
      if (hasAnswerId()) {
        if (getAnswerId()
            != other.getAnswerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasConfigId()) {
        hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
        hash = (53 * hash) + getConfigId();
      }
      if (hasAnswerId()) {
        hash = (37 * hash) + ANSWERID_FIELD_NUMBER;
        hash = (53 * hash) + getAnswerId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CompleteQuestionnaireC2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CompleteQuestionnaireC2S)
        com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireC2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireC2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S.class, com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        configId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        answerId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireC2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S build() {
        com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S result = new com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.configId_ = configId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.answerId_ = answerId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S.getDefaultInstance()) return this;
        if (other.hasConfigId()) {
          setConfigId(other.getConfigId());
        }
        if (other.hasAnswerId()) {
          setAnswerId(other.getAnswerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int configId_ ;
      /**
       * <code>optional int32 configId = 1;</code>
       * @return Whether the configId field is set.
       */
      @java.lang.Override
      public boolean hasConfigId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        bitField0_ |= 0x00000001;
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        configId_ = 0;
        onChanged();
        return this;
      }

      private int answerId_ ;
      /**
       * <code>optional int32 answerId = 2;</code>
       * @return Whether the answerId field is set.
       */
      @java.lang.Override
      public boolean hasAnswerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 answerId = 2;</code>
       * @return The answerId.
       */
      @java.lang.Override
      public int getAnswerId() {
        return answerId_;
      }
      /**
       * <code>optional int32 answerId = 2;</code>
       * @param value The answerId to set.
       * @return This builder for chaining.
       */
      public Builder setAnswerId(int value) {
        bitField0_ |= 0x00000002;
        answerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 answerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnswerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        answerId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CompleteQuestionnaireC2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CompleteQuestionnaireC2S)
    private static final com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CompleteQuestionnaireC2S>
        PARSER = new com.google.protobuf.AbstractParser<CompleteQuestionnaireC2S>() {
      @java.lang.Override
      public CompleteQuestionnaireC2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CompleteQuestionnaireC2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CompleteQuestionnaireC2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CompleteQuestionnaireC2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireC2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CompleteQuestionnaireS2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CompleteQuestionnaireS2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    boolean hasReward();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    com.yorha.proto.StructPB.YoAssetPackagePB getReward();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CompleteQuestionnaireS2C}
   */
  public static final class CompleteQuestionnaireS2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CompleteQuestionnaireS2C)
      CompleteQuestionnaireS2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CompleteQuestionnaireS2C.newBuilder() to construct.
    private CompleteQuestionnaireS2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CompleteQuestionnaireS2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CompleteQuestionnaireS2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CompleteQuestionnaireS2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.YoAssetPackagePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = reward_.toBuilder();
              }
              reward_ = input.readMessage(com.yorha.proto.StructPB.YoAssetPackagePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(reward_);
                reward_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireS2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireS2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C.class, com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C.Builder.class);
    }

    private int bitField0_;
    public static final int REWARD_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    @java.lang.Override
    public boolean hasReward() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getReward());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getReward());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C other = (com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C) obj;

      if (hasReward() != other.hasReward()) return false;
      if (hasReward()) {
        if (!getReward()
            .equals(other.getReward())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReward()) {
        hash = (37 * hash) + REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getReward().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CompleteQuestionnaireS2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CompleteQuestionnaireS2C)
        com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireS2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireS2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C.class, com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardBuilder_ == null) {
          reward_ = null;
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_CompleteQuestionnaireS2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C build() {
        com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C result = new com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (rewardBuilder_ == null) {
            result.reward_ = reward_;
          } else {
            result.reward_ = rewardBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C.getDefaultInstance()) return this;
        if (other.hasReward()) {
          mergeReward(other.getReward());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> rewardBuilder_;
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return Whether the reward field is set.
       */
      public boolean hasReward() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return The reward.
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
        if (rewardBuilder_ == null) {
          return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        } else {
          return rewardBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          reward_ = value;
          onChanged();
        } else {
          rewardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(
          com.yorha.proto.StructPB.YoAssetPackagePB.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          reward_ = builderForValue.build();
          onChanged();
        } else {
          rewardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder mergeReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              reward_ != null &&
              reward_ != com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance()) {
            reward_ =
              com.yorha.proto.StructPB.YoAssetPackagePB.newBuilder(reward_).mergeFrom(value).buildPartial();
          } else {
            reward_ = value;
          }
          onChanged();
        } else {
          rewardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder clearReward() {
        if (rewardBuilder_ == null) {
          reward_ = null;
          onChanged();
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB.Builder getRewardBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRewardFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
        if (rewardBuilder_ != null) {
          return rewardBuilder_.getMessageOrBuilder();
        } else {
          return reward_ == null ?
              com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> 
          getRewardFieldBuilder() {
        if (rewardBuilder_ == null) {
          rewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder>(
                  getReward(),
                  getParentForChildren(),
                  isClean());
          reward_ = null;
        }
        return rewardBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CompleteQuestionnaireS2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CompleteQuestionnaireS2C)
    private static final com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CompleteQuestionnaireS2C>
        PARSER = new com.google.protobuf.AbstractParser<CompleteQuestionnaireS2C>() {
      @java.lang.Override
      public CompleteQuestionnaireS2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CompleteQuestionnaireS2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CompleteQuestionnaireS2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CompleteQuestionnaireS2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.CompleteQuestionnaireS2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QuestionnaireExpiredC2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QuestionnaireExpiredC2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 configId = 1;</code>
     * @return Whether the configId field is set.
     */
    boolean hasConfigId();
    /**
     * <code>optional int32 configId = 1;</code>
     * @return The configId.
     */
    int getConfigId();

    /**
     * <code>optional bool isActive = 2;</code>
     * @return Whether the isActive field is set.
     */
    boolean hasIsActive();
    /**
     * <code>optional bool isActive = 2;</code>
     * @return The isActive.
     */
    boolean getIsActive();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QuestionnaireExpiredC2S}
   */
  public static final class QuestionnaireExpiredC2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QuestionnaireExpiredC2S)
      QuestionnaireExpiredC2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QuestionnaireExpiredC2S.newBuilder() to construct.
    private QuestionnaireExpiredC2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QuestionnaireExpiredC2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QuestionnaireExpiredC2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QuestionnaireExpiredC2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              configId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              isActive_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredC2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredC2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S.class, com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S.Builder.class);
    }

    private int bitField0_;
    public static final int CONFIGID_FIELD_NUMBER = 1;
    private int configId_;
    /**
     * <code>optional int32 configId = 1;</code>
     * @return Whether the configId field is set.
     */
    @java.lang.Override
    public boolean hasConfigId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 configId = 1;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    public static final int ISACTIVE_FIELD_NUMBER = 2;
    private boolean isActive_;
    /**
     * <code>optional bool isActive = 2;</code>
     * @return Whether the isActive field is set.
     */
    @java.lang.Override
    public boolean hasIsActive() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isActive = 2;</code>
     * @return The isActive.
     */
    @java.lang.Override
    public boolean getIsActive() {
      return isActive_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, configId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, isActive_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isActive_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S other = (com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S) obj;

      if (hasConfigId() != other.hasConfigId()) return false;
      if (hasConfigId()) {
        if (getConfigId()
            != other.getConfigId()) return false;
      }
      if (hasIsActive() != other.hasIsActive()) return false;
      if (hasIsActive()) {
        if (getIsActive()
            != other.getIsActive()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasConfigId()) {
        hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
        hash = (53 * hash) + getConfigId();
      }
      if (hasIsActive()) {
        hash = (37 * hash) + ISACTIVE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsActive());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QuestionnaireExpiredC2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QuestionnaireExpiredC2S)
        com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredC2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredC2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S.class, com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        configId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        isActive_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredC2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S build() {
        com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S result = new com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.configId_ = configId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isActive_ = isActive_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S.getDefaultInstance()) return this;
        if (other.hasConfigId()) {
          setConfigId(other.getConfigId());
        }
        if (other.hasIsActive()) {
          setIsActive(other.getIsActive());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int configId_ ;
      /**
       * <code>optional int32 configId = 1;</code>
       * @return Whether the configId field is set.
       */
      @java.lang.Override
      public boolean hasConfigId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        bitField0_ |= 0x00000001;
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 configId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        configId_ = 0;
        onChanged();
        return this;
      }

      private boolean isActive_ ;
      /**
       * <code>optional bool isActive = 2;</code>
       * @return Whether the isActive field is set.
       */
      @java.lang.Override
      public boolean hasIsActive() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool isActive = 2;</code>
       * @return The isActive.
       */
      @java.lang.Override
      public boolean getIsActive() {
        return isActive_;
      }
      /**
       * <code>optional bool isActive = 2;</code>
       * @param value The isActive to set.
       * @return This builder for chaining.
       */
      public Builder setIsActive(boolean value) {
        bitField0_ |= 0x00000002;
        isActive_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isActive = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsActive() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isActive_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QuestionnaireExpiredC2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QuestionnaireExpiredC2S)
    private static final com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QuestionnaireExpiredC2S>
        PARSER = new com.google.protobuf.AbstractParser<QuestionnaireExpiredC2S>() {
      @java.lang.Override
      public QuestionnaireExpiredC2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QuestionnaireExpiredC2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QuestionnaireExpiredC2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QuestionnaireExpiredC2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredC2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QuestionnaireExpiredS2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QuestionnaireExpiredS2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.QuestionnaireExpiredS2C}
   */
  public static final class QuestionnaireExpiredS2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QuestionnaireExpiredS2C)
      QuestionnaireExpiredS2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QuestionnaireExpiredS2C.newBuilder() to construct.
    private QuestionnaireExpiredS2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QuestionnaireExpiredS2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QuestionnaireExpiredS2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QuestionnaireExpiredS2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredS2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredS2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C.class, com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C other = (com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QuestionnaireExpiredS2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QuestionnaireExpiredS2C)
        com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredS2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredS2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C.class, com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_QuestionnaireExpiredS2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C build() {
        com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C result = new com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QuestionnaireExpiredS2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QuestionnaireExpiredS2C)
    private static final com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QuestionnaireExpiredS2C>
        PARSER = new com.google.protobuf.AbstractParser<QuestionnaireExpiredS2C>() {
      @java.lang.Override
      public QuestionnaireExpiredS2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QuestionnaireExpiredS2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QuestionnaireExpiredS2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QuestionnaireExpiredS2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.QuestionnaireExpiredS2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_UpdateClientStage_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_UpdateClientStage_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 groupId = 1;</code>
     * @return Whether the groupId field is set.
     */
    boolean hasGroupId();
    /**
     * <code>optional int32 groupId = 1;</code>
     * @return The groupId.
     */
    int getGroupId();

    /**
     * <code>optional int32 stage = 2;</code>
     * @return Whether the stage field is set.
     */
    boolean hasStage();
    /**
     * <code>optional int32 stage = 2;</code>
     * @return The stage.
     */
    int getStage();

    /**
     * <code>optional bool flag = 3;</code>
     * @return Whether the flag field is set.
     */
    boolean hasFlag();
    /**
     * <code>optional bool flag = 3;</code>
     * @return The flag.
     */
    boolean getFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_UpdateClientStage_C2S}
   */
  public static final class Player_UpdateClientStage_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_UpdateClientStage_C2S)
      Player_UpdateClientStage_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_UpdateClientStage_C2S.newBuilder() to construct.
    private Player_UpdateClientStage_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_UpdateClientStage_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_UpdateClientStage_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_UpdateClientStage_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              groupId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              stage_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              flag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int GROUPID_FIELD_NUMBER = 1;
    private int groupId_;
    /**
     * <code>optional int32 groupId = 1;</code>
     * @return Whether the groupId field is set.
     */
    @java.lang.Override
    public boolean hasGroupId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 groupId = 1;</code>
     * @return The groupId.
     */
    @java.lang.Override
    public int getGroupId() {
      return groupId_;
    }

    public static final int STAGE_FIELD_NUMBER = 2;
    private int stage_;
    /**
     * <code>optional int32 stage = 2;</code>
     * @return Whether the stage field is set.
     */
    @java.lang.Override
    public boolean hasStage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 stage = 2;</code>
     * @return The stage.
     */
    @java.lang.Override
    public int getStage() {
      return stage_;
    }

    public static final int FLAG_FIELD_NUMBER = 3;
    private boolean flag_;
    /**
     * <code>optional bool flag = 3;</code>
     * @return Whether the flag field is set.
     */
    @java.lang.Override
    public boolean hasFlag() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bool flag = 3;</code>
     * @return The flag.
     */
    @java.lang.Override
    public boolean getFlag() {
      return flag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, groupId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, stage_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(3, flag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, groupId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, stage_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, flag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S other = (com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S) obj;

      if (hasGroupId() != other.hasGroupId()) return false;
      if (hasGroupId()) {
        if (getGroupId()
            != other.getGroupId()) return false;
      }
      if (hasStage() != other.hasStage()) return false;
      if (hasStage()) {
        if (getStage()
            != other.getStage()) return false;
      }
      if (hasFlag() != other.hasFlag()) return false;
      if (hasFlag()) {
        if (getFlag()
            != other.getFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGroupId()) {
        hash = (37 * hash) + GROUPID_FIELD_NUMBER;
        hash = (53 * hash) + getGroupId();
      }
      if (hasStage()) {
        hash = (37 * hash) + STAGE_FIELD_NUMBER;
        hash = (53 * hash) + getStage();
      }
      if (hasFlag()) {
        hash = (37 * hash) + FLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_UpdateClientStage_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_UpdateClientStage_C2S)
        com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        groupId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        stage_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        flag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S result = new com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.groupId_ = groupId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.stage_ = stage_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.flag_ = flag_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S.getDefaultInstance()) return this;
        if (other.hasGroupId()) {
          setGroupId(other.getGroupId());
        }
        if (other.hasStage()) {
          setStage(other.getStage());
        }
        if (other.hasFlag()) {
          setFlag(other.getFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int groupId_ ;
      /**
       * <code>optional int32 groupId = 1;</code>
       * @return Whether the groupId field is set.
       */
      @java.lang.Override
      public boolean hasGroupId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 groupId = 1;</code>
       * @return The groupId.
       */
      @java.lang.Override
      public int getGroupId() {
        return groupId_;
      }
      /**
       * <code>optional int32 groupId = 1;</code>
       * @param value The groupId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupId(int value) {
        bitField0_ |= 0x00000001;
        groupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 groupId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        groupId_ = 0;
        onChanged();
        return this;
      }

      private int stage_ ;
      /**
       * <code>optional int32 stage = 2;</code>
       * @return Whether the stage field is set.
       */
      @java.lang.Override
      public boolean hasStage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 stage = 2;</code>
       * @return The stage.
       */
      @java.lang.Override
      public int getStage() {
        return stage_;
      }
      /**
       * <code>optional int32 stage = 2;</code>
       * @param value The stage to set.
       * @return This builder for chaining.
       */
      public Builder setStage(int value) {
        bitField0_ |= 0x00000002;
        stage_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 stage = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        stage_ = 0;
        onChanged();
        return this;
      }

      private boolean flag_ ;
      /**
       * <code>optional bool flag = 3;</code>
       * @return Whether the flag field is set.
       */
      @java.lang.Override
      public boolean hasFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool flag = 3;</code>
       * @return The flag.
       */
      @java.lang.Override
      public boolean getFlag() {
        return flag_;
      }
      /**
       * <code>optional bool flag = 3;</code>
       * @param value The flag to set.
       * @return This builder for chaining.
       */
      public Builder setFlag(boolean value) {
        bitField0_ |= 0x00000004;
        flag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool flag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        flag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_UpdateClientStage_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_UpdateClientStage_C2S)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_UpdateClientStage_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_UpdateClientStage_C2S>() {
      @java.lang.Override
      public Player_UpdateClientStage_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_UpdateClientStage_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_UpdateClientStage_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_UpdateClientStage_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_UpdateClientStage_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_UpdateClientStage_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_UpdateClientStage_S2C}
   */
  public static final class Player_UpdateClientStage_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_UpdateClientStage_S2C)
      Player_UpdateClientStage_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_UpdateClientStage_S2C.newBuilder() to construct.
    private Player_UpdateClientStage_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_UpdateClientStage_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_UpdateClientStage_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_UpdateClientStage_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C other = (com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_UpdateClientStage_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_UpdateClientStage_S2C)
        com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C result = new com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_UpdateClientStage_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_UpdateClientStage_S2C)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_UpdateClientStage_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_UpdateClientStage_S2C>() {
      @java.lang.Override
      public Player_UpdateClientStage_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_UpdateClientStage_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_UpdateClientStage_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_UpdateClientStage_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_UpdateClientStage_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CompleteMainTask_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CompleteMainTask_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 taskId = 1;</code>
     * @return Whether the taskId field is set.
     */
    boolean hasTaskId();
    /**
     * <code>optional int32 taskId = 1;</code>
     * @return The taskId.
     */
    int getTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CompleteMainTask_C2S}
   */
  public static final class Player_CompleteMainTask_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CompleteMainTask_C2S)
      Player_CompleteMainTask_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CompleteMainTask_C2S.newBuilder() to construct.
    private Player_CompleteMainTask_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CompleteMainTask_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CompleteMainTask_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CompleteMainTask_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              taskId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int TASKID_FIELD_NUMBER = 1;
    private int taskId_;
    /**
     * <code>optional int32 taskId = 1;</code>
     * @return Whether the taskId field is set.
     */
    @java.lang.Override
    public boolean hasTaskId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 taskId = 1;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public int getTaskId() {
      return taskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, taskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, taskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S other = (com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S) obj;

      if (hasTaskId() != other.hasTaskId()) return false;
      if (hasTaskId()) {
        if (getTaskId()
            != other.getTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTaskId()) {
        hash = (37 * hash) + TASKID_FIELD_NUMBER;
        hash = (53 * hash) + getTaskId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CompleteMainTask_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CompleteMainTask_C2S)
        com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S.class, com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        taskId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S result = new com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.taskId_ = taskId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S.getDefaultInstance()) return this;
        if (other.hasTaskId()) {
          setTaskId(other.getTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int taskId_ ;
      /**
       * <code>optional int32 taskId = 1;</code>
       * @return Whether the taskId field is set.
       */
      @java.lang.Override
      public boolean hasTaskId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 taskId = 1;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public int getTaskId() {
        return taskId_;
      }
      /**
       * <code>optional int32 taskId = 1;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(int value) {
        bitField0_ |= 0x00000001;
        taskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 taskId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        taskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CompleteMainTask_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CompleteMainTask_C2S)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CompleteMainTask_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_CompleteMainTask_C2S>() {
      @java.lang.Override
      public Player_CompleteMainTask_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CompleteMainTask_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CompleteMainTask_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CompleteMainTask_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CompleteMainTask_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CompleteMainTask_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool taskComplete = 1;</code>
     * @return Whether the taskComplete field is set.
     */
    boolean hasTaskComplete();
    /**
     * <code>optional bool taskComplete = 1;</code>
     * @return The taskComplete.
     */
    boolean getTaskComplete();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CompleteMainTask_S2C}
   */
  public static final class Player_CompleteMainTask_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CompleteMainTask_S2C)
      Player_CompleteMainTask_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CompleteMainTask_S2C.newBuilder() to construct.
    private Player_CompleteMainTask_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CompleteMainTask_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CompleteMainTask_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CompleteMainTask_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              taskComplete_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int TASKCOMPLETE_FIELD_NUMBER = 1;
    private boolean taskComplete_;
    /**
     * <code>optional bool taskComplete = 1;</code>
     * @return Whether the taskComplete field is set.
     */
    @java.lang.Override
    public boolean hasTaskComplete() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool taskComplete = 1;</code>
     * @return The taskComplete.
     */
    @java.lang.Override
    public boolean getTaskComplete() {
      return taskComplete_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, taskComplete_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, taskComplete_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C other = (com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C) obj;

      if (hasTaskComplete() != other.hasTaskComplete()) return false;
      if (hasTaskComplete()) {
        if (getTaskComplete()
            != other.getTaskComplete()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTaskComplete()) {
        hash = (37 * hash) + TASKCOMPLETE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getTaskComplete());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CompleteMainTask_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CompleteMainTask_S2C)
        com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C.class, com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        taskComplete_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C build() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C buildPartial() {
        com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C result = new com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.taskComplete_ = taskComplete_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C) {
          return mergeFrom((com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C other) {
        if (other == com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C.getDefaultInstance()) return this;
        if (other.hasTaskComplete()) {
          setTaskComplete(other.getTaskComplete());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean taskComplete_ ;
      /**
       * <code>optional bool taskComplete = 1;</code>
       * @return Whether the taskComplete field is set.
       */
      @java.lang.Override
      public boolean hasTaskComplete() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool taskComplete = 1;</code>
       * @return The taskComplete.
       */
      @java.lang.Override
      public boolean getTaskComplete() {
        return taskComplete_;
      }
      /**
       * <code>optional bool taskComplete = 1;</code>
       * @param value The taskComplete to set.
       * @return This builder for chaining.
       */
      public Builder setTaskComplete(boolean value) {
        bitField0_ |= 0x00000001;
        taskComplete_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool taskComplete = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskComplete() {
        bitField0_ = (bitField0_ & ~0x00000001);
        taskComplete_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CompleteMainTask_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CompleteMainTask_S2C)
    private static final com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C();
    }

    public static com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CompleteMainTask_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_CompleteMainTask_S2C>() {
      @java.lang.Override
      public Player_CompleteMainTask_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CompleteMainTask_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CompleteMainTask_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CompleteMainTask_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerInnerBuildLogic.Player_CompleteMainTask_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_NoticeBoard_NTF_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_NoticeBoard_NTF_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_NoticeInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_NoticeInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ReceiveInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ReceiveInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CompleteQuestionnaireC2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CompleteQuestionnaireC2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CompleteQuestionnaireS2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CompleteQuestionnaireS2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QuestionnaireExpiredC2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QuestionnaireExpiredC2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QuestionnaireExpiredS2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QuestionnaireExpiredS2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n5ss_proto/gen/player/cs/player_inner_bu" +
      "ild_logic.proto\022\017com.yorha.proto\032\"cs_pro" +
      "to/gen/common/structPB.proto\032%ss_proto/g" +
      "en/common/common_enum.proto\"8\n\037Player_Pr" +
      "omptForPlaneUnlock_C2S\022\025\n\rbattlePlaneId\030" +
      "\001 \001(\003\"!\n\037Player_PromptForPlaneUnlock_S2C" +
      "\"\033\n\031Player_GetNoticeBoard_C2S\"F\n\031Player_" +
      "GetNoticeBoard_S2C\022)\n\004info\030\001 \003(\0132\033.com.y" +
      "orha.proto.NoticeInfo\"C\n\026Player_NoticeBo" +
      "ard_NTF\022)\n\004info\030\001 \003(\0132\033.com.yorha.proto." +
      "NoticeInfo\"\262\001\n\nNoticeInfo\022\013\n\003tab\030\001 \001(\t\022\r" +
      "\n\005title\030\002 \001(\t\022\014\n\004desc\030\003 \001(\t\022\r\n\005image\030\004 \001" +
      "(\t\022\023\n\013startTimeMs\030\005 \001(\003\022\021\n\toffTimeMs\030\006 \001" +
      "(\003\022\016\n\006weight\030\007 \001(\005\022\022\n\nactivePush\030\010 \001(\005\022\023" +
      "\n\013displayTime\030\t \001(\005\022\n\n\002id\030\n \001(\005\"I\n\013Recei" +
      "veInfo\022+\n\004type\030\001 \001(\0162\035.com.yorha.proto.C" +
      "urrencyType\022\r\n\005value\030\003 \001(\003\"3\n\037Player_Tak" +
      "eProsperityReward_C2S\022\020\n\010configId\030\001 \001(\005\"" +
      "T\n\037Player_TakeProsperityReward_S2C\0221\n\006re" +
      "ward\030\001 \001(\0132!.com.yorha.proto.YoAssetPack" +
      "agePB\">\n\030CompleteQuestionnaireC2S\022\020\n\010con" +
      "figId\030\001 \001(\005\022\020\n\010answerId\030\002 \001(\005\"M\n\030Complet" +
      "eQuestionnaireS2C\0221\n\006reward\030\001 \001(\0132!.com." +
      "yorha.proto.YoAssetPackagePB\"=\n\027Question" +
      "naireExpiredC2S\022\020\n\010configId\030\001 \001(\005\022\020\n\010isA" +
      "ctive\030\002 \001(\010\"\031\n\027QuestionnaireExpiredS2C\"L" +
      "\n\034Player_UpdateClientStage_C2S\022\017\n\007groupI" +
      "d\030\001 \001(\005\022\r\n\005stage\030\002 \001(\005\022\014\n\004flag\030\003 \001(\010\"\036\n\034" +
      "Player_UpdateClientStage_S2C\"-\n\033Player_C" +
      "ompleteMainTask_C2S\022\016\n\006taskId\030\001 \001(\005\"3\n\033P" +
      "layer_CompleteMainTask_S2C\022\024\n\014taskComple" +
      "te\030\001 \001(\010B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_C2S_descriptor,
        new java.lang.String[] { "BattlePlaneId", });
    internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PromptForPlaneUnlock_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetNoticeBoard_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetNoticeBoard_S2C_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_Player_NoticeBoard_NTF_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_NoticeBoard_NTF_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_NoticeBoard_NTF_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_NoticeInfo_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_NoticeInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_NoticeInfo_descriptor,
        new java.lang.String[] { "Tab", "Title", "Desc", "Image", "StartTimeMs", "OffTimeMs", "Weight", "ActivePush", "DisplayTime", "Id", });
    internal_static_com_yorha_proto_ReceiveInfo_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_ReceiveInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ReceiveInfo_descriptor,
        new java.lang.String[] { "Type", "Value", });
    internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TakeProsperityReward_C2S_descriptor,
        new java.lang.String[] { "ConfigId", });
    internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TakeProsperityReward_S2C_descriptor,
        new java.lang.String[] { "Reward", });
    internal_static_com_yorha_proto_CompleteQuestionnaireC2S_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_CompleteQuestionnaireC2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CompleteQuestionnaireC2S_descriptor,
        new java.lang.String[] { "ConfigId", "AnswerId", });
    internal_static_com_yorha_proto_CompleteQuestionnaireS2C_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_CompleteQuestionnaireS2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CompleteQuestionnaireS2C_descriptor,
        new java.lang.String[] { "Reward", });
    internal_static_com_yorha_proto_QuestionnaireExpiredC2S_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_QuestionnaireExpiredC2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QuestionnaireExpiredC2S_descriptor,
        new java.lang.String[] { "ConfigId", "IsActive", });
    internal_static_com_yorha_proto_QuestionnaireExpiredS2C_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_QuestionnaireExpiredS2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QuestionnaireExpiredS2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_UpdateClientStage_C2S_descriptor,
        new java.lang.String[] { "GroupId", "Stage", "Flag", });
    internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_UpdateClientStage_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CompleteMainTask_C2S_descriptor,
        new java.lang.String[] { "TaskId", });
    internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CompleteMainTask_S2C_descriptor,
        new java.lang.String[] { "TaskComplete", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

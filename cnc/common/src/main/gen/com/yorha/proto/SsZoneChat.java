// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/zoneChat/ss_zoneChat.proto

package com.yorha.proto;

public final class SsZoneChat {
  private SsZoneChat() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FetchChatMsgAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchChatMsgAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 fromId = 1;</code>
     * @return Whether the fromId field is set.
     */
    boolean hasFromId();
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return The fromId.
     */
    long getFromId();

    /**
     * <code>optional int64 toId = 2;</code>
     * @return Whether the toId field is set.
     */
    boolean hasToId();
    /**
     * <code>optional int64 toId = 2;</code>
     * @return The toId.
     */
    long getToId();

    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return A list containing the shieldList.
     */
    java.util.List<java.lang.Long> getShieldListList();
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return The count of shieldList.
     */
    int getShieldListCount();
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @param index The index of the element to return.
     * @return The shieldList at the given index.
     */
    long getShieldList(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchChatMsgAsk}
   */
  public static final class FetchChatMsgAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchChatMsgAsk)
      FetchChatMsgAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchChatMsgAsk.newBuilder() to construct.
    private FetchChatMsgAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchChatMsgAsk() {
      shieldList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchChatMsgAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchChatMsgAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              fromId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toId_ = input.readInt64();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                shieldList_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              shieldList_.addLong(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                shieldList_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                shieldList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          shieldList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneChat.FetchChatMsgAsk.class, com.yorha.proto.SsZoneChat.FetchChatMsgAsk.Builder.class);
    }

    private int bitField0_;
    public static final int FROMID_FIELD_NUMBER = 1;
    private long fromId_;
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return Whether the fromId field is set.
     */
    @java.lang.Override
    public boolean hasFromId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 fromId = 1;</code>
     * @return The fromId.
     */
    @java.lang.Override
    public long getFromId() {
      return fromId_;
    }

    public static final int TOID_FIELD_NUMBER = 2;
    private long toId_;
    /**
     * <code>optional int64 toId = 2;</code>
     * @return Whether the toId field is set.
     */
    @java.lang.Override
    public boolean hasToId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 toId = 2;</code>
     * @return The toId.
     */
    @java.lang.Override
    public long getToId() {
      return toId_;
    }

    public static final int SHIELDLIST_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.LongList shieldList_;
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return A list containing the shieldList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getShieldListList() {
      return shieldList_;
    }
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @return The count of shieldList.
     */
    public int getShieldListCount() {
      return shieldList_.size();
    }
    /**
     * <code>repeated int64 shieldList = 3;</code>
     * @param index The index of the element to return.
     * @return The shieldList at the given index.
     */
    public long getShieldList(int index) {
      return shieldList_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, fromId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toId_);
      }
      for (int i = 0; i < shieldList_.size(); i++) {
        output.writeInt64(3, shieldList_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, fromId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < shieldList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(shieldList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getShieldListList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneChat.FetchChatMsgAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneChat.FetchChatMsgAsk other = (com.yorha.proto.SsZoneChat.FetchChatMsgAsk) obj;

      if (hasFromId() != other.hasFromId()) return false;
      if (hasFromId()) {
        if (getFromId()
            != other.getFromId()) return false;
      }
      if (hasToId() != other.hasToId()) return false;
      if (hasToId()) {
        if (getToId()
            != other.getToId()) return false;
      }
      if (!getShieldListList()
          .equals(other.getShieldListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasFromId()) {
        hash = (37 * hash) + FROMID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getFromId());
      }
      if (hasToId()) {
        hash = (37 * hash) + TOID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToId());
      }
      if (getShieldListCount() > 0) {
        hash = (37 * hash) + SHIELDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getShieldListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneChat.FetchChatMsgAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchChatMsgAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchChatMsgAsk)
        com.yorha.proto.SsZoneChat.FetchChatMsgAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneChat.FetchChatMsgAsk.class, com.yorha.proto.SsZoneChat.FetchChatMsgAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneChat.FetchChatMsgAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fromId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        toId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        shieldList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.FetchChatMsgAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneChat.FetchChatMsgAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.FetchChatMsgAsk build() {
        com.yorha.proto.SsZoneChat.FetchChatMsgAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.FetchChatMsgAsk buildPartial() {
        com.yorha.proto.SsZoneChat.FetchChatMsgAsk result = new com.yorha.proto.SsZoneChat.FetchChatMsgAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.fromId_ = fromId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toId_ = toId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          shieldList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.shieldList_ = shieldList_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneChat.FetchChatMsgAsk) {
          return mergeFrom((com.yorha.proto.SsZoneChat.FetchChatMsgAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneChat.FetchChatMsgAsk other) {
        if (other == com.yorha.proto.SsZoneChat.FetchChatMsgAsk.getDefaultInstance()) return this;
        if (other.hasFromId()) {
          setFromId(other.getFromId());
        }
        if (other.hasToId()) {
          setToId(other.getToId());
        }
        if (!other.shieldList_.isEmpty()) {
          if (shieldList_.isEmpty()) {
            shieldList_ = other.shieldList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureShieldListIsMutable();
            shieldList_.addAll(other.shieldList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneChat.FetchChatMsgAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneChat.FetchChatMsgAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long fromId_ ;
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return Whether the fromId field is set.
       */
      @java.lang.Override
      public boolean hasFromId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return The fromId.
       */
      @java.lang.Override
      public long getFromId() {
        return fromId_;
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @param value The fromId to set.
       * @return This builder for chaining.
       */
      public Builder setFromId(long value) {
        bitField0_ |= 0x00000001;
        fromId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 fromId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        fromId_ = 0L;
        onChanged();
        return this;
      }

      private long toId_ ;
      /**
       * <code>optional int64 toId = 2;</code>
       * @return Whether the toId field is set.
       */
      @java.lang.Override
      public boolean hasToId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @return The toId.
       */
      @java.lang.Override
      public long getToId() {
        return toId_;
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @param value The toId to set.
       * @return This builder for chaining.
       */
      public Builder setToId(long value) {
        bitField0_ |= 0x00000002;
        toId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 toId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList shieldList_ = emptyLongList();
      private void ensureShieldListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          shieldList_ = mutableCopy(shieldList_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return A list containing the shieldList.
       */
      public java.util.List<java.lang.Long>
          getShieldListList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(shieldList_) : shieldList_;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return The count of shieldList.
       */
      public int getShieldListCount() {
        return shieldList_.size();
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param index The index of the element to return.
       * @return The shieldList at the given index.
       */
      public long getShieldList(int index) {
        return shieldList_.getLong(index);
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param index The index to set the value at.
       * @param value The shieldList to set.
       * @return This builder for chaining.
       */
      public Builder setShieldList(
          int index, long value) {
        ensureShieldListIsMutable();
        shieldList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param value The shieldList to add.
       * @return This builder for chaining.
       */
      public Builder addShieldList(long value) {
        ensureShieldListIsMutable();
        shieldList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @param values The shieldList to add.
       * @return This builder for chaining.
       */
      public Builder addAllShieldList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureShieldListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, shieldList_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 shieldList = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearShieldList() {
        shieldList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchChatMsgAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchChatMsgAsk)
    private static final com.yorha.proto.SsZoneChat.FetchChatMsgAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneChat.FetchChatMsgAsk();
    }

    public static com.yorha.proto.SsZoneChat.FetchChatMsgAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchChatMsgAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchChatMsgAsk>() {
      @java.lang.Override
      public FetchChatMsgAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchChatMsgAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchChatMsgAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchChatMsgAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneChat.FetchChatMsgAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchChatMsgAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchChatMsgAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    java.util.List<com.yorha.proto.CommonMsg.ChatMessage> 
        getChatMsgsList();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index);
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    int getChatMsgsCount();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
        getChatMsgsOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchChatMsgAns}
   */
  public static final class FetchChatMsgAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchChatMsgAns)
      FetchChatMsgAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchChatMsgAns.newBuilder() to construct.
    private FetchChatMsgAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchChatMsgAns() {
      chatMsgs_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchChatMsgAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchChatMsgAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                chatMsgs_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ChatMessage>();
                mutable_bitField0_ |= 0x00000001;
              }
              chatMsgs_.add(
                  input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          chatMsgs_ = java.util.Collections.unmodifiableList(chatMsgs_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneChat.FetchChatMsgAns.class, com.yorha.proto.SsZoneChat.FetchChatMsgAns.Builder.class);
    }

    public static final int CHATMSGS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.CommonMsg.ChatMessage> chatMsgs_;
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.CommonMsg.ChatMessage> getChatMsgsList() {
      return chatMsgs_;
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
        getChatMsgsOrBuilderList() {
      return chatMsgs_;
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public int getChatMsgsCount() {
      return chatMsgs_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index) {
      return chatMsgs_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
        int index) {
      return chatMsgs_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < chatMsgs_.size(); i++) {
        output.writeMessage(1, chatMsgs_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < chatMsgs_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, chatMsgs_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneChat.FetchChatMsgAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneChat.FetchChatMsgAns other = (com.yorha.proto.SsZoneChat.FetchChatMsgAns) obj;

      if (!getChatMsgsList()
          .equals(other.getChatMsgsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChatMsgsCount() > 0) {
        hash = (37 * hash) + CHATMSGS_FIELD_NUMBER;
        hash = (53 * hash) + getChatMsgsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneChat.FetchChatMsgAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchChatMsgAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchChatMsgAns)
        com.yorha.proto.SsZoneChat.FetchChatMsgAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneChat.FetchChatMsgAns.class, com.yorha.proto.SsZoneChat.FetchChatMsgAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneChat.FetchChatMsgAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMsgsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatMsgsBuilder_ == null) {
          chatMsgs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          chatMsgsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_FetchChatMsgAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.FetchChatMsgAns getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneChat.FetchChatMsgAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.FetchChatMsgAns build() {
        com.yorha.proto.SsZoneChat.FetchChatMsgAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.FetchChatMsgAns buildPartial() {
        com.yorha.proto.SsZoneChat.FetchChatMsgAns result = new com.yorha.proto.SsZoneChat.FetchChatMsgAns(this);
        int from_bitField0_ = bitField0_;
        if (chatMsgsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            chatMsgs_ = java.util.Collections.unmodifiableList(chatMsgs_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.chatMsgs_ = chatMsgs_;
        } else {
          result.chatMsgs_ = chatMsgsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneChat.FetchChatMsgAns) {
          return mergeFrom((com.yorha.proto.SsZoneChat.FetchChatMsgAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneChat.FetchChatMsgAns other) {
        if (other == com.yorha.proto.SsZoneChat.FetchChatMsgAns.getDefaultInstance()) return this;
        if (chatMsgsBuilder_ == null) {
          if (!other.chatMsgs_.isEmpty()) {
            if (chatMsgs_.isEmpty()) {
              chatMsgs_ = other.chatMsgs_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureChatMsgsIsMutable();
              chatMsgs_.addAll(other.chatMsgs_);
            }
            onChanged();
          }
        } else {
          if (!other.chatMsgs_.isEmpty()) {
            if (chatMsgsBuilder_.isEmpty()) {
              chatMsgsBuilder_.dispose();
              chatMsgsBuilder_ = null;
              chatMsgs_ = other.chatMsgs_;
              bitField0_ = (bitField0_ & ~0x00000001);
              chatMsgsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChatMsgsFieldBuilder() : null;
            } else {
              chatMsgsBuilder_.addAllMessages(other.chatMsgs_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneChat.FetchChatMsgAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneChat.FetchChatMsgAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.CommonMsg.ChatMessage> chatMsgs_ =
        java.util.Collections.emptyList();
      private void ensureChatMsgsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          chatMsgs_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ChatMessage>(chatMsgs_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMsgsBuilder_;

      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ChatMessage> getChatMsgsList() {
        if (chatMsgsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(chatMsgs_);
        } else {
          return chatMsgsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public int getChatMsgsCount() {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.size();
        } else {
          return chatMsgsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMsgs(int index) {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.get(index);
        } else {
          return chatMsgsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder setChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.set(index, value);
          onChanged();
        } else {
          chatMsgsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder setChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.set(index, builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.add(value);
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMsgsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatMsgsIsMutable();
          chatMsgs_.add(index, value);
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.add(builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addChatMsgs(
          int index, com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.add(index, builderForValue.build());
          onChanged();
        } else {
          chatMsgsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder addAllChatMsgs(
          java.lang.Iterable<? extends com.yorha.proto.CommonMsg.ChatMessage> values) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, chatMsgs_);
          onChanged();
        } else {
          chatMsgsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder clearChatMsgs() {
        if (chatMsgsBuilder_ == null) {
          chatMsgs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          chatMsgsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public Builder removeChatMsgs(int index) {
        if (chatMsgsBuilder_ == null) {
          ensureChatMsgsIsMutable();
          chatMsgs_.remove(index);
          onChanged();
        } else {
          chatMsgsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMsgsBuilder(
          int index) {
        return getChatMsgsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMsgsOrBuilder(
          int index) {
        if (chatMsgsBuilder_ == null) {
          return chatMsgs_.get(index);  } else {
          return chatMsgsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
           getChatMsgsOrBuilderList() {
        if (chatMsgsBuilder_ != null) {
          return chatMsgsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(chatMsgs_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder addChatMsgsBuilder() {
        return getChatMsgsFieldBuilder().addBuilder(
            com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder addChatMsgsBuilder(
          int index) {
        return getChatMsgsFieldBuilder().addBuilder(
            index, com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.ChatMessage chatMsgs = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ChatMessage.Builder> 
           getChatMsgsBuilderList() {
        return getChatMsgsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMsgsFieldBuilder() {
        if (chatMsgsBuilder_ == null) {
          chatMsgsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  chatMsgs_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          chatMsgs_ = null;
        }
        return chatMsgsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchChatMsgAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchChatMsgAns)
    private static final com.yorha.proto.SsZoneChat.FetchChatMsgAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneChat.FetchChatMsgAns();
    }

    public static com.yorha.proto.SsZoneChat.FetchChatMsgAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchChatMsgAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchChatMsgAns>() {
      @java.lang.Override
      public FetchChatMsgAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchChatMsgAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchChatMsgAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchChatMsgAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneChat.FetchChatMsgAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendChatMsgAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendChatMsgAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return Whether the chatMessage field is set.
     */
    boolean hasChatMessage();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return The chatMessage.
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMessage();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder();

    /**
     * <pre>
     * 被对方屏蔽列表
     * </pre>
     *
     * <code>repeated int64 beShieldList = 2;</code>
     * @return A list containing the beShieldList.
     */
    java.util.List<java.lang.Long> getBeShieldListList();
    /**
     * <pre>
     * 被对方屏蔽列表
     * </pre>
     *
     * <code>repeated int64 beShieldList = 2;</code>
     * @return The count of beShieldList.
     */
    int getBeShieldListCount();
    /**
     * <pre>
     * 被对方屏蔽列表
     * </pre>
     *
     * <code>repeated int64 beShieldList = 2;</code>
     * @param index The index of the element to return.
     * @return The beShieldList at the given index.
     */
    long getBeShieldList(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendChatMsgAsk}
   */
  public static final class SendChatMsgAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendChatMsgAsk)
      SendChatMsgAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendChatMsgAsk.newBuilder() to construct.
    private SendChatMsgAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendChatMsgAsk() {
      beShieldList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendChatMsgAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendChatMsgAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ChatMessage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = chatMessage_.toBuilder();
              }
              chatMessage_ = input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatMessage_);
                chatMessage_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                beShieldList_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              beShieldList_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                beShieldList_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                beShieldList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          beShieldList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneChat.SendChatMsgAsk.class, com.yorha.proto.SsZoneChat.SendChatMsgAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHATMESSAGE_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return Whether the chatMessage field is set.
     */
    @java.lang.Override
    public boolean hasChatMessage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return The chatMessage.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }

    public static final int BESHIELDLIST_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList beShieldList_;
    /**
     * <pre>
     * 被对方屏蔽列表
     * </pre>
     *
     * <code>repeated int64 beShieldList = 2;</code>
     * @return A list containing the beShieldList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getBeShieldListList() {
      return beShieldList_;
    }
    /**
     * <pre>
     * 被对方屏蔽列表
     * </pre>
     *
     * <code>repeated int64 beShieldList = 2;</code>
     * @return The count of beShieldList.
     */
    public int getBeShieldListCount() {
      return beShieldList_.size();
    }
    /**
     * <pre>
     * 被对方屏蔽列表
     * </pre>
     *
     * <code>repeated int64 beShieldList = 2;</code>
     * @param index The index of the element to return.
     * @return The beShieldList at the given index.
     */
    public long getBeShieldList(int index) {
      return beShieldList_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getChatMessage());
      }
      for (int i = 0; i < beShieldList_.size(); i++) {
        output.writeInt64(2, beShieldList_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getChatMessage());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < beShieldList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(beShieldList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getBeShieldListList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneChat.SendChatMsgAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneChat.SendChatMsgAsk other = (com.yorha.proto.SsZoneChat.SendChatMsgAsk) obj;

      if (hasChatMessage() != other.hasChatMessage()) return false;
      if (hasChatMessage()) {
        if (!getChatMessage()
            .equals(other.getChatMessage())) return false;
      }
      if (!getBeShieldListList()
          .equals(other.getBeShieldListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatMessage()) {
        hash = (37 * hash) + CHATMESSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getChatMessage().hashCode();
      }
      if (getBeShieldListCount() > 0) {
        hash = (37 * hash) + BESHIELDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getBeShieldListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneChat.SendChatMsgAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendChatMsgAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendChatMsgAsk)
        com.yorha.proto.SsZoneChat.SendChatMsgAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneChat.SendChatMsgAsk.class, com.yorha.proto.SsZoneChat.SendChatMsgAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneChat.SendChatMsgAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMessageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        beShieldList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.SendChatMsgAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneChat.SendChatMsgAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.SendChatMsgAsk build() {
        com.yorha.proto.SsZoneChat.SendChatMsgAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.SendChatMsgAsk buildPartial() {
        com.yorha.proto.SsZoneChat.SendChatMsgAsk result = new com.yorha.proto.SsZoneChat.SendChatMsgAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (chatMessageBuilder_ == null) {
            result.chatMessage_ = chatMessage_;
          } else {
            result.chatMessage_ = chatMessageBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          beShieldList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.beShieldList_ = beShieldList_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneChat.SendChatMsgAsk) {
          return mergeFrom((com.yorha.proto.SsZoneChat.SendChatMsgAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneChat.SendChatMsgAsk other) {
        if (other == com.yorha.proto.SsZoneChat.SendChatMsgAsk.getDefaultInstance()) return this;
        if (other.hasChatMessage()) {
          mergeChatMessage(other.getChatMessage());
        }
        if (!other.beShieldList_.isEmpty()) {
          if (beShieldList_.isEmpty()) {
            beShieldList_ = other.beShieldList_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureBeShieldListIsMutable();
            beShieldList_.addAll(other.beShieldList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneChat.SendChatMsgAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneChat.SendChatMsgAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMessageBuilder_;
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       * @return Whether the chatMessage field is set.
       */
      public boolean hasChatMessage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       * @return The chatMessage.
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
        if (chatMessageBuilder_ == null) {
          return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        } else {
          return chatMessageBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder setChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatMessage_ = value;
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder setChatMessage(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = builderForValue.build();
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder mergeChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              chatMessage_ != null &&
              chatMessage_ != com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance()) {
            chatMessage_ =
              com.yorha.proto.CommonMsg.ChatMessage.newBuilder(chatMessage_).mergeFrom(value).buildPartial();
          } else {
            chatMessage_ = value;
          }
          onChanged();
        } else {
          chatMessageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder clearChatMessage() {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
          onChanged();
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMessageBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatMessageFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
        if (chatMessageBuilder_ != null) {
          return chatMessageBuilder_.getMessageOrBuilder();
        } else {
          return chatMessage_ == null ?
              com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMessageFieldBuilder() {
        if (chatMessageBuilder_ == null) {
          chatMessageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  getChatMessage(),
                  getParentForChildren(),
                  isClean());
          chatMessage_ = null;
        }
        return chatMessageBuilder_;
      }

      private com.google.protobuf.Internal.LongList beShieldList_ = emptyLongList();
      private void ensureBeShieldListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          beShieldList_ = mutableCopy(beShieldList_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       * 被对方屏蔽列表
       * </pre>
       *
       * <code>repeated int64 beShieldList = 2;</code>
       * @return A list containing the beShieldList.
       */
      public java.util.List<java.lang.Long>
          getBeShieldListList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(beShieldList_) : beShieldList_;
      }
      /**
       * <pre>
       * 被对方屏蔽列表
       * </pre>
       *
       * <code>repeated int64 beShieldList = 2;</code>
       * @return The count of beShieldList.
       */
      public int getBeShieldListCount() {
        return beShieldList_.size();
      }
      /**
       * <pre>
       * 被对方屏蔽列表
       * </pre>
       *
       * <code>repeated int64 beShieldList = 2;</code>
       * @param index The index of the element to return.
       * @return The beShieldList at the given index.
       */
      public long getBeShieldList(int index) {
        return beShieldList_.getLong(index);
      }
      /**
       * <pre>
       * 被对方屏蔽列表
       * </pre>
       *
       * <code>repeated int64 beShieldList = 2;</code>
       * @param index The index to set the value at.
       * @param value The beShieldList to set.
       * @return This builder for chaining.
       */
      public Builder setBeShieldList(
          int index, long value) {
        ensureBeShieldListIsMutable();
        beShieldList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被对方屏蔽列表
       * </pre>
       *
       * <code>repeated int64 beShieldList = 2;</code>
       * @param value The beShieldList to add.
       * @return This builder for chaining.
       */
      public Builder addBeShieldList(long value) {
        ensureBeShieldListIsMutable();
        beShieldList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被对方屏蔽列表
       * </pre>
       *
       * <code>repeated int64 beShieldList = 2;</code>
       * @param values The beShieldList to add.
       * @return This builder for chaining.
       */
      public Builder addAllBeShieldList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureBeShieldListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, beShieldList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被对方屏蔽列表
       * </pre>
       *
       * <code>repeated int64 beShieldList = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeShieldList() {
        beShieldList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendChatMsgAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendChatMsgAsk)
    private static final com.yorha.proto.SsZoneChat.SendChatMsgAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneChat.SendChatMsgAsk();
    }

    public static com.yorha.proto.SsZoneChat.SendChatMsgAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendChatMsgAsk>
        PARSER = new com.google.protobuf.AbstractParser<SendChatMsgAsk>() {
      @java.lang.Override
      public SendChatMsgAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendChatMsgAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendChatMsgAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendChatMsgAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneChat.SendChatMsgAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendChatMsgAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendChatMsgAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return Whether the messageId field is set.
     */
    boolean hasMessageId();
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return The messageId.
     */
    long getMessageId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendChatMsgAns}
   */
  public static final class SendChatMsgAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendChatMsgAns)
      SendChatMsgAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendChatMsgAns.newBuilder() to construct.
    private SendChatMsgAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendChatMsgAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendChatMsgAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendChatMsgAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              messageId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneChat.SendChatMsgAns.class, com.yorha.proto.SsZoneChat.SendChatMsgAns.Builder.class);
    }

    private int bitField0_;
    public static final int MESSAGEID_FIELD_NUMBER = 1;
    private long messageId_;
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return Whether the messageId field is set.
     */
    @java.lang.Override
    public boolean hasMessageId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 这条消息的自增id
     * </pre>
     *
     * <code>optional int64 messageId = 1;</code>
     * @return The messageId.
     */
    @java.lang.Override
    public long getMessageId() {
      return messageId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, messageId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, messageId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneChat.SendChatMsgAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneChat.SendChatMsgAns other = (com.yorha.proto.SsZoneChat.SendChatMsgAns) obj;

      if (hasMessageId() != other.hasMessageId()) return false;
      if (hasMessageId()) {
        if (getMessageId()
            != other.getMessageId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMessageId()) {
        hash = (37 * hash) + MESSAGEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMessageId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.SendChatMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneChat.SendChatMsgAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendChatMsgAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendChatMsgAns)
        com.yorha.proto.SsZoneChat.SendChatMsgAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneChat.SendChatMsgAns.class, com.yorha.proto.SsZoneChat.SendChatMsgAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneChat.SendChatMsgAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        messageId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_SendChatMsgAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.SendChatMsgAns getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneChat.SendChatMsgAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.SendChatMsgAns build() {
        com.yorha.proto.SsZoneChat.SendChatMsgAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.SendChatMsgAns buildPartial() {
        com.yorha.proto.SsZoneChat.SendChatMsgAns result = new com.yorha.proto.SsZoneChat.SendChatMsgAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.messageId_ = messageId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneChat.SendChatMsgAns) {
          return mergeFrom((com.yorha.proto.SsZoneChat.SendChatMsgAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneChat.SendChatMsgAns other) {
        if (other == com.yorha.proto.SsZoneChat.SendChatMsgAns.getDefaultInstance()) return this;
        if (other.hasMessageId()) {
          setMessageId(other.getMessageId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneChat.SendChatMsgAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneChat.SendChatMsgAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long messageId_ ;
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return Whether the messageId field is set.
       */
      @java.lang.Override
      public boolean hasMessageId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return The messageId.
       */
      @java.lang.Override
      public long getMessageId() {
        return messageId_;
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @param value The messageId to set.
       * @return This builder for chaining.
       */
      public Builder setMessageId(long value) {
        bitField0_ |= 0x00000001;
        messageId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 这条消息的自增id
       * </pre>
       *
       * <code>optional int64 messageId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMessageId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        messageId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendChatMsgAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendChatMsgAns)
    private static final com.yorha.proto.SsZoneChat.SendChatMsgAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneChat.SendChatMsgAns();
    }

    public static com.yorha.proto.SsZoneChat.SendChatMsgAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendChatMsgAns>
        PARSER = new com.google.protobuf.AbstractParser<SendChatMsgAns>() {
      @java.lang.Override
      public SendChatMsgAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendChatMsgAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendChatMsgAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendChatMsgAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneChat.SendChatMsgAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IdIpSendChatMsgAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.IdIpSendChatMsgAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return Whether the chatMessage field is set.
     */
    boolean hasChatMessage();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return The chatMessage.
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMessage();
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder();

    /**
     * <pre>
     * 循环次数
     * </pre>
     *
     * <code>optional int32 loopTimes = 2;</code>
     * @return Whether the loopTimes field is set.
     */
    boolean hasLoopTimes();
    /**
     * <pre>
     * 循环次数
     * </pre>
     *
     * <code>optional int32 loopTimes = 2;</code>
     * @return The loopTimes.
     */
    int getLoopTimes();

    /**
     * <pre>
     * 循环间隔(分钟)
     * </pre>
     *
     * <code>optional int32 loopIntervalMiniute = 3;</code>
     * @return Whether the loopIntervalMiniute field is set.
     */
    boolean hasLoopIntervalMiniute();
    /**
     * <pre>
     * 循环间隔(分钟)
     * </pre>
     *
     * <code>optional int32 loopIntervalMiniute = 3;</code>
     * @return The loopIntervalMiniute.
     */
    int getLoopIntervalMiniute();
  }
  /**
   * Protobuf type {@code com.yorha.proto.IdIpSendChatMsgAsk}
   */
  public static final class IdIpSendChatMsgAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.IdIpSendChatMsgAsk)
      IdIpSendChatMsgAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IdIpSendChatMsgAsk.newBuilder() to construct.
    private IdIpSendChatMsgAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IdIpSendChatMsgAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IdIpSendChatMsgAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IdIpSendChatMsgAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ChatMessage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = chatMessage_.toBuilder();
              }
              chatMessage_ = input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatMessage_);
                chatMessage_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              loopTimes_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              loopIntervalMiniute_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk.class, com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHATMESSAGE_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return Whether the chatMessage field is set.
     */
    @java.lang.Override
    public boolean hasChatMessage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     * @return The chatMessage.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }
    /**
     * <pre>
     * 消息内容
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }

    public static final int LOOPTIMES_FIELD_NUMBER = 2;
    private int loopTimes_;
    /**
     * <pre>
     * 循环次数
     * </pre>
     *
     * <code>optional int32 loopTimes = 2;</code>
     * @return Whether the loopTimes field is set.
     */
    @java.lang.Override
    public boolean hasLoopTimes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 循环次数
     * </pre>
     *
     * <code>optional int32 loopTimes = 2;</code>
     * @return The loopTimes.
     */
    @java.lang.Override
    public int getLoopTimes() {
      return loopTimes_;
    }

    public static final int LOOPINTERVALMINIUTE_FIELD_NUMBER = 3;
    private int loopIntervalMiniute_;
    /**
     * <pre>
     * 循环间隔(分钟)
     * </pre>
     *
     * <code>optional int32 loopIntervalMiniute = 3;</code>
     * @return Whether the loopIntervalMiniute field is set.
     */
    @java.lang.Override
    public boolean hasLoopIntervalMiniute() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 循环间隔(分钟)
     * </pre>
     *
     * <code>optional int32 loopIntervalMiniute = 3;</code>
     * @return The loopIntervalMiniute.
     */
    @java.lang.Override
    public int getLoopIntervalMiniute() {
      return loopIntervalMiniute_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getChatMessage());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, loopTimes_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, loopIntervalMiniute_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getChatMessage());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, loopTimes_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, loopIntervalMiniute_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk other = (com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk) obj;

      if (hasChatMessage() != other.hasChatMessage()) return false;
      if (hasChatMessage()) {
        if (!getChatMessage()
            .equals(other.getChatMessage())) return false;
      }
      if (hasLoopTimes() != other.hasLoopTimes()) return false;
      if (hasLoopTimes()) {
        if (getLoopTimes()
            != other.getLoopTimes()) return false;
      }
      if (hasLoopIntervalMiniute() != other.hasLoopIntervalMiniute()) return false;
      if (hasLoopIntervalMiniute()) {
        if (getLoopIntervalMiniute()
            != other.getLoopIntervalMiniute()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatMessage()) {
        hash = (37 * hash) + CHATMESSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getChatMessage().hashCode();
      }
      if (hasLoopTimes()) {
        hash = (37 * hash) + LOOPTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getLoopTimes();
      }
      if (hasLoopIntervalMiniute()) {
        hash = (37 * hash) + LOOPINTERVALMINIUTE_FIELD_NUMBER;
        hash = (53 * hash) + getLoopIntervalMiniute();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.IdIpSendChatMsgAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.IdIpSendChatMsgAsk)
        com.yorha.proto.SsZoneChat.IdIpSendChatMsgAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk.class, com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMessageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        loopTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        loopIntervalMiniute_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk build() {
        com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk buildPartial() {
        com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk result = new com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (chatMessageBuilder_ == null) {
            result.chatMessage_ = chatMessage_;
          } else {
            result.chatMessage_ = chatMessageBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.loopTimes_ = loopTimes_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.loopIntervalMiniute_ = loopIntervalMiniute_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk) {
          return mergeFrom((com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk other) {
        if (other == com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk.getDefaultInstance()) return this;
        if (other.hasChatMessage()) {
          mergeChatMessage(other.getChatMessage());
        }
        if (other.hasLoopTimes()) {
          setLoopTimes(other.getLoopTimes());
        }
        if (other.hasLoopIntervalMiniute()) {
          setLoopIntervalMiniute(other.getLoopIntervalMiniute());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMessageBuilder_;
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       * @return Whether the chatMessage field is set.
       */
      public boolean hasChatMessage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       * @return The chatMessage.
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
        if (chatMessageBuilder_ == null) {
          return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        } else {
          return chatMessageBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder setChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatMessage_ = value;
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder setChatMessage(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = builderForValue.build();
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder mergeChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              chatMessage_ != null &&
              chatMessage_ != com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance()) {
            chatMessage_ =
              com.yorha.proto.CommonMsg.ChatMessage.newBuilder(chatMessage_).mergeFrom(value).buildPartial();
          } else {
            chatMessage_ = value;
          }
          onChanged();
        } else {
          chatMessageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public Builder clearChatMessage() {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
          onChanged();
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMessageBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatMessageFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
        if (chatMessageBuilder_ != null) {
          return chatMessageBuilder_.getMessageOrBuilder();
        } else {
          return chatMessage_ == null ?
              com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        }
      }
      /**
       * <pre>
       * 消息内容
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMessageFieldBuilder() {
        if (chatMessageBuilder_ == null) {
          chatMessageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  getChatMessage(),
                  getParentForChildren(),
                  isClean());
          chatMessage_ = null;
        }
        return chatMessageBuilder_;
      }

      private int loopTimes_ ;
      /**
       * <pre>
       * 循环次数
       * </pre>
       *
       * <code>optional int32 loopTimes = 2;</code>
       * @return Whether the loopTimes field is set.
       */
      @java.lang.Override
      public boolean hasLoopTimes() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 循环次数
       * </pre>
       *
       * <code>optional int32 loopTimes = 2;</code>
       * @return The loopTimes.
       */
      @java.lang.Override
      public int getLoopTimes() {
        return loopTimes_;
      }
      /**
       * <pre>
       * 循环次数
       * </pre>
       *
       * <code>optional int32 loopTimes = 2;</code>
       * @param value The loopTimes to set.
       * @return This builder for chaining.
       */
      public Builder setLoopTimes(int value) {
        bitField0_ |= 0x00000002;
        loopTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 循环次数
       * </pre>
       *
       * <code>optional int32 loopTimes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLoopTimes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        loopTimes_ = 0;
        onChanged();
        return this;
      }

      private int loopIntervalMiniute_ ;
      /**
       * <pre>
       * 循环间隔(分钟)
       * </pre>
       *
       * <code>optional int32 loopIntervalMiniute = 3;</code>
       * @return Whether the loopIntervalMiniute field is set.
       */
      @java.lang.Override
      public boolean hasLoopIntervalMiniute() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 循环间隔(分钟)
       * </pre>
       *
       * <code>optional int32 loopIntervalMiniute = 3;</code>
       * @return The loopIntervalMiniute.
       */
      @java.lang.Override
      public int getLoopIntervalMiniute() {
        return loopIntervalMiniute_;
      }
      /**
       * <pre>
       * 循环间隔(分钟)
       * </pre>
       *
       * <code>optional int32 loopIntervalMiniute = 3;</code>
       * @param value The loopIntervalMiniute to set.
       * @return This builder for chaining.
       */
      public Builder setLoopIntervalMiniute(int value) {
        bitField0_ |= 0x00000004;
        loopIntervalMiniute_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 循环间隔(分钟)
       * </pre>
       *
       * <code>optional int32 loopIntervalMiniute = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLoopIntervalMiniute() {
        bitField0_ = (bitField0_ & ~0x00000004);
        loopIntervalMiniute_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.IdIpSendChatMsgAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.IdIpSendChatMsgAsk)
    private static final com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk();
    }

    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IdIpSendChatMsgAsk>
        PARSER = new com.google.protobuf.AbstractParser<IdIpSendChatMsgAsk>() {
      @java.lang.Override
      public IdIpSendChatMsgAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IdIpSendChatMsgAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IdIpSendChatMsgAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IdIpSendChatMsgAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneChat.IdIpSendChatMsgAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IdIpSendChatMsgAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.IdIpSendChatMsgAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.IdIpSendChatMsgAns}
   */
  public static final class IdIpSendChatMsgAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.IdIpSendChatMsgAns)
      IdIpSendChatMsgAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IdIpSendChatMsgAns.newBuilder() to construct.
    private IdIpSendChatMsgAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IdIpSendChatMsgAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IdIpSendChatMsgAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IdIpSendChatMsgAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns.class, com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns other = (com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.IdIpSendChatMsgAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.IdIpSendChatMsgAns)
        com.yorha.proto.SsZoneChat.IdIpSendChatMsgAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns.class, com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpSendChatMsgAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns build() {
        com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns buildPartial() {
        com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns result = new com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns) {
          return mergeFrom((com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns other) {
        if (other == com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.IdIpSendChatMsgAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.IdIpSendChatMsgAns)
    private static final com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns();
    }

    public static com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IdIpSendChatMsgAns>
        PARSER = new com.google.protobuf.AbstractParser<IdIpSendChatMsgAns>() {
      @java.lang.Override
      public IdIpSendChatMsgAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IdIpSendChatMsgAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IdIpSendChatMsgAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IdIpSendChatMsgAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneChat.IdIpSendChatMsgAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IdIpExecScriptMsgAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.IdIpExecScriptMsgAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 脚本内容
     * </pre>
     *
     * <code>optional string script = 1;</code>
     * @return Whether the script field is set.
     */
    boolean hasScript();
    /**
     * <pre>
     * 脚本内容
     * </pre>
     *
     * <code>optional string script = 1;</code>
     * @return The script.
     */
    java.lang.String getScript();
    /**
     * <pre>
     * 脚本内容
     * </pre>
     *
     * <code>optional string script = 1;</code>
     * @return The bytes for script.
     */
    com.google.protobuf.ByteString
        getScriptBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.IdIpExecScriptMsgAsk}
   */
  public static final class IdIpExecScriptMsgAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.IdIpExecScriptMsgAsk)
      IdIpExecScriptMsgAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IdIpExecScriptMsgAsk.newBuilder() to construct.
    private IdIpExecScriptMsgAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IdIpExecScriptMsgAsk() {
      script_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IdIpExecScriptMsgAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IdIpExecScriptMsgAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              script_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk.class, com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk.Builder.class);
    }

    private int bitField0_;
    public static final int SCRIPT_FIELD_NUMBER = 1;
    private volatile java.lang.Object script_;
    /**
     * <pre>
     * 脚本内容
     * </pre>
     *
     * <code>optional string script = 1;</code>
     * @return Whether the script field is set.
     */
    @java.lang.Override
    public boolean hasScript() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 脚本内容
     * </pre>
     *
     * <code>optional string script = 1;</code>
     * @return The script.
     */
    @java.lang.Override
    public java.lang.String getScript() {
      java.lang.Object ref = script_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          script_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 脚本内容
     * </pre>
     *
     * <code>optional string script = 1;</code>
     * @return The bytes for script.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getScriptBytes() {
      java.lang.Object ref = script_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        script_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, script_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, script_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk other = (com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk) obj;

      if (hasScript() != other.hasScript()) return false;
      if (hasScript()) {
        if (!getScript()
            .equals(other.getScript())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasScript()) {
        hash = (37 * hash) + SCRIPT_FIELD_NUMBER;
        hash = (53 * hash) + getScript().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.IdIpExecScriptMsgAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.IdIpExecScriptMsgAsk)
        com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk.class, com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        script_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk build() {
        com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk buildPartial() {
        com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk result = new com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.script_ = script_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk) {
          return mergeFrom((com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk other) {
        if (other == com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk.getDefaultInstance()) return this;
        if (other.hasScript()) {
          bitField0_ |= 0x00000001;
          script_ = other.script_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object script_ = "";
      /**
       * <pre>
       * 脚本内容
       * </pre>
       *
       * <code>optional string script = 1;</code>
       * @return Whether the script field is set.
       */
      public boolean hasScript() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 脚本内容
       * </pre>
       *
       * <code>optional string script = 1;</code>
       * @return The script.
       */
      public java.lang.String getScript() {
        java.lang.Object ref = script_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            script_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 脚本内容
       * </pre>
       *
       * <code>optional string script = 1;</code>
       * @return The bytes for script.
       */
      public com.google.protobuf.ByteString
          getScriptBytes() {
        java.lang.Object ref = script_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          script_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 脚本内容
       * </pre>
       *
       * <code>optional string script = 1;</code>
       * @param value The script to set.
       * @return This builder for chaining.
       */
      public Builder setScript(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        script_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 脚本内容
       * </pre>
       *
       * <code>optional string script = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearScript() {
        bitField0_ = (bitField0_ & ~0x00000001);
        script_ = getDefaultInstance().getScript();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 脚本内容
       * </pre>
       *
       * <code>optional string script = 1;</code>
       * @param value The bytes for script to set.
       * @return This builder for chaining.
       */
      public Builder setScriptBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        script_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.IdIpExecScriptMsgAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.IdIpExecScriptMsgAsk)
    private static final com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk();
    }

    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IdIpExecScriptMsgAsk>
        PARSER = new com.google.protobuf.AbstractParser<IdIpExecScriptMsgAsk>() {
      @java.lang.Override
      public IdIpExecScriptMsgAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IdIpExecScriptMsgAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IdIpExecScriptMsgAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IdIpExecScriptMsgAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IdIpExecScriptMsgAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.IdIpExecScriptMsgAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string result = 1;</code>
     * @return Whether the result field is set.
     */
    boolean hasResult();
    /**
     * <code>optional string result = 1;</code>
     * @return The result.
     */
    java.lang.String getResult();
    /**
     * <code>optional string result = 1;</code>
     * @return The bytes for result.
     */
    com.google.protobuf.ByteString
        getResultBytes();

    /**
     * <pre>
     * 间隔时间
     * </pre>
     *
     * <code>optional int64 interval = 2;</code>
     * @return Whether the interval field is set.
     */
    boolean hasInterval();
    /**
     * <pre>
     * 间隔时间
     * </pre>
     *
     * <code>optional int64 interval = 2;</code>
     * @return The interval.
     */
    long getInterval();
  }
  /**
   * Protobuf type {@code com.yorha.proto.IdIpExecScriptMsgAns}
   */
  public static final class IdIpExecScriptMsgAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.IdIpExecScriptMsgAns)
      IdIpExecScriptMsgAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IdIpExecScriptMsgAns.newBuilder() to construct.
    private IdIpExecScriptMsgAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IdIpExecScriptMsgAns() {
      result_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IdIpExecScriptMsgAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IdIpExecScriptMsgAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              result_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              interval_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns.class, com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private volatile java.lang.Object result_;
    /**
     * <code>optional string result = 1;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public java.lang.String getResult() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          result_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string result = 1;</code>
     * @return The bytes for result.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getResultBytes() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        result_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INTERVAL_FIELD_NUMBER = 2;
    private long interval_;
    /**
     * <pre>
     * 间隔时间
     * </pre>
     *
     * <code>optional int64 interval = 2;</code>
     * @return Whether the interval field is set.
     */
    @java.lang.Override
    public boolean hasInterval() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 间隔时间
     * </pre>
     *
     * <code>optional int64 interval = 2;</code>
     * @return The interval.
     */
    @java.lang.Override
    public long getInterval() {
      return interval_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, result_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, interval_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, result_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, interval_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns other = (com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns) obj;

      if (hasResult() != other.hasResult()) return false;
      if (hasResult()) {
        if (!getResult()
            .equals(other.getResult())) return false;
      }
      if (hasInterval() != other.hasInterval()) return false;
      if (hasInterval()) {
        if (getInterval()
            != other.getInterval()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      if (hasInterval()) {
        hash = (37 * hash) + INTERVAL_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getInterval());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.IdIpExecScriptMsgAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.IdIpExecScriptMsgAns)
        com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns.class, com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        result_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        interval_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneChat.internal_static_com_yorha_proto_IdIpExecScriptMsgAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns build() {
        com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns buildPartial() {
        com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns result = new com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.result_ = result_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.interval_ = interval_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns) {
          return mergeFrom((com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns other) {
        if (other == com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns.getDefaultInstance()) return this;
        if (other.hasResult()) {
          bitField0_ |= 0x00000001;
          result_ = other.result_;
          onChanged();
        }
        if (other.hasInterval()) {
          setInterval(other.getInterval());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object result_ = "";
      /**
       * <code>optional string result = 1;</code>
       * @return Whether the result field is set.
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string result = 1;</code>
       * @return The result.
       */
      public java.lang.String getResult() {
        java.lang.Object ref = result_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            result_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string result = 1;</code>
       * @return The bytes for result.
       */
      public com.google.protobuf.ByteString
          getResultBytes() {
        java.lang.Object ref = result_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          result_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = getDefaultInstance().getResult();
        onChanged();
        return this;
      }
      /**
       * <code>optional string result = 1;</code>
       * @param value The bytes for result to set.
       * @return This builder for chaining.
       */
      public Builder setResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        result_ = value;
        onChanged();
        return this;
      }

      private long interval_ ;
      /**
       * <pre>
       * 间隔时间
       * </pre>
       *
       * <code>optional int64 interval = 2;</code>
       * @return Whether the interval field is set.
       */
      @java.lang.Override
      public boolean hasInterval() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 间隔时间
       * </pre>
       *
       * <code>optional int64 interval = 2;</code>
       * @return The interval.
       */
      @java.lang.Override
      public long getInterval() {
        return interval_;
      }
      /**
       * <pre>
       * 间隔时间
       * </pre>
       *
       * <code>optional int64 interval = 2;</code>
       * @param value The interval to set.
       * @return This builder for chaining.
       */
      public Builder setInterval(long value) {
        bitField0_ |= 0x00000002;
        interval_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 间隔时间
       * </pre>
       *
       * <code>optional int64 interval = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearInterval() {
        bitField0_ = (bitField0_ & ~0x00000002);
        interval_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.IdIpExecScriptMsgAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.IdIpExecScriptMsgAns)
    private static final com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns();
    }

    public static com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IdIpExecScriptMsgAns>
        PARSER = new com.google.protobuf.AbstractParser<IdIpExecScriptMsgAns>() {
      @java.lang.Override
      public IdIpExecScriptMsgAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IdIpExecScriptMsgAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IdIpExecScriptMsgAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IdIpExecScriptMsgAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneChat.IdIpExecScriptMsgAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchChatMsgAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchChatMsgAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchChatMsgAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchChatMsgAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendChatMsgAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendChatMsgAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendChatMsgAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendChatMsgAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_IdIpSendChatMsgAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_IdIpSendChatMsgAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_IdIpSendChatMsgAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_IdIpSendChatMsgAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_IdIpExecScriptMsgAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_IdIpExecScriptMsgAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'ss_proto/gen/zoneChat/ss_zoneChat.prot" +
      "o\022\017com.yorha.proto\032$ss_proto/gen/common/" +
      "common_msg.proto\"C\n\017FetchChatMsgAsk\022\016\n\006f" +
      "romId\030\001 \001(\003\022\014\n\004toId\030\002 \001(\003\022\022\n\nshieldList\030" +
      "\003 \003(\003\"A\n\017FetchChatMsgAns\022.\n\010chatMsgs\030\001 \003" +
      "(\0132\034.com.yorha.proto.ChatMessage\"Y\n\016Send" +
      "ChatMsgAsk\0221\n\013chatMessage\030\001 \001(\0132\034.com.yo" +
      "rha.proto.ChatMessage\022\024\n\014beShieldList\030\002 " +
      "\003(\003\"#\n\016SendChatMsgAns\022\021\n\tmessageId\030\001 \001(\003" +
      "\"w\n\022IdIpSendChatMsgAsk\0221\n\013chatMessage\030\001 " +
      "\001(\0132\034.com.yorha.proto.ChatMessage\022\021\n\tloo" +
      "pTimes\030\002 \001(\005\022\033\n\023loopIntervalMiniute\030\003 \001(" +
      "\005\"\024\n\022IdIpSendChatMsgAns\"&\n\024IdIpExecScrip" +
      "tMsgAsk\022\016\n\006script\030\001 \001(\t\"8\n\024IdIpExecScrip" +
      "tMsgAns\022\016\n\006result\030\001 \001(\t\022\020\n\010interval\030\002 \001(" +
      "\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_FetchChatMsgAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_FetchChatMsgAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchChatMsgAsk_descriptor,
        new java.lang.String[] { "FromId", "ToId", "ShieldList", });
    internal_static_com_yorha_proto_FetchChatMsgAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_FetchChatMsgAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchChatMsgAns_descriptor,
        new java.lang.String[] { "ChatMsgs", });
    internal_static_com_yorha_proto_SendChatMsgAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SendChatMsgAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendChatMsgAsk_descriptor,
        new java.lang.String[] { "ChatMessage", "BeShieldList", });
    internal_static_com_yorha_proto_SendChatMsgAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_SendChatMsgAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendChatMsgAns_descriptor,
        new java.lang.String[] { "MessageId", });
    internal_static_com_yorha_proto_IdIpSendChatMsgAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_IdIpSendChatMsgAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_IdIpSendChatMsgAsk_descriptor,
        new java.lang.String[] { "ChatMessage", "LoopTimes", "LoopIntervalMiniute", });
    internal_static_com_yorha_proto_IdIpSendChatMsgAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_IdIpSendChatMsgAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_IdIpSendChatMsgAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_IdIpExecScriptMsgAsk_descriptor,
        new java.lang.String[] { "Script", });
    internal_static_com_yorha_proto_IdIpExecScriptMsgAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_IdIpExecScriptMsgAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_IdIpExecScriptMsgAns_descriptor,
        new java.lang.String[] { "Result", "Interval", });
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

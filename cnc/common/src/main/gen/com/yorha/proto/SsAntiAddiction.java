// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/antiAddiction/ss_anti_addiction.proto

package com.yorha.proto;

public final class SsAntiAddiction {
  private SsAntiAddiction() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface JudgeTimingAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JudgeTimingAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 factType = 1;</code>
     * @return Whether the factType field is set.
     */
    boolean hasFactType();
    /**
     * <code>optional int32 factType = 1;</code>
     * @return The factType.
     */
    int getFactType();

    /**
     * <code>optional string userId = 2;</code>
     * @return Whether the userId field is set.
     */
    boolean hasUserId();
    /**
     * <code>optional string userId = 2;</code>
     * @return The userId.
     */
    java.lang.String getUserId();
    /**
     * <code>optional string userId = 2;</code>
     * @return The bytes for userId.
     */
    com.google.protobuf.ByteString
        getUserIdBytes();

    /**
     * <code>optional int32 duration = 3;</code>
     * @return Whether the duration field is set.
     */
    boolean hasDuration();
    /**
     * <code>optional int32 duration = 3;</code>
     * @return The duration.
     */
    int getDuration();

    /**
     * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
     * @return Whether the deviceInfo field is set.
     */
    boolean hasDeviceInfo();
    /**
     * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
     * @return The deviceInfo.
     */
    com.yorha.proto.SsAntiAddiction.DeviceInfo getDeviceInfo();
    /**
     * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
     */
    com.yorha.proto.SsAntiAddiction.DeviceInfoOrBuilder getDeviceInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.JudgeTimingAsk}
   */
  public static final class JudgeTimingAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JudgeTimingAsk)
      JudgeTimingAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JudgeTimingAsk.newBuilder() to construct.
    private JudgeTimingAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JudgeTimingAsk() {
      userId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JudgeTimingAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JudgeTimingAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              factType_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              userId_ = bs;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              duration_ = input.readInt32();
              break;
            }
            case 34: {
              com.yorha.proto.SsAntiAddiction.DeviceInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = deviceInfo_.toBuilder();
              }
              deviceInfo_ = input.readMessage(com.yorha.proto.SsAntiAddiction.DeviceInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(deviceInfo_);
                deviceInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.JudgeTimingAsk.class, com.yorha.proto.SsAntiAddiction.JudgeTimingAsk.Builder.class);
    }

    private int bitField0_;
    public static final int FACTTYPE_FIELD_NUMBER = 1;
    private int factType_;
    /**
     * <code>optional int32 factType = 1;</code>
     * @return Whether the factType field is set.
     */
    @java.lang.Override
    public boolean hasFactType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 factType = 1;</code>
     * @return The factType.
     */
    @java.lang.Override
    public int getFactType() {
      return factType_;
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private volatile java.lang.Object userId_;
    /**
     * <code>optional string userId = 2;</code>
     * @return Whether the userId field is set.
     */
    @java.lang.Override
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public java.lang.String getUserId() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          userId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string userId = 2;</code>
     * @return The bytes for userId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserIdBytes() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DURATION_FIELD_NUMBER = 3;
    private int duration_;
    /**
     * <code>optional int32 duration = 3;</code>
     * @return Whether the duration field is set.
     */
    @java.lang.Override
    public boolean hasDuration() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 duration = 3;</code>
     * @return The duration.
     */
    @java.lang.Override
    public int getDuration() {
      return duration_;
    }

    public static final int DEVICEINFO_FIELD_NUMBER = 4;
    private com.yorha.proto.SsAntiAddiction.DeviceInfo deviceInfo_;
    /**
     * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
     * @return Whether the deviceInfo field is set.
     */
    @java.lang.Override
    public boolean hasDeviceInfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
     * @return The deviceInfo.
     */
    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.DeviceInfo getDeviceInfo() {
      return deviceInfo_ == null ? com.yorha.proto.SsAntiAddiction.DeviceInfo.getDefaultInstance() : deviceInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.DeviceInfoOrBuilder getDeviceInfoOrBuilder() {
      return deviceInfo_ == null ? com.yorha.proto.SsAntiAddiction.DeviceInfo.getDefaultInstance() : deviceInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, factType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, userId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, duration_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getDeviceInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, factType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, userId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, duration_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getDeviceInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.JudgeTimingAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.JudgeTimingAsk other = (com.yorha.proto.SsAntiAddiction.JudgeTimingAsk) obj;

      if (hasFactType() != other.hasFactType()) return false;
      if (hasFactType()) {
        if (getFactType()
            != other.getFactType()) return false;
      }
      if (hasUserId() != other.hasUserId()) return false;
      if (hasUserId()) {
        if (!getUserId()
            .equals(other.getUserId())) return false;
      }
      if (hasDuration() != other.hasDuration()) return false;
      if (hasDuration()) {
        if (getDuration()
            != other.getDuration()) return false;
      }
      if (hasDeviceInfo() != other.hasDeviceInfo()) return false;
      if (hasDeviceInfo()) {
        if (!getDeviceInfo()
            .equals(other.getDeviceInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasFactType()) {
        hash = (37 * hash) + FACTTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getFactType();
      }
      if (hasUserId()) {
        hash = (37 * hash) + USERID_FIELD_NUMBER;
        hash = (53 * hash) + getUserId().hashCode();
      }
      if (hasDuration()) {
        hash = (37 * hash) + DURATION_FIELD_NUMBER;
        hash = (53 * hash) + getDuration();
      }
      if (hasDeviceInfo()) {
        hash = (37 * hash) + DEVICEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getDeviceInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.JudgeTimingAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JudgeTimingAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JudgeTimingAsk)
        com.yorha.proto.SsAntiAddiction.JudgeTimingAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.JudgeTimingAsk.class, com.yorha.proto.SsAntiAddiction.JudgeTimingAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.JudgeTimingAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDeviceInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        factType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        duration_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (deviceInfoBuilder_ == null) {
          deviceInfo_ = null;
        } else {
          deviceInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.JudgeTimingAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingAsk build() {
        com.yorha.proto.SsAntiAddiction.JudgeTimingAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingAsk buildPartial() {
        com.yorha.proto.SsAntiAddiction.JudgeTimingAsk result = new com.yorha.proto.SsAntiAddiction.JudgeTimingAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.factType_ = factType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.duration_ = duration_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (deviceInfoBuilder_ == null) {
            result.deviceInfo_ = deviceInfo_;
          } else {
            result.deviceInfo_ = deviceInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.JudgeTimingAsk) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.JudgeTimingAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.JudgeTimingAsk other) {
        if (other == com.yorha.proto.SsAntiAddiction.JudgeTimingAsk.getDefaultInstance()) return this;
        if (other.hasFactType()) {
          setFactType(other.getFactType());
        }
        if (other.hasUserId()) {
          bitField0_ |= 0x00000002;
          userId_ = other.userId_;
          onChanged();
        }
        if (other.hasDuration()) {
          setDuration(other.getDuration());
        }
        if (other.hasDeviceInfo()) {
          mergeDeviceInfo(other.getDeviceInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.JudgeTimingAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.JudgeTimingAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int factType_ ;
      /**
       * <code>optional int32 factType = 1;</code>
       * @return Whether the factType field is set.
       */
      @java.lang.Override
      public boolean hasFactType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 factType = 1;</code>
       * @return The factType.
       */
      @java.lang.Override
      public int getFactType() {
        return factType_;
      }
      /**
       * <code>optional int32 factType = 1;</code>
       * @param value The factType to set.
       * @return This builder for chaining.
       */
      public Builder setFactType(int value) {
        bitField0_ |= 0x00000001;
        factType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 factType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFactType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        factType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object userId_ = "";
      /**
       * <code>optional string userId = 2;</code>
       * @return Whether the userId field is set.
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string userId = 2;</code>
       * @return The userId.
       */
      public java.lang.String getUserId() {
        java.lang.Object ref = userId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            userId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string userId = 2;</code>
       * @return The bytes for userId.
       */
      public com.google.protobuf.ByteString
          getUserIdBytes() {
        java.lang.Object ref = userId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userId_ = getDefaultInstance().getUserId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 2;</code>
       * @param value The bytes for userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        userId_ = value;
        onChanged();
        return this;
      }

      private int duration_ ;
      /**
       * <code>optional int32 duration = 3;</code>
       * @return Whether the duration field is set.
       */
      @java.lang.Override
      public boolean hasDuration() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 duration = 3;</code>
       * @return The duration.
       */
      @java.lang.Override
      public int getDuration() {
        return duration_;
      }
      /**
       * <code>optional int32 duration = 3;</code>
       * @param value The duration to set.
       * @return This builder for chaining.
       */
      public Builder setDuration(int value) {
        bitField0_ |= 0x00000004;
        duration_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 duration = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDuration() {
        bitField0_ = (bitField0_ & ~0x00000004);
        duration_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.SsAntiAddiction.DeviceInfo deviceInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAntiAddiction.DeviceInfo, com.yorha.proto.SsAntiAddiction.DeviceInfo.Builder, com.yorha.proto.SsAntiAddiction.DeviceInfoOrBuilder> deviceInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       * @return Whether the deviceInfo field is set.
       */
      public boolean hasDeviceInfo() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       * @return The deviceInfo.
       */
      public com.yorha.proto.SsAntiAddiction.DeviceInfo getDeviceInfo() {
        if (deviceInfoBuilder_ == null) {
          return deviceInfo_ == null ? com.yorha.proto.SsAntiAddiction.DeviceInfo.getDefaultInstance() : deviceInfo_;
        } else {
          return deviceInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       */
      public Builder setDeviceInfo(com.yorha.proto.SsAntiAddiction.DeviceInfo value) {
        if (deviceInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          deviceInfo_ = value;
          onChanged();
        } else {
          deviceInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       */
      public Builder setDeviceInfo(
          com.yorha.proto.SsAntiAddiction.DeviceInfo.Builder builderForValue) {
        if (deviceInfoBuilder_ == null) {
          deviceInfo_ = builderForValue.build();
          onChanged();
        } else {
          deviceInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       */
      public Builder mergeDeviceInfo(com.yorha.proto.SsAntiAddiction.DeviceInfo value) {
        if (deviceInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              deviceInfo_ != null &&
              deviceInfo_ != com.yorha.proto.SsAntiAddiction.DeviceInfo.getDefaultInstance()) {
            deviceInfo_ =
              com.yorha.proto.SsAntiAddiction.DeviceInfo.newBuilder(deviceInfo_).mergeFrom(value).buildPartial();
          } else {
            deviceInfo_ = value;
          }
          onChanged();
        } else {
          deviceInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       */
      public Builder clearDeviceInfo() {
        if (deviceInfoBuilder_ == null) {
          deviceInfo_ = null;
          onChanged();
        } else {
          deviceInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       */
      public com.yorha.proto.SsAntiAddiction.DeviceInfo.Builder getDeviceInfoBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getDeviceInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       */
      public com.yorha.proto.SsAntiAddiction.DeviceInfoOrBuilder getDeviceInfoOrBuilder() {
        if (deviceInfoBuilder_ != null) {
          return deviceInfoBuilder_.getMessageOrBuilder();
        } else {
          return deviceInfo_ == null ?
              com.yorha.proto.SsAntiAddiction.DeviceInfo.getDefaultInstance() : deviceInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.DeviceInfo deviceInfo = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAntiAddiction.DeviceInfo, com.yorha.proto.SsAntiAddiction.DeviceInfo.Builder, com.yorha.proto.SsAntiAddiction.DeviceInfoOrBuilder> 
          getDeviceInfoFieldBuilder() {
        if (deviceInfoBuilder_ == null) {
          deviceInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsAntiAddiction.DeviceInfo, com.yorha.proto.SsAntiAddiction.DeviceInfo.Builder, com.yorha.proto.SsAntiAddiction.DeviceInfoOrBuilder>(
                  getDeviceInfo(),
                  getParentForChildren(),
                  isClean());
          deviceInfo_ = null;
        }
        return deviceInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JudgeTimingAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JudgeTimingAsk)
    private static final com.yorha.proto.SsAntiAddiction.JudgeTimingAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.JudgeTimingAsk();
    }

    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JudgeTimingAsk>
        PARSER = new com.google.protobuf.AbstractParser<JudgeTimingAsk>() {
      @java.lang.Override
      public JudgeTimingAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JudgeTimingAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JudgeTimingAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JudgeTimingAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgeTimingAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JudgeTimingAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JudgeTimingAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
     * @return Whether the result field is set.
     */
    boolean hasResult();
    /**
     * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
     * @return The result.
     */
    com.yorha.proto.SsAntiAddiction.JudgeTimingResult getResult();
    /**
     * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
     */
    com.yorha.proto.SsAntiAddiction.JudgeTimingResultOrBuilder getResultOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.JudgeTimingAns}
   */
  public static final class JudgeTimingAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JudgeTimingAns)
      JudgeTimingAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JudgeTimingAns.newBuilder() to construct.
    private JudgeTimingAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JudgeTimingAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JudgeTimingAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JudgeTimingAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsAntiAddiction.JudgeTimingResult.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = result_.toBuilder();
              }
              result_ = input.readMessage(com.yorha.proto.SsAntiAddiction.JudgeTimingResult.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(result_);
                result_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.JudgeTimingAns.class, com.yorha.proto.SsAntiAddiction.JudgeTimingAns.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private com.yorha.proto.SsAntiAddiction.JudgeTimingResult result_;
    /**
     * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgeTimingResult getResult() {
      return result_ == null ? com.yorha.proto.SsAntiAddiction.JudgeTimingResult.getDefaultInstance() : result_;
    }
    /**
     * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgeTimingResultOrBuilder getResultOrBuilder() {
      return result_ == null ? com.yorha.proto.SsAntiAddiction.JudgeTimingResult.getDefaultInstance() : result_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getResult());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getResult());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.JudgeTimingAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.JudgeTimingAns other = (com.yorha.proto.SsAntiAddiction.JudgeTimingAns) obj;

      if (hasResult() != other.hasResult()) return false;
      if (hasResult()) {
        if (!getResult()
            .equals(other.getResult())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.JudgeTimingAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JudgeTimingAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JudgeTimingAns)
        com.yorha.proto.SsAntiAddiction.JudgeTimingAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.JudgeTimingAns.class, com.yorha.proto.SsAntiAddiction.JudgeTimingAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.JudgeTimingAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getResultFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (resultBuilder_ == null) {
          result_ = null;
        } else {
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingAns getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.JudgeTimingAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingAns build() {
        com.yorha.proto.SsAntiAddiction.JudgeTimingAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingAns buildPartial() {
        com.yorha.proto.SsAntiAddiction.JudgeTimingAns result = new com.yorha.proto.SsAntiAddiction.JudgeTimingAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (resultBuilder_ == null) {
            result.result_ = result_;
          } else {
            result.result_ = resultBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.JudgeTimingAns) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.JudgeTimingAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.JudgeTimingAns other) {
        if (other == com.yorha.proto.SsAntiAddiction.JudgeTimingAns.getDefaultInstance()) return this;
        if (other.hasResult()) {
          mergeResult(other.getResult());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.JudgeTimingAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.JudgeTimingAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsAntiAddiction.JudgeTimingResult result_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAntiAddiction.JudgeTimingResult, com.yorha.proto.SsAntiAddiction.JudgeTimingResult.Builder, com.yorha.proto.SsAntiAddiction.JudgeTimingResultOrBuilder> resultBuilder_;
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       * @return Whether the result field is set.
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       * @return The result.
       */
      public com.yorha.proto.SsAntiAddiction.JudgeTimingResult getResult() {
        if (resultBuilder_ == null) {
          return result_ == null ? com.yorha.proto.SsAntiAddiction.JudgeTimingResult.getDefaultInstance() : result_;
        } else {
          return resultBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       */
      public Builder setResult(com.yorha.proto.SsAntiAddiction.JudgeTimingResult value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          result_ = value;
          onChanged();
        } else {
          resultBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       */
      public Builder setResult(
          com.yorha.proto.SsAntiAddiction.JudgeTimingResult.Builder builderForValue) {
        if (resultBuilder_ == null) {
          result_ = builderForValue.build();
          onChanged();
        } else {
          resultBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       */
      public Builder mergeResult(com.yorha.proto.SsAntiAddiction.JudgeTimingResult value) {
        if (resultBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              result_ != null &&
              result_ != com.yorha.proto.SsAntiAddiction.JudgeTimingResult.getDefaultInstance()) {
            result_ =
              com.yorha.proto.SsAntiAddiction.JudgeTimingResult.newBuilder(result_).mergeFrom(value).buildPartial();
          } else {
            result_ = value;
          }
          onChanged();
        } else {
          resultBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = null;
          onChanged();
        } else {
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       */
      public com.yorha.proto.SsAntiAddiction.JudgeTimingResult.Builder getResultBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getResultFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       */
      public com.yorha.proto.SsAntiAddiction.JudgeTimingResultOrBuilder getResultOrBuilder() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilder();
        } else {
          return result_ == null ?
              com.yorha.proto.SsAntiAddiction.JudgeTimingResult.getDefaultInstance() : result_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.JudgeTimingResult result = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAntiAddiction.JudgeTimingResult, com.yorha.proto.SsAntiAddiction.JudgeTimingResult.Builder, com.yorha.proto.SsAntiAddiction.JudgeTimingResultOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsAntiAddiction.JudgeTimingResult, com.yorha.proto.SsAntiAddiction.JudgeTimingResult.Builder, com.yorha.proto.SsAntiAddiction.JudgeTimingResultOrBuilder>(
                  getResult(),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JudgeTimingAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JudgeTimingAns)
    private static final com.yorha.proto.SsAntiAddiction.JudgeTimingAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.JudgeTimingAns();
    }

    public static com.yorha.proto.SsAntiAddiction.JudgeTimingAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JudgeTimingAns>
        PARSER = new com.google.protobuf.AbstractParser<JudgeTimingAns>() {
      @java.lang.Override
      public JudgeTimingAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JudgeTimingAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JudgeTimingAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JudgeTimingAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgeTimingAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JudgeTimingResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JudgeTimingResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    boolean hasRet();
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    int getRet();

    /**
     * <code>optional string msg = 2;</code>
     * @return Whether the msg field is set.
     */
    boolean hasMsg();
    /**
     * <code>optional string msg = 2;</code>
     * @return The msg.
     */
    java.lang.String getMsg();
    /**
     * <code>optional string msg = 2;</code>
     * @return The bytes for msg.
     */
    com.google.protobuf.ByteString
        getMsgBytes();

    /**
     * <code>optional string traceId = 3;</code>
     * @return Whether the traceId field is set.
     */
    boolean hasTraceId();
    /**
     * <code>optional string traceId = 3;</code>
     * @return The traceId.
     */
    java.lang.String getTraceId();
    /**
     * <code>optional string traceId = 3;</code>
     * @return The bytes for traceId.
     */
    com.google.protobuf.ByteString
        getTraceIdBytes();

    /**
     * <code>optional string context = 4;</code>
     * @return Whether the context field is set.
     */
    boolean hasContext();
    /**
     * <code>optional string context = 4;</code>
     * @return The context.
     */
    java.lang.String getContext();
    /**
     * <code>optional string context = 4;</code>
     * @return The bytes for context.
     */
    com.google.protobuf.ByteString
        getContextBytes();

    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    java.util.List<com.yorha.proto.User.HopeInstruction> 
        getInstructionsList();
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    com.yorha.proto.User.HopeInstruction getInstructions(int index);
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    int getInstructionsCount();
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    java.util.List<? extends com.yorha.proto.User.HopeInstructionOrBuilder> 
        getInstructionsOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    com.yorha.proto.User.HopeInstructionOrBuilder getInstructionsOrBuilder(
        int index);

    /**
     * <code>optional int32 duration = 6;</code>
     * @return Whether the duration field is set.
     */
    boolean hasDuration();
    /**
     * <code>optional int32 duration = 6;</code>
     * @return The duration.
     */
    int getDuration();
  }
  /**
   * Protobuf type {@code com.yorha.proto.JudgeTimingResult}
   */
  public static final class JudgeTimingResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JudgeTimingResult)
      JudgeTimingResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JudgeTimingResult.newBuilder() to construct.
    private JudgeTimingResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JudgeTimingResult() {
      msg_ = "";
      traceId_ = "";
      context_ = "";
      instructions_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JudgeTimingResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JudgeTimingResult(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ret_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              msg_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              traceId_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              context_ = bs;
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) != 0)) {
                instructions_ = new java.util.ArrayList<com.yorha.proto.User.HopeInstruction>();
                mutable_bitField0_ |= 0x00000010;
              }
              instructions_.add(
                  input.readMessage(com.yorha.proto.User.HopeInstruction.PARSER, extensionRegistry));
              break;
            }
            case 48: {
              bitField0_ |= 0x00000010;
              duration_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) != 0)) {
          instructions_ = java.util.Collections.unmodifiableList(instructions_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.JudgeTimingResult.class, com.yorha.proto.SsAntiAddiction.JudgeTimingResult.Builder.class);
    }

    private int bitField0_;
    public static final int RET_FIELD_NUMBER = 1;
    private int ret_;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }

    public static final int MSG_FIELD_NUMBER = 2;
    private volatile java.lang.Object msg_;
    /**
     * <code>optional string msg = 2;</code>
     * @return Whether the msg field is set.
     */
    @java.lang.Override
    public boolean hasMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string msg = 2;</code>
     * @return The msg.
     */
    @java.lang.Override
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          msg_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string msg = 2;</code>
     * @return The bytes for msg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TRACEID_FIELD_NUMBER = 3;
    private volatile java.lang.Object traceId_;
    /**
     * <code>optional string traceId = 3;</code>
     * @return Whether the traceId field is set.
     */
    @java.lang.Override
    public boolean hasTraceId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string traceId = 3;</code>
     * @return The traceId.
     */
    @java.lang.Override
    public java.lang.String getTraceId() {
      java.lang.Object ref = traceId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          traceId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string traceId = 3;</code>
     * @return The bytes for traceId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTraceIdBytes() {
      java.lang.Object ref = traceId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        traceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONTEXT_FIELD_NUMBER = 4;
    private volatile java.lang.Object context_;
    /**
     * <code>optional string context = 4;</code>
     * @return Whether the context field is set.
     */
    @java.lang.Override
    public boolean hasContext() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string context = 4;</code>
     * @return The context.
     */
    @java.lang.Override
    public java.lang.String getContext() {
      java.lang.Object ref = context_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          context_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string context = 4;</code>
     * @return The bytes for context.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContextBytes() {
      java.lang.Object ref = context_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        context_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INSTRUCTIONS_FIELD_NUMBER = 5;
    private java.util.List<com.yorha.proto.User.HopeInstruction> instructions_;
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.User.HopeInstruction> getInstructionsList() {
      return instructions_;
    }
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.User.HopeInstructionOrBuilder> 
        getInstructionsOrBuilderList() {
      return instructions_;
    }
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public int getInstructionsCount() {
      return instructions_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.User.HopeInstruction getInstructions(int index) {
      return instructions_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.User.HopeInstructionOrBuilder getInstructionsOrBuilder(
        int index) {
      return instructions_.get(index);
    }

    public static final int DURATION_FIELD_NUMBER = 6;
    private int duration_;
    /**
     * <code>optional int32 duration = 6;</code>
     * @return Whether the duration field is set.
     */
    @java.lang.Override
    public boolean hasDuration() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 duration = 6;</code>
     * @return The duration.
     */
    @java.lang.Override
    public int getDuration() {
      return duration_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, ret_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, msg_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, traceId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, context_);
      }
      for (int i = 0; i < instructions_.size(); i++) {
        output.writeMessage(5, instructions_.get(i));
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(6, duration_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, ret_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, msg_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, traceId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, context_);
      }
      for (int i = 0; i < instructions_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, instructions_.get(i));
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, duration_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.JudgeTimingResult)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.JudgeTimingResult other = (com.yorha.proto.SsAntiAddiction.JudgeTimingResult) obj;

      if (hasRet() != other.hasRet()) return false;
      if (hasRet()) {
        if (getRet()
            != other.getRet()) return false;
      }
      if (hasMsg() != other.hasMsg()) return false;
      if (hasMsg()) {
        if (!getMsg()
            .equals(other.getMsg())) return false;
      }
      if (hasTraceId() != other.hasTraceId()) return false;
      if (hasTraceId()) {
        if (!getTraceId()
            .equals(other.getTraceId())) return false;
      }
      if (hasContext() != other.hasContext()) return false;
      if (hasContext()) {
        if (!getContext()
            .equals(other.getContext())) return false;
      }
      if (!getInstructionsList()
          .equals(other.getInstructionsList())) return false;
      if (hasDuration() != other.hasDuration()) return false;
      if (hasDuration()) {
        if (getDuration()
            != other.getDuration()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRet()) {
        hash = (37 * hash) + RET_FIELD_NUMBER;
        hash = (53 * hash) + getRet();
      }
      if (hasMsg()) {
        hash = (37 * hash) + MSG_FIELD_NUMBER;
        hash = (53 * hash) + getMsg().hashCode();
      }
      if (hasTraceId()) {
        hash = (37 * hash) + TRACEID_FIELD_NUMBER;
        hash = (53 * hash) + getTraceId().hashCode();
      }
      if (hasContext()) {
        hash = (37 * hash) + CONTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getContext().hashCode();
      }
      if (getInstructionsCount() > 0) {
        hash = (37 * hash) + INSTRUCTIONS_FIELD_NUMBER;
        hash = (53 * hash) + getInstructionsList().hashCode();
      }
      if (hasDuration()) {
        hash = (37 * hash) + DURATION_FIELD_NUMBER;
        hash = (53 * hash) + getDuration();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.JudgeTimingResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JudgeTimingResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JudgeTimingResult)
        com.yorha.proto.SsAntiAddiction.JudgeTimingResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.JudgeTimingResult.class, com.yorha.proto.SsAntiAddiction.JudgeTimingResult.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.JudgeTimingResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInstructionsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ret_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        msg_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        traceId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        context_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        if (instructionsBuilder_ == null) {
          instructions_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          instructionsBuilder_.clear();
        }
        duration_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgeTimingResult_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingResult getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.JudgeTimingResult.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingResult build() {
        com.yorha.proto.SsAntiAddiction.JudgeTimingResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgeTimingResult buildPartial() {
        com.yorha.proto.SsAntiAddiction.JudgeTimingResult result = new com.yorha.proto.SsAntiAddiction.JudgeTimingResult(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ret_ = ret_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msg_ = msg_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.traceId_ = traceId_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.context_ = context_;
        if (instructionsBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0)) {
            instructions_ = java.util.Collections.unmodifiableList(instructions_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.instructions_ = instructions_;
        } else {
          result.instructions_ = instructionsBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.duration_ = duration_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.JudgeTimingResult) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.JudgeTimingResult)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.JudgeTimingResult other) {
        if (other == com.yorha.proto.SsAntiAddiction.JudgeTimingResult.getDefaultInstance()) return this;
        if (other.hasRet()) {
          setRet(other.getRet());
        }
        if (other.hasMsg()) {
          bitField0_ |= 0x00000002;
          msg_ = other.msg_;
          onChanged();
        }
        if (other.hasTraceId()) {
          bitField0_ |= 0x00000004;
          traceId_ = other.traceId_;
          onChanged();
        }
        if (other.hasContext()) {
          bitField0_ |= 0x00000008;
          context_ = other.context_;
          onChanged();
        }
        if (instructionsBuilder_ == null) {
          if (!other.instructions_.isEmpty()) {
            if (instructions_.isEmpty()) {
              instructions_ = other.instructions_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureInstructionsIsMutable();
              instructions_.addAll(other.instructions_);
            }
            onChanged();
          }
        } else {
          if (!other.instructions_.isEmpty()) {
            if (instructionsBuilder_.isEmpty()) {
              instructionsBuilder_.dispose();
              instructionsBuilder_ = null;
              instructions_ = other.instructions_;
              bitField0_ = (bitField0_ & ~0x00000010);
              instructionsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInstructionsFieldBuilder() : null;
            } else {
              instructionsBuilder_.addAllMessages(other.instructions_);
            }
          }
        }
        if (other.hasDuration()) {
          setDuration(other.getDuration());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.JudgeTimingResult parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.JudgeTimingResult) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int ret_ ;
      /**
       * <code>optional int32 ret = 1;</code>
       * @return Whether the ret field is set.
       */
      @java.lang.Override
      public boolean hasRet() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @return The ret.
       */
      @java.lang.Override
      public int getRet() {
        return ret_;
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @param value The ret to set.
       * @return This builder for chaining.
       */
      public Builder setRet(int value) {
        bitField0_ |= 0x00000001;
        ret_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRet() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ret_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object msg_ = "";
      /**
       * <code>optional string msg = 2;</code>
       * @return Whether the msg field is set.
       */
      public boolean hasMsg() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string msg = 2;</code>
       * @return The msg.
       */
      public java.lang.String getMsg() {
        java.lang.Object ref = msg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            msg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string msg = 2;</code>
       * @return The bytes for msg.
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
        java.lang.Object ref = msg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string msg = 2;</code>
       * @param value The msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string msg = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsg() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msg_ = getDefaultInstance().getMsg();
        onChanged();
        return this;
      }
      /**
       * <code>optional string msg = 2;</code>
       * @param value The bytes for msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msg_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object traceId_ = "";
      /**
       * <code>optional string traceId = 3;</code>
       * @return Whether the traceId field is set.
       */
      public boolean hasTraceId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @return The traceId.
       */
      public java.lang.String getTraceId() {
        java.lang.Object ref = traceId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            traceId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @return The bytes for traceId.
       */
      public com.google.protobuf.ByteString
          getTraceIdBytes() {
        java.lang.Object ref = traceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          traceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @param value The traceId to set.
       * @return This builder for chaining.
       */
      public Builder setTraceId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        traceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTraceId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        traceId_ = getDefaultInstance().getTraceId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @param value The bytes for traceId to set.
       * @return This builder for chaining.
       */
      public Builder setTraceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        traceId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object context_ = "";
      /**
       * <code>optional string context = 4;</code>
       * @return Whether the context field is set.
       */
      public boolean hasContext() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string context = 4;</code>
       * @return The context.
       */
      public java.lang.String getContext() {
        java.lang.Object ref = context_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            context_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string context = 4;</code>
       * @return The bytes for context.
       */
      public com.google.protobuf.ByteString
          getContextBytes() {
        java.lang.Object ref = context_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          context_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string context = 4;</code>
       * @param value The context to set.
       * @return This builder for chaining.
       */
      public Builder setContext(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        context_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string context = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearContext() {
        bitField0_ = (bitField0_ & ~0x00000008);
        context_ = getDefaultInstance().getContext();
        onChanged();
        return this;
      }
      /**
       * <code>optional string context = 4;</code>
       * @param value The bytes for context to set.
       * @return This builder for chaining.
       */
      public Builder setContextBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        context_ = value;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.User.HopeInstruction> instructions_ =
        java.util.Collections.emptyList();
      private void ensureInstructionsIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          instructions_ = new java.util.ArrayList<com.yorha.proto.User.HopeInstruction>(instructions_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder> instructionsBuilder_;

      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public java.util.List<com.yorha.proto.User.HopeInstruction> getInstructionsList() {
        if (instructionsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(instructions_);
        } else {
          return instructionsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public int getInstructionsCount() {
        if (instructionsBuilder_ == null) {
          return instructions_.size();
        } else {
          return instructionsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstruction getInstructions(int index) {
        if (instructionsBuilder_ == null) {
          return instructions_.get(index);
        } else {
          return instructionsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder setInstructions(
          int index, com.yorha.proto.User.HopeInstruction value) {
        if (instructionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInstructionsIsMutable();
          instructions_.set(index, value);
          onChanged();
        } else {
          instructionsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder setInstructions(
          int index, com.yorha.proto.User.HopeInstruction.Builder builderForValue) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          instructions_.set(index, builderForValue.build());
          onChanged();
        } else {
          instructionsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addInstructions(com.yorha.proto.User.HopeInstruction value) {
        if (instructionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInstructionsIsMutable();
          instructions_.add(value);
          onChanged();
        } else {
          instructionsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addInstructions(
          int index, com.yorha.proto.User.HopeInstruction value) {
        if (instructionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInstructionsIsMutable();
          instructions_.add(index, value);
          onChanged();
        } else {
          instructionsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addInstructions(
          com.yorha.proto.User.HopeInstruction.Builder builderForValue) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          instructions_.add(builderForValue.build());
          onChanged();
        } else {
          instructionsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addInstructions(
          int index, com.yorha.proto.User.HopeInstruction.Builder builderForValue) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          instructions_.add(index, builderForValue.build());
          onChanged();
        } else {
          instructionsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addAllInstructions(
          java.lang.Iterable<? extends com.yorha.proto.User.HopeInstruction> values) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, instructions_);
          onChanged();
        } else {
          instructionsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder clearInstructions() {
        if (instructionsBuilder_ == null) {
          instructions_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          instructionsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder removeInstructions(int index) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          instructions_.remove(index);
          onChanged();
        } else {
          instructionsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstruction.Builder getInstructionsBuilder(
          int index) {
        return getInstructionsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstructionOrBuilder getInstructionsOrBuilder(
          int index) {
        if (instructionsBuilder_ == null) {
          return instructions_.get(index);  } else {
          return instructionsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public java.util.List<? extends com.yorha.proto.User.HopeInstructionOrBuilder> 
           getInstructionsOrBuilderList() {
        if (instructionsBuilder_ != null) {
          return instructionsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(instructions_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstruction.Builder addInstructionsBuilder() {
        return getInstructionsFieldBuilder().addBuilder(
            com.yorha.proto.User.HopeInstruction.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstruction.Builder addInstructionsBuilder(
          int index) {
        return getInstructionsFieldBuilder().addBuilder(
            index, com.yorha.proto.User.HopeInstruction.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public java.util.List<com.yorha.proto.User.HopeInstruction.Builder> 
           getInstructionsBuilderList() {
        return getInstructionsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder> 
          getInstructionsFieldBuilder() {
        if (instructionsBuilder_ == null) {
          instructionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder>(
                  instructions_,
                  ((bitField0_ & 0x00000010) != 0),
                  getParentForChildren(),
                  isClean());
          instructions_ = null;
        }
        return instructionsBuilder_;
      }

      private int duration_ ;
      /**
       * <code>optional int32 duration = 6;</code>
       * @return Whether the duration field is set.
       */
      @java.lang.Override
      public boolean hasDuration() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional int32 duration = 6;</code>
       * @return The duration.
       */
      @java.lang.Override
      public int getDuration() {
        return duration_;
      }
      /**
       * <code>optional int32 duration = 6;</code>
       * @param value The duration to set.
       * @return This builder for chaining.
       */
      public Builder setDuration(int value) {
        bitField0_ |= 0x00000020;
        duration_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 duration = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearDuration() {
        bitField0_ = (bitField0_ & ~0x00000020);
        duration_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JudgeTimingResult)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JudgeTimingResult)
    private static final com.yorha.proto.SsAntiAddiction.JudgeTimingResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.JudgeTimingResult();
    }

    public static com.yorha.proto.SsAntiAddiction.JudgeTimingResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JudgeTimingResult>
        PARSER = new com.google.protobuf.AbstractParser<JudgeTimingResult>() {
      @java.lang.Override
      public JudgeTimingResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JudgeTimingResult(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JudgeTimingResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JudgeTimingResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgeTimingResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JudgePayAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JudgePayAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string userId = 1;</code>
     * @return Whether the userId field is set.
     */
    boolean hasUserId();
    /**
     * <code>optional string userId = 1;</code>
     * @return The userId.
     */
    java.lang.String getUserId();
    /**
     * <code>optional string userId = 1;</code>
     * @return The bytes for userId.
     */
    com.google.protobuf.ByteString
        getUserIdBytes();

    /**
     * <code>optional int32 payAmount = 2;</code>
     * @return Whether the payAmount field is set.
     */
    boolean hasPayAmount();
    /**
     * <code>optional int32 payAmount = 2;</code>
     * @return The payAmount.
     */
    int getPayAmount();
  }
  /**
   * Protobuf type {@code com.yorha.proto.JudgePayAsk}
   */
  public static final class JudgePayAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JudgePayAsk)
      JudgePayAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JudgePayAsk.newBuilder() to construct.
    private JudgePayAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JudgePayAsk() {
      userId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JudgePayAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JudgePayAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              userId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              payAmount_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.JudgePayAsk.class, com.yorha.proto.SsAntiAddiction.JudgePayAsk.Builder.class);
    }

    private int bitField0_;
    public static final int USERID_FIELD_NUMBER = 1;
    private volatile java.lang.Object userId_;
    /**
     * <code>optional string userId = 1;</code>
     * @return Whether the userId field is set.
     */
    @java.lang.Override
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string userId = 1;</code>
     * @return The userId.
     */
    @java.lang.Override
    public java.lang.String getUserId() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          userId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string userId = 1;</code>
     * @return The bytes for userId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserIdBytes() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PAYAMOUNT_FIELD_NUMBER = 2;
    private int payAmount_;
    /**
     * <code>optional int32 payAmount = 2;</code>
     * @return Whether the payAmount field is set.
     */
    @java.lang.Override
    public boolean hasPayAmount() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 payAmount = 2;</code>
     * @return The payAmount.
     */
    @java.lang.Override
    public int getPayAmount() {
      return payAmount_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, payAmount_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, payAmount_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.JudgePayAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.JudgePayAsk other = (com.yorha.proto.SsAntiAddiction.JudgePayAsk) obj;

      if (hasUserId() != other.hasUserId()) return false;
      if (hasUserId()) {
        if (!getUserId()
            .equals(other.getUserId())) return false;
      }
      if (hasPayAmount() != other.hasPayAmount()) return false;
      if (hasPayAmount()) {
        if (getPayAmount()
            != other.getPayAmount()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUserId()) {
        hash = (37 * hash) + USERID_FIELD_NUMBER;
        hash = (53 * hash) + getUserId().hashCode();
      }
      if (hasPayAmount()) {
        hash = (37 * hash) + PAYAMOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getPayAmount();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.JudgePayAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JudgePayAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JudgePayAsk)
        com.yorha.proto.SsAntiAddiction.JudgePayAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.JudgePayAsk.class, com.yorha.proto.SsAntiAddiction.JudgePayAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.JudgePayAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        userId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        payAmount_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.JudgePayAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayAsk build() {
        com.yorha.proto.SsAntiAddiction.JudgePayAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayAsk buildPartial() {
        com.yorha.proto.SsAntiAddiction.JudgePayAsk result = new com.yorha.proto.SsAntiAddiction.JudgePayAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.payAmount_ = payAmount_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.JudgePayAsk) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.JudgePayAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.JudgePayAsk other) {
        if (other == com.yorha.proto.SsAntiAddiction.JudgePayAsk.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          bitField0_ |= 0x00000001;
          userId_ = other.userId_;
          onChanged();
        }
        if (other.hasPayAmount()) {
          setPayAmount(other.getPayAmount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.JudgePayAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.JudgePayAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object userId_ = "";
      /**
       * <code>optional string userId = 1;</code>
       * @return Whether the userId field is set.
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return The userId.
       */
      public java.lang.String getUserId() {
        java.lang.Object ref = userId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            userId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return The bytes for userId.
       */
      public com.google.protobuf.ByteString
          getUserIdBytes() {
        java.lang.Object ref = userId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string userId = 1;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = getDefaultInstance().getUserId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 1;</code>
       * @param value The bytes for userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }

      private int payAmount_ ;
      /**
       * <code>optional int32 payAmount = 2;</code>
       * @return Whether the payAmount field is set.
       */
      @java.lang.Override
      public boolean hasPayAmount() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 payAmount = 2;</code>
       * @return The payAmount.
       */
      @java.lang.Override
      public int getPayAmount() {
        return payAmount_;
      }
      /**
       * <code>optional int32 payAmount = 2;</code>
       * @param value The payAmount to set.
       * @return This builder for chaining.
       */
      public Builder setPayAmount(int value) {
        bitField0_ |= 0x00000002;
        payAmount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 payAmount = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayAmount() {
        bitField0_ = (bitField0_ & ~0x00000002);
        payAmount_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JudgePayAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JudgePayAsk)
    private static final com.yorha.proto.SsAntiAddiction.JudgePayAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.JudgePayAsk();
    }

    public static com.yorha.proto.SsAntiAddiction.JudgePayAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JudgePayAsk>
        PARSER = new com.google.protobuf.AbstractParser<JudgePayAsk>() {
      @java.lang.Override
      public JudgePayAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JudgePayAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JudgePayAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JudgePayAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgePayAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JudgePayAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JudgePayAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
     * @return Whether the result field is set.
     */
    boolean hasResult();
    /**
     * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
     * @return The result.
     */
    com.yorha.proto.SsAntiAddiction.JudgePayResult getResult();
    /**
     * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
     */
    com.yorha.proto.SsAntiAddiction.JudgePayResultOrBuilder getResultOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.JudgePayAns}
   */
  public static final class JudgePayAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JudgePayAns)
      JudgePayAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JudgePayAns.newBuilder() to construct.
    private JudgePayAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JudgePayAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JudgePayAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JudgePayAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsAntiAddiction.JudgePayResult.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = result_.toBuilder();
              }
              result_ = input.readMessage(com.yorha.proto.SsAntiAddiction.JudgePayResult.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(result_);
                result_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.JudgePayAns.class, com.yorha.proto.SsAntiAddiction.JudgePayAns.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private com.yorha.proto.SsAntiAddiction.JudgePayResult result_;
    /**
     * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgePayResult getResult() {
      return result_ == null ? com.yorha.proto.SsAntiAddiction.JudgePayResult.getDefaultInstance() : result_;
    }
    /**
     * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgePayResultOrBuilder getResultOrBuilder() {
      return result_ == null ? com.yorha.proto.SsAntiAddiction.JudgePayResult.getDefaultInstance() : result_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getResult());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getResult());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.JudgePayAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.JudgePayAns other = (com.yorha.proto.SsAntiAddiction.JudgePayAns) obj;

      if (hasResult() != other.hasResult()) return false;
      if (hasResult()) {
        if (!getResult()
            .equals(other.getResult())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.JudgePayAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JudgePayAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JudgePayAns)
        com.yorha.proto.SsAntiAddiction.JudgePayAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.JudgePayAns.class, com.yorha.proto.SsAntiAddiction.JudgePayAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.JudgePayAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getResultFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (resultBuilder_ == null) {
          result_ = null;
        } else {
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayAns getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.JudgePayAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayAns build() {
        com.yorha.proto.SsAntiAddiction.JudgePayAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayAns buildPartial() {
        com.yorha.proto.SsAntiAddiction.JudgePayAns result = new com.yorha.proto.SsAntiAddiction.JudgePayAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (resultBuilder_ == null) {
            result.result_ = result_;
          } else {
            result.result_ = resultBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.JudgePayAns) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.JudgePayAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.JudgePayAns other) {
        if (other == com.yorha.proto.SsAntiAddiction.JudgePayAns.getDefaultInstance()) return this;
        if (other.hasResult()) {
          mergeResult(other.getResult());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.JudgePayAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.JudgePayAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsAntiAddiction.JudgePayResult result_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAntiAddiction.JudgePayResult, com.yorha.proto.SsAntiAddiction.JudgePayResult.Builder, com.yorha.proto.SsAntiAddiction.JudgePayResultOrBuilder> resultBuilder_;
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       * @return Whether the result field is set.
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       * @return The result.
       */
      public com.yorha.proto.SsAntiAddiction.JudgePayResult getResult() {
        if (resultBuilder_ == null) {
          return result_ == null ? com.yorha.proto.SsAntiAddiction.JudgePayResult.getDefaultInstance() : result_;
        } else {
          return resultBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       */
      public Builder setResult(com.yorha.proto.SsAntiAddiction.JudgePayResult value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          result_ = value;
          onChanged();
        } else {
          resultBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       */
      public Builder setResult(
          com.yorha.proto.SsAntiAddiction.JudgePayResult.Builder builderForValue) {
        if (resultBuilder_ == null) {
          result_ = builderForValue.build();
          onChanged();
        } else {
          resultBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       */
      public Builder mergeResult(com.yorha.proto.SsAntiAddiction.JudgePayResult value) {
        if (resultBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              result_ != null &&
              result_ != com.yorha.proto.SsAntiAddiction.JudgePayResult.getDefaultInstance()) {
            result_ =
              com.yorha.proto.SsAntiAddiction.JudgePayResult.newBuilder(result_).mergeFrom(value).buildPartial();
          } else {
            result_ = value;
          }
          onChanged();
        } else {
          resultBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = null;
          onChanged();
        } else {
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       */
      public com.yorha.proto.SsAntiAddiction.JudgePayResult.Builder getResultBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getResultFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       */
      public com.yorha.proto.SsAntiAddiction.JudgePayResultOrBuilder getResultOrBuilder() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilder();
        } else {
          return result_ == null ?
              com.yorha.proto.SsAntiAddiction.JudgePayResult.getDefaultInstance() : result_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.JudgePayResult result = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAntiAddiction.JudgePayResult, com.yorha.proto.SsAntiAddiction.JudgePayResult.Builder, com.yorha.proto.SsAntiAddiction.JudgePayResultOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsAntiAddiction.JudgePayResult, com.yorha.proto.SsAntiAddiction.JudgePayResult.Builder, com.yorha.proto.SsAntiAddiction.JudgePayResultOrBuilder>(
                  getResult(),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JudgePayAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JudgePayAns)
    private static final com.yorha.proto.SsAntiAddiction.JudgePayAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.JudgePayAns();
    }

    public static com.yorha.proto.SsAntiAddiction.JudgePayAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JudgePayAns>
        PARSER = new com.google.protobuf.AbstractParser<JudgePayAns>() {
      @java.lang.Override
      public JudgePayAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JudgePayAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JudgePayAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JudgePayAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgePayAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JudgePayResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JudgePayResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    boolean hasRet();
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    int getRet();

    /**
     * <code>optional string msg = 2;</code>
     * @return Whether the msg field is set.
     */
    boolean hasMsg();
    /**
     * <code>optional string msg = 2;</code>
     * @return The msg.
     */
    java.lang.String getMsg();
    /**
     * <code>optional string msg = 2;</code>
     * @return The bytes for msg.
     */
    com.google.protobuf.ByteString
        getMsgBytes();

    /**
     * <code>optional string traceId = 3;</code>
     * @return Whether the traceId field is set.
     */
    boolean hasTraceId();
    /**
     * <code>optional string traceId = 3;</code>
     * @return The traceId.
     */
    java.lang.String getTraceId();
    /**
     * <code>optional string traceId = 3;</code>
     * @return The bytes for traceId.
     */
    com.google.protobuf.ByteString
        getTraceIdBytes();

    /**
     * <code>optional string context = 4;</code>
     * @return Whether the context field is set.
     */
    boolean hasContext();
    /**
     * <code>optional string context = 4;</code>
     * @return The context.
     */
    java.lang.String getContext();
    /**
     * <code>optional string context = 4;</code>
     * @return The bytes for context.
     */
    com.google.protobuf.ByteString
        getContextBytes();

    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    java.util.List<com.yorha.proto.User.HopeInstruction> 
        getInstructionsList();
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    com.yorha.proto.User.HopeInstruction getInstructions(int index);
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    int getInstructionsCount();
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    java.util.List<? extends com.yorha.proto.User.HopeInstructionOrBuilder> 
        getInstructionsOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    com.yorha.proto.User.HopeInstructionOrBuilder getInstructionsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.JudgePayResult}
   */
  public static final class JudgePayResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JudgePayResult)
      JudgePayResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JudgePayResult.newBuilder() to construct.
    private JudgePayResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JudgePayResult() {
      msg_ = "";
      traceId_ = "";
      context_ = "";
      instructions_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JudgePayResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JudgePayResult(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ret_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              msg_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              traceId_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              context_ = bs;
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) != 0)) {
                instructions_ = new java.util.ArrayList<com.yorha.proto.User.HopeInstruction>();
                mutable_bitField0_ |= 0x00000010;
              }
              instructions_.add(
                  input.readMessage(com.yorha.proto.User.HopeInstruction.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) != 0)) {
          instructions_ = java.util.Collections.unmodifiableList(instructions_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.JudgePayResult.class, com.yorha.proto.SsAntiAddiction.JudgePayResult.Builder.class);
    }

    private int bitField0_;
    public static final int RET_FIELD_NUMBER = 1;
    private int ret_;
    /**
     * <code>optional int32 ret = 1;</code>
     * @return Whether the ret field is set.
     */
    @java.lang.Override
    public boolean hasRet() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }

    public static final int MSG_FIELD_NUMBER = 2;
    private volatile java.lang.Object msg_;
    /**
     * <code>optional string msg = 2;</code>
     * @return Whether the msg field is set.
     */
    @java.lang.Override
    public boolean hasMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string msg = 2;</code>
     * @return The msg.
     */
    @java.lang.Override
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          msg_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string msg = 2;</code>
     * @return The bytes for msg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TRACEID_FIELD_NUMBER = 3;
    private volatile java.lang.Object traceId_;
    /**
     * <code>optional string traceId = 3;</code>
     * @return Whether the traceId field is set.
     */
    @java.lang.Override
    public boolean hasTraceId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string traceId = 3;</code>
     * @return The traceId.
     */
    @java.lang.Override
    public java.lang.String getTraceId() {
      java.lang.Object ref = traceId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          traceId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string traceId = 3;</code>
     * @return The bytes for traceId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTraceIdBytes() {
      java.lang.Object ref = traceId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        traceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONTEXT_FIELD_NUMBER = 4;
    private volatile java.lang.Object context_;
    /**
     * <code>optional string context = 4;</code>
     * @return Whether the context field is set.
     */
    @java.lang.Override
    public boolean hasContext() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string context = 4;</code>
     * @return The context.
     */
    @java.lang.Override
    public java.lang.String getContext() {
      java.lang.Object ref = context_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          context_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string context = 4;</code>
     * @return The bytes for context.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContextBytes() {
      java.lang.Object ref = context_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        context_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INSTRUCTIONS_FIELD_NUMBER = 5;
    private java.util.List<com.yorha.proto.User.HopeInstruction> instructions_;
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.User.HopeInstruction> getInstructionsList() {
      return instructions_;
    }
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.User.HopeInstructionOrBuilder> 
        getInstructionsOrBuilderList() {
      return instructions_;
    }
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public int getInstructionsCount() {
      return instructions_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.User.HopeInstruction getInstructions(int index) {
      return instructions_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.User.HopeInstructionOrBuilder getInstructionsOrBuilder(
        int index) {
      return instructions_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, ret_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, msg_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, traceId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, context_);
      }
      for (int i = 0; i < instructions_.size(); i++) {
        output.writeMessage(5, instructions_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, ret_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, msg_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, traceId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, context_);
      }
      for (int i = 0; i < instructions_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, instructions_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.JudgePayResult)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.JudgePayResult other = (com.yorha.proto.SsAntiAddiction.JudgePayResult) obj;

      if (hasRet() != other.hasRet()) return false;
      if (hasRet()) {
        if (getRet()
            != other.getRet()) return false;
      }
      if (hasMsg() != other.hasMsg()) return false;
      if (hasMsg()) {
        if (!getMsg()
            .equals(other.getMsg())) return false;
      }
      if (hasTraceId() != other.hasTraceId()) return false;
      if (hasTraceId()) {
        if (!getTraceId()
            .equals(other.getTraceId())) return false;
      }
      if (hasContext() != other.hasContext()) return false;
      if (hasContext()) {
        if (!getContext()
            .equals(other.getContext())) return false;
      }
      if (!getInstructionsList()
          .equals(other.getInstructionsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRet()) {
        hash = (37 * hash) + RET_FIELD_NUMBER;
        hash = (53 * hash) + getRet();
      }
      if (hasMsg()) {
        hash = (37 * hash) + MSG_FIELD_NUMBER;
        hash = (53 * hash) + getMsg().hashCode();
      }
      if (hasTraceId()) {
        hash = (37 * hash) + TRACEID_FIELD_NUMBER;
        hash = (53 * hash) + getTraceId().hashCode();
      }
      if (hasContext()) {
        hash = (37 * hash) + CONTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getContext().hashCode();
      }
      if (getInstructionsCount() > 0) {
        hash = (37 * hash) + INSTRUCTIONS_FIELD_NUMBER;
        hash = (53 * hash) + getInstructionsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.JudgePayResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.JudgePayResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JudgePayResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JudgePayResult)
        com.yorha.proto.SsAntiAddiction.JudgePayResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.JudgePayResult.class, com.yorha.proto.SsAntiAddiction.JudgePayResult.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.JudgePayResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInstructionsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ret_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        msg_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        traceId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        context_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        if (instructionsBuilder_ == null) {
          instructions_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          instructionsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_JudgePayResult_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayResult getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.JudgePayResult.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayResult build() {
        com.yorha.proto.SsAntiAddiction.JudgePayResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.JudgePayResult buildPartial() {
        com.yorha.proto.SsAntiAddiction.JudgePayResult result = new com.yorha.proto.SsAntiAddiction.JudgePayResult(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ret_ = ret_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msg_ = msg_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.traceId_ = traceId_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.context_ = context_;
        if (instructionsBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0)) {
            instructions_ = java.util.Collections.unmodifiableList(instructions_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.instructions_ = instructions_;
        } else {
          result.instructions_ = instructionsBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.JudgePayResult) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.JudgePayResult)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.JudgePayResult other) {
        if (other == com.yorha.proto.SsAntiAddiction.JudgePayResult.getDefaultInstance()) return this;
        if (other.hasRet()) {
          setRet(other.getRet());
        }
        if (other.hasMsg()) {
          bitField0_ |= 0x00000002;
          msg_ = other.msg_;
          onChanged();
        }
        if (other.hasTraceId()) {
          bitField0_ |= 0x00000004;
          traceId_ = other.traceId_;
          onChanged();
        }
        if (other.hasContext()) {
          bitField0_ |= 0x00000008;
          context_ = other.context_;
          onChanged();
        }
        if (instructionsBuilder_ == null) {
          if (!other.instructions_.isEmpty()) {
            if (instructions_.isEmpty()) {
              instructions_ = other.instructions_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureInstructionsIsMutable();
              instructions_.addAll(other.instructions_);
            }
            onChanged();
          }
        } else {
          if (!other.instructions_.isEmpty()) {
            if (instructionsBuilder_.isEmpty()) {
              instructionsBuilder_.dispose();
              instructionsBuilder_ = null;
              instructions_ = other.instructions_;
              bitField0_ = (bitField0_ & ~0x00000010);
              instructionsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInstructionsFieldBuilder() : null;
            } else {
              instructionsBuilder_.addAllMessages(other.instructions_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.JudgePayResult parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.JudgePayResult) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int ret_ ;
      /**
       * <code>optional int32 ret = 1;</code>
       * @return Whether the ret field is set.
       */
      @java.lang.Override
      public boolean hasRet() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @return The ret.
       */
      @java.lang.Override
      public int getRet() {
        return ret_;
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @param value The ret to set.
       * @return This builder for chaining.
       */
      public Builder setRet(int value) {
        bitField0_ |= 0x00000001;
        ret_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ret = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRet() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ret_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object msg_ = "";
      /**
       * <code>optional string msg = 2;</code>
       * @return Whether the msg field is set.
       */
      public boolean hasMsg() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string msg = 2;</code>
       * @return The msg.
       */
      public java.lang.String getMsg() {
        java.lang.Object ref = msg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            msg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string msg = 2;</code>
       * @return The bytes for msg.
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
        java.lang.Object ref = msg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string msg = 2;</code>
       * @param value The msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string msg = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsg() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msg_ = getDefaultInstance().getMsg();
        onChanged();
        return this;
      }
      /**
       * <code>optional string msg = 2;</code>
       * @param value The bytes for msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msg_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object traceId_ = "";
      /**
       * <code>optional string traceId = 3;</code>
       * @return Whether the traceId field is set.
       */
      public boolean hasTraceId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @return The traceId.
       */
      public java.lang.String getTraceId() {
        java.lang.Object ref = traceId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            traceId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @return The bytes for traceId.
       */
      public com.google.protobuf.ByteString
          getTraceIdBytes() {
        java.lang.Object ref = traceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          traceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @param value The traceId to set.
       * @return This builder for chaining.
       */
      public Builder setTraceId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        traceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTraceId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        traceId_ = getDefaultInstance().getTraceId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string traceId = 3;</code>
       * @param value The bytes for traceId to set.
       * @return This builder for chaining.
       */
      public Builder setTraceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        traceId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object context_ = "";
      /**
       * <code>optional string context = 4;</code>
       * @return Whether the context field is set.
       */
      public boolean hasContext() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string context = 4;</code>
       * @return The context.
       */
      public java.lang.String getContext() {
        java.lang.Object ref = context_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            context_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string context = 4;</code>
       * @return The bytes for context.
       */
      public com.google.protobuf.ByteString
          getContextBytes() {
        java.lang.Object ref = context_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          context_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string context = 4;</code>
       * @param value The context to set.
       * @return This builder for chaining.
       */
      public Builder setContext(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        context_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string context = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearContext() {
        bitField0_ = (bitField0_ & ~0x00000008);
        context_ = getDefaultInstance().getContext();
        onChanged();
        return this;
      }
      /**
       * <code>optional string context = 4;</code>
       * @param value The bytes for context to set.
       * @return This builder for chaining.
       */
      public Builder setContextBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        context_ = value;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.User.HopeInstruction> instructions_ =
        java.util.Collections.emptyList();
      private void ensureInstructionsIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          instructions_ = new java.util.ArrayList<com.yorha.proto.User.HopeInstruction>(instructions_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder> instructionsBuilder_;

      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public java.util.List<com.yorha.proto.User.HopeInstruction> getInstructionsList() {
        if (instructionsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(instructions_);
        } else {
          return instructionsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public int getInstructionsCount() {
        if (instructionsBuilder_ == null) {
          return instructions_.size();
        } else {
          return instructionsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstruction getInstructions(int index) {
        if (instructionsBuilder_ == null) {
          return instructions_.get(index);
        } else {
          return instructionsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder setInstructions(
          int index, com.yorha.proto.User.HopeInstruction value) {
        if (instructionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInstructionsIsMutable();
          instructions_.set(index, value);
          onChanged();
        } else {
          instructionsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder setInstructions(
          int index, com.yorha.proto.User.HopeInstruction.Builder builderForValue) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          instructions_.set(index, builderForValue.build());
          onChanged();
        } else {
          instructionsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addInstructions(com.yorha.proto.User.HopeInstruction value) {
        if (instructionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInstructionsIsMutable();
          instructions_.add(value);
          onChanged();
        } else {
          instructionsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addInstructions(
          int index, com.yorha.proto.User.HopeInstruction value) {
        if (instructionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInstructionsIsMutable();
          instructions_.add(index, value);
          onChanged();
        } else {
          instructionsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addInstructions(
          com.yorha.proto.User.HopeInstruction.Builder builderForValue) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          instructions_.add(builderForValue.build());
          onChanged();
        } else {
          instructionsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addInstructions(
          int index, com.yorha.proto.User.HopeInstruction.Builder builderForValue) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          instructions_.add(index, builderForValue.build());
          onChanged();
        } else {
          instructionsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder addAllInstructions(
          java.lang.Iterable<? extends com.yorha.proto.User.HopeInstruction> values) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, instructions_);
          onChanged();
        } else {
          instructionsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder clearInstructions() {
        if (instructionsBuilder_ == null) {
          instructions_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          instructionsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public Builder removeInstructions(int index) {
        if (instructionsBuilder_ == null) {
          ensureInstructionsIsMutable();
          instructions_.remove(index);
          onChanged();
        } else {
          instructionsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstruction.Builder getInstructionsBuilder(
          int index) {
        return getInstructionsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstructionOrBuilder getInstructionsOrBuilder(
          int index) {
        if (instructionsBuilder_ == null) {
          return instructions_.get(index);  } else {
          return instructionsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public java.util.List<? extends com.yorha.proto.User.HopeInstructionOrBuilder> 
           getInstructionsOrBuilderList() {
        if (instructionsBuilder_ != null) {
          return instructionsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(instructions_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstruction.Builder addInstructionsBuilder() {
        return getInstructionsFieldBuilder().addBuilder(
            com.yorha.proto.User.HopeInstruction.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public com.yorha.proto.User.HopeInstruction.Builder addInstructionsBuilder(
          int index) {
        return getInstructionsFieldBuilder().addBuilder(
            index, com.yorha.proto.User.HopeInstruction.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.HopeInstruction instructions = 5;</code>
       */
      public java.util.List<com.yorha.proto.User.HopeInstruction.Builder> 
           getInstructionsBuilderList() {
        return getInstructionsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder> 
          getInstructionsFieldBuilder() {
        if (instructionsBuilder_ == null) {
          instructionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder>(
                  instructions_,
                  ((bitField0_ & 0x00000010) != 0),
                  getParentForChildren(),
                  isClean());
          instructions_ = null;
        }
        return instructionsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JudgePayResult)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JudgePayResult)
    private static final com.yorha.proto.SsAntiAddiction.JudgePayResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.JudgePayResult();
    }

    public static com.yorha.proto.SsAntiAddiction.JudgePayResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JudgePayResult>
        PARSER = new com.google.protobuf.AbstractParser<JudgePayResult>() {
      @java.lang.Override
      public JudgePayResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JudgePayResult(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JudgePayResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JudgePayResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.JudgePayResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReportPayCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ReportPayCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
     * @return Whether the payInfo field is set.
     */
    boolean hasPayInfo();
    /**
     * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
     * @return The payInfo.
     */
    com.yorha.proto.SsAntiAddiction.PayInfo getPayInfo();
    /**
     * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
     */
    com.yorha.proto.SsAntiAddiction.PayInfoOrBuilder getPayInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ReportPayCmd}
   */
  public static final class ReportPayCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ReportPayCmd)
      ReportPayCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReportPayCmd.newBuilder() to construct.
    private ReportPayCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReportPayCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReportPayCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReportPayCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.SsAntiAddiction.PayInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = payInfo_.toBuilder();
              }
              payInfo_ = input.readMessage(com.yorha.proto.SsAntiAddiction.PayInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(payInfo_);
                payInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportPayCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportPayCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.ReportPayCmd.class, com.yorha.proto.SsAntiAddiction.ReportPayCmd.Builder.class);
    }

    private int bitField0_;
    public static final int PAYINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.SsAntiAddiction.PayInfo payInfo_;
    /**
     * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
     * @return Whether the payInfo field is set.
     */
    @java.lang.Override
    public boolean hasPayInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
     * @return The payInfo.
     */
    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.PayInfo getPayInfo() {
      return payInfo_ == null ? com.yorha.proto.SsAntiAddiction.PayInfo.getDefaultInstance() : payInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.PayInfoOrBuilder getPayInfoOrBuilder() {
      return payInfo_ == null ? com.yorha.proto.SsAntiAddiction.PayInfo.getDefaultInstance() : payInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPayInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPayInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.ReportPayCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.ReportPayCmd other = (com.yorha.proto.SsAntiAddiction.ReportPayCmd) obj;

      if (hasPayInfo() != other.hasPayInfo()) return false;
      if (hasPayInfo()) {
        if (!getPayInfo()
            .equals(other.getPayInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPayInfo()) {
        hash = (37 * hash) + PAYINFO_FIELD_NUMBER;
        hash = (53 * hash) + getPayInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.ReportPayCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ReportPayCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ReportPayCmd)
        com.yorha.proto.SsAntiAddiction.ReportPayCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportPayCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportPayCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.ReportPayCmd.class, com.yorha.proto.SsAntiAddiction.ReportPayCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.ReportPayCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPayInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (payInfoBuilder_ == null) {
          payInfo_ = null;
        } else {
          payInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportPayCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.ReportPayCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.ReportPayCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.ReportPayCmd build() {
        com.yorha.proto.SsAntiAddiction.ReportPayCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.ReportPayCmd buildPartial() {
        com.yorha.proto.SsAntiAddiction.ReportPayCmd result = new com.yorha.proto.SsAntiAddiction.ReportPayCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (payInfoBuilder_ == null) {
            result.payInfo_ = payInfo_;
          } else {
            result.payInfo_ = payInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.ReportPayCmd) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.ReportPayCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.ReportPayCmd other) {
        if (other == com.yorha.proto.SsAntiAddiction.ReportPayCmd.getDefaultInstance()) return this;
        if (other.hasPayInfo()) {
          mergePayInfo(other.getPayInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.ReportPayCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.ReportPayCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.SsAntiAddiction.PayInfo payInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAntiAddiction.PayInfo, com.yorha.proto.SsAntiAddiction.PayInfo.Builder, com.yorha.proto.SsAntiAddiction.PayInfoOrBuilder> payInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       * @return Whether the payInfo field is set.
       */
      public boolean hasPayInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       * @return The payInfo.
       */
      public com.yorha.proto.SsAntiAddiction.PayInfo getPayInfo() {
        if (payInfoBuilder_ == null) {
          return payInfo_ == null ? com.yorha.proto.SsAntiAddiction.PayInfo.getDefaultInstance() : payInfo_;
        } else {
          return payInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       */
      public Builder setPayInfo(com.yorha.proto.SsAntiAddiction.PayInfo value) {
        if (payInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          payInfo_ = value;
          onChanged();
        } else {
          payInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       */
      public Builder setPayInfo(
          com.yorha.proto.SsAntiAddiction.PayInfo.Builder builderForValue) {
        if (payInfoBuilder_ == null) {
          payInfo_ = builderForValue.build();
          onChanged();
        } else {
          payInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       */
      public Builder mergePayInfo(com.yorha.proto.SsAntiAddiction.PayInfo value) {
        if (payInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              payInfo_ != null &&
              payInfo_ != com.yorha.proto.SsAntiAddiction.PayInfo.getDefaultInstance()) {
            payInfo_ =
              com.yorha.proto.SsAntiAddiction.PayInfo.newBuilder(payInfo_).mergeFrom(value).buildPartial();
          } else {
            payInfo_ = value;
          }
          onChanged();
        } else {
          payInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       */
      public Builder clearPayInfo() {
        if (payInfoBuilder_ == null) {
          payInfo_ = null;
          onChanged();
        } else {
          payInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       */
      public com.yorha.proto.SsAntiAddiction.PayInfo.Builder getPayInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPayInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       */
      public com.yorha.proto.SsAntiAddiction.PayInfoOrBuilder getPayInfoOrBuilder() {
        if (payInfoBuilder_ != null) {
          return payInfoBuilder_.getMessageOrBuilder();
        } else {
          return payInfo_ == null ?
              com.yorha.proto.SsAntiAddiction.PayInfo.getDefaultInstance() : payInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PayInfo payInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAntiAddiction.PayInfo, com.yorha.proto.SsAntiAddiction.PayInfo.Builder, com.yorha.proto.SsAntiAddiction.PayInfoOrBuilder> 
          getPayInfoFieldBuilder() {
        if (payInfoBuilder_ == null) {
          payInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsAntiAddiction.PayInfo, com.yorha.proto.SsAntiAddiction.PayInfo.Builder, com.yorha.proto.SsAntiAddiction.PayInfoOrBuilder>(
                  getPayInfo(),
                  getParentForChildren(),
                  isClean());
          payInfo_ = null;
        }
        return payInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ReportPayCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ReportPayCmd)
    private static final com.yorha.proto.SsAntiAddiction.ReportPayCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.ReportPayCmd();
    }

    public static com.yorha.proto.SsAntiAddiction.ReportPayCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ReportPayCmd>
        PARSER = new com.google.protobuf.AbstractParser<ReportPayCmd>() {
      @java.lang.Override
      public ReportPayCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReportPayCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReportPayCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReportPayCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.ReportPayCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReportExecuteCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ReportExecuteCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string userId = 1;</code>
     * @return Whether the userId field is set.
     */
    boolean hasUserId();
    /**
     * <code>optional string userId = 1;</code>
     * @return The userId.
     */
    java.lang.String getUserId();
    /**
     * <code>optional string userId = 1;</code>
     * @return The bytes for userId.
     */
    com.google.protobuf.ByteString
        getUserIdBytes();

    /**
     * <code>optional string ruleName = 2;</code>
     * @return Whether the ruleName field is set.
     */
    boolean hasRuleName();
    /**
     * <code>optional string ruleName = 2;</code>
     * @return The ruleName.
     */
    java.lang.String getRuleName();
    /**
     * <code>optional string ruleName = 2;</code>
     * @return The bytes for ruleName.
     */
    com.google.protobuf.ByteString
        getRuleNameBytes();

    /**
     * <code>optional string instrTraceId = 3;</code>
     * @return Whether the instrTraceId field is set.
     */
    boolean hasInstrTraceId();
    /**
     * <code>optional string instrTraceId = 3;</code>
     * @return The instrTraceId.
     */
    java.lang.String getInstrTraceId();
    /**
     * <code>optional string instrTraceId = 3;</code>
     * @return The bytes for instrTraceId.
     */
    com.google.protobuf.ByteString
        getInstrTraceIdBytes();

    /**
     * <code>optional int64 execTime = 4;</code>
     * @return Whether the execTime field is set.
     */
    boolean hasExecTime();
    /**
     * <code>optional int64 execTime = 4;</code>
     * @return The execTime.
     */
    long getExecTime();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ReportExecuteCmd}
   */
  public static final class ReportExecuteCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ReportExecuteCmd)
      ReportExecuteCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReportExecuteCmd.newBuilder() to construct.
    private ReportExecuteCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReportExecuteCmd() {
      userId_ = "";
      ruleName_ = "";
      instrTraceId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReportExecuteCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReportExecuteCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              userId_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              ruleName_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              instrTraceId_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              execTime_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportExecuteCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportExecuteCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.ReportExecuteCmd.class, com.yorha.proto.SsAntiAddiction.ReportExecuteCmd.Builder.class);
    }

    private int bitField0_;
    public static final int USERID_FIELD_NUMBER = 1;
    private volatile java.lang.Object userId_;
    /**
     * <code>optional string userId = 1;</code>
     * @return Whether the userId field is set.
     */
    @java.lang.Override
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string userId = 1;</code>
     * @return The userId.
     */
    @java.lang.Override
    public java.lang.String getUserId() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          userId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string userId = 1;</code>
     * @return The bytes for userId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserIdBytes() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RULENAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object ruleName_;
    /**
     * <code>optional string ruleName = 2;</code>
     * @return Whether the ruleName field is set.
     */
    @java.lang.Override
    public boolean hasRuleName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string ruleName = 2;</code>
     * @return The ruleName.
     */
    @java.lang.Override
    public java.lang.String getRuleName() {
      java.lang.Object ref = ruleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ruleName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string ruleName = 2;</code>
     * @return The bytes for ruleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getRuleNameBytes() {
      java.lang.Object ref = ruleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ruleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INSTRTRACEID_FIELD_NUMBER = 3;
    private volatile java.lang.Object instrTraceId_;
    /**
     * <code>optional string instrTraceId = 3;</code>
     * @return Whether the instrTraceId field is set.
     */
    @java.lang.Override
    public boolean hasInstrTraceId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string instrTraceId = 3;</code>
     * @return The instrTraceId.
     */
    @java.lang.Override
    public java.lang.String getInstrTraceId() {
      java.lang.Object ref = instrTraceId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          instrTraceId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string instrTraceId = 3;</code>
     * @return The bytes for instrTraceId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInstrTraceIdBytes() {
      java.lang.Object ref = instrTraceId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        instrTraceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EXECTIME_FIELD_NUMBER = 4;
    private long execTime_;
    /**
     * <code>optional int64 execTime = 4;</code>
     * @return Whether the execTime field is set.
     */
    @java.lang.Override
    public boolean hasExecTime() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 execTime = 4;</code>
     * @return The execTime.
     */
    @java.lang.Override
    public long getExecTime() {
      return execTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, ruleName_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, instrTraceId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, execTime_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, ruleName_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, instrTraceId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, execTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.ReportExecuteCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.ReportExecuteCmd other = (com.yorha.proto.SsAntiAddiction.ReportExecuteCmd) obj;

      if (hasUserId() != other.hasUserId()) return false;
      if (hasUserId()) {
        if (!getUserId()
            .equals(other.getUserId())) return false;
      }
      if (hasRuleName() != other.hasRuleName()) return false;
      if (hasRuleName()) {
        if (!getRuleName()
            .equals(other.getRuleName())) return false;
      }
      if (hasInstrTraceId() != other.hasInstrTraceId()) return false;
      if (hasInstrTraceId()) {
        if (!getInstrTraceId()
            .equals(other.getInstrTraceId())) return false;
      }
      if (hasExecTime() != other.hasExecTime()) return false;
      if (hasExecTime()) {
        if (getExecTime()
            != other.getExecTime()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUserId()) {
        hash = (37 * hash) + USERID_FIELD_NUMBER;
        hash = (53 * hash) + getUserId().hashCode();
      }
      if (hasRuleName()) {
        hash = (37 * hash) + RULENAME_FIELD_NUMBER;
        hash = (53 * hash) + getRuleName().hashCode();
      }
      if (hasInstrTraceId()) {
        hash = (37 * hash) + INSTRTRACEID_FIELD_NUMBER;
        hash = (53 * hash) + getInstrTraceId().hashCode();
      }
      if (hasExecTime()) {
        hash = (37 * hash) + EXECTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getExecTime());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.ReportExecuteCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ReportExecuteCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ReportExecuteCmd)
        com.yorha.proto.SsAntiAddiction.ReportExecuteCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportExecuteCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportExecuteCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.ReportExecuteCmd.class, com.yorha.proto.SsAntiAddiction.ReportExecuteCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.ReportExecuteCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        userId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        ruleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        instrTraceId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        execTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_ReportExecuteCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.ReportExecuteCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.ReportExecuteCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.ReportExecuteCmd build() {
        com.yorha.proto.SsAntiAddiction.ReportExecuteCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.ReportExecuteCmd buildPartial() {
        com.yorha.proto.SsAntiAddiction.ReportExecuteCmd result = new com.yorha.proto.SsAntiAddiction.ReportExecuteCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.ruleName_ = ruleName_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.instrTraceId_ = instrTraceId_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.execTime_ = execTime_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.ReportExecuteCmd) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.ReportExecuteCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.ReportExecuteCmd other) {
        if (other == com.yorha.proto.SsAntiAddiction.ReportExecuteCmd.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          bitField0_ |= 0x00000001;
          userId_ = other.userId_;
          onChanged();
        }
        if (other.hasRuleName()) {
          bitField0_ |= 0x00000002;
          ruleName_ = other.ruleName_;
          onChanged();
        }
        if (other.hasInstrTraceId()) {
          bitField0_ |= 0x00000004;
          instrTraceId_ = other.instrTraceId_;
          onChanged();
        }
        if (other.hasExecTime()) {
          setExecTime(other.getExecTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.ReportExecuteCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.ReportExecuteCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object userId_ = "";
      /**
       * <code>optional string userId = 1;</code>
       * @return Whether the userId field is set.
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return The userId.
       */
      public java.lang.String getUserId() {
        java.lang.Object ref = userId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            userId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return The bytes for userId.
       */
      public com.google.protobuf.ByteString
          getUserIdBytes() {
        java.lang.Object ref = userId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string userId = 1;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = getDefaultInstance().getUserId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 1;</code>
       * @param value The bytes for userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ruleName_ = "";
      /**
       * <code>optional string ruleName = 2;</code>
       * @return Whether the ruleName field is set.
       */
      public boolean hasRuleName() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string ruleName = 2;</code>
       * @return The ruleName.
       */
      public java.lang.String getRuleName() {
        java.lang.Object ref = ruleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ruleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string ruleName = 2;</code>
       * @return The bytes for ruleName.
       */
      public com.google.protobuf.ByteString
          getRuleNameBytes() {
        java.lang.Object ref = ruleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ruleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string ruleName = 2;</code>
       * @param value The ruleName to set.
       * @return This builder for chaining.
       */
      public Builder setRuleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        ruleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string ruleName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRuleName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ruleName_ = getDefaultInstance().getRuleName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string ruleName = 2;</code>
       * @param value The bytes for ruleName to set.
       * @return This builder for chaining.
       */
      public Builder setRuleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        ruleName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object instrTraceId_ = "";
      /**
       * <code>optional string instrTraceId = 3;</code>
       * @return Whether the instrTraceId field is set.
       */
      public boolean hasInstrTraceId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string instrTraceId = 3;</code>
       * @return The instrTraceId.
       */
      public java.lang.String getInstrTraceId() {
        java.lang.Object ref = instrTraceId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            instrTraceId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string instrTraceId = 3;</code>
       * @return The bytes for instrTraceId.
       */
      public com.google.protobuf.ByteString
          getInstrTraceIdBytes() {
        java.lang.Object ref = instrTraceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          instrTraceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string instrTraceId = 3;</code>
       * @param value The instrTraceId to set.
       * @return This builder for chaining.
       */
      public Builder setInstrTraceId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        instrTraceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string instrTraceId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearInstrTraceId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        instrTraceId_ = getDefaultInstance().getInstrTraceId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string instrTraceId = 3;</code>
       * @param value The bytes for instrTraceId to set.
       * @return This builder for chaining.
       */
      public Builder setInstrTraceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        instrTraceId_ = value;
        onChanged();
        return this;
      }

      private long execTime_ ;
      /**
       * <code>optional int64 execTime = 4;</code>
       * @return Whether the execTime field is set.
       */
      @java.lang.Override
      public boolean hasExecTime() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int64 execTime = 4;</code>
       * @return The execTime.
       */
      @java.lang.Override
      public long getExecTime() {
        return execTime_;
      }
      /**
       * <code>optional int64 execTime = 4;</code>
       * @param value The execTime to set.
       * @return This builder for chaining.
       */
      public Builder setExecTime(long value) {
        bitField0_ |= 0x00000008;
        execTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 execTime = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearExecTime() {
        bitField0_ = (bitField0_ & ~0x00000008);
        execTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ReportExecuteCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ReportExecuteCmd)
    private static final com.yorha.proto.SsAntiAddiction.ReportExecuteCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.ReportExecuteCmd();
    }

    public static com.yorha.proto.SsAntiAddiction.ReportExecuteCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ReportExecuteCmd>
        PARSER = new com.google.protobuf.AbstractParser<ReportExecuteCmd>() {
      @java.lang.Override
      public ReportExecuteCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReportExecuteCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReportExecuteCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReportExecuteCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.ReportExecuteCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeviceInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DeviceInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string outerIp = 1;</code>
     * @return Whether the outerIp field is set.
     */
    boolean hasOuterIp();
    /**
     * <code>optional string outerIp = 1;</code>
     * @return The outerIp.
     */
    java.lang.String getOuterIp();
    /**
     * <code>optional string outerIp = 1;</code>
     * @return The bytes for outerIp.
     */
    com.google.protobuf.ByteString
        getOuterIpBytes();

    /**
     * <code>optional string deviceId = 2;</code>
     * @return Whether the deviceId field is set.
     */
    boolean hasDeviceId();
    /**
     * <code>optional string deviceId = 2;</code>
     * @return The deviceId.
     */
    java.lang.String getDeviceId();
    /**
     * <code>optional string deviceId = 2;</code>
     * @return The bytes for deviceId.
     */
    com.google.protobuf.ByteString
        getDeviceIdBytes();

    /**
     * <code>optional string qimei36 = 3;</code>
     * @return Whether the qimei36 field is set.
     */
    boolean hasQimei36();
    /**
     * <code>optional string qimei36 = 3;</code>
     * @return The qimei36.
     */
    java.lang.String getQimei36();
    /**
     * <code>optional string qimei36 = 3;</code>
     * @return The bytes for qimei36.
     */
    com.google.protobuf.ByteString
        getQimei36Bytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DeviceInfo}
   */
  public static final class DeviceInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DeviceInfo)
      DeviceInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeviceInfo.newBuilder() to construct.
    private DeviceInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceInfo() {
      outerIp_ = "";
      deviceId_ = "";
      qimei36_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DeviceInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DeviceInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              outerIp_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              deviceId_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              qimei36_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_DeviceInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_DeviceInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.DeviceInfo.class, com.yorha.proto.SsAntiAddiction.DeviceInfo.Builder.class);
    }

    private int bitField0_;
    public static final int OUTERIP_FIELD_NUMBER = 1;
    private volatile java.lang.Object outerIp_;
    /**
     * <code>optional string outerIp = 1;</code>
     * @return Whether the outerIp field is set.
     */
    @java.lang.Override
    public boolean hasOuterIp() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string outerIp = 1;</code>
     * @return The outerIp.
     */
    @java.lang.Override
    public java.lang.String getOuterIp() {
      java.lang.Object ref = outerIp_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          outerIp_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string outerIp = 1;</code>
     * @return The bytes for outerIp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOuterIpBytes() {
      java.lang.Object ref = outerIp_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        outerIp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEVICEID_FIELD_NUMBER = 2;
    private volatile java.lang.Object deviceId_;
    /**
     * <code>optional string deviceId = 2;</code>
     * @return Whether the deviceId field is set.
     */
    @java.lang.Override
    public boolean hasDeviceId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string deviceId = 2;</code>
     * @return The deviceId.
     */
    @java.lang.Override
    public java.lang.String getDeviceId() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          deviceId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string deviceId = 2;</code>
     * @return The bytes for deviceId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      java.lang.Object ref = deviceId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int QIMEI36_FIELD_NUMBER = 3;
    private volatile java.lang.Object qimei36_;
    /**
     * <code>optional string qimei36 = 3;</code>
     * @return Whether the qimei36 field is set.
     */
    @java.lang.Override
    public boolean hasQimei36() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string qimei36 = 3;</code>
     * @return The qimei36.
     */
    @java.lang.Override
    public java.lang.String getQimei36() {
      java.lang.Object ref = qimei36_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          qimei36_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string qimei36 = 3;</code>
     * @return The bytes for qimei36.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getQimei36Bytes() {
      java.lang.Object ref = qimei36_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        qimei36_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, outerIp_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, deviceId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, qimei36_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, outerIp_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, deviceId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, qimei36_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.DeviceInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.DeviceInfo other = (com.yorha.proto.SsAntiAddiction.DeviceInfo) obj;

      if (hasOuterIp() != other.hasOuterIp()) return false;
      if (hasOuterIp()) {
        if (!getOuterIp()
            .equals(other.getOuterIp())) return false;
      }
      if (hasDeviceId() != other.hasDeviceId()) return false;
      if (hasDeviceId()) {
        if (!getDeviceId()
            .equals(other.getDeviceId())) return false;
      }
      if (hasQimei36() != other.hasQimei36()) return false;
      if (hasQimei36()) {
        if (!getQimei36()
            .equals(other.getQimei36())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOuterIp()) {
        hash = (37 * hash) + OUTERIP_FIELD_NUMBER;
        hash = (53 * hash) + getOuterIp().hashCode();
      }
      if (hasDeviceId()) {
        hash = (37 * hash) + DEVICEID_FIELD_NUMBER;
        hash = (53 * hash) + getDeviceId().hashCode();
      }
      if (hasQimei36()) {
        hash = (37 * hash) + QIMEI36_FIELD_NUMBER;
        hash = (53 * hash) + getQimei36().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.DeviceInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.DeviceInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DeviceInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DeviceInfo)
        com.yorha.proto.SsAntiAddiction.DeviceInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_DeviceInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_DeviceInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.DeviceInfo.class, com.yorha.proto.SsAntiAddiction.DeviceInfo.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.DeviceInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        outerIp_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        deviceId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        qimei36_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_DeviceInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.DeviceInfo getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.DeviceInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.DeviceInfo build() {
        com.yorha.proto.SsAntiAddiction.DeviceInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.DeviceInfo buildPartial() {
        com.yorha.proto.SsAntiAddiction.DeviceInfo result = new com.yorha.proto.SsAntiAddiction.DeviceInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.outerIp_ = outerIp_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.deviceId_ = deviceId_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.qimei36_ = qimei36_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.DeviceInfo) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.DeviceInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.DeviceInfo other) {
        if (other == com.yorha.proto.SsAntiAddiction.DeviceInfo.getDefaultInstance()) return this;
        if (other.hasOuterIp()) {
          bitField0_ |= 0x00000001;
          outerIp_ = other.outerIp_;
          onChanged();
        }
        if (other.hasDeviceId()) {
          bitField0_ |= 0x00000002;
          deviceId_ = other.deviceId_;
          onChanged();
        }
        if (other.hasQimei36()) {
          bitField0_ |= 0x00000004;
          qimei36_ = other.qimei36_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.DeviceInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.DeviceInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object outerIp_ = "";
      /**
       * <code>optional string outerIp = 1;</code>
       * @return Whether the outerIp field is set.
       */
      public boolean hasOuterIp() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string outerIp = 1;</code>
       * @return The outerIp.
       */
      public java.lang.String getOuterIp() {
        java.lang.Object ref = outerIp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            outerIp_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string outerIp = 1;</code>
       * @return The bytes for outerIp.
       */
      public com.google.protobuf.ByteString
          getOuterIpBytes() {
        java.lang.Object ref = outerIp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          outerIp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string outerIp = 1;</code>
       * @param value The outerIp to set.
       * @return This builder for chaining.
       */
      public Builder setOuterIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        outerIp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string outerIp = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOuterIp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        outerIp_ = getDefaultInstance().getOuterIp();
        onChanged();
        return this;
      }
      /**
       * <code>optional string outerIp = 1;</code>
       * @param value The bytes for outerIp to set.
       * @return This builder for chaining.
       */
      public Builder setOuterIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        outerIp_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object deviceId_ = "";
      /**
       * <code>optional string deviceId = 2;</code>
       * @return Whether the deviceId field is set.
       */
      public boolean hasDeviceId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string deviceId = 2;</code>
       * @return The deviceId.
       */
      public java.lang.String getDeviceId() {
        java.lang.Object ref = deviceId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            deviceId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string deviceId = 2;</code>
       * @return The bytes for deviceId.
       */
      public com.google.protobuf.ByteString
          getDeviceIdBytes() {
        java.lang.Object ref = deviceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          deviceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string deviceId = 2;</code>
       * @param value The deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        deviceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string deviceId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        deviceId_ = getDefaultInstance().getDeviceId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string deviceId = 2;</code>
       * @param value The bytes for deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        deviceId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object qimei36_ = "";
      /**
       * <code>optional string qimei36 = 3;</code>
       * @return Whether the qimei36 field is set.
       */
      public boolean hasQimei36() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string qimei36 = 3;</code>
       * @return The qimei36.
       */
      public java.lang.String getQimei36() {
        java.lang.Object ref = qimei36_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            qimei36_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string qimei36 = 3;</code>
       * @return The bytes for qimei36.
       */
      public com.google.protobuf.ByteString
          getQimei36Bytes() {
        java.lang.Object ref = qimei36_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          qimei36_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string qimei36 = 3;</code>
       * @param value The qimei36 to set.
       * @return This builder for chaining.
       */
      public Builder setQimei36(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        qimei36_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string qimei36 = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearQimei36() {
        bitField0_ = (bitField0_ & ~0x00000004);
        qimei36_ = getDefaultInstance().getQimei36();
        onChanged();
        return this;
      }
      /**
       * <code>optional string qimei36 = 3;</code>
       * @param value The bytes for qimei36 to set.
       * @return This builder for chaining.
       */
      public Builder setQimei36Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        qimei36_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DeviceInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DeviceInfo)
    private static final com.yorha.proto.SsAntiAddiction.DeviceInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.DeviceInfo();
    }

    public static com.yorha.proto.SsAntiAddiction.DeviceInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DeviceInfo>
        PARSER = new com.google.protobuf.AbstractParser<DeviceInfo>() {
      @java.lang.Override
      public DeviceInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DeviceInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DeviceInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DeviceInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.DeviceInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PayInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PayInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string userId = 1;</code>
     * @return Whether the userId field is set.
     */
    boolean hasUserId();
    /**
     * <code>optional string userId = 1;</code>
     * @return The userId.
     */
    java.lang.String getUserId();
    /**
     * <code>optional string userId = 1;</code>
     * @return The bytes for userId.
     */
    com.google.protobuf.ByteString
        getUserIdBytes();

    /**
     * <code>optional int32 payAmount = 2;</code>
     * @return Whether the payAmount field is set.
     */
    boolean hasPayAmount();
    /**
     * <code>optional int32 payAmount = 2;</code>
     * @return The payAmount.
     */
    int getPayAmount();

    /**
     * <code>optional int32 payTimestamp = 3;</code>
     * @return Whether the payTimestamp field is set.
     */
    boolean hasPayTimestamp();
    /**
     * <code>optional int32 payTimestamp = 3;</code>
     * @return The payTimestamp.
     */
    int getPayTimestamp();

    /**
     * <code>optional string payOrderId = 4;</code>
     * @return Whether the payOrderId field is set.
     */
    boolean hasPayOrderId();
    /**
     * <code>optional string payOrderId = 4;</code>
     * @return The payOrderId.
     */
    java.lang.String getPayOrderId();
    /**
     * <code>optional string payOrderId = 4;</code>
     * @return The bytes for payOrderId.
     */
    com.google.protobuf.ByteString
        getPayOrderIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PayInfo}
   */
  public static final class PayInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PayInfo)
      PayInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PayInfo.newBuilder() to construct.
    private PayInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PayInfo() {
      userId_ = "";
      payOrderId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PayInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PayInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              userId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              payAmount_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              payTimestamp_ = input.readInt32();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              payOrderId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_PayInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_PayInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAntiAddiction.PayInfo.class, com.yorha.proto.SsAntiAddiction.PayInfo.Builder.class);
    }

    private int bitField0_;
    public static final int USERID_FIELD_NUMBER = 1;
    private volatile java.lang.Object userId_;
    /**
     * <code>optional string userId = 1;</code>
     * @return Whether the userId field is set.
     */
    @java.lang.Override
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string userId = 1;</code>
     * @return The userId.
     */
    @java.lang.Override
    public java.lang.String getUserId() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          userId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string userId = 1;</code>
     * @return The bytes for userId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserIdBytes() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PAYAMOUNT_FIELD_NUMBER = 2;
    private int payAmount_;
    /**
     * <code>optional int32 payAmount = 2;</code>
     * @return Whether the payAmount field is set.
     */
    @java.lang.Override
    public boolean hasPayAmount() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 payAmount = 2;</code>
     * @return The payAmount.
     */
    @java.lang.Override
    public int getPayAmount() {
      return payAmount_;
    }

    public static final int PAYTIMESTAMP_FIELD_NUMBER = 3;
    private int payTimestamp_;
    /**
     * <code>optional int32 payTimestamp = 3;</code>
     * @return Whether the payTimestamp field is set.
     */
    @java.lang.Override
    public boolean hasPayTimestamp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 payTimestamp = 3;</code>
     * @return The payTimestamp.
     */
    @java.lang.Override
    public int getPayTimestamp() {
      return payTimestamp_;
    }

    public static final int PAYORDERID_FIELD_NUMBER = 4;
    private volatile java.lang.Object payOrderId_;
    /**
     * <code>optional string payOrderId = 4;</code>
     * @return Whether the payOrderId field is set.
     */
    @java.lang.Override
    public boolean hasPayOrderId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string payOrderId = 4;</code>
     * @return The payOrderId.
     */
    @java.lang.Override
    public java.lang.String getPayOrderId() {
      java.lang.Object ref = payOrderId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          payOrderId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string payOrderId = 4;</code>
     * @return The bytes for payOrderId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPayOrderIdBytes() {
      java.lang.Object ref = payOrderId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payOrderId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, payAmount_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, payTimestamp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, payOrderId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, payAmount_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, payTimestamp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, payOrderId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAntiAddiction.PayInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAntiAddiction.PayInfo other = (com.yorha.proto.SsAntiAddiction.PayInfo) obj;

      if (hasUserId() != other.hasUserId()) return false;
      if (hasUserId()) {
        if (!getUserId()
            .equals(other.getUserId())) return false;
      }
      if (hasPayAmount() != other.hasPayAmount()) return false;
      if (hasPayAmount()) {
        if (getPayAmount()
            != other.getPayAmount()) return false;
      }
      if (hasPayTimestamp() != other.hasPayTimestamp()) return false;
      if (hasPayTimestamp()) {
        if (getPayTimestamp()
            != other.getPayTimestamp()) return false;
      }
      if (hasPayOrderId() != other.hasPayOrderId()) return false;
      if (hasPayOrderId()) {
        if (!getPayOrderId()
            .equals(other.getPayOrderId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUserId()) {
        hash = (37 * hash) + USERID_FIELD_NUMBER;
        hash = (53 * hash) + getUserId().hashCode();
      }
      if (hasPayAmount()) {
        hash = (37 * hash) + PAYAMOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getPayAmount();
      }
      if (hasPayTimestamp()) {
        hash = (37 * hash) + PAYTIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + getPayTimestamp();
      }
      if (hasPayOrderId()) {
        hash = (37 * hash) + PAYORDERID_FIELD_NUMBER;
        hash = (53 * hash) + getPayOrderId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAntiAddiction.PayInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAntiAddiction.PayInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PayInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PayInfo)
        com.yorha.proto.SsAntiAddiction.PayInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_PayInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_PayInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAntiAddiction.PayInfo.class, com.yorha.proto.SsAntiAddiction.PayInfo.Builder.class);
      }

      // Construct using com.yorha.proto.SsAntiAddiction.PayInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        userId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        payAmount_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        payTimestamp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        payOrderId_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAntiAddiction.internal_static_com_yorha_proto_PayInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.PayInfo getDefaultInstanceForType() {
        return com.yorha.proto.SsAntiAddiction.PayInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.PayInfo build() {
        com.yorha.proto.SsAntiAddiction.PayInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAntiAddiction.PayInfo buildPartial() {
        com.yorha.proto.SsAntiAddiction.PayInfo result = new com.yorha.proto.SsAntiAddiction.PayInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.payAmount_ = payAmount_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.payTimestamp_ = payTimestamp_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.payOrderId_ = payOrderId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAntiAddiction.PayInfo) {
          return mergeFrom((com.yorha.proto.SsAntiAddiction.PayInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAntiAddiction.PayInfo other) {
        if (other == com.yorha.proto.SsAntiAddiction.PayInfo.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          bitField0_ |= 0x00000001;
          userId_ = other.userId_;
          onChanged();
        }
        if (other.hasPayAmount()) {
          setPayAmount(other.getPayAmount());
        }
        if (other.hasPayTimestamp()) {
          setPayTimestamp(other.getPayTimestamp());
        }
        if (other.hasPayOrderId()) {
          bitField0_ |= 0x00000008;
          payOrderId_ = other.payOrderId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAntiAddiction.PayInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAntiAddiction.PayInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object userId_ = "";
      /**
       * <code>optional string userId = 1;</code>
       * @return Whether the userId field is set.
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return The userId.
       */
      public java.lang.String getUserId() {
        java.lang.Object ref = userId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            userId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return The bytes for userId.
       */
      public com.google.protobuf.ByteString
          getUserIdBytes() {
        java.lang.Object ref = userId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string userId = 1;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = getDefaultInstance().getUserId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 1;</code>
       * @param value The bytes for userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }

      private int payAmount_ ;
      /**
       * <code>optional int32 payAmount = 2;</code>
       * @return Whether the payAmount field is set.
       */
      @java.lang.Override
      public boolean hasPayAmount() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 payAmount = 2;</code>
       * @return The payAmount.
       */
      @java.lang.Override
      public int getPayAmount() {
        return payAmount_;
      }
      /**
       * <code>optional int32 payAmount = 2;</code>
       * @param value The payAmount to set.
       * @return This builder for chaining.
       */
      public Builder setPayAmount(int value) {
        bitField0_ |= 0x00000002;
        payAmount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 payAmount = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayAmount() {
        bitField0_ = (bitField0_ & ~0x00000002);
        payAmount_ = 0;
        onChanged();
        return this;
      }

      private int payTimestamp_ ;
      /**
       * <code>optional int32 payTimestamp = 3;</code>
       * @return Whether the payTimestamp field is set.
       */
      @java.lang.Override
      public boolean hasPayTimestamp() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 payTimestamp = 3;</code>
       * @return The payTimestamp.
       */
      @java.lang.Override
      public int getPayTimestamp() {
        return payTimestamp_;
      }
      /**
       * <code>optional int32 payTimestamp = 3;</code>
       * @param value The payTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setPayTimestamp(int value) {
        bitField0_ |= 0x00000004;
        payTimestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 payTimestamp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        payTimestamp_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object payOrderId_ = "";
      /**
       * <code>optional string payOrderId = 4;</code>
       * @return Whether the payOrderId field is set.
       */
      public boolean hasPayOrderId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string payOrderId = 4;</code>
       * @return The payOrderId.
       */
      public java.lang.String getPayOrderId() {
        java.lang.Object ref = payOrderId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            payOrderId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string payOrderId = 4;</code>
       * @return The bytes for payOrderId.
       */
      public com.google.protobuf.ByteString
          getPayOrderIdBytes() {
        java.lang.Object ref = payOrderId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          payOrderId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string payOrderId = 4;</code>
       * @param value The payOrderId to set.
       * @return This builder for chaining.
       */
      public Builder setPayOrderId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        payOrderId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string payOrderId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayOrderId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        payOrderId_ = getDefaultInstance().getPayOrderId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string payOrderId = 4;</code>
       * @param value The bytes for payOrderId to set.
       * @return This builder for chaining.
       */
      public Builder setPayOrderIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        payOrderId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PayInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PayInfo)
    private static final com.yorha.proto.SsAntiAddiction.PayInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAntiAddiction.PayInfo();
    }

    public static com.yorha.proto.SsAntiAddiction.PayInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PayInfo>
        PARSER = new com.google.protobuf.AbstractParser<PayInfo>() {
      @java.lang.Override
      public PayInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PayInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PayInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PayInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAntiAddiction.PayInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JudgeTimingAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JudgeTimingAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JudgeTimingAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JudgeTimingAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JudgeTimingResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JudgeTimingResult_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JudgePayAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JudgePayAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JudgePayAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JudgePayAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JudgePayResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JudgePayResult_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ReportPayCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ReportPayCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ReportExecuteCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ReportExecuteCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DeviceInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DeviceInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PayInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PayInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n2ss_proto/gen/antiAddiction/ss_anti_add" +
      "iction.proto\022\017com.yorha.proto\032\036ss_proto/" +
      "gen/common/user.proto\"u\n\016JudgeTimingAsk\022" +
      "\020\n\010factType\030\001 \001(\005\022\016\n\006userId\030\002 \001(\t\022\020\n\010dur" +
      "ation\030\003 \001(\005\022/\n\ndeviceInfo\030\004 \001(\0132\033.com.yo" +
      "rha.proto.DeviceInfo\"D\n\016JudgeTimingAns\0222" +
      "\n\006result\030\001 \001(\0132\".com.yorha.proto.JudgeTi" +
      "mingResult\"\231\001\n\021JudgeTimingResult\022\013\n\003ret\030" +
      "\001 \001(\005\022\013\n\003msg\030\002 \001(\t\022\017\n\007traceId\030\003 \001(\t\022\017\n\007c" +
      "ontext\030\004 \001(\t\0226\n\014instructions\030\005 \003(\0132 .com" +
      ".yorha.proto.HopeInstruction\022\020\n\010duration" +
      "\030\006 \001(\005\"0\n\013JudgePayAsk\022\016\n\006userId\030\001 \001(\t\022\021\n" +
      "\tpayAmount\030\002 \001(\005\">\n\013JudgePayAns\022/\n\006resul" +
      "t\030\001 \001(\0132\037.com.yorha.proto.JudgePayResult" +
      "\"\204\001\n\016JudgePayResult\022\013\n\003ret\030\001 \001(\005\022\013\n\003msg\030" +
      "\002 \001(\t\022\017\n\007traceId\030\003 \001(\t\022\017\n\007context\030\004 \001(\t\022" +
      "6\n\014instructions\030\005 \003(\0132 .com.yorha.proto." +
      "HopeInstruction\"9\n\014ReportPayCmd\022)\n\007payIn" +
      "fo\030\001 \001(\0132\030.com.yorha.proto.PayInfo\"\\\n\020Re" +
      "portExecuteCmd\022\016\n\006userId\030\001 \001(\t\022\020\n\010ruleNa" +
      "me\030\002 \001(\t\022\024\n\014instrTraceId\030\003 \001(\t\022\020\n\010execTi" +
      "me\030\004 \001(\003\"@\n\nDeviceInfo\022\017\n\007outerIp\030\001 \001(\t\022" +
      "\020\n\010deviceId\030\002 \001(\t\022\017\n\007qimei36\030\003 \001(\t\"V\n\007Pa" +
      "yInfo\022\016\n\006userId\030\001 \001(\t\022\021\n\tpayAmount\030\002 \001(\005" +
      "\022\024\n\014payTimestamp\030\003 \001(\005\022\022\n\npayOrderId\030\004 \001" +
      "(\tB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.User.getDescriptor(),
        });
    internal_static_com_yorha_proto_JudgeTimingAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_JudgeTimingAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JudgeTimingAsk_descriptor,
        new java.lang.String[] { "FactType", "UserId", "Duration", "DeviceInfo", });
    internal_static_com_yorha_proto_JudgeTimingAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_JudgeTimingAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JudgeTimingAns_descriptor,
        new java.lang.String[] { "Result", });
    internal_static_com_yorha_proto_JudgeTimingResult_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_JudgeTimingResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JudgeTimingResult_descriptor,
        new java.lang.String[] { "Ret", "Msg", "TraceId", "Context", "Instructions", "Duration", });
    internal_static_com_yorha_proto_JudgePayAsk_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_JudgePayAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JudgePayAsk_descriptor,
        new java.lang.String[] { "UserId", "PayAmount", });
    internal_static_com_yorha_proto_JudgePayAns_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_JudgePayAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JudgePayAns_descriptor,
        new java.lang.String[] { "Result", });
    internal_static_com_yorha_proto_JudgePayResult_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_JudgePayResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JudgePayResult_descriptor,
        new java.lang.String[] { "Ret", "Msg", "TraceId", "Context", "Instructions", });
    internal_static_com_yorha_proto_ReportPayCmd_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_ReportPayCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ReportPayCmd_descriptor,
        new java.lang.String[] { "PayInfo", });
    internal_static_com_yorha_proto_ReportExecuteCmd_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_ReportExecuteCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ReportExecuteCmd_descriptor,
        new java.lang.String[] { "UserId", "RuleName", "InstrTraceId", "ExecTime", });
    internal_static_com_yorha_proto_DeviceInfo_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_DeviceInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DeviceInfo_descriptor,
        new java.lang.String[] { "OuterIp", "DeviceId", "Qimei36", });
    internal_static_com_yorha_proto_PayInfo_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_PayInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PayInfo_descriptor,
        new java.lang.String[] { "UserId", "PayAmount", "PayTimestamp", "PayOrderId", });
    com.yorha.proto.User.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

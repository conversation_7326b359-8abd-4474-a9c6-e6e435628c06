// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/monster/monsterPB.proto

package com.yorha.proto;

public final class MonsterPB {
  private MonsterPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MonsterEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MonsterEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 monsterId = 1;</code>
     * @return Whether the monsterId field is set.
     */
    boolean hasMonsterId();
    /**
     * <code>optional int32 monsterId = 1;</code>
     * @return The monsterId.
     */
    int getMonsterId();

    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     * @return Whether the troop field is set.
     */
    boolean hasTroop();
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     * @return The troop.
     */
    com.yorha.proto.StructPlayerPB.TroopPB getTroop();
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     */
    com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder();

    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     * @return Whether the move field is set.
     */
    boolean hasMove();
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     * @return The move.
     */
    com.yorha.proto.StructPB.MovePB getMove();
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     */
    com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder();

    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
     * @return Whether the battle field is set.
     */
    boolean hasBattle();
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
     * @return The battle.
     */
    com.yorha.proto.StructBattlePB.BattlePB getBattle();
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
     */
    com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder();

    /**
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
     * @return Whether the buff field is set.
     */
    boolean hasBuff();
    /**
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
     * @return The buff.
     */
    com.yorha.proto.StructBattlePB.Int32BuffMapPB getBuff();
    /**
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
     */
    com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder getBuffOrBuilder();

    /**
     * <code>optional .com.yorha.proto.Camp camp = 6;</code>
     * @return Whether the camp field is set.
     */
    boolean hasCamp();
    /**
     * <code>optional .com.yorha.proto.Camp camp = 6;</code>
     * @return The camp.
     */
    com.yorha.proto.CommonEnum.Camp getCamp();

    /**
     * <pre>
     * 生命周期到期时间
     * </pre>
     *
     * <code>optional int64 lifeTime = 7;</code>
     * @return Whether the lifeTime field is set.
     */
    boolean hasLifeTime();
    /**
     * <pre>
     * 生命周期到期时间
     * </pre>
     *
     * <code>optional int64 lifeTime = 7;</code>
     * @return The lifeTime.
     */
    long getLifeTime();

    /**
     * <code>optional .com.yorha.proto.MonsterCreateType createType = 8;</code>
     * @return Whether the createType field is set.
     */
    boolean hasCreateType();
    /**
     * <code>optional .com.yorha.proto.MonsterCreateType createType = 8;</code>
     * @return The createType.
     */
    com.yorha.proto.CommonEnum.MonsterCreateType getCreateType();

    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 9;</code>
     * @return Whether the bornTime field is set.
     */
    boolean hasBornTime();
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 9;</code>
     * @return The bornTime.
     */
    long getBornTime();

    /**
     * <pre>
     * 死亡销毁时间
     * </pre>
     *
     * <code>optional int64 deadTime = 10;</code>
     * @return Whether the deadTime field is set.
     */
    boolean hasDeadTime();
    /**
     * <pre>
     * 死亡销毁时间
     * </pre>
     *
     * <code>optional int64 deadTime = 10;</code>
     * @return The deadTime.
     */
    long getDeadTime();

    /**
     * <pre>
     * 蓄力
     * </pre>
     *
     * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
     * @return Whether the cast field is set.
     */
    boolean hasCast();
    /**
     * <pre>
     * 蓄力
     * </pre>
     *
     * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
     * @return The cast.
     */
    com.yorha.proto.StructPB.MonsterCastPB getCast();
    /**
     * <pre>
     * 蓄力
     * </pre>
     *
     * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
     */
    com.yorha.proto.StructPB.MonsterCastPBOrBuilder getCastOrBuilder();

    /**
     * <pre>
     * 组id 可能一个一组 可能多个一组
     * </pre>
     *
     * <code>optional int32 groupId = 12;</code>
     * @return Whether the groupId field is set.
     */
    boolean hasGroupId();
    /**
     * <pre>
     * 组id 可能一个一组 可能多个一组
     * </pre>
     *
     * <code>optional int32 groupId = 12;</code>
     * @return The groupId.
     */
    int getGroupId();

    /**
     * <pre>
     * 召唤者玩家id
     * </pre>
     *
     * <code>optional int64 summonPlayerId = 14;</code>
     * @return Whether the summonPlayerId field is set.
     */
    boolean hasSummonPlayerId();
    /**
     * <pre>
     * 召唤者玩家id
     * </pre>
     *
     * <code>optional int64 summonPlayerId = 14;</code>
     * @return The summonPlayerId.
     */
    long getSummonPlayerId();

    /**
     * <pre>
     * 某些联盟专属野怪 护送/卡鲁拉克等
     * </pre>
     *
     * <code>optional int64 clanId = 16;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <pre>
     * 某些联盟专属野怪 护送/卡鲁拉克等
     * </pre>
     *
     * <code>optional int64 clanId = 16;</code>
     * @return The clanId.
     */
    long getClanId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MonsterEntityPB}
   */
  public static final class MonsterEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MonsterEntityPB)
      MonsterEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MonsterEntityPB.newBuilder() to construct.
    private MonsterEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MonsterEntityPB() {
      camp_ = 0;
      createType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MonsterEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MonsterEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              monsterId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.StructPlayerPB.TroopPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = troop_.toBuilder();
              }
              troop_ = input.readMessage(com.yorha.proto.StructPlayerPB.TroopPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(troop_);
                troop_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.yorha.proto.StructPB.MovePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = move_.toBuilder();
              }
              move_ = input.readMessage(com.yorha.proto.StructPB.MovePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(move_);
                move_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 34: {
              com.yorha.proto.StructBattlePB.BattlePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = battle_.toBuilder();
              }
              battle_ = input.readMessage(com.yorha.proto.StructBattlePB.BattlePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battle_);
                battle_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 42: {
              com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = buff_.toBuilder();
              }
              buff_ = input.readMessage(com.yorha.proto.StructBattlePB.Int32BuffMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buff_);
                buff_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 48: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Camp value = com.yorha.proto.CommonEnum.Camp.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(6, rawValue);
              } else {
                bitField0_ |= 0x00000020;
                camp_ = rawValue;
              }
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              lifeTime_ = input.readInt64();
              break;
            }
            case 64: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MonsterCreateType value = com.yorha.proto.CommonEnum.MonsterCreateType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(8, rawValue);
              } else {
                bitField0_ |= 0x00000080;
                createType_ = rawValue;
              }
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              bornTime_ = input.readInt64();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              deadTime_ = input.readInt64();
              break;
            }
            case 90: {
              com.yorha.proto.StructPB.MonsterCastPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000400) != 0)) {
                subBuilder = cast_.toBuilder();
              }
              cast_ = input.readMessage(com.yorha.proto.StructPB.MonsterCastPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cast_);
                cast_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000400;
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              groupId_ = input.readInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00001000;
              summonPlayerId_ = input.readInt64();
              break;
            }
            case 128: {
              bitField0_ |= 0x00002000;
              clanId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MonsterPB.internal_static_com_yorha_proto_MonsterEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MonsterPB.internal_static_com_yorha_proto_MonsterEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MonsterPB.MonsterEntityPB.class, com.yorha.proto.MonsterPB.MonsterEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int MONSTERID_FIELD_NUMBER = 1;
    private int monsterId_;
    /**
     * <code>optional int32 monsterId = 1;</code>
     * @return Whether the monsterId field is set.
     */
    @java.lang.Override
    public boolean hasMonsterId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 monsterId = 1;</code>
     * @return The monsterId.
     */
    @java.lang.Override
    public int getMonsterId() {
      return monsterId_;
    }

    public static final int TROOP_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPlayerPB.TroopPB troop_;
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     * @return Whether the troop field is set.
     */
    @java.lang.Override
    public boolean hasTroop() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     * @return The troop.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }

    public static final int MOVE_FIELD_NUMBER = 3;
    private com.yorha.proto.StructPB.MovePB move_;
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     * @return Whether the move field is set.
     */
    @java.lang.Override
    public boolean hasMove() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     * @return The move.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.MovePB getMove() {
      return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
    }
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder() {
      return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
    }

    public static final int BATTLE_FIELD_NUMBER = 4;
    private com.yorha.proto.StructBattlePB.BattlePB battle_;
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
     * @return Whether the battle field is set.
     */
    @java.lang.Override
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
     * @return The battle.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }

    public static final int BUFF_FIELD_NUMBER = 5;
    private com.yorha.proto.StructBattlePB.Int32BuffMapPB buff_;
    /**
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
     * @return Whether the buff field is set.
     */
    @java.lang.Override
    public boolean hasBuff() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
     * @return The buff.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.Int32BuffMapPB getBuff() {
      return buff_ == null ? com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance() : buff_;
    }
    /**
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder getBuffOrBuilder() {
      return buff_ == null ? com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance() : buff_;
    }

    public static final int CAMP_FIELD_NUMBER = 6;
    private int camp_;
    /**
     * <code>optional .com.yorha.proto.Camp camp = 6;</code>
     * @return Whether the camp field is set.
     */
    @java.lang.Override public boolean hasCamp() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Camp camp = 6;</code>
     * @return The camp.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Camp getCamp() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
      return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
    }

    public static final int LIFETIME_FIELD_NUMBER = 7;
    private long lifeTime_;
    /**
     * <pre>
     * 生命周期到期时间
     * </pre>
     *
     * <code>optional int64 lifeTime = 7;</code>
     * @return Whether the lifeTime field is set.
     */
    @java.lang.Override
    public boolean hasLifeTime() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 生命周期到期时间
     * </pre>
     *
     * <code>optional int64 lifeTime = 7;</code>
     * @return The lifeTime.
     */
    @java.lang.Override
    public long getLifeTime() {
      return lifeTime_;
    }

    public static final int CREATETYPE_FIELD_NUMBER = 8;
    private int createType_;
    /**
     * <code>optional .com.yorha.proto.MonsterCreateType createType = 8;</code>
     * @return Whether the createType field is set.
     */
    @java.lang.Override public boolean hasCreateType() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MonsterCreateType createType = 8;</code>
     * @return The createType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MonsterCreateType getCreateType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MonsterCreateType result = com.yorha.proto.CommonEnum.MonsterCreateType.valueOf(createType_);
      return result == null ? com.yorha.proto.CommonEnum.MonsterCreateType.SPAWN : result;
    }

    public static final int BORNTIME_FIELD_NUMBER = 9;
    private long bornTime_;
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 9;</code>
     * @return Whether the bornTime field is set.
     */
    @java.lang.Override
    public boolean hasBornTime() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 9;</code>
     * @return The bornTime.
     */
    @java.lang.Override
    public long getBornTime() {
      return bornTime_;
    }

    public static final int DEADTIME_FIELD_NUMBER = 10;
    private long deadTime_;
    /**
     * <pre>
     * 死亡销毁时间
     * </pre>
     *
     * <code>optional int64 deadTime = 10;</code>
     * @return Whether the deadTime field is set.
     */
    @java.lang.Override
    public boolean hasDeadTime() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 死亡销毁时间
     * </pre>
     *
     * <code>optional int64 deadTime = 10;</code>
     * @return The deadTime.
     */
    @java.lang.Override
    public long getDeadTime() {
      return deadTime_;
    }

    public static final int CAST_FIELD_NUMBER = 11;
    private com.yorha.proto.StructPB.MonsterCastPB cast_;
    /**
     * <pre>
     * 蓄力
     * </pre>
     *
     * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
     * @return Whether the cast field is set.
     */
    @java.lang.Override
    public boolean hasCast() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 蓄力
     * </pre>
     *
     * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
     * @return The cast.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.MonsterCastPB getCast() {
      return cast_ == null ? com.yorha.proto.StructPB.MonsterCastPB.getDefaultInstance() : cast_;
    }
    /**
     * <pre>
     * 蓄力
     * </pre>
     *
     * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.MonsterCastPBOrBuilder getCastOrBuilder() {
      return cast_ == null ? com.yorha.proto.StructPB.MonsterCastPB.getDefaultInstance() : cast_;
    }

    public static final int GROUPID_FIELD_NUMBER = 12;
    private int groupId_;
    /**
     * <pre>
     * 组id 可能一个一组 可能多个一组
     * </pre>
     *
     * <code>optional int32 groupId = 12;</code>
     * @return Whether the groupId field is set.
     */
    @java.lang.Override
    public boolean hasGroupId() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 组id 可能一个一组 可能多个一组
     * </pre>
     *
     * <code>optional int32 groupId = 12;</code>
     * @return The groupId.
     */
    @java.lang.Override
    public int getGroupId() {
      return groupId_;
    }

    public static final int SUMMONPLAYERID_FIELD_NUMBER = 14;
    private long summonPlayerId_;
    /**
     * <pre>
     * 召唤者玩家id
     * </pre>
     *
     * <code>optional int64 summonPlayerId = 14;</code>
     * @return Whether the summonPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasSummonPlayerId() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 召唤者玩家id
     * </pre>
     *
     * <code>optional int64 summonPlayerId = 14;</code>
     * @return The summonPlayerId.
     */
    @java.lang.Override
    public long getSummonPlayerId() {
      return summonPlayerId_;
    }

    public static final int CLANID_FIELD_NUMBER = 16;
    private long clanId_;
    /**
     * <pre>
     * 某些联盟专属野怪 护送/卡鲁拉克等
     * </pre>
     *
     * <code>optional int64 clanId = 16;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 某些联盟专属野怪 护送/卡鲁拉克等
     * </pre>
     *
     * <code>optional int64 clanId = 16;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, monsterId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getTroop());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getMove());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getBattle());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getBuff());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeEnum(6, camp_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt64(7, lifeTime_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeEnum(8, createType_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt64(9, bornTime_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt64(10, deadTime_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeMessage(11, getCast());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeInt32(12, groupId_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeInt64(14, summonPlayerId_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeInt64(16, clanId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, monsterId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTroop());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getMove());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getBattle());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getBuff());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(6, camp_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, lifeTime_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(8, createType_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, bornTime_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, deadTime_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, getCast());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, groupId_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(14, summonPlayerId_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(16, clanId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MonsterPB.MonsterEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.MonsterPB.MonsterEntityPB other = (com.yorha.proto.MonsterPB.MonsterEntityPB) obj;

      if (hasMonsterId() != other.hasMonsterId()) return false;
      if (hasMonsterId()) {
        if (getMonsterId()
            != other.getMonsterId()) return false;
      }
      if (hasTroop() != other.hasTroop()) return false;
      if (hasTroop()) {
        if (!getTroop()
            .equals(other.getTroop())) return false;
      }
      if (hasMove() != other.hasMove()) return false;
      if (hasMove()) {
        if (!getMove()
            .equals(other.getMove())) return false;
      }
      if (hasBattle() != other.hasBattle()) return false;
      if (hasBattle()) {
        if (!getBattle()
            .equals(other.getBattle())) return false;
      }
      if (hasBuff() != other.hasBuff()) return false;
      if (hasBuff()) {
        if (!getBuff()
            .equals(other.getBuff())) return false;
      }
      if (hasCamp() != other.hasCamp()) return false;
      if (hasCamp()) {
        if (camp_ != other.camp_) return false;
      }
      if (hasLifeTime() != other.hasLifeTime()) return false;
      if (hasLifeTime()) {
        if (getLifeTime()
            != other.getLifeTime()) return false;
      }
      if (hasCreateType() != other.hasCreateType()) return false;
      if (hasCreateType()) {
        if (createType_ != other.createType_) return false;
      }
      if (hasBornTime() != other.hasBornTime()) return false;
      if (hasBornTime()) {
        if (getBornTime()
            != other.getBornTime()) return false;
      }
      if (hasDeadTime() != other.hasDeadTime()) return false;
      if (hasDeadTime()) {
        if (getDeadTime()
            != other.getDeadTime()) return false;
      }
      if (hasCast() != other.hasCast()) return false;
      if (hasCast()) {
        if (!getCast()
            .equals(other.getCast())) return false;
      }
      if (hasGroupId() != other.hasGroupId()) return false;
      if (hasGroupId()) {
        if (getGroupId()
            != other.getGroupId()) return false;
      }
      if (hasSummonPlayerId() != other.hasSummonPlayerId()) return false;
      if (hasSummonPlayerId()) {
        if (getSummonPlayerId()
            != other.getSummonPlayerId()) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMonsterId()) {
        hash = (37 * hash) + MONSTERID_FIELD_NUMBER;
        hash = (53 * hash) + getMonsterId();
      }
      if (hasTroop()) {
        hash = (37 * hash) + TROOP_FIELD_NUMBER;
        hash = (53 * hash) + getTroop().hashCode();
      }
      if (hasMove()) {
        hash = (37 * hash) + MOVE_FIELD_NUMBER;
        hash = (53 * hash) + getMove().hashCode();
      }
      if (hasBattle()) {
        hash = (37 * hash) + BATTLE_FIELD_NUMBER;
        hash = (53 * hash) + getBattle().hashCode();
      }
      if (hasBuff()) {
        hash = (37 * hash) + BUFF_FIELD_NUMBER;
        hash = (53 * hash) + getBuff().hashCode();
      }
      if (hasCamp()) {
        hash = (37 * hash) + CAMP_FIELD_NUMBER;
        hash = (53 * hash) + camp_;
      }
      if (hasLifeTime()) {
        hash = (37 * hash) + LIFETIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLifeTime());
      }
      if (hasCreateType()) {
        hash = (37 * hash) + CREATETYPE_FIELD_NUMBER;
        hash = (53 * hash) + createType_;
      }
      if (hasBornTime()) {
        hash = (37 * hash) + BORNTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBornTime());
      }
      if (hasDeadTime()) {
        hash = (37 * hash) + DEADTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getDeadTime());
      }
      if (hasCast()) {
        hash = (37 * hash) + CAST_FIELD_NUMBER;
        hash = (53 * hash) + getCast().hashCode();
      }
      if (hasGroupId()) {
        hash = (37 * hash) + GROUPID_FIELD_NUMBER;
        hash = (53 * hash) + getGroupId();
      }
      if (hasSummonPlayerId()) {
        hash = (37 * hash) + SUMMONPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSummonPlayerId());
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MonsterPB.MonsterEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MonsterPB.MonsterEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MonsterEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MonsterEntityPB)
        com.yorha.proto.MonsterPB.MonsterEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MonsterPB.internal_static_com_yorha_proto_MonsterEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MonsterPB.internal_static_com_yorha_proto_MonsterEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MonsterPB.MonsterEntityPB.class, com.yorha.proto.MonsterPB.MonsterEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.MonsterPB.MonsterEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTroopFieldBuilder();
          getMoveFieldBuilder();
          getBattleFieldBuilder();
          getBuffFieldBuilder();
          getCastFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        monsterId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (troopBuilder_ == null) {
          troop_ = null;
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (moveBuilder_ == null) {
          move_ = null;
        } else {
          moveBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (battleBuilder_ == null) {
          battle_ = null;
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (buffBuilder_ == null) {
          buff_ = null;
        } else {
          buffBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        camp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        lifeTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        createType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        bornTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        deadTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        if (castBuilder_ == null) {
          cast_ = null;
        } else {
          castBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        groupId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        summonPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00001000);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00002000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MonsterPB.internal_static_com_yorha_proto_MonsterEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MonsterPB.MonsterEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MonsterPB.MonsterEntityPB build() {
        com.yorha.proto.MonsterPB.MonsterEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MonsterPB.MonsterEntityPB buildPartial() {
        com.yorha.proto.MonsterPB.MonsterEntityPB result = new com.yorha.proto.MonsterPB.MonsterEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.monsterId_ = monsterId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (troopBuilder_ == null) {
            result.troop_ = troop_;
          } else {
            result.troop_ = troopBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (moveBuilder_ == null) {
            result.move_ = move_;
          } else {
            result.move_ = moveBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (battleBuilder_ == null) {
            result.battle_ = battle_;
          } else {
            result.battle_ = battleBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (buffBuilder_ == null) {
            result.buff_ = buff_;
          } else {
            result.buff_ = buffBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.camp_ = camp_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.lifeTime_ = lifeTime_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.createType_ = createType_;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.bornTime_ = bornTime_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.deadTime_ = deadTime_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          if (castBuilder_ == null) {
            result.cast_ = cast_;
          } else {
            result.cast_ = castBuilder_.build();
          }
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.groupId_ = groupId_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.summonPlayerId_ = summonPlayerId_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00002000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MonsterPB.MonsterEntityPB) {
          return mergeFrom((com.yorha.proto.MonsterPB.MonsterEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MonsterPB.MonsterEntityPB other) {
        if (other == com.yorha.proto.MonsterPB.MonsterEntityPB.getDefaultInstance()) return this;
        if (other.hasMonsterId()) {
          setMonsterId(other.getMonsterId());
        }
        if (other.hasTroop()) {
          mergeTroop(other.getTroop());
        }
        if (other.hasMove()) {
          mergeMove(other.getMove());
        }
        if (other.hasBattle()) {
          mergeBattle(other.getBattle());
        }
        if (other.hasBuff()) {
          mergeBuff(other.getBuff());
        }
        if (other.hasCamp()) {
          setCamp(other.getCamp());
        }
        if (other.hasLifeTime()) {
          setLifeTime(other.getLifeTime());
        }
        if (other.hasCreateType()) {
          setCreateType(other.getCreateType());
        }
        if (other.hasBornTime()) {
          setBornTime(other.getBornTime());
        }
        if (other.hasDeadTime()) {
          setDeadTime(other.getDeadTime());
        }
        if (other.hasCast()) {
          mergeCast(other.getCast());
        }
        if (other.hasGroupId()) {
          setGroupId(other.getGroupId());
        }
        if (other.hasSummonPlayerId()) {
          setSummonPlayerId(other.getSummonPlayerId());
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MonsterPB.MonsterEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MonsterPB.MonsterEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int monsterId_ ;
      /**
       * <code>optional int32 monsterId = 1;</code>
       * @return Whether the monsterId field is set.
       */
      @java.lang.Override
      public boolean hasMonsterId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 monsterId = 1;</code>
       * @return The monsterId.
       */
      @java.lang.Override
      public int getMonsterId() {
        return monsterId_;
      }
      /**
       * <code>optional int32 monsterId = 1;</code>
       * @param value The monsterId to set.
       * @return This builder for chaining.
       */
      public Builder setMonsterId(int value) {
        bitField0_ |= 0x00000001;
        monsterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 monsterId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonsterId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        monsterId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPlayerPB.TroopPB troop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> troopBuilder_;
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       * @return Whether the troop field is set.
       */
      public boolean hasTroop() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       * @return The troop.
       */
      public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
        if (troopBuilder_ == null) {
          return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        } else {
          return troopBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public Builder setTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          troop_ = value;
          onChanged();
        } else {
          troopBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public Builder setTroop(
          com.yorha.proto.StructPlayerPB.TroopPB.Builder builderForValue) {
        if (troopBuilder_ == null) {
          troop_ = builderForValue.build();
          onChanged();
        } else {
          troopBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public Builder mergeTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              troop_ != null &&
              troop_ != com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance()) {
            troop_ =
              com.yorha.proto.StructPlayerPB.TroopPB.newBuilder(troop_).mergeFrom(value).buildPartial();
          } else {
            troop_ = value;
          }
          onChanged();
        } else {
          troopBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public Builder clearTroop() {
        if (troopBuilder_ == null) {
          troop_ = null;
          onChanged();
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPB.Builder getTroopBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getTroopFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
        if (troopBuilder_ != null) {
          return troopBuilder_.getMessageOrBuilder();
        } else {
          return troop_ == null ?
              com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> 
          getTroopFieldBuilder() {
        if (troopBuilder_ == null) {
          troopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder>(
                  getTroop(),
                  getParentForChildren(),
                  isClean());
          troop_ = null;
        }
        return troopBuilder_;
      }

      private com.yorha.proto.StructPB.MovePB move_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder> moveBuilder_;
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       * @return Whether the move field is set.
       */
      public boolean hasMove() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       * @return The move.
       */
      public com.yorha.proto.StructPB.MovePB getMove() {
        if (moveBuilder_ == null) {
          return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
        } else {
          return moveBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public Builder setMove(com.yorha.proto.StructPB.MovePB value) {
        if (moveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          move_ = value;
          onChanged();
        } else {
          moveBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public Builder setMove(
          com.yorha.proto.StructPB.MovePB.Builder builderForValue) {
        if (moveBuilder_ == null) {
          move_ = builderForValue.build();
          onChanged();
        } else {
          moveBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public Builder mergeMove(com.yorha.proto.StructPB.MovePB value) {
        if (moveBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              move_ != null &&
              move_ != com.yorha.proto.StructPB.MovePB.getDefaultInstance()) {
            move_ =
              com.yorha.proto.StructPB.MovePB.newBuilder(move_).mergeFrom(value).buildPartial();
          } else {
            move_ = value;
          }
          onChanged();
        } else {
          moveBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public Builder clearMove() {
        if (moveBuilder_ == null) {
          move_ = null;
          onChanged();
        } else {
          moveBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public com.yorha.proto.StructPB.MovePB.Builder getMoveBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getMoveFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder() {
        if (moveBuilder_ != null) {
          return moveBuilder_.getMessageOrBuilder();
        } else {
          return move_ == null ?
              com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder> 
          getMoveFieldBuilder() {
        if (moveBuilder_ == null) {
          moveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder>(
                  getMove(),
                  getParentForChildren(),
                  isClean());
          move_ = null;
        }
        return moveBuilder_;
      }

      private com.yorha.proto.StructBattlePB.BattlePB battle_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> battleBuilder_;
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       * @return Whether the battle field is set.
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       * @return The battle.
       */
      public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
        if (battleBuilder_ == null) {
          return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        } else {
          return battleBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       */
      public Builder setBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battle_ = value;
          onChanged();
        } else {
          battleBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       */
      public Builder setBattle(
          com.yorha.proto.StructBattlePB.BattlePB.Builder builderForValue) {
        if (battleBuilder_ == null) {
          battle_ = builderForValue.build();
          onChanged();
        } else {
          battleBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       */
      public Builder mergeBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              battle_ != null &&
              battle_ != com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance()) {
            battle_ =
              com.yorha.proto.StructBattlePB.BattlePB.newBuilder(battle_).mergeFrom(value).buildPartial();
          } else {
            battle_ = value;
          }
          onChanged();
        } else {
          battleBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       */
      public Builder clearBattle() {
        if (battleBuilder_ == null) {
          battle_ = null;
          onChanged();
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePB.Builder getBattleBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getBattleFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
        if (battleBuilder_ != null) {
          return battleBuilder_.getMessageOrBuilder();
        } else {
          return battle_ == null ?
              com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> 
          getBattleFieldBuilder() {
        if (battleBuilder_ == null) {
          battleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder>(
                  getBattle(),
                  getParentForChildren(),
                  isClean());
          battle_ = null;
        }
        return battleBuilder_;
      }

      private com.yorha.proto.StructBattlePB.Int32BuffMapPB buff_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.Int32BuffMapPB, com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder, com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder> buffBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       * @return Whether the buff field is set.
       */
      public boolean hasBuff() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       * @return The buff.
       */
      public com.yorha.proto.StructBattlePB.Int32BuffMapPB getBuff() {
        if (buffBuilder_ == null) {
          return buff_ == null ? com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance() : buff_;
        } else {
          return buffBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       */
      public Builder setBuff(com.yorha.proto.StructBattlePB.Int32BuffMapPB value) {
        if (buffBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buff_ = value;
          onChanged();
        } else {
          buffBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       */
      public Builder setBuff(
          com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder builderForValue) {
        if (buffBuilder_ == null) {
          buff_ = builderForValue.build();
          onChanged();
        } else {
          buffBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       */
      public Builder mergeBuff(com.yorha.proto.StructBattlePB.Int32BuffMapPB value) {
        if (buffBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              buff_ != null &&
              buff_ != com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance()) {
            buff_ =
              com.yorha.proto.StructBattlePB.Int32BuffMapPB.newBuilder(buff_).mergeFrom(value).buildPartial();
          } else {
            buff_ = value;
          }
          onChanged();
        } else {
          buffBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       */
      public Builder clearBuff() {
        if (buffBuilder_ == null) {
          buff_ = null;
          onChanged();
        } else {
          buffBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       */
      public com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder getBuffBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getBuffFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       */
      public com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder getBuffOrBuilder() {
        if (buffBuilder_ != null) {
          return buffBuilder_.getMessageOrBuilder();
        } else {
          return buff_ == null ?
              com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance() : buff_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.Int32BuffMapPB, com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder, com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder> 
          getBuffFieldBuilder() {
        if (buffBuilder_ == null) {
          buffBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.Int32BuffMapPB, com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder, com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder>(
                  getBuff(),
                  getParentForChildren(),
                  isClean());
          buff_ = null;
        }
        return buffBuilder_;
      }

      private int camp_ = 0;
      /**
       * <code>optional .com.yorha.proto.Camp camp = 6;</code>
       * @return Whether the camp field is set.
       */
      @java.lang.Override public boolean hasCamp() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Camp camp = 6;</code>
       * @return The camp.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Camp getCamp() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
        return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.Camp camp = 6;</code>
       * @param value The camp to set.
       * @return This builder for chaining.
       */
      public Builder setCamp(com.yorha.proto.CommonEnum.Camp value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000020;
        camp_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Camp camp = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCamp() {
        bitField0_ = (bitField0_ & ~0x00000020);
        camp_ = 0;
        onChanged();
        return this;
      }

      private long lifeTime_ ;
      /**
       * <pre>
       * 生命周期到期时间
       * </pre>
       *
       * <code>optional int64 lifeTime = 7;</code>
       * @return Whether the lifeTime field is set.
       */
      @java.lang.Override
      public boolean hasLifeTime() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 生命周期到期时间
       * </pre>
       *
       * <code>optional int64 lifeTime = 7;</code>
       * @return The lifeTime.
       */
      @java.lang.Override
      public long getLifeTime() {
        return lifeTime_;
      }
      /**
       * <pre>
       * 生命周期到期时间
       * </pre>
       *
       * <code>optional int64 lifeTime = 7;</code>
       * @param value The lifeTime to set.
       * @return This builder for chaining.
       */
      public Builder setLifeTime(long value) {
        bitField0_ |= 0x00000040;
        lifeTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 生命周期到期时间
       * </pre>
       *
       * <code>optional int64 lifeTime = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearLifeTime() {
        bitField0_ = (bitField0_ & ~0x00000040);
        lifeTime_ = 0L;
        onChanged();
        return this;
      }

      private int createType_ = 0;
      /**
       * <code>optional .com.yorha.proto.MonsterCreateType createType = 8;</code>
       * @return Whether the createType field is set.
       */
      @java.lang.Override public boolean hasCreateType() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MonsterCreateType createType = 8;</code>
       * @return The createType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MonsterCreateType getCreateType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MonsterCreateType result = com.yorha.proto.CommonEnum.MonsterCreateType.valueOf(createType_);
        return result == null ? com.yorha.proto.CommonEnum.MonsterCreateType.SPAWN : result;
      }
      /**
       * <code>optional .com.yorha.proto.MonsterCreateType createType = 8;</code>
       * @param value The createType to set.
       * @return This builder for chaining.
       */
      public Builder setCreateType(com.yorha.proto.CommonEnum.MonsterCreateType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000080;
        createType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MonsterCreateType createType = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreateType() {
        bitField0_ = (bitField0_ & ~0x00000080);
        createType_ = 0;
        onChanged();
        return this;
      }

      private long bornTime_ ;
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 9;</code>
       * @return Whether the bornTime field is set.
       */
      @java.lang.Override
      public boolean hasBornTime() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 9;</code>
       * @return The bornTime.
       */
      @java.lang.Override
      public long getBornTime() {
        return bornTime_;
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 9;</code>
       * @param value The bornTime to set.
       * @return This builder for chaining.
       */
      public Builder setBornTime(long value) {
        bitField0_ |= 0x00000100;
        bornTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearBornTime() {
        bitField0_ = (bitField0_ & ~0x00000100);
        bornTime_ = 0L;
        onChanged();
        return this;
      }

      private long deadTime_ ;
      /**
       * <pre>
       * 死亡销毁时间
       * </pre>
       *
       * <code>optional int64 deadTime = 10;</code>
       * @return Whether the deadTime field is set.
       */
      @java.lang.Override
      public boolean hasDeadTime() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 死亡销毁时间
       * </pre>
       *
       * <code>optional int64 deadTime = 10;</code>
       * @return The deadTime.
       */
      @java.lang.Override
      public long getDeadTime() {
        return deadTime_;
      }
      /**
       * <pre>
       * 死亡销毁时间
       * </pre>
       *
       * <code>optional int64 deadTime = 10;</code>
       * @param value The deadTime to set.
       * @return This builder for chaining.
       */
      public Builder setDeadTime(long value) {
        bitField0_ |= 0x00000200;
        deadTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 死亡销毁时间
       * </pre>
       *
       * <code>optional int64 deadTime = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeadTime() {
        bitField0_ = (bitField0_ & ~0x00000200);
        deadTime_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.MonsterCastPB cast_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.MonsterCastPB, com.yorha.proto.StructPB.MonsterCastPB.Builder, com.yorha.proto.StructPB.MonsterCastPBOrBuilder> castBuilder_;
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       * @return Whether the cast field is set.
       */
      public boolean hasCast() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       * @return The cast.
       */
      public com.yorha.proto.StructPB.MonsterCastPB getCast() {
        if (castBuilder_ == null) {
          return cast_ == null ? com.yorha.proto.StructPB.MonsterCastPB.getDefaultInstance() : cast_;
        } else {
          return castBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       */
      public Builder setCast(com.yorha.proto.StructPB.MonsterCastPB value) {
        if (castBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cast_ = value;
          onChanged();
        } else {
          castBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       */
      public Builder setCast(
          com.yorha.proto.StructPB.MonsterCastPB.Builder builderForValue) {
        if (castBuilder_ == null) {
          cast_ = builderForValue.build();
          onChanged();
        } else {
          castBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       */
      public Builder mergeCast(com.yorha.proto.StructPB.MonsterCastPB value) {
        if (castBuilder_ == null) {
          if (((bitField0_ & 0x00000400) != 0) &&
              cast_ != null &&
              cast_ != com.yorha.proto.StructPB.MonsterCastPB.getDefaultInstance()) {
            cast_ =
              com.yorha.proto.StructPB.MonsterCastPB.newBuilder(cast_).mergeFrom(value).buildPartial();
          } else {
            cast_ = value;
          }
          onChanged();
        } else {
          castBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       */
      public Builder clearCast() {
        if (castBuilder_ == null) {
          cast_ = null;
          onChanged();
        } else {
          castBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       */
      public com.yorha.proto.StructPB.MonsterCastPB.Builder getCastBuilder() {
        bitField0_ |= 0x00000400;
        onChanged();
        return getCastFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       */
      public com.yorha.proto.StructPB.MonsterCastPBOrBuilder getCastOrBuilder() {
        if (castBuilder_ != null) {
          return castBuilder_.getMessageOrBuilder();
        } else {
          return cast_ == null ?
              com.yorha.proto.StructPB.MonsterCastPB.getDefaultInstance() : cast_;
        }
      }
      /**
       * <pre>
       * 蓄力
       * </pre>
       *
       * <code>optional .com.yorha.proto.MonsterCastPB cast = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.MonsterCastPB, com.yorha.proto.StructPB.MonsterCastPB.Builder, com.yorha.proto.StructPB.MonsterCastPBOrBuilder> 
          getCastFieldBuilder() {
        if (castBuilder_ == null) {
          castBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.MonsterCastPB, com.yorha.proto.StructPB.MonsterCastPB.Builder, com.yorha.proto.StructPB.MonsterCastPBOrBuilder>(
                  getCast(),
                  getParentForChildren(),
                  isClean());
          cast_ = null;
        }
        return castBuilder_;
      }

      private int groupId_ ;
      /**
       * <pre>
       * 组id 可能一个一组 可能多个一组
       * </pre>
       *
       * <code>optional int32 groupId = 12;</code>
       * @return Whether the groupId field is set.
       */
      @java.lang.Override
      public boolean hasGroupId() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 组id 可能一个一组 可能多个一组
       * </pre>
       *
       * <code>optional int32 groupId = 12;</code>
       * @return The groupId.
       */
      @java.lang.Override
      public int getGroupId() {
        return groupId_;
      }
      /**
       * <pre>
       * 组id 可能一个一组 可能多个一组
       * </pre>
       *
       * <code>optional int32 groupId = 12;</code>
       * @param value The groupId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupId(int value) {
        bitField0_ |= 0x00000800;
        groupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 组id 可能一个一组 可能多个一组
       * </pre>
       *
       * <code>optional int32 groupId = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupId() {
        bitField0_ = (bitField0_ & ~0x00000800);
        groupId_ = 0;
        onChanged();
        return this;
      }

      private long summonPlayerId_ ;
      /**
       * <pre>
       * 召唤者玩家id
       * </pre>
       *
       * <code>optional int64 summonPlayerId = 14;</code>
       * @return Whether the summonPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasSummonPlayerId() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 召唤者玩家id
       * </pre>
       *
       * <code>optional int64 summonPlayerId = 14;</code>
       * @return The summonPlayerId.
       */
      @java.lang.Override
      public long getSummonPlayerId() {
        return summonPlayerId_;
      }
      /**
       * <pre>
       * 召唤者玩家id
       * </pre>
       *
       * <code>optional int64 summonPlayerId = 14;</code>
       * @param value The summonPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setSummonPlayerId(long value) {
        bitField0_ |= 0x00001000;
        summonPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 召唤者玩家id
       * </pre>
       *
       * <code>optional int64 summonPlayerId = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearSummonPlayerId() {
        bitField0_ = (bitField0_ & ~0x00001000);
        summonPlayerId_ = 0L;
        onChanged();
        return this;
      }

      private long clanId_ ;
      /**
       * <pre>
       * 某些联盟专属野怪 护送/卡鲁拉克等
       * </pre>
       *
       * <code>optional int64 clanId = 16;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 某些联盟专属野怪 护送/卡鲁拉克等
       * </pre>
       *
       * <code>optional int64 clanId = 16;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <pre>
       * 某些联盟专属野怪 护送/卡鲁拉克等
       * </pre>
       *
       * <code>optional int64 clanId = 16;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00002000;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 某些联盟专属野怪 护送/卡鲁拉克等
       * </pre>
       *
       * <code>optional int64 clanId = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00002000);
        clanId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MonsterEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MonsterEntityPB)
    private static final com.yorha.proto.MonsterPB.MonsterEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MonsterPB.MonsterEntityPB();
    }

    public static com.yorha.proto.MonsterPB.MonsterEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MonsterEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<MonsterEntityPB>() {
      @java.lang.Override
      public MonsterEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MonsterEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MonsterEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MonsterEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MonsterPB.MonsterEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MonsterEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MonsterEntityPB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$cs_proto/gen/monster/monsterPB.proto\022\017" +
      "com.yorha.proto\032\"cs_proto/gen/common/str" +
      "uctPB.proto\032)cs_proto/gen/common/struct_" +
      "battlePB.proto\032)cs_proto/gen/common/stru" +
      "ct_playerPB.proto\032%ss_proto/gen/common/c" +
      "ommon_enum.proto\"\310\003\n\017MonsterEntityPB\022\021\n\t" +
      "monsterId\030\001 \001(\005\022\'\n\005troop\030\002 \001(\0132\030.com.yor" +
      "ha.proto.TroopPB\022%\n\004move\030\003 \001(\0132\027.com.yor" +
      "ha.proto.MovePB\022)\n\006battle\030\004 \001(\0132\031.com.yo" +
      "rha.proto.BattlePB\022-\n\004buff\030\005 \001(\0132\037.com.y" +
      "orha.proto.Int32BuffMapPB\022#\n\004camp\030\006 \001(\0162" +
      "\025.com.yorha.proto.Camp\022\020\n\010lifeTime\030\007 \001(\003" +
      "\0226\n\ncreateType\030\010 \001(\0162\".com.yorha.proto.M" +
      "onsterCreateType\022\020\n\010bornTime\030\t \001(\003\022\020\n\010de" +
      "adTime\030\n \001(\003\022,\n\004cast\030\013 \001(\0132\036.com.yorha.p" +
      "roto.MonsterCastPB\022\017\n\007groupId\030\014 \001(\005\022\026\n\016s" +
      "ummonPlayerId\030\016 \001(\003\022\016\n\006clanId\030\020 \001(\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.StructBattlePB.getDescriptor(),
          com.yorha.proto.StructPlayerPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_MonsterEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_MonsterEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MonsterEntityPB_descriptor,
        new java.lang.String[] { "MonsterId", "Troop", "Move", "Battle", "Buff", "Camp", "LifeTime", "CreateType", "BornTime", "DeadTime", "Cast", "GroupId", "SummonPlayerId", "ClanId", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.StructBattlePB.getDescriptor();
    com.yorha.proto.StructPlayerPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/ss/ss_player_chat.proto

package com.yorha.proto;

public final class SsPlayerChat {
  private SsPlayerChat() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PrepareJoinGroupChatAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PrepareJoinGroupChatAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    java.lang.String getChannelId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    com.google.protobuf.ByteString
        getChannelIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PrepareJoinGroupChatAsk}
   */
  public static final class PrepareJoinGroupChatAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PrepareJoinGroupChatAsk)
      PrepareJoinGroupChatAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PrepareJoinGroupChatAsk.newBuilder() to construct.
    private PrepareJoinGroupChatAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PrepareJoinGroupChatAsk() {
      channelId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PrepareJoinGroupChatAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PrepareJoinGroupChatAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              channelId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk.class, com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNELID_FIELD_NUMBER = 1;
    private volatile java.lang.Object channelId_;
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public java.lang.String getChannelId() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelIdBytes() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk other = (com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk) obj;

      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (!getChannelId()
            .equals(other.getChannelId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PrepareJoinGroupChatAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PrepareJoinGroupChatAsk)
        com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk.class, com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk build() {
        com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk buildPartial() {
        com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk result = new com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelId_ = channelId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk other) {
        if (other == com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk.getDefaultInstance()) return this;
        if (other.hasChannelId()) {
          bitField0_ |= 0x00000001;
          channelId_ = other.channelId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object channelId_ = "";
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return Whether the channelId field is set.
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return The channelId.
       */
      public java.lang.String getChannelId() {
        java.lang.Object ref = channelId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return The bytes for channelId.
       */
      public com.google.protobuf.ByteString
          getChannelIdBytes() {
        java.lang.Object ref = channelId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelId_ = getDefaultInstance().getChannelId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @param value The bytes for channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PrepareJoinGroupChatAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PrepareJoinGroupChatAsk)
    private static final com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk();
    }

    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PrepareJoinGroupChatAsk>
        PARSER = new com.google.protobuf.AbstractParser<PrepareJoinGroupChatAsk>() {
      @java.lang.Override
      public PrepareJoinGroupChatAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PrepareJoinGroupChatAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PrepareJoinGroupChatAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PrepareJoinGroupChatAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PrepareJoinGroupChatAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PrepareJoinGroupChatAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家是否准备好
     * </pre>
     *
     * <code>optional bool prepare = 3;</code>
     * @return Whether the prepare field is set.
     */
    boolean hasPrepare();
    /**
     * <pre>
     * 玩家是否准备好
     * </pre>
     *
     * <code>optional bool prepare = 3;</code>
     * @return The prepare.
     */
    boolean getPrepare();
  }
  /**
   * <pre>
   * 废弃index 1, 2
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.PrepareJoinGroupChatAns}
   */
  public static final class PrepareJoinGroupChatAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PrepareJoinGroupChatAns)
      PrepareJoinGroupChatAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PrepareJoinGroupChatAns.newBuilder() to construct.
    private PrepareJoinGroupChatAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PrepareJoinGroupChatAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PrepareJoinGroupChatAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PrepareJoinGroupChatAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 24: {
              bitField0_ |= 0x00000001;
              prepare_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns.class, com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns.Builder.class);
    }

    private int bitField0_;
    public static final int PREPARE_FIELD_NUMBER = 3;
    private boolean prepare_;
    /**
     * <pre>
     * 玩家是否准备好
     * </pre>
     *
     * <code>optional bool prepare = 3;</code>
     * @return Whether the prepare field is set.
     */
    @java.lang.Override
    public boolean hasPrepare() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家是否准备好
     * </pre>
     *
     * <code>optional bool prepare = 3;</code>
     * @return The prepare.
     */
    @java.lang.Override
    public boolean getPrepare() {
      return prepare_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, prepare_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, prepare_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns other = (com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns) obj;

      if (hasPrepare() != other.hasPrepare()) return false;
      if (hasPrepare()) {
        if (getPrepare()
            != other.getPrepare()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPrepare()) {
        hash = (37 * hash) + PREPARE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getPrepare());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 废弃index 1, 2
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.PrepareJoinGroupChatAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PrepareJoinGroupChatAns)
        com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns.class, com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        prepare_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_PrepareJoinGroupChatAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns build() {
        com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns buildPartial() {
        com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns result = new com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.prepare_ = prepare_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns other) {
        if (other == com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns.getDefaultInstance()) return this;
        if (other.hasPrepare()) {
          setPrepare(other.getPrepare());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean prepare_ ;
      /**
       * <pre>
       * 玩家是否准备好
       * </pre>
       *
       * <code>optional bool prepare = 3;</code>
       * @return Whether the prepare field is set.
       */
      @java.lang.Override
      public boolean hasPrepare() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家是否准备好
       * </pre>
       *
       * <code>optional bool prepare = 3;</code>
       * @return The prepare.
       */
      @java.lang.Override
      public boolean getPrepare() {
        return prepare_;
      }
      /**
       * <pre>
       * 玩家是否准备好
       * </pre>
       *
       * <code>optional bool prepare = 3;</code>
       * @param value The prepare to set.
       * @return This builder for chaining.
       */
      public Builder setPrepare(boolean value) {
        bitField0_ |= 0x00000001;
        prepare_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家是否准备好
       * </pre>
       *
       * <code>optional bool prepare = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPrepare() {
        bitField0_ = (bitField0_ & ~0x00000001);
        prepare_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PrepareJoinGroupChatAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PrepareJoinGroupChatAns)
    private static final com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns();
    }

    public static com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PrepareJoinGroupChatAns>
        PARSER = new com.google.protobuf.AbstractParser<PrepareJoinGroupChatAns>() {
      @java.lang.Override
      public PrepareJoinGroupChatAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PrepareJoinGroupChatAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PrepareJoinGroupChatAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PrepareJoinGroupChatAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.PrepareJoinGroupChatAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClearPrepareGroupChatCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClearPrepareGroupChatCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    java.lang.String getChannelId();
    /**
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    com.google.protobuf.ByteString
        getChannelIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClearPrepareGroupChatCmd}
   */
  public static final class ClearPrepareGroupChatCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClearPrepareGroupChatCmd)
      ClearPrepareGroupChatCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClearPrepareGroupChatCmd.newBuilder() to construct.
    private ClearPrepareGroupChatCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClearPrepareGroupChatCmd() {
      channelId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClearPrepareGroupChatCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClearPrepareGroupChatCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              channelId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd.class, com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNELID_FIELD_NUMBER = 1;
    private volatile java.lang.Object channelId_;
    /**
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public java.lang.String getChannelId() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelIdBytes() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd other = (com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd) obj;

      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (!getChannelId()
            .equals(other.getChannelId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClearPrepareGroupChatCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClearPrepareGroupChatCmd)
        com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd.class, com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd build() {
        com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd buildPartial() {
        com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd result = new com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelId_ = channelId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd other) {
        if (other == com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd.getDefaultInstance()) return this;
        if (other.hasChannelId()) {
          bitField0_ |= 0x00000001;
          channelId_ = other.channelId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object channelId_ = "";
      /**
       * <code>optional string channelId = 1;</code>
       * @return Whether the channelId field is set.
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return The channelId.
       */
      public java.lang.String getChannelId() {
        java.lang.Object ref = channelId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return The bytes for channelId.
       */
      public com.google.protobuf.ByteString
          getChannelIdBytes() {
        java.lang.Object ref = channelId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelId_ = getDefaultInstance().getChannelId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @param value The bytes for channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClearPrepareGroupChatCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClearPrepareGroupChatCmd)
    private static final com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd();
    }

    public static com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClearPrepareGroupChatCmd>
        PARSER = new com.google.protobuf.AbstractParser<ClearPrepareGroupChatCmd>() {
      @java.lang.Override
      public ClearPrepareGroupChatCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClearPrepareGroupChatCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClearPrepareGroupChatCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClearPrepareGroupChatCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.ClearPrepareGroupChatCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface JoinGroupChatCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.JoinGroupChatCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    java.lang.String getChannelId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    com.google.protobuf.ByteString
        getChannelIdBytes();

    /**
     * <pre>
     * 群聊最后一条消息的编号
     * </pre>
     *
     * <code>optional int64 maxIndex = 2;</code>
     * @return Whether the maxIndex field is set.
     */
    boolean hasMaxIndex();
    /**
     * <pre>
     * 群聊最后一条消息的编号
     * </pre>
     *
     * <code>optional int64 maxIndex = 2;</code>
     * @return The maxIndex.
     */
    long getMaxIndex();

    /**
     * <pre>
     * 群主id
     * </pre>
     *
     * <code>optional int64 owner = 3;</code>
     * @return Whether the owner field is set.
     */
    boolean hasOwner();
    /**
     * <pre>
     * 群主id
     * </pre>
     *
     * <code>optional int64 owner = 3;</code>
     * @return The owner.
     */
    long getOwner();

    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string groupName = 4;</code>
     * @return Whether the groupName field is set.
     */
    boolean hasGroupName();
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string groupName = 4;</code>
     * @return The groupName.
     */
    java.lang.String getGroupName();
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string groupName = 4;</code>
     * @return The bytes for groupName.
     */
    com.google.protobuf.ByteString
        getGroupNameBytes();

    /**
     * <pre>
     * 群聊版本
     * </pre>
     *
     * <code>optional int32 version = 5;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <pre>
     * 群聊版本
     * </pre>
     *
     * <code>optional int32 version = 5;</code>
     * @return The version.
     */
    int getVersion();
  }
  /**
   * Protobuf type {@code com.yorha.proto.JoinGroupChatCmd}
   */
  public static final class JoinGroupChatCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.JoinGroupChatCmd)
      JoinGroupChatCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use JoinGroupChatCmd.newBuilder() to construct.
    private JoinGroupChatCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private JoinGroupChatCmd() {
      channelId_ = "";
      groupName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new JoinGroupChatCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private JoinGroupChatCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              channelId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              maxIndex_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              owner_ = input.readInt64();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              groupName_ = bs;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              version_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_JoinGroupChatCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_JoinGroupChatCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.JoinGroupChatCmd.class, com.yorha.proto.SsPlayerChat.JoinGroupChatCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNELID_FIELD_NUMBER = 1;
    private volatile java.lang.Object channelId_;
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public java.lang.String getChannelId() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelIdBytes() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MAXINDEX_FIELD_NUMBER = 2;
    private long maxIndex_;
    /**
     * <pre>
     * 群聊最后一条消息的编号
     * </pre>
     *
     * <code>optional int64 maxIndex = 2;</code>
     * @return Whether the maxIndex field is set.
     */
    @java.lang.Override
    public boolean hasMaxIndex() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 群聊最后一条消息的编号
     * </pre>
     *
     * <code>optional int64 maxIndex = 2;</code>
     * @return The maxIndex.
     */
    @java.lang.Override
    public long getMaxIndex() {
      return maxIndex_;
    }

    public static final int OWNER_FIELD_NUMBER = 3;
    private long owner_;
    /**
     * <pre>
     * 群主id
     * </pre>
     *
     * <code>optional int64 owner = 3;</code>
     * @return Whether the owner field is set.
     */
    @java.lang.Override
    public boolean hasOwner() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 群主id
     * </pre>
     *
     * <code>optional int64 owner = 3;</code>
     * @return The owner.
     */
    @java.lang.Override
    public long getOwner() {
      return owner_;
    }

    public static final int GROUPNAME_FIELD_NUMBER = 4;
    private volatile java.lang.Object groupName_;
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string groupName = 4;</code>
     * @return Whether the groupName field is set.
     */
    @java.lang.Override
    public boolean hasGroupName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string groupName = 4;</code>
     * @return The groupName.
     */
    @java.lang.Override
    public java.lang.String getGroupName() {
      java.lang.Object ref = groupName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          groupName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 群聊名
     * </pre>
     *
     * <code>optional string groupName = 4;</code>
     * @return The bytes for groupName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGroupNameBytes() {
      java.lang.Object ref = groupName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        groupName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VERSION_FIELD_NUMBER = 5;
    private int version_;
    /**
     * <pre>
     * 群聊版本
     * </pre>
     *
     * <code>optional int32 version = 5;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 群聊版本
     * </pre>
     *
     * <code>optional int32 version = 5;</code>
     * @return The version.
     */
    @java.lang.Override
    public int getVersion() {
      return version_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, maxIndex_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, owner_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, groupName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, version_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, maxIndex_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, owner_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, groupName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, version_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.JoinGroupChatCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.JoinGroupChatCmd other = (com.yorha.proto.SsPlayerChat.JoinGroupChatCmd) obj;

      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (!getChannelId()
            .equals(other.getChannelId())) return false;
      }
      if (hasMaxIndex() != other.hasMaxIndex()) return false;
      if (hasMaxIndex()) {
        if (getMaxIndex()
            != other.getMaxIndex()) return false;
      }
      if (hasOwner() != other.hasOwner()) return false;
      if (hasOwner()) {
        if (getOwner()
            != other.getOwner()) return false;
      }
      if (hasGroupName() != other.hasGroupName()) return false;
      if (hasGroupName()) {
        if (!getGroupName()
            .equals(other.getGroupName())) return false;
      }
      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId().hashCode();
      }
      if (hasMaxIndex()) {
        hash = (37 * hash) + MAXINDEX_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMaxIndex());
      }
      if (hasOwner()) {
        hash = (37 * hash) + OWNER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwner());
      }
      if (hasGroupName()) {
        hash = (37 * hash) + GROUPNAME_FIELD_NUMBER;
        hash = (53 * hash) + getGroupName().hashCode();
      }
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getVersion();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.JoinGroupChatCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.JoinGroupChatCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.JoinGroupChatCmd)
        com.yorha.proto.SsPlayerChat.JoinGroupChatCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_JoinGroupChatCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_JoinGroupChatCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.JoinGroupChatCmd.class, com.yorha.proto.SsPlayerChat.JoinGroupChatCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.JoinGroupChatCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        maxIndex_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        owner_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        groupName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        version_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_JoinGroupChatCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.JoinGroupChatCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.JoinGroupChatCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.JoinGroupChatCmd build() {
        com.yorha.proto.SsPlayerChat.JoinGroupChatCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.JoinGroupChatCmd buildPartial() {
        com.yorha.proto.SsPlayerChat.JoinGroupChatCmd result = new com.yorha.proto.SsPlayerChat.JoinGroupChatCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelId_ = channelId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.maxIndex_ = maxIndex_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.owner_ = owner_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.groupName_ = groupName_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.JoinGroupChatCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.JoinGroupChatCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.JoinGroupChatCmd other) {
        if (other == com.yorha.proto.SsPlayerChat.JoinGroupChatCmd.getDefaultInstance()) return this;
        if (other.hasChannelId()) {
          bitField0_ |= 0x00000001;
          channelId_ = other.channelId_;
          onChanged();
        }
        if (other.hasMaxIndex()) {
          setMaxIndex(other.getMaxIndex());
        }
        if (other.hasOwner()) {
          setOwner(other.getOwner());
        }
        if (other.hasGroupName()) {
          bitField0_ |= 0x00000008;
          groupName_ = other.groupName_;
          onChanged();
        }
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.JoinGroupChatCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.JoinGroupChatCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object channelId_ = "";
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return Whether the channelId field is set.
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return The channelId.
       */
      public java.lang.String getChannelId() {
        java.lang.Object ref = channelId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return The bytes for channelId.
       */
      public com.google.protobuf.ByteString
          getChannelIdBytes() {
        java.lang.Object ref = channelId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelId_ = getDefaultInstance().getChannelId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @param value The bytes for channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }

      private long maxIndex_ ;
      /**
       * <pre>
       * 群聊最后一条消息的编号
       * </pre>
       *
       * <code>optional int64 maxIndex = 2;</code>
       * @return Whether the maxIndex field is set.
       */
      @java.lang.Override
      public boolean hasMaxIndex() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 群聊最后一条消息的编号
       * </pre>
       *
       * <code>optional int64 maxIndex = 2;</code>
       * @return The maxIndex.
       */
      @java.lang.Override
      public long getMaxIndex() {
        return maxIndex_;
      }
      /**
       * <pre>
       * 群聊最后一条消息的编号
       * </pre>
       *
       * <code>optional int64 maxIndex = 2;</code>
       * @param value The maxIndex to set.
       * @return This builder for chaining.
       */
      public Builder setMaxIndex(long value) {
        bitField0_ |= 0x00000002;
        maxIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊最后一条消息的编号
       * </pre>
       *
       * <code>optional int64 maxIndex = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxIndex() {
        bitField0_ = (bitField0_ & ~0x00000002);
        maxIndex_ = 0L;
        onChanged();
        return this;
      }

      private long owner_ ;
      /**
       * <pre>
       * 群主id
       * </pre>
       *
       * <code>optional int64 owner = 3;</code>
       * @return Whether the owner field is set.
       */
      @java.lang.Override
      public boolean hasOwner() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 群主id
       * </pre>
       *
       * <code>optional int64 owner = 3;</code>
       * @return The owner.
       */
      @java.lang.Override
      public long getOwner() {
        return owner_;
      }
      /**
       * <pre>
       * 群主id
       * </pre>
       *
       * <code>optional int64 owner = 3;</code>
       * @param value The owner to set.
       * @return This builder for chaining.
       */
      public Builder setOwner(long value) {
        bitField0_ |= 0x00000004;
        owner_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群主id
       * </pre>
       *
       * <code>optional int64 owner = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwner() {
        bitField0_ = (bitField0_ & ~0x00000004);
        owner_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object groupName_ = "";
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string groupName = 4;</code>
       * @return Whether the groupName field is set.
       */
      public boolean hasGroupName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string groupName = 4;</code>
       * @return The groupName.
       */
      public java.lang.String getGroupName() {
        java.lang.Object ref = groupName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            groupName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string groupName = 4;</code>
       * @return The bytes for groupName.
       */
      public com.google.protobuf.ByteString
          getGroupNameBytes() {
        java.lang.Object ref = groupName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          groupName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string groupName = 4;</code>
       * @param value The groupName to set.
       * @return This builder for chaining.
       */
      public Builder setGroupName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        groupName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string groupName = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        groupName_ = getDefaultInstance().getGroupName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊名
       * </pre>
       *
       * <code>optional string groupName = 4;</code>
       * @param value The bytes for groupName to set.
       * @return This builder for chaining.
       */
      public Builder setGroupNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        groupName_ = value;
        onChanged();
        return this;
      }

      private int version_ ;
      /**
       * <pre>
       * 群聊版本
       * </pre>
       *
       * <code>optional int32 version = 5;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 群聊版本
       * </pre>
       *
       * <code>optional int32 version = 5;</code>
       * @return The version.
       */
      @java.lang.Override
      public int getVersion() {
        return version_;
      }
      /**
       * <pre>
       * 群聊版本
       * </pre>
       *
       * <code>optional int32 version = 5;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(int value) {
        bitField0_ |= 0x00000010;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊版本
       * </pre>
       *
       * <code>optional int32 version = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000010);
        version_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.JoinGroupChatCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.JoinGroupChatCmd)
    private static final com.yorha.proto.SsPlayerChat.JoinGroupChatCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.JoinGroupChatCmd();
    }

    public static com.yorha.proto.SsPlayerChat.JoinGroupChatCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<JoinGroupChatCmd>
        PARSER = new com.google.protobuf.AbstractParser<JoinGroupChatCmd>() {
      @java.lang.Override
      public JoinGroupChatCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new JoinGroupChatCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<JoinGroupChatCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<JoinGroupChatCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.JoinGroupChatCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LeaveGroupChatCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.LeaveGroupChatCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    java.lang.String getChannelId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    com.google.protobuf.ByteString
        getChannelIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.LeaveGroupChatCmd}
   */
  public static final class LeaveGroupChatCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.LeaveGroupChatCmd)
      LeaveGroupChatCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LeaveGroupChatCmd.newBuilder() to construct.
    private LeaveGroupChatCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LeaveGroupChatCmd() {
      channelId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LeaveGroupChatCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private LeaveGroupChatCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              channelId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_LeaveGroupChatCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_LeaveGroupChatCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd.class, com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNELID_FIELD_NUMBER = 1;
    private volatile java.lang.Object channelId_;
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public java.lang.String getChannelId() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelIdBytes() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd other = (com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd) obj;

      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (!getChannelId()
            .equals(other.getChannelId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.LeaveGroupChatCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.LeaveGroupChatCmd)
        com.yorha.proto.SsPlayerChat.LeaveGroupChatCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_LeaveGroupChatCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_LeaveGroupChatCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd.class, com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_LeaveGroupChatCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd build() {
        com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd buildPartial() {
        com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd result = new com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelId_ = channelId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd other) {
        if (other == com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd.getDefaultInstance()) return this;
        if (other.hasChannelId()) {
          bitField0_ |= 0x00000001;
          channelId_ = other.channelId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object channelId_ = "";
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return Whether the channelId field is set.
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return The channelId.
       */
      public java.lang.String getChannelId() {
        java.lang.Object ref = channelId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return The bytes for channelId.
       */
      public com.google.protobuf.ByteString
          getChannelIdBytes() {
        java.lang.Object ref = channelId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelId_ = getDefaultInstance().getChannelId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string channelId = 1;</code>
       * @param value The bytes for channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.LeaveGroupChatCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.LeaveGroupChatCmd)
    private static final com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd();
    }

    public static com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<LeaveGroupChatCmd>
        PARSER = new com.google.protobuf.AbstractParser<LeaveGroupChatCmd>() {
      @java.lang.Override
      public LeaveGroupChatCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LeaveGroupChatCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<LeaveGroupChatCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LeaveGroupChatCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.LeaveGroupChatCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface HandleNewMessageAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.HandleNewMessageAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     * @return Whether the chatSession field is set.
     */
    boolean hasChatSession();
    /**
     * <pre>
     * 聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     * @return The chatSession.
     */
    com.yorha.proto.CommonMsg.ChatSession getChatSession();
    /**
     * <pre>
     * 聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder();

    /**
     * <pre>
     * 新消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
     * @return Whether the newMessage field is set.
     */
    boolean hasNewMessage();
    /**
     * <pre>
     * 新消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
     * @return The newMessage.
     */
    com.yorha.proto.CommonMsg.ChatMessage getNewMessage();
    /**
     * <pre>
     * 新消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getNewMessageOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.HandleNewMessageAsk}
   */
  public static final class HandleNewMessageAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.HandleNewMessageAsk)
      HandleNewMessageAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use HandleNewMessageAsk.newBuilder() to construct.
    private HandleNewMessageAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private HandleNewMessageAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new HandleNewMessageAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private HandleNewMessageAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ChatSession.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = chatSession_.toBuilder();
              }
              chatSession_ = input.readMessage(com.yorha.proto.CommonMsg.ChatSession.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatSession_);
                chatSession_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.ChatMessage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = newMessage_.toBuilder();
              }
              newMessage_ = input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(newMessage_);
                newMessage_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.HandleNewMessageAsk.class, com.yorha.proto.SsPlayerChat.HandleNewMessageAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHATSESSION_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ChatSession chatSession_;
    /**
     * <pre>
     * 聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     * @return Whether the chatSession field is set.
     */
    @java.lang.Override
    public boolean hasChatSession() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     * @return The chatSession.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatSession getChatSession() {
      return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
    }
    /**
     * <pre>
     * 聊天会话
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder() {
      return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
    }

    public static final int NEWMESSAGE_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.ChatMessage newMessage_;
    /**
     * <pre>
     * 新消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
     * @return Whether the newMessage field is set.
     */
    @java.lang.Override
    public boolean hasNewMessage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 新消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
     * @return The newMessage.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getNewMessage() {
      return newMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : newMessage_;
    }
    /**
     * <pre>
     * 新消息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getNewMessageOrBuilder() {
      return newMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : newMessage_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getChatSession());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getNewMessage());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getChatSession());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getNewMessage());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.HandleNewMessageAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.HandleNewMessageAsk other = (com.yorha.proto.SsPlayerChat.HandleNewMessageAsk) obj;

      if (hasChatSession() != other.hasChatSession()) return false;
      if (hasChatSession()) {
        if (!getChatSession()
            .equals(other.getChatSession())) return false;
      }
      if (hasNewMessage() != other.hasNewMessage()) return false;
      if (hasNewMessage()) {
        if (!getNewMessage()
            .equals(other.getNewMessage())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatSession()) {
        hash = (37 * hash) + CHATSESSION_FIELD_NUMBER;
        hash = (53 * hash) + getChatSession().hashCode();
      }
      if (hasNewMessage()) {
        hash = (37 * hash) + NEWMESSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getNewMessage().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.HandleNewMessageAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.HandleNewMessageAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.HandleNewMessageAsk)
        com.yorha.proto.SsPlayerChat.HandleNewMessageAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.HandleNewMessageAsk.class, com.yorha.proto.SsPlayerChat.HandleNewMessageAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.HandleNewMessageAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatSessionFieldBuilder();
          getNewMessageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatSessionBuilder_ == null) {
          chatSession_ = null;
        } else {
          chatSessionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (newMessageBuilder_ == null) {
          newMessage_ = null;
        } else {
          newMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.HandleNewMessageAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.HandleNewMessageAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.HandleNewMessageAsk build() {
        com.yorha.proto.SsPlayerChat.HandleNewMessageAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.HandleNewMessageAsk buildPartial() {
        com.yorha.proto.SsPlayerChat.HandleNewMessageAsk result = new com.yorha.proto.SsPlayerChat.HandleNewMessageAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (chatSessionBuilder_ == null) {
            result.chatSession_ = chatSession_;
          } else {
            result.chatSession_ = chatSessionBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (newMessageBuilder_ == null) {
            result.newMessage_ = newMessage_;
          } else {
            result.newMessage_ = newMessageBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.HandleNewMessageAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.HandleNewMessageAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.HandleNewMessageAsk other) {
        if (other == com.yorha.proto.SsPlayerChat.HandleNewMessageAsk.getDefaultInstance()) return this;
        if (other.hasChatSession()) {
          mergeChatSession(other.getChatSession());
        }
        if (other.hasNewMessage()) {
          mergeNewMessage(other.getNewMessage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.HandleNewMessageAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.HandleNewMessageAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatSession chatSession_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder> chatSessionBuilder_;
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       * @return Whether the chatSession field is set.
       */
      public boolean hasChatSession() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       * @return The chatSession.
       */
      public com.yorha.proto.CommonMsg.ChatSession getChatSession() {
        if (chatSessionBuilder_ == null) {
          return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
        } else {
          return chatSessionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public Builder setChatSession(com.yorha.proto.CommonMsg.ChatSession value) {
        if (chatSessionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatSession_ = value;
          onChanged();
        } else {
          chatSessionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public Builder setChatSession(
          com.yorha.proto.CommonMsg.ChatSession.Builder builderForValue) {
        if (chatSessionBuilder_ == null) {
          chatSession_ = builderForValue.build();
          onChanged();
        } else {
          chatSessionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public Builder mergeChatSession(com.yorha.proto.CommonMsg.ChatSession value) {
        if (chatSessionBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              chatSession_ != null &&
              chatSession_ != com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance()) {
            chatSession_ =
              com.yorha.proto.CommonMsg.ChatSession.newBuilder(chatSession_).mergeFrom(value).buildPartial();
          } else {
            chatSession_ = value;
          }
          onChanged();
        } else {
          chatSessionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public Builder clearChatSession() {
        if (chatSessionBuilder_ == null) {
          chatSession_ = null;
          onChanged();
        } else {
          chatSessionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatSession.Builder getChatSessionBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatSessionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder() {
        if (chatSessionBuilder_ != null) {
          return chatSessionBuilder_.getMessageOrBuilder();
        } else {
          return chatSession_ == null ?
              com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
        }
      }
      /**
       * <pre>
       * 聊天会话
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder> 
          getChatSessionFieldBuilder() {
        if (chatSessionBuilder_ == null) {
          chatSessionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder>(
                  getChatSession(),
                  getParentForChildren(),
                  isClean());
          chatSession_ = null;
        }
        return chatSessionBuilder_;
      }

      private com.yorha.proto.CommonMsg.ChatMessage newMessage_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> newMessageBuilder_;
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       * @return Whether the newMessage field is set.
       */
      public boolean hasNewMessage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       * @return The newMessage.
       */
      public com.yorha.proto.CommonMsg.ChatMessage getNewMessage() {
        if (newMessageBuilder_ == null) {
          return newMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : newMessage_;
        } else {
          return newMessageBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       */
      public Builder setNewMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (newMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          newMessage_ = value;
          onChanged();
        } else {
          newMessageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       */
      public Builder setNewMessage(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (newMessageBuilder_ == null) {
          newMessage_ = builderForValue.build();
          onChanged();
        } else {
          newMessageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       */
      public Builder mergeNewMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (newMessageBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              newMessage_ != null &&
              newMessage_ != com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance()) {
            newMessage_ =
              com.yorha.proto.CommonMsg.ChatMessage.newBuilder(newMessage_).mergeFrom(value).buildPartial();
          } else {
            newMessage_ = value;
          }
          onChanged();
        } else {
          newMessageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       */
      public Builder clearNewMessage() {
        if (newMessageBuilder_ == null) {
          newMessage_ = null;
          onChanged();
        } else {
          newMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getNewMessageBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getNewMessageFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getNewMessageOrBuilder() {
        if (newMessageBuilder_ != null) {
          return newMessageBuilder_.getMessageOrBuilder();
        } else {
          return newMessage_ == null ?
              com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : newMessage_;
        }
      }
      /**
       * <pre>
       * 新消息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ChatMessage newMessage = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getNewMessageFieldBuilder() {
        if (newMessageBuilder_ == null) {
          newMessageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  getNewMessage(),
                  getParentForChildren(),
                  isClean());
          newMessage_ = null;
        }
        return newMessageBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.HandleNewMessageAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.HandleNewMessageAsk)
    private static final com.yorha.proto.SsPlayerChat.HandleNewMessageAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.HandleNewMessageAsk();
    }

    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<HandleNewMessageAsk>
        PARSER = new com.google.protobuf.AbstractParser<HandleNewMessageAsk>() {
      @java.lang.Override
      public HandleNewMessageAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new HandleNewMessageAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<HandleNewMessageAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<HandleNewMessageAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.HandleNewMessageAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface HandleNewMessageAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.HandleNewMessageAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   * 废弃index 1, 2
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.HandleNewMessageAns}
   */
  public static final class HandleNewMessageAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.HandleNewMessageAns)
      HandleNewMessageAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use HandleNewMessageAns.newBuilder() to construct.
    private HandleNewMessageAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private HandleNewMessageAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new HandleNewMessageAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private HandleNewMessageAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.HandleNewMessageAns.class, com.yorha.proto.SsPlayerChat.HandleNewMessageAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.HandleNewMessageAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.HandleNewMessageAns other = (com.yorha.proto.SsPlayerChat.HandleNewMessageAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.HandleNewMessageAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 废弃index 1, 2
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.HandleNewMessageAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.HandleNewMessageAns)
        com.yorha.proto.SsPlayerChat.HandleNewMessageAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.HandleNewMessageAns.class, com.yorha.proto.SsPlayerChat.HandleNewMessageAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.HandleNewMessageAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_HandleNewMessageAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.HandleNewMessageAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.HandleNewMessageAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.HandleNewMessageAns build() {
        com.yorha.proto.SsPlayerChat.HandleNewMessageAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.HandleNewMessageAns buildPartial() {
        com.yorha.proto.SsPlayerChat.HandleNewMessageAns result = new com.yorha.proto.SsPlayerChat.HandleNewMessageAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.HandleNewMessageAns) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.HandleNewMessageAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.HandleNewMessageAns other) {
        if (other == com.yorha.proto.SsPlayerChat.HandleNewMessageAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.HandleNewMessageAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.HandleNewMessageAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.HandleNewMessageAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.HandleNewMessageAns)
    private static final com.yorha.proto.SsPlayerChat.HandleNewMessageAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.HandleNewMessageAns();
    }

    public static com.yorha.proto.SsPlayerChat.HandleNewMessageAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<HandleNewMessageAns>
        PARSER = new com.google.protobuf.AbstractParser<HandleNewMessageAns>() {
      @java.lang.Override
      public HandleNewMessageAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new HandleNewMessageAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<HandleNewMessageAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<HandleNewMessageAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.HandleNewMessageAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GroupInfoChangeNtfOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GroupInfoChangeNtf)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return Whether the groupChatId field is set.
     */
    boolean hasGroupChatId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return The groupChatId.
     */
    java.lang.String getGroupChatId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return The bytes for groupChatId.
     */
    com.google.protobuf.ByteString
        getGroupChatIdBytes();

    /**
     * <pre>
     * 新的版本号
     * </pre>
     *
     * <code>optional int32 version = 2;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <pre>
     * 新的版本号
     * </pre>
     *
     * <code>optional int32 version = 2;</code>
     * @return The version.
     */
    int getVersion();

    /**
     * <pre>
     * 新的群主
     * </pre>
     *
     * <code>optional int64 owner = 3;</code>
     * @return Whether the owner field is set.
     */
    boolean hasOwner();
    /**
     * <pre>
     * 新的群主
     * </pre>
     *
     * <code>optional int64 owner = 3;</code>
     * @return The owner.
     */
    long getOwner();

    /**
     * <pre>
     * 新的群名
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <pre>
     * 新的群名
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     * 新的群名
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GroupInfoChangeNtf}
   */
  public static final class GroupInfoChangeNtf extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GroupInfoChangeNtf)
      GroupInfoChangeNtfOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GroupInfoChangeNtf.newBuilder() to construct.
    private GroupInfoChangeNtf(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GroupInfoChangeNtf() {
      groupChatId_ = "";
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GroupInfoChangeNtf();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GroupInfoChangeNtf(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              groupChatId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              version_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              owner_ = input.readInt64();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              name_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupInfoChangeNtf_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupInfoChangeNtf_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf.class, com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf.Builder.class);
    }

    private int bitField0_;
    public static final int GROUPCHATID_FIELD_NUMBER = 1;
    private volatile java.lang.Object groupChatId_;
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return Whether the groupChatId field is set.
     */
    @java.lang.Override
    public boolean hasGroupChatId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return The groupChatId.
     */
    @java.lang.Override
    public java.lang.String getGroupChatId() {
      java.lang.Object ref = groupChatId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          groupChatId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return The bytes for groupChatId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGroupChatIdBytes() {
      java.lang.Object ref = groupChatId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        groupChatId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VERSION_FIELD_NUMBER = 2;
    private int version_;
    /**
     * <pre>
     * 新的版本号
     * </pre>
     *
     * <code>optional int32 version = 2;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 新的版本号
     * </pre>
     *
     * <code>optional int32 version = 2;</code>
     * @return The version.
     */
    @java.lang.Override
    public int getVersion() {
      return version_;
    }

    public static final int OWNER_FIELD_NUMBER = 3;
    private long owner_;
    /**
     * <pre>
     * 新的群主
     * </pre>
     *
     * <code>optional int64 owner = 3;</code>
     * @return Whether the owner field is set.
     */
    @java.lang.Override
    public boolean hasOwner() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 新的群主
     * </pre>
     *
     * <code>optional int64 owner = 3;</code>
     * @return The owner.
     */
    @java.lang.Override
    public long getOwner() {
      return owner_;
    }

    public static final int NAME_FIELD_NUMBER = 4;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     * 新的群名
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 新的群名
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 新的群名
     * </pre>
     *
     * <code>optional string name = 4;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, groupChatId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, version_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, owner_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, groupChatId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, version_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, owner_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf other = (com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf) obj;

      if (hasGroupChatId() != other.hasGroupChatId()) return false;
      if (hasGroupChatId()) {
        if (!getGroupChatId()
            .equals(other.getGroupChatId())) return false;
      }
      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (hasOwner() != other.hasOwner()) return false;
      if (hasOwner()) {
        if (getOwner()
            != other.getOwner()) return false;
      }
      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGroupChatId()) {
        hash = (37 * hash) + GROUPCHATID_FIELD_NUMBER;
        hash = (53 * hash) + getGroupChatId().hashCode();
      }
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getVersion();
      }
      if (hasOwner()) {
        hash = (37 * hash) + OWNER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwner());
      }
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GroupInfoChangeNtf}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GroupInfoChangeNtf)
        com.yorha.proto.SsPlayerChat.GroupInfoChangeNtfOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupInfoChangeNtf_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupInfoChangeNtf_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf.class, com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        groupChatId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        version_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        owner_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupInfoChangeNtf_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf build() {
        com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf buildPartial() {
        com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf result = new com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.groupChatId_ = groupChatId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.owner_ = owner_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.name_ = name_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf other) {
        if (other == com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf.getDefaultInstance()) return this;
        if (other.hasGroupChatId()) {
          bitField0_ |= 0x00000001;
          groupChatId_ = other.groupChatId_;
          onChanged();
        }
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        if (other.hasOwner()) {
          setOwner(other.getOwner());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000008;
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object groupChatId_ = "";
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @return Whether the groupChatId field is set.
       */
      public boolean hasGroupChatId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @return The groupChatId.
       */
      public java.lang.String getGroupChatId() {
        java.lang.Object ref = groupChatId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            groupChatId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @return The bytes for groupChatId.
       */
      public com.google.protobuf.ByteString
          getGroupChatIdBytes() {
        java.lang.Object ref = groupChatId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          groupChatId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @param value The groupChatId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupChatId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        groupChatId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupChatId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        groupChatId_ = getDefaultInstance().getGroupChatId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @param value The bytes for groupChatId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupChatIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        groupChatId_ = value;
        onChanged();
        return this;
      }

      private int version_ ;
      /**
       * <pre>
       * 新的版本号
       * </pre>
       *
       * <code>optional int32 version = 2;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 新的版本号
       * </pre>
       *
       * <code>optional int32 version = 2;</code>
       * @return The version.
       */
      @java.lang.Override
      public int getVersion() {
        return version_;
      }
      /**
       * <pre>
       * 新的版本号
       * </pre>
       *
       * <code>optional int32 version = 2;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(int value) {
        bitField0_ |= 0x00000002;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新的版本号
       * </pre>
       *
       * <code>optional int32 version = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000002);
        version_ = 0;
        onChanged();
        return this;
      }

      private long owner_ ;
      /**
       * <pre>
       * 新的群主
       * </pre>
       *
       * <code>optional int64 owner = 3;</code>
       * @return Whether the owner field is set.
       */
      @java.lang.Override
      public boolean hasOwner() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 新的群主
       * </pre>
       *
       * <code>optional int64 owner = 3;</code>
       * @return The owner.
       */
      @java.lang.Override
      public long getOwner() {
        return owner_;
      }
      /**
       * <pre>
       * 新的群主
       * </pre>
       *
       * <code>optional int64 owner = 3;</code>
       * @param value The owner to set.
       * @return This builder for chaining.
       */
      public Builder setOwner(long value) {
        bitField0_ |= 0x00000004;
        owner_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新的群主
       * </pre>
       *
       * <code>optional int64 owner = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwner() {
        bitField0_ = (bitField0_ & ~0x00000004);
        owner_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       * 新的群名
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 新的群名
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 新的群名
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 新的群名
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新的群名
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新的群名
       * </pre>
       *
       * <code>optional string name = 4;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GroupInfoChangeNtf)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GroupInfoChangeNtf)
    private static final com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf();
    }

    public static com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GroupInfoChangeNtf>
        PARSER = new com.google.protobuf.AbstractParser<GroupInfoChangeNtf>() {
      @java.lang.Override
      public GroupInfoChangeNtf parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GroupInfoChangeNtf(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GroupInfoChangeNtf> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GroupInfoChangeNtf> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.GroupInfoChangeNtf getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GroupDismissNtfOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GroupDismissNtf)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return Whether the groupChatId field is set.
     */
    boolean hasGroupChatId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return The groupChatId.
     */
    java.lang.String getGroupChatId();
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return The bytes for groupChatId.
     */
    com.google.protobuf.ByteString
        getGroupChatIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GroupDismissNtf}
   */
  public static final class GroupDismissNtf extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GroupDismissNtf)
      GroupDismissNtfOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GroupDismissNtf.newBuilder() to construct.
    private GroupDismissNtf(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GroupDismissNtf() {
      groupChatId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GroupDismissNtf();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GroupDismissNtf(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              groupChatId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupDismissNtf_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupDismissNtf_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.GroupDismissNtf.class, com.yorha.proto.SsPlayerChat.GroupDismissNtf.Builder.class);
    }

    private int bitField0_;
    public static final int GROUPCHATID_FIELD_NUMBER = 1;
    private volatile java.lang.Object groupChatId_;
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return Whether the groupChatId field is set.
     */
    @java.lang.Override
    public boolean hasGroupChatId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return The groupChatId.
     */
    @java.lang.Override
    public java.lang.String getGroupChatId() {
      java.lang.Object ref = groupChatId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          groupChatId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 群聊id
     * </pre>
     *
     * <code>optional string groupChatId = 1;</code>
     * @return The bytes for groupChatId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGroupChatIdBytes() {
      java.lang.Object ref = groupChatId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        groupChatId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, groupChatId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, groupChatId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.GroupDismissNtf)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.GroupDismissNtf other = (com.yorha.proto.SsPlayerChat.GroupDismissNtf) obj;

      if (hasGroupChatId() != other.hasGroupChatId()) return false;
      if (hasGroupChatId()) {
        if (!getGroupChatId()
            .equals(other.getGroupChatId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGroupChatId()) {
        hash = (37 * hash) + GROUPCHATID_FIELD_NUMBER;
        hash = (53 * hash) + getGroupChatId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.GroupDismissNtf prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GroupDismissNtf}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GroupDismissNtf)
        com.yorha.proto.SsPlayerChat.GroupDismissNtfOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupDismissNtf_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupDismissNtf_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.GroupDismissNtf.class, com.yorha.proto.SsPlayerChat.GroupDismissNtf.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.GroupDismissNtf.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        groupChatId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupDismissNtf_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupDismissNtf getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.GroupDismissNtf.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupDismissNtf build() {
        com.yorha.proto.SsPlayerChat.GroupDismissNtf result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupDismissNtf buildPartial() {
        com.yorha.proto.SsPlayerChat.GroupDismissNtf result = new com.yorha.proto.SsPlayerChat.GroupDismissNtf(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.groupChatId_ = groupChatId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.GroupDismissNtf) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.GroupDismissNtf)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.GroupDismissNtf other) {
        if (other == com.yorha.proto.SsPlayerChat.GroupDismissNtf.getDefaultInstance()) return this;
        if (other.hasGroupChatId()) {
          bitField0_ |= 0x00000001;
          groupChatId_ = other.groupChatId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.GroupDismissNtf parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.GroupDismissNtf) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object groupChatId_ = "";
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @return Whether the groupChatId field is set.
       */
      public boolean hasGroupChatId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @return The groupChatId.
       */
      public java.lang.String getGroupChatId() {
        java.lang.Object ref = groupChatId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            groupChatId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @return The bytes for groupChatId.
       */
      public com.google.protobuf.ByteString
          getGroupChatIdBytes() {
        java.lang.Object ref = groupChatId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          groupChatId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @param value The groupChatId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupChatId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        groupChatId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroupChatId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        groupChatId_ = getDefaultInstance().getGroupChatId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 群聊id
       * </pre>
       *
       * <code>optional string groupChatId = 1;</code>
       * @param value The bytes for groupChatId to set.
       * @return This builder for chaining.
       */
      public Builder setGroupChatIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        groupChatId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GroupDismissNtf)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GroupDismissNtf)
    private static final com.yorha.proto.SsPlayerChat.GroupDismissNtf DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.GroupDismissNtf();
    }

    public static com.yorha.proto.SsPlayerChat.GroupDismissNtf getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GroupDismissNtf>
        PARSER = new com.google.protobuf.AbstractParser<GroupDismissNtf>() {
      @java.lang.Override
      public GroupDismissNtf parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GroupDismissNtf(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GroupDismissNtf> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GroupDismissNtf> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.GroupDismissNtf getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface IgnoreMsgNtfOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.IgnoreMsgNtf)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     * @return Whether the chatSession field is set.
     */
    boolean hasChatSession();
    /**
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     * @return The chatSession.
     */
    com.yorha.proto.CommonMsg.ChatSession getChatSession();
    /**
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     */
    com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.IgnoreMsgNtf}
   */
  public static final class IgnoreMsgNtf extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.IgnoreMsgNtf)
      IgnoreMsgNtfOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use IgnoreMsgNtf.newBuilder() to construct.
    private IgnoreMsgNtf(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private IgnoreMsgNtf() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new IgnoreMsgNtf();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private IgnoreMsgNtf(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ChatSession.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = chatSession_.toBuilder();
              }
              chatSession_ = input.readMessage(com.yorha.proto.CommonMsg.ChatSession.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatSession_);
                chatSession_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_IgnoreMsgNtf_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_IgnoreMsgNtf_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.IgnoreMsgNtf.class, com.yorha.proto.SsPlayerChat.IgnoreMsgNtf.Builder.class);
    }

    private int bitField0_;
    public static final int CHATSESSION_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ChatSession chatSession_;
    /**
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     * @return Whether the chatSession field is set.
     */
    @java.lang.Override
    public boolean hasChatSession() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     * @return The chatSession.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatSession getChatSession() {
      return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
    }
    /**
     * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder() {
      return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getChatSession());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getChatSession());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.IgnoreMsgNtf)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.IgnoreMsgNtf other = (com.yorha.proto.SsPlayerChat.IgnoreMsgNtf) obj;

      if (hasChatSession() != other.hasChatSession()) return false;
      if (hasChatSession()) {
        if (!getChatSession()
            .equals(other.getChatSession())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatSession()) {
        hash = (37 * hash) + CHATSESSION_FIELD_NUMBER;
        hash = (53 * hash) + getChatSession().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.IgnoreMsgNtf prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.IgnoreMsgNtf}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.IgnoreMsgNtf)
        com.yorha.proto.SsPlayerChat.IgnoreMsgNtfOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_IgnoreMsgNtf_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_IgnoreMsgNtf_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.IgnoreMsgNtf.class, com.yorha.proto.SsPlayerChat.IgnoreMsgNtf.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.IgnoreMsgNtf.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatSessionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatSessionBuilder_ == null) {
          chatSession_ = null;
        } else {
          chatSessionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_IgnoreMsgNtf_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.IgnoreMsgNtf getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.IgnoreMsgNtf.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.IgnoreMsgNtf build() {
        com.yorha.proto.SsPlayerChat.IgnoreMsgNtf result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.IgnoreMsgNtf buildPartial() {
        com.yorha.proto.SsPlayerChat.IgnoreMsgNtf result = new com.yorha.proto.SsPlayerChat.IgnoreMsgNtf(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (chatSessionBuilder_ == null) {
            result.chatSession_ = chatSession_;
          } else {
            result.chatSession_ = chatSessionBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.IgnoreMsgNtf) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.IgnoreMsgNtf)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.IgnoreMsgNtf other) {
        if (other == com.yorha.proto.SsPlayerChat.IgnoreMsgNtf.getDefaultInstance()) return this;
        if (other.hasChatSession()) {
          mergeChatSession(other.getChatSession());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.IgnoreMsgNtf parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.IgnoreMsgNtf) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatSession chatSession_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder> chatSessionBuilder_;
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       * @return Whether the chatSession field is set.
       */
      public boolean hasChatSession() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       * @return The chatSession.
       */
      public com.yorha.proto.CommonMsg.ChatSession getChatSession() {
        if (chatSessionBuilder_ == null) {
          return chatSession_ == null ? com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
        } else {
          return chatSessionBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public Builder setChatSession(com.yorha.proto.CommonMsg.ChatSession value) {
        if (chatSessionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatSession_ = value;
          onChanged();
        } else {
          chatSessionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public Builder setChatSession(
          com.yorha.proto.CommonMsg.ChatSession.Builder builderForValue) {
        if (chatSessionBuilder_ == null) {
          chatSession_ = builderForValue.build();
          onChanged();
        } else {
          chatSessionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public Builder mergeChatSession(com.yorha.proto.CommonMsg.ChatSession value) {
        if (chatSessionBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              chatSession_ != null &&
              chatSession_ != com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance()) {
            chatSession_ =
              com.yorha.proto.CommonMsg.ChatSession.newBuilder(chatSession_).mergeFrom(value).buildPartial();
          } else {
            chatSession_ = value;
          }
          onChanged();
        } else {
          chatSessionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public Builder clearChatSession() {
        if (chatSessionBuilder_ == null) {
          chatSession_ = null;
          onChanged();
        } else {
          chatSessionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatSession.Builder getChatSessionBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatSessionFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ChatSessionOrBuilder getChatSessionOrBuilder() {
        if (chatSessionBuilder_ != null) {
          return chatSessionBuilder_.getMessageOrBuilder();
        } else {
          return chatSession_ == null ?
              com.yorha.proto.CommonMsg.ChatSession.getDefaultInstance() : chatSession_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ChatSession chatSession = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder> 
          getChatSessionFieldBuilder() {
        if (chatSessionBuilder_ == null) {
          chatSessionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatSession, com.yorha.proto.CommonMsg.ChatSession.Builder, com.yorha.proto.CommonMsg.ChatSessionOrBuilder>(
                  getChatSession(),
                  getParentForChildren(),
                  isClean());
          chatSession_ = null;
        }
        return chatSessionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.IgnoreMsgNtf)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.IgnoreMsgNtf)
    private static final com.yorha.proto.SsPlayerChat.IgnoreMsgNtf DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.IgnoreMsgNtf();
    }

    public static com.yorha.proto.SsPlayerChat.IgnoreMsgNtf getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<IgnoreMsgNtf>
        PARSER = new com.google.protobuf.AbstractParser<IgnoreMsgNtf>() {
      @java.lang.Override
      public IgnoreMsgNtf parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new IgnoreMsgNtf(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<IgnoreMsgNtf> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IgnoreMsgNtf> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.IgnoreMsgNtf getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TryClearPrepareChatNtfOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.TryClearPrepareChatNtf)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    java.lang.String getChannelId();
    /**
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    com.google.protobuf.ByteString
        getChannelIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.TryClearPrepareChatNtf}
   */
  public static final class TryClearPrepareChatNtf extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.TryClearPrepareChatNtf)
      TryClearPrepareChatNtfOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TryClearPrepareChatNtf.newBuilder() to construct.
    private TryClearPrepareChatNtf(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TryClearPrepareChatNtf() {
      channelId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TryClearPrepareChatNtf();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TryClearPrepareChatNtf(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              channelId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_TryClearPrepareChatNtf_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_TryClearPrepareChatNtf_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf.class, com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNELID_FIELD_NUMBER = 1;
    private volatile java.lang.Object channelId_;
    /**
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public java.lang.String getChannelId() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelIdBytes() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf other = (com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf) obj;

      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (!getChannelId()
            .equals(other.getChannelId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.TryClearPrepareChatNtf}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.TryClearPrepareChatNtf)
        com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtfOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_TryClearPrepareChatNtf_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_TryClearPrepareChatNtf_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf.class, com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_TryClearPrepareChatNtf_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf build() {
        com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf buildPartial() {
        com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf result = new com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelId_ = channelId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf other) {
        if (other == com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf.getDefaultInstance()) return this;
        if (other.hasChannelId()) {
          bitField0_ |= 0x00000001;
          channelId_ = other.channelId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object channelId_ = "";
      /**
       * <code>optional string channelId = 1;</code>
       * @return Whether the channelId field is set.
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return The channelId.
       */
      public java.lang.String getChannelId() {
        java.lang.Object ref = channelId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return The bytes for channelId.
       */
      public com.google.protobuf.ByteString
          getChannelIdBytes() {
        java.lang.Object ref = channelId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelId_ = getDefaultInstance().getChannelId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @param value The bytes for channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.TryClearPrepareChatNtf)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.TryClearPrepareChatNtf)
    private static final com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf();
    }

    public static com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TryClearPrepareChatNtf>
        PARSER = new com.google.protobuf.AbstractParser<TryClearPrepareChatNtf>() {
      @java.lang.Override
      public TryClearPrepareChatNtf parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TryClearPrepareChatNtf(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TryClearPrepareChatNtf> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TryClearPrepareChatNtf> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.TryClearPrepareChatNtf getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReceivePrivateMsgAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ReceivePrivateMsgAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    java.lang.String getChannelId();
    /**
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    com.google.protobuf.ByteString
        getChannelIdBytes();

    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
     * @return Whether the chatMessage field is set.
     */
    boolean hasChatMessage();
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
     * @return The chatMessage.
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMessage();
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ReceivePrivateMsgAsk}
   */
  public static final class ReceivePrivateMsgAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ReceivePrivateMsgAsk)
      ReceivePrivateMsgAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReceivePrivateMsgAsk.newBuilder() to construct.
    private ReceivePrivateMsgAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReceivePrivateMsgAsk() {
      channelId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReceivePrivateMsgAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReceivePrivateMsgAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              channelId_ = bs;
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.ChatMessage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = chatMessage_.toBuilder();
              }
              chatMessage_ = input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatMessage_);
                chatMessage_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk.class, com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNELID_FIELD_NUMBER = 1;
    private volatile java.lang.Object channelId_;
    /**
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public java.lang.String getChannelId() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelIdBytes() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CHATMESSAGE_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
     * @return Whether the chatMessage field is set.
     */
    @java.lang.Override
    public boolean hasChatMessage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
     * @return The chatMessage.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getChatMessage());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getChatMessage());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk other = (com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk) obj;

      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (!getChannelId()
            .equals(other.getChannelId())) return false;
      }
      if (hasChatMessage() != other.hasChatMessage()) return false;
      if (hasChatMessage()) {
        if (!getChatMessage()
            .equals(other.getChatMessage())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId().hashCode();
      }
      if (hasChatMessage()) {
        hash = (37 * hash) + CHATMESSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getChatMessage().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ReceivePrivateMsgAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ReceivePrivateMsgAsk)
        com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk.class, com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMessageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk build() {
        com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk buildPartial() {
        com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk result = new com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelId_ = channelId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (chatMessageBuilder_ == null) {
            result.chatMessage_ = chatMessage_;
          } else {
            result.chatMessage_ = chatMessageBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk other) {
        if (other == com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk.getDefaultInstance()) return this;
        if (other.hasChannelId()) {
          bitField0_ |= 0x00000001;
          channelId_ = other.channelId_;
          onChanged();
        }
        if (other.hasChatMessage()) {
          mergeChatMessage(other.getChatMessage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object channelId_ = "";
      /**
       * <code>optional string channelId = 1;</code>
       * @return Whether the channelId field is set.
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return The channelId.
       */
      public java.lang.String getChannelId() {
        java.lang.Object ref = channelId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return The bytes for channelId.
       */
      public com.google.protobuf.ByteString
          getChannelIdBytes() {
        java.lang.Object ref = channelId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelId_ = getDefaultInstance().getChannelId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @param value The bytes for channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMessageBuilder_;
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       * @return Whether the chatMessage field is set.
       */
      public boolean hasChatMessage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       * @return The chatMessage.
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
        if (chatMessageBuilder_ == null) {
          return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        } else {
          return chatMessageBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       */
      public Builder setChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatMessage_ = value;
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       */
      public Builder setChatMessage(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = builderForValue.build();
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       */
      public Builder mergeChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              chatMessage_ != null &&
              chatMessage_ != com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance()) {
            chatMessage_ =
              com.yorha.proto.CommonMsg.ChatMessage.newBuilder(chatMessage_).mergeFrom(value).buildPartial();
          } else {
            chatMessage_ = value;
          }
          onChanged();
        } else {
          chatMessageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       */
      public Builder clearChatMessage() {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
          onChanged();
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMessageBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getChatMessageFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
        if (chatMessageBuilder_ != null) {
          return chatMessageBuilder_.getMessageOrBuilder();
        } else {
          return chatMessage_ == null ?
              com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMessageFieldBuilder() {
        if (chatMessageBuilder_ == null) {
          chatMessageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  getChatMessage(),
                  getParentForChildren(),
                  isClean());
          chatMessage_ = null;
        }
        return chatMessageBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ReceivePrivateMsgAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ReceivePrivateMsgAsk)
    private static final com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk();
    }

    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ReceivePrivateMsgAsk>
        PARSER = new com.google.protobuf.AbstractParser<ReceivePrivateMsgAsk>() {
      @java.lang.Override
      public ReceivePrivateMsgAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReceivePrivateMsgAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReceivePrivateMsgAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReceivePrivateMsgAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReceivePrivateMsgAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ReceivePrivateMsgAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
     * @return Whether the chatMessage field is set.
     */
    boolean hasChatMessage();
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
     * @return The chatMessage.
     */
    com.yorha.proto.CommonMsg.ChatMessage getChatMessage();
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
     */
    com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder();

    /**
     * <pre>
     * 聊天频道创建时间
     * </pre>
     *
     * <code>optional int64 chatChannelCreateTsMs = 4;</code>
     * @return Whether the chatChannelCreateTsMs field is set.
     */
    boolean hasChatChannelCreateTsMs();
    /**
     * <pre>
     * 聊天频道创建时间
     * </pre>
     *
     * <code>optional int64 chatChannelCreateTsMs = 4;</code>
     * @return The chatChannelCreateTsMs.
     */
    long getChatChannelCreateTsMs();
  }
  /**
   * <pre>
   * 废弃index 1, 2
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.ReceivePrivateMsgAns}
   */
  public static final class ReceivePrivateMsgAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ReceivePrivateMsgAns)
      ReceivePrivateMsgAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReceivePrivateMsgAns.newBuilder() to construct.
    private ReceivePrivateMsgAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReceivePrivateMsgAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReceivePrivateMsgAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReceivePrivateMsgAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 26: {
              com.yorha.proto.CommonMsg.ChatMessage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = chatMessage_.toBuilder();
              }
              chatMessage_ = input.readMessage(com.yorha.proto.CommonMsg.ChatMessage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chatMessage_);
                chatMessage_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000002;
              chatChannelCreateTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns.class, com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns.Builder.class);
    }

    private int bitField0_;
    public static final int CHATMESSAGE_FIELD_NUMBER = 3;
    private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
     * @return Whether the chatMessage field is set.
     */
    @java.lang.Override
    public boolean hasChatMessage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
     * @return The chatMessage.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }
    /**
     * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
      return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
    }

    public static final int CHATCHANNELCREATETSMS_FIELD_NUMBER = 4;
    private long chatChannelCreateTsMs_;
    /**
     * <pre>
     * 聊天频道创建时间
     * </pre>
     *
     * <code>optional int64 chatChannelCreateTsMs = 4;</code>
     * @return Whether the chatChannelCreateTsMs field is set.
     */
    @java.lang.Override
    public boolean hasChatChannelCreateTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 聊天频道创建时间
     * </pre>
     *
     * <code>optional int64 chatChannelCreateTsMs = 4;</code>
     * @return The chatChannelCreateTsMs.
     */
    @java.lang.Override
    public long getChatChannelCreateTsMs() {
      return chatChannelCreateTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getChatMessage());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(4, chatChannelCreateTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getChatMessage());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, chatChannelCreateTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns other = (com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns) obj;

      if (hasChatMessage() != other.hasChatMessage()) return false;
      if (hasChatMessage()) {
        if (!getChatMessage()
            .equals(other.getChatMessage())) return false;
      }
      if (hasChatChannelCreateTsMs() != other.hasChatChannelCreateTsMs()) return false;
      if (hasChatChannelCreateTsMs()) {
        if (getChatChannelCreateTsMs()
            != other.getChatChannelCreateTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChatMessage()) {
        hash = (37 * hash) + CHATMESSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getChatMessage().hashCode();
      }
      if (hasChatChannelCreateTsMs()) {
        hash = (37 * hash) + CHATCHANNELCREATETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getChatChannelCreateTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 废弃index 1, 2
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.ReceivePrivateMsgAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ReceivePrivateMsgAns)
        com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns.class, com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatMessageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        chatChannelCreateTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_ReceivePrivateMsgAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns build() {
        com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns buildPartial() {
        com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns result = new com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (chatMessageBuilder_ == null) {
            result.chatMessage_ = chatMessage_;
          } else {
            result.chatMessage_ = chatMessageBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.chatChannelCreateTsMs_ = chatChannelCreateTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns other) {
        if (other == com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns.getDefaultInstance()) return this;
        if (other.hasChatMessage()) {
          mergeChatMessage(other.getChatMessage());
        }
        if (other.hasChatChannelCreateTsMs()) {
          setChatChannelCreateTsMs(other.getChatChannelCreateTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ChatMessage chatMessage_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> chatMessageBuilder_;
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       * @return Whether the chatMessage field is set.
       */
      public boolean hasChatMessage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       * @return The chatMessage.
       */
      public com.yorha.proto.CommonMsg.ChatMessage getChatMessage() {
        if (chatMessageBuilder_ == null) {
          return chatMessage_ == null ? com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        } else {
          return chatMessageBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       */
      public Builder setChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatMessage_ = value;
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       */
      public Builder setChatMessage(
          com.yorha.proto.CommonMsg.ChatMessage.Builder builderForValue) {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = builderForValue.build();
          onChanged();
        } else {
          chatMessageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       */
      public Builder mergeChatMessage(com.yorha.proto.CommonMsg.ChatMessage value) {
        if (chatMessageBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              chatMessage_ != null &&
              chatMessage_ != com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance()) {
            chatMessage_ =
              com.yorha.proto.CommonMsg.ChatMessage.newBuilder(chatMessage_).mergeFrom(value).buildPartial();
          } else {
            chatMessage_ = value;
          }
          onChanged();
        } else {
          chatMessageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       */
      public Builder clearChatMessage() {
        if (chatMessageBuilder_ == null) {
          chatMessage_ = null;
          onChanged();
        } else {
          chatMessageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessage.Builder getChatMessageBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getChatMessageFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       */
      public com.yorha.proto.CommonMsg.ChatMessageOrBuilder getChatMessageOrBuilder() {
        if (chatMessageBuilder_ != null) {
          return chatMessageBuilder_.getMessageOrBuilder();
        } else {
          return chatMessage_ == null ?
              com.yorha.proto.CommonMsg.ChatMessage.getDefaultInstance() : chatMessage_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ChatMessage chatMessage = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder> 
          getChatMessageFieldBuilder() {
        if (chatMessageBuilder_ == null) {
          chatMessageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ChatMessage, com.yorha.proto.CommonMsg.ChatMessage.Builder, com.yorha.proto.CommonMsg.ChatMessageOrBuilder>(
                  getChatMessage(),
                  getParentForChildren(),
                  isClean());
          chatMessage_ = null;
        }
        return chatMessageBuilder_;
      }

      private long chatChannelCreateTsMs_ ;
      /**
       * <pre>
       * 聊天频道创建时间
       * </pre>
       *
       * <code>optional int64 chatChannelCreateTsMs = 4;</code>
       * @return Whether the chatChannelCreateTsMs field is set.
       */
      @java.lang.Override
      public boolean hasChatChannelCreateTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 聊天频道创建时间
       * </pre>
       *
       * <code>optional int64 chatChannelCreateTsMs = 4;</code>
       * @return The chatChannelCreateTsMs.
       */
      @java.lang.Override
      public long getChatChannelCreateTsMs() {
        return chatChannelCreateTsMs_;
      }
      /**
       * <pre>
       * 聊天频道创建时间
       * </pre>
       *
       * <code>optional int64 chatChannelCreateTsMs = 4;</code>
       * @param value The chatChannelCreateTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setChatChannelCreateTsMs(long value) {
        bitField0_ |= 0x00000002;
        chatChannelCreateTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 聊天频道创建时间
       * </pre>
       *
       * <code>optional int64 chatChannelCreateTsMs = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearChatChannelCreateTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        chatChannelCreateTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ReceivePrivateMsgAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ReceivePrivateMsgAns)
    private static final com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns();
    }

    public static com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ReceivePrivateMsgAns>
        PARSER = new com.google.protobuf.AbstractParser<ReceivePrivateMsgAns>() {
      @java.lang.Override
      public ReceivePrivateMsgAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReceivePrivateMsgAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReceivePrivateMsgAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReceivePrivateMsgAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.ReceivePrivateMsgAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GroupChatExpireCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GroupChatExpireCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    java.lang.String getChannelId();
    /**
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    com.google.protobuf.ByteString
        getChannelIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GroupChatExpireCmd}
   */
  public static final class GroupChatExpireCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GroupChatExpireCmd)
      GroupChatExpireCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GroupChatExpireCmd.newBuilder() to construct.
    private GroupChatExpireCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GroupChatExpireCmd() {
      channelId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GroupChatExpireCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GroupChatExpireCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              channelId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupChatExpireCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupChatExpireCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerChat.GroupChatExpireCmd.class, com.yorha.proto.SsPlayerChat.GroupChatExpireCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNELID_FIELD_NUMBER = 1;
    private volatile java.lang.Object channelId_;
    /**
     * <code>optional string channelId = 1;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string channelId = 1;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public java.lang.String getChannelId() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelId = 1;</code>
     * @return The bytes for channelId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelIdBytes() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerChat.GroupChatExpireCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerChat.GroupChatExpireCmd other = (com.yorha.proto.SsPlayerChat.GroupChatExpireCmd) obj;

      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (!getChannelId()
            .equals(other.getChannelId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerChat.GroupChatExpireCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GroupChatExpireCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GroupChatExpireCmd)
        com.yorha.proto.SsPlayerChat.GroupChatExpireCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupChatExpireCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupChatExpireCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerChat.GroupChatExpireCmd.class, com.yorha.proto.SsPlayerChat.GroupChatExpireCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerChat.GroupChatExpireCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerChat.internal_static_com_yorha_proto_GroupChatExpireCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupChatExpireCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerChat.GroupChatExpireCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupChatExpireCmd build() {
        com.yorha.proto.SsPlayerChat.GroupChatExpireCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerChat.GroupChatExpireCmd buildPartial() {
        com.yorha.proto.SsPlayerChat.GroupChatExpireCmd result = new com.yorha.proto.SsPlayerChat.GroupChatExpireCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelId_ = channelId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerChat.GroupChatExpireCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerChat.GroupChatExpireCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerChat.GroupChatExpireCmd other) {
        if (other == com.yorha.proto.SsPlayerChat.GroupChatExpireCmd.getDefaultInstance()) return this;
        if (other.hasChannelId()) {
          bitField0_ |= 0x00000001;
          channelId_ = other.channelId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerChat.GroupChatExpireCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerChat.GroupChatExpireCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object channelId_ = "";
      /**
       * <code>optional string channelId = 1;</code>
       * @return Whether the channelId field is set.
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return The channelId.
       */
      public java.lang.String getChannelId() {
        java.lang.Object ref = channelId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return The bytes for channelId.
       */
      public com.google.protobuf.ByteString
          getChannelIdBytes() {
        java.lang.Object ref = channelId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelId_ = getDefaultInstance().getChannelId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 1;</code>
       * @param value The bytes for channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GroupChatExpireCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GroupChatExpireCmd)
    private static final com.yorha.proto.SsPlayerChat.GroupChatExpireCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerChat.GroupChatExpireCmd();
    }

    public static com.yorha.proto.SsPlayerChat.GroupChatExpireCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GroupChatExpireCmd>
        PARSER = new com.google.protobuf.AbstractParser<GroupChatExpireCmd>() {
      @java.lang.Override
      public GroupChatExpireCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GroupChatExpireCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GroupChatExpireCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GroupChatExpireCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerChat.GroupChatExpireCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PrepareJoinGroupChatAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PrepareJoinGroupChatAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_JoinGroupChatCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_JoinGroupChatCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_LeaveGroupChatCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_LeaveGroupChatCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_HandleNewMessageAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_HandleNewMessageAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_HandleNewMessageAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_HandleNewMessageAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GroupInfoChangeNtf_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GroupInfoChangeNtf_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GroupDismissNtf_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GroupDismissNtf_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_IgnoreMsgNtf_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_IgnoreMsgNtf_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_TryClearPrepareChatNtf_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_TryClearPrepareChatNtf_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ReceivePrivateMsgAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ReceivePrivateMsgAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ReceivePrivateMsgAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ReceivePrivateMsgAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GroupChatExpireCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GroupChatExpireCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/player/ss/ss_player_chat." +
      "proto\022\017com.yorha.proto\032$ss_proto/gen/com" +
      "mon/common_msg.proto\",\n\027PrepareJoinGroup" +
      "ChatAsk\022\021\n\tchannelId\030\001 \001(\t\"*\n\027PrepareJoi" +
      "nGroupChatAns\022\017\n\007prepare\030\003 \001(\010\"-\n\030ClearP" +
      "repareGroupChatCmd\022\021\n\tchannelId\030\001 \001(\t\"j\n" +
      "\020JoinGroupChatCmd\022\021\n\tchannelId\030\001 \001(\t\022\020\n\010" +
      "maxIndex\030\002 \001(\003\022\r\n\005owner\030\003 \001(\003\022\021\n\tgroupNa" +
      "me\030\004 \001(\t\022\017\n\007version\030\005 \001(\005\"&\n\021LeaveGroupC" +
      "hatCmd\022\021\n\tchannelId\030\001 \001(\t\"z\n\023HandleNewMe" +
      "ssageAsk\0221\n\013chatSession\030\001 \001(\0132\034.com.yorh" +
      "a.proto.ChatSession\0220\n\nnewMessage\030\002 \001(\0132" +
      "\034.com.yorha.proto.ChatMessage\"\025\n\023HandleN" +
      "ewMessageAns\"W\n\022GroupInfoChangeNtf\022\023\n\013gr" +
      "oupChatId\030\001 \001(\t\022\017\n\007version\030\002 \001(\005\022\r\n\005owne" +
      "r\030\003 \001(\003\022\014\n\004name\030\004 \001(\t\"&\n\017GroupDismissNtf" +
      "\022\023\n\013groupChatId\030\001 \001(\t\"A\n\014IgnoreMsgNtf\0221\n" +
      "\013chatSession\030\001 \001(\0132\034.com.yorha.proto.Cha" +
      "tSession\"+\n\026TryClearPrepareChatNtf\022\021\n\tch" +
      "annelId\030\001 \001(\t\"\\\n\024ReceivePrivateMsgAsk\022\021\n" +
      "\tchannelId\030\001 \001(\t\0221\n\013chatMessage\030\002 \001(\0132\034." +
      "com.yorha.proto.ChatMessage\"h\n\024ReceivePr" +
      "ivateMsgAns\0221\n\013chatMessage\030\003 \001(\0132\034.com.y" +
      "orha.proto.ChatMessage\022\035\n\025chatChannelCre" +
      "ateTsMs\030\004 \001(\003\"\'\n\022GroupChatExpireCmd\022\021\n\tc" +
      "hannelId\030\001 \001(\tB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PrepareJoinGroupChatAsk_descriptor,
        new java.lang.String[] { "ChannelId", });
    internal_static_com_yorha_proto_PrepareJoinGroupChatAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_PrepareJoinGroupChatAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PrepareJoinGroupChatAns_descriptor,
        new java.lang.String[] { "Prepare", });
    internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClearPrepareGroupChatCmd_descriptor,
        new java.lang.String[] { "ChannelId", });
    internal_static_com_yorha_proto_JoinGroupChatCmd_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_JoinGroupChatCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_JoinGroupChatCmd_descriptor,
        new java.lang.String[] { "ChannelId", "MaxIndex", "Owner", "GroupName", "Version", });
    internal_static_com_yorha_proto_LeaveGroupChatCmd_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_LeaveGroupChatCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_LeaveGroupChatCmd_descriptor,
        new java.lang.String[] { "ChannelId", });
    internal_static_com_yorha_proto_HandleNewMessageAsk_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_HandleNewMessageAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_HandleNewMessageAsk_descriptor,
        new java.lang.String[] { "ChatSession", "NewMessage", });
    internal_static_com_yorha_proto_HandleNewMessageAns_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_HandleNewMessageAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_HandleNewMessageAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_GroupInfoChangeNtf_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_GroupInfoChangeNtf_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GroupInfoChangeNtf_descriptor,
        new java.lang.String[] { "GroupChatId", "Version", "Owner", "Name", });
    internal_static_com_yorha_proto_GroupDismissNtf_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_GroupDismissNtf_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GroupDismissNtf_descriptor,
        new java.lang.String[] { "GroupChatId", });
    internal_static_com_yorha_proto_IgnoreMsgNtf_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_IgnoreMsgNtf_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_IgnoreMsgNtf_descriptor,
        new java.lang.String[] { "ChatSession", });
    internal_static_com_yorha_proto_TryClearPrepareChatNtf_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_TryClearPrepareChatNtf_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_TryClearPrepareChatNtf_descriptor,
        new java.lang.String[] { "ChannelId", });
    internal_static_com_yorha_proto_ReceivePrivateMsgAsk_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_ReceivePrivateMsgAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ReceivePrivateMsgAsk_descriptor,
        new java.lang.String[] { "ChannelId", "ChatMessage", });
    internal_static_com_yorha_proto_ReceivePrivateMsgAns_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_ReceivePrivateMsgAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ReceivePrivateMsgAns_descriptor,
        new java.lang.String[] { "ChatMessage", "ChatChannelCreateTsMs", });
    internal_static_com_yorha_proto_GroupChatExpireCmd_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_GroupChatExpireCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GroupChatExpireCmd_descriptor,
        new java.lang.String[] { "ChannelId", });
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

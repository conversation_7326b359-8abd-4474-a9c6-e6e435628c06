// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/dropObject/drop_object.proto

package com.yorha.proto;

public final class DropObject {
  private DropObject() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DropObjectEntityOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DropObjectEntity)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 配置表id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 配置表id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     * @return The point.
     */
    com.yorha.proto.Struct.Point getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 已采集过的玩家记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
     * @return Whether the pickUpPlayers field is set.
     */
    boolean hasPickUpPlayers();
    /**
     * <pre>
     * 已采集过的玩家记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
     * @return The pickUpPlayers.
     */
    com.yorha.proto.Basic.Int64List getPickUpPlayers();
    /**
     * <pre>
     * 已采集过的玩家记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
     */
    com.yorha.proto.Basic.Int64ListOrBuilder getPickUpPlayersOrBuilder();

    /**
     * <pre>
     * 已采集的次数
     * </pre>
     *
     * <code>optional int32 pickUpTimes = 4;</code>
     * @return Whether the pickUpTimes field is set.
     */
    boolean hasPickUpTimes();
    /**
     * <pre>
     * 已采集的次数
     * </pre>
     *
     * <code>optional int32 pickUpTimes = 4;</code>
     * @return The pickUpTimes.
     */
    int getPickUpTimes();

    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 5;</code>
     * @return Whether the bornTime field is set.
     */
    boolean hasBornTime();
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 5;</code>
     * @return The bornTime.
     */
    long getBornTime();

    /**
     * <pre>
     * 私有玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 6;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <pre>
     * 私有玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 6;</code>
     * @return The ownerId.
     */
    long getOwnerId();

    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
     * @return Whether the pickUpArmy field is set.
     */
    boolean hasPickUpArmy();
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
     * @return The pickUpArmy.
     */
    com.yorha.proto.Basic.Int64List getPickUpArmy();
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
     */
    com.yorha.proto.Basic.Int64ListOrBuilder getPickUpArmyOrBuilder();

    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
     * @return Whether the itemReward field is set.
     */
    boolean hasItemReward();
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
     * @return The itemReward.
     */
    com.yorha.proto.Struct.ItemList getItemReward();
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
     */
    com.yorha.proto.Struct.ItemListOrBuilder getItemRewardOrBuilder();

    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
     * @return Whether the groupLimit field is set.
     */
    boolean hasGroupLimit();
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
     * @return The groupLimit.
     */
    com.yorha.proto.Struct.DropGroupLimit getGroupLimit();
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
     */
    com.yorha.proto.Struct.DropGroupLimitOrBuilder getGroupLimitOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DropObjectEntity}
   */
  public static final class DropObjectEntity extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DropObjectEntity)
      DropObjectEntityOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DropObjectEntity.newBuilder() to construct.
    private DropObjectEntity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DropObjectEntity() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DropObjectEntity();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DropObjectEntity(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              templateId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.yorha.proto.Basic.Int64List.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = pickUpPlayers_.toBuilder();
              }
              pickUpPlayers_ = input.readMessage(com.yorha.proto.Basic.Int64List.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pickUpPlayers_);
                pickUpPlayers_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              pickUpTimes_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              bornTime_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              ownerId_ = input.readInt64();
              break;
            }
            case 58: {
              com.yorha.proto.Basic.Int64List.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = pickUpArmy_.toBuilder();
              }
              pickUpArmy_ = input.readMessage(com.yorha.proto.Basic.Int64List.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pickUpArmy_);
                pickUpArmy_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 66: {
              com.yorha.proto.Struct.ItemList.Builder subBuilder = null;
              if (((bitField0_ & 0x00000080) != 0)) {
                subBuilder = itemReward_.toBuilder();
              }
              itemReward_ = input.readMessage(com.yorha.proto.Struct.ItemList.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(itemReward_);
                itemReward_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000080;
              break;
            }
            case 74: {
              com.yorha.proto.Struct.DropGroupLimit.Builder subBuilder = null;
              if (((bitField0_ & 0x00000100) != 0)) {
                subBuilder = groupLimit_.toBuilder();
              }
              groupLimit_ = input.readMessage(com.yorha.proto.Struct.DropGroupLimit.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(groupLimit_);
                groupLimit_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000100;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.DropObject.internal_static_com_yorha_proto_DropObjectEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.DropObject.internal_static_com_yorha_proto_DropObjectEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.DropObject.DropObjectEntity.class, com.yorha.proto.DropObject.DropObjectEntity.Builder.class);
    }

    private int bitField0_;
    public static final int TEMPLATEID_FIELD_NUMBER = 1;
    private int templateId_;
    /**
     * <pre>
     * 配置表id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 配置表id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int POINT_FIELD_NUMBER = 2;
    private com.yorha.proto.Struct.Point point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }

    public static final int PICKUPPLAYERS_FIELD_NUMBER = 3;
    private com.yorha.proto.Basic.Int64List pickUpPlayers_;
    /**
     * <pre>
     * 已采集过的玩家记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
     * @return Whether the pickUpPlayers field is set.
     */
    @java.lang.Override
    public boolean hasPickUpPlayers() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 已采集过的玩家记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
     * @return The pickUpPlayers.
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int64List getPickUpPlayers() {
      return pickUpPlayers_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : pickUpPlayers_;
    }
    /**
     * <pre>
     * 已采集过的玩家记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int64ListOrBuilder getPickUpPlayersOrBuilder() {
      return pickUpPlayers_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : pickUpPlayers_;
    }

    public static final int PICKUPTIMES_FIELD_NUMBER = 4;
    private int pickUpTimes_;
    /**
     * <pre>
     * 已采集的次数
     * </pre>
     *
     * <code>optional int32 pickUpTimes = 4;</code>
     * @return Whether the pickUpTimes field is set.
     */
    @java.lang.Override
    public boolean hasPickUpTimes() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 已采集的次数
     * </pre>
     *
     * <code>optional int32 pickUpTimes = 4;</code>
     * @return The pickUpTimes.
     */
    @java.lang.Override
    public int getPickUpTimes() {
      return pickUpTimes_;
    }

    public static final int BORNTIME_FIELD_NUMBER = 5;
    private long bornTime_;
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 5;</code>
     * @return Whether the bornTime field is set.
     */
    @java.lang.Override
    public boolean hasBornTime() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 5;</code>
     * @return The bornTime.
     */
    @java.lang.Override
    public long getBornTime() {
      return bornTime_;
    }

    public static final int OWNERID_FIELD_NUMBER = 6;
    private long ownerId_;
    /**
     * <pre>
     * 私有玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 6;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 私有玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 6;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    public static final int PICKUPARMY_FIELD_NUMBER = 7;
    private com.yorha.proto.Basic.Int64List pickUpArmy_;
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
     * @return Whether the pickUpArmy field is set.
     */
    @java.lang.Override
    public boolean hasPickUpArmy() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
     * @return The pickUpArmy.
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int64List getPickUpArmy() {
      return pickUpArmy_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : pickUpArmy_;
    }
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int64ListOrBuilder getPickUpArmyOrBuilder() {
      return pickUpArmy_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : pickUpArmy_;
    }

    public static final int ITEMREWARD_FIELD_NUMBER = 8;
    private com.yorha.proto.Struct.ItemList itemReward_;
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
     * @return Whether the itemReward field is set.
     */
    @java.lang.Override
    public boolean hasItemReward() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
     * @return The itemReward.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ItemList getItemReward() {
      return itemReward_ == null ? com.yorha.proto.Struct.ItemList.getDefaultInstance() : itemReward_;
    }
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ItemListOrBuilder getItemRewardOrBuilder() {
      return itemReward_ == null ? com.yorha.proto.Struct.ItemList.getDefaultInstance() : itemReward_;
    }

    public static final int GROUPLIMIT_FIELD_NUMBER = 9;
    private com.yorha.proto.Struct.DropGroupLimit groupLimit_;
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
     * @return Whether the groupLimit field is set.
     */
    @java.lang.Override
    public boolean hasGroupLimit() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
     * @return The groupLimit.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.DropGroupLimit getGroupLimit() {
      return groupLimit_ == null ? com.yorha.proto.Struct.DropGroupLimit.getDefaultInstance() : groupLimit_;
    }
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.DropGroupLimitOrBuilder getGroupLimitOrBuilder() {
      return groupLimit_ == null ? com.yorha.proto.Struct.DropGroupLimit.getDefaultInstance() : groupLimit_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getPickUpPlayers());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, pickUpTimes_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, bornTime_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt64(6, ownerId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(7, getPickUpArmy());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(8, getItemReward());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeMessage(9, getGroupLimit());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getPickUpPlayers());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, pickUpTimes_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, bornTime_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, ownerId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getPickUpArmy());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getItemReward());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, getGroupLimit());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.DropObject.DropObjectEntity)) {
        return super.equals(obj);
      }
      com.yorha.proto.DropObject.DropObjectEntity other = (com.yorha.proto.DropObject.DropObjectEntity) obj;

      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasPickUpPlayers() != other.hasPickUpPlayers()) return false;
      if (hasPickUpPlayers()) {
        if (!getPickUpPlayers()
            .equals(other.getPickUpPlayers())) return false;
      }
      if (hasPickUpTimes() != other.hasPickUpTimes()) return false;
      if (hasPickUpTimes()) {
        if (getPickUpTimes()
            != other.getPickUpTimes()) return false;
      }
      if (hasBornTime() != other.hasBornTime()) return false;
      if (hasBornTime()) {
        if (getBornTime()
            != other.getBornTime()) return false;
      }
      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (hasPickUpArmy() != other.hasPickUpArmy()) return false;
      if (hasPickUpArmy()) {
        if (!getPickUpArmy()
            .equals(other.getPickUpArmy())) return false;
      }
      if (hasItemReward() != other.hasItemReward()) return false;
      if (hasItemReward()) {
        if (!getItemReward()
            .equals(other.getItemReward())) return false;
      }
      if (hasGroupLimit() != other.hasGroupLimit()) return false;
      if (hasGroupLimit()) {
        if (!getGroupLimit()
            .equals(other.getGroupLimit())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasPickUpPlayers()) {
        hash = (37 * hash) + PICKUPPLAYERS_FIELD_NUMBER;
        hash = (53 * hash) + getPickUpPlayers().hashCode();
      }
      if (hasPickUpTimes()) {
        hash = (37 * hash) + PICKUPTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getPickUpTimes();
      }
      if (hasBornTime()) {
        hash = (37 * hash) + BORNTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBornTime());
      }
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      if (hasPickUpArmy()) {
        hash = (37 * hash) + PICKUPARMY_FIELD_NUMBER;
        hash = (53 * hash) + getPickUpArmy().hashCode();
      }
      if (hasItemReward()) {
        hash = (37 * hash) + ITEMREWARD_FIELD_NUMBER;
        hash = (53 * hash) + getItemReward().hashCode();
      }
      if (hasGroupLimit()) {
        hash = (37 * hash) + GROUPLIMIT_FIELD_NUMBER;
        hash = (53 * hash) + getGroupLimit().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DropObject.DropObjectEntity parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.DropObject.DropObjectEntity prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DropObjectEntity}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DropObjectEntity)
        com.yorha.proto.DropObject.DropObjectEntityOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.DropObject.internal_static_com_yorha_proto_DropObjectEntity_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.DropObject.internal_static_com_yorha_proto_DropObjectEntity_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.DropObject.DropObjectEntity.class, com.yorha.proto.DropObject.DropObjectEntity.Builder.class);
      }

      // Construct using com.yorha.proto.DropObject.DropObjectEntity.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getPickUpPlayersFieldBuilder();
          getPickUpArmyFieldBuilder();
          getItemRewardFieldBuilder();
          getGroupLimitFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (pickUpPlayersBuilder_ == null) {
          pickUpPlayers_ = null;
        } else {
          pickUpPlayersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        pickUpTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        bornTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        if (pickUpArmyBuilder_ == null) {
          pickUpArmy_ = null;
        } else {
          pickUpArmyBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        if (itemRewardBuilder_ == null) {
          itemReward_ = null;
        } else {
          itemRewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        if (groupLimitBuilder_ == null) {
          groupLimit_ = null;
        } else {
          groupLimitBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.DropObject.internal_static_com_yorha_proto_DropObjectEntity_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.DropObject.DropObjectEntity getDefaultInstanceForType() {
        return com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.DropObject.DropObjectEntity build() {
        com.yorha.proto.DropObject.DropObjectEntity result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.DropObject.DropObjectEntity buildPartial() {
        com.yorha.proto.DropObject.DropObjectEntity result = new com.yorha.proto.DropObject.DropObjectEntity(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (pickUpPlayersBuilder_ == null) {
            result.pickUpPlayers_ = pickUpPlayers_;
          } else {
            result.pickUpPlayers_ = pickUpPlayersBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.pickUpTimes_ = pickUpTimes_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.bornTime_ = bornTime_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          if (pickUpArmyBuilder_ == null) {
            result.pickUpArmy_ = pickUpArmy_;
          } else {
            result.pickUpArmy_ = pickUpArmyBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          if (itemRewardBuilder_ == null) {
            result.itemReward_ = itemReward_;
          } else {
            result.itemReward_ = itemRewardBuilder_.build();
          }
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          if (groupLimitBuilder_ == null) {
            result.groupLimit_ = groupLimit_;
          } else {
            result.groupLimit_ = groupLimitBuilder_.build();
          }
          to_bitField0_ |= 0x00000100;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.DropObject.DropObjectEntity) {
          return mergeFrom((com.yorha.proto.DropObject.DropObjectEntity)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.DropObject.DropObjectEntity other) {
        if (other == com.yorha.proto.DropObject.DropObjectEntity.getDefaultInstance()) return this;
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasPickUpPlayers()) {
          mergePickUpPlayers(other.getPickUpPlayers());
        }
        if (other.hasPickUpTimes()) {
          setPickUpTimes(other.getPickUpTimes());
        }
        if (other.hasBornTime()) {
          setBornTime(other.getBornTime());
        }
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        if (other.hasPickUpArmy()) {
          mergePickUpArmy(other.getPickUpArmy());
        }
        if (other.hasItemReward()) {
          mergeItemReward(other.getItemReward());
        }
        if (other.hasGroupLimit()) {
          mergeGroupLimit(other.getGroupLimit());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.DropObject.DropObjectEntity parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.DropObject.DropObjectEntity) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int templateId_ ;
      /**
       * <pre>
       * 配置表id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 配置表id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 配置表id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000001;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 配置表id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Point point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       * @return The point.
       */
      public com.yorha.proto.Struct.Point getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public Builder setPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public Builder setPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public Builder mergePoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            point_ =
              com.yorha.proto.Struct.Point.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private com.yorha.proto.Basic.Int64List pickUpPlayers_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder> pickUpPlayersBuilder_;
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       * @return Whether the pickUpPlayers field is set.
       */
      public boolean hasPickUpPlayers() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       * @return The pickUpPlayers.
       */
      public com.yorha.proto.Basic.Int64List getPickUpPlayers() {
        if (pickUpPlayersBuilder_ == null) {
          return pickUpPlayers_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : pickUpPlayers_;
        } else {
          return pickUpPlayersBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       */
      public Builder setPickUpPlayers(com.yorha.proto.Basic.Int64List value) {
        if (pickUpPlayersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pickUpPlayers_ = value;
          onChanged();
        } else {
          pickUpPlayersBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       */
      public Builder setPickUpPlayers(
          com.yorha.proto.Basic.Int64List.Builder builderForValue) {
        if (pickUpPlayersBuilder_ == null) {
          pickUpPlayers_ = builderForValue.build();
          onChanged();
        } else {
          pickUpPlayersBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       */
      public Builder mergePickUpPlayers(com.yorha.proto.Basic.Int64List value) {
        if (pickUpPlayersBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              pickUpPlayers_ != null &&
              pickUpPlayers_ != com.yorha.proto.Basic.Int64List.getDefaultInstance()) {
            pickUpPlayers_ =
              com.yorha.proto.Basic.Int64List.newBuilder(pickUpPlayers_).mergeFrom(value).buildPartial();
          } else {
            pickUpPlayers_ = value;
          }
          onChanged();
        } else {
          pickUpPlayersBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       */
      public Builder clearPickUpPlayers() {
        if (pickUpPlayersBuilder_ == null) {
          pickUpPlayers_ = null;
          onChanged();
        } else {
          pickUpPlayersBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       */
      public com.yorha.proto.Basic.Int64List.Builder getPickUpPlayersBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPickUpPlayersFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       */
      public com.yorha.proto.Basic.Int64ListOrBuilder getPickUpPlayersOrBuilder() {
        if (pickUpPlayersBuilder_ != null) {
          return pickUpPlayersBuilder_.getMessageOrBuilder();
        } else {
          return pickUpPlayers_ == null ?
              com.yorha.proto.Basic.Int64List.getDefaultInstance() : pickUpPlayers_;
        }
      }
      /**
       * <pre>
       * 已采集过的玩家记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpPlayers = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder> 
          getPickUpPlayersFieldBuilder() {
        if (pickUpPlayersBuilder_ == null) {
          pickUpPlayersBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder>(
                  getPickUpPlayers(),
                  getParentForChildren(),
                  isClean());
          pickUpPlayers_ = null;
        }
        return pickUpPlayersBuilder_;
      }

      private int pickUpTimes_ ;
      /**
       * <pre>
       * 已采集的次数
       * </pre>
       *
       * <code>optional int32 pickUpTimes = 4;</code>
       * @return Whether the pickUpTimes field is set.
       */
      @java.lang.Override
      public boolean hasPickUpTimes() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 已采集的次数
       * </pre>
       *
       * <code>optional int32 pickUpTimes = 4;</code>
       * @return The pickUpTimes.
       */
      @java.lang.Override
      public int getPickUpTimes() {
        return pickUpTimes_;
      }
      /**
       * <pre>
       * 已采集的次数
       * </pre>
       *
       * <code>optional int32 pickUpTimes = 4;</code>
       * @param value The pickUpTimes to set.
       * @return This builder for chaining.
       */
      public Builder setPickUpTimes(int value) {
        bitField0_ |= 0x00000008;
        pickUpTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已采集的次数
       * </pre>
       *
       * <code>optional int32 pickUpTimes = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPickUpTimes() {
        bitField0_ = (bitField0_ & ~0x00000008);
        pickUpTimes_ = 0;
        onChanged();
        return this;
      }

      private long bornTime_ ;
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 5;</code>
       * @return Whether the bornTime field is set.
       */
      @java.lang.Override
      public boolean hasBornTime() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 5;</code>
       * @return The bornTime.
       */
      @java.lang.Override
      public long getBornTime() {
        return bornTime_;
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 5;</code>
       * @param value The bornTime to set.
       * @return This builder for chaining.
       */
      public Builder setBornTime(long value) {
        bitField0_ |= 0x00000010;
        bornTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearBornTime() {
        bitField0_ = (bitField0_ & ~0x00000010);
        bornTime_ = 0L;
        onChanged();
        return this;
      }

      private long ownerId_ ;
      /**
       * <pre>
       * 私有玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 6;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 私有玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 6;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <pre>
       * 私有玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 6;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000020;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 私有玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        ownerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.Basic.Int64List pickUpArmy_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder> pickUpArmyBuilder_;
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       * @return Whether the pickUpArmy field is set.
       */
      public boolean hasPickUpArmy() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       * @return The pickUpArmy.
       */
      public com.yorha.proto.Basic.Int64List getPickUpArmy() {
        if (pickUpArmyBuilder_ == null) {
          return pickUpArmy_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : pickUpArmy_;
        } else {
          return pickUpArmyBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       */
      public Builder setPickUpArmy(com.yorha.proto.Basic.Int64List value) {
        if (pickUpArmyBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pickUpArmy_ = value;
          onChanged();
        } else {
          pickUpArmyBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       */
      public Builder setPickUpArmy(
          com.yorha.proto.Basic.Int64List.Builder builderForValue) {
        if (pickUpArmyBuilder_ == null) {
          pickUpArmy_ = builderForValue.build();
          onChanged();
        } else {
          pickUpArmyBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       */
      public Builder mergePickUpArmy(com.yorha.proto.Basic.Int64List value) {
        if (pickUpArmyBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
              pickUpArmy_ != null &&
              pickUpArmy_ != com.yorha.proto.Basic.Int64List.getDefaultInstance()) {
            pickUpArmy_ =
              com.yorha.proto.Basic.Int64List.newBuilder(pickUpArmy_).mergeFrom(value).buildPartial();
          } else {
            pickUpArmy_ = value;
          }
          onChanged();
        } else {
          pickUpArmyBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       */
      public Builder clearPickUpArmy() {
        if (pickUpArmyBuilder_ == null) {
          pickUpArmy_ = null;
          onChanged();
        } else {
          pickUpArmyBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       */
      public com.yorha.proto.Basic.Int64List.Builder getPickUpArmyBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getPickUpArmyFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       */
      public com.yorha.proto.Basic.Int64ListOrBuilder getPickUpArmyOrBuilder() {
        if (pickUpArmyBuilder_ != null) {
          return pickUpArmyBuilder_.getMessageOrBuilder();
        } else {
          return pickUpArmy_ == null ?
              com.yorha.proto.Basic.Int64List.getDefaultInstance() : pickUpArmy_;
        }
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64List pickUpArmy = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder> 
          getPickUpArmyFieldBuilder() {
        if (pickUpArmyBuilder_ == null) {
          pickUpArmyBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder>(
                  getPickUpArmy(),
                  getParentForChildren(),
                  isClean());
          pickUpArmy_ = null;
        }
        return pickUpArmyBuilder_;
      }

      private com.yorha.proto.Struct.ItemList itemReward_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.ItemList, com.yorha.proto.Struct.ItemList.Builder, com.yorha.proto.Struct.ItemListOrBuilder> itemRewardBuilder_;
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       * @return Whether the itemReward field is set.
       */
      public boolean hasItemReward() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       * @return The itemReward.
       */
      public com.yorha.proto.Struct.ItemList getItemReward() {
        if (itemRewardBuilder_ == null) {
          return itemReward_ == null ? com.yorha.proto.Struct.ItemList.getDefaultInstance() : itemReward_;
        } else {
          return itemRewardBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       */
      public Builder setItemReward(com.yorha.proto.Struct.ItemList value) {
        if (itemRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          itemReward_ = value;
          onChanged();
        } else {
          itemRewardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       */
      public Builder setItemReward(
          com.yorha.proto.Struct.ItemList.Builder builderForValue) {
        if (itemRewardBuilder_ == null) {
          itemReward_ = builderForValue.build();
          onChanged();
        } else {
          itemRewardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       */
      public Builder mergeItemReward(com.yorha.proto.Struct.ItemList value) {
        if (itemRewardBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
              itemReward_ != null &&
              itemReward_ != com.yorha.proto.Struct.ItemList.getDefaultInstance()) {
            itemReward_ =
              com.yorha.proto.Struct.ItemList.newBuilder(itemReward_).mergeFrom(value).buildPartial();
          } else {
            itemReward_ = value;
          }
          onChanged();
        } else {
          itemRewardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       */
      public Builder clearItemReward() {
        if (itemRewardBuilder_ == null) {
          itemReward_ = null;
          onChanged();
        } else {
          itemRewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       */
      public com.yorha.proto.Struct.ItemList.Builder getItemRewardBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getItemRewardFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       */
      public com.yorha.proto.Struct.ItemListOrBuilder getItemRewardOrBuilder() {
        if (itemRewardBuilder_ != null) {
          return itemRewardBuilder_.getMessageOrBuilder();
        } else {
          return itemReward_ == null ?
              com.yorha.proto.Struct.ItemList.getDefaultInstance() : itemReward_;
        }
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemList itemReward = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.ItemList, com.yorha.proto.Struct.ItemList.Builder, com.yorha.proto.Struct.ItemListOrBuilder> 
          getItemRewardFieldBuilder() {
        if (itemRewardBuilder_ == null) {
          itemRewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.ItemList, com.yorha.proto.Struct.ItemList.Builder, com.yorha.proto.Struct.ItemListOrBuilder>(
                  getItemReward(),
                  getParentForChildren(),
                  isClean());
          itemReward_ = null;
        }
        return itemRewardBuilder_;
      }

      private com.yorha.proto.Struct.DropGroupLimit groupLimit_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.DropGroupLimit, com.yorha.proto.Struct.DropGroupLimit.Builder, com.yorha.proto.Struct.DropGroupLimitOrBuilder> groupLimitBuilder_;
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       * @return Whether the groupLimit field is set.
       */
      public boolean hasGroupLimit() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       * @return The groupLimit.
       */
      public com.yorha.proto.Struct.DropGroupLimit getGroupLimit() {
        if (groupLimitBuilder_ == null) {
          return groupLimit_ == null ? com.yorha.proto.Struct.DropGroupLimit.getDefaultInstance() : groupLimit_;
        } else {
          return groupLimitBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       */
      public Builder setGroupLimit(com.yorha.proto.Struct.DropGroupLimit value) {
        if (groupLimitBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          groupLimit_ = value;
          onChanged();
        } else {
          groupLimitBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       */
      public Builder setGroupLimit(
          com.yorha.proto.Struct.DropGroupLimit.Builder builderForValue) {
        if (groupLimitBuilder_ == null) {
          groupLimit_ = builderForValue.build();
          onChanged();
        } else {
          groupLimitBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       */
      public Builder mergeGroupLimit(com.yorha.proto.Struct.DropGroupLimit value) {
        if (groupLimitBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0) &&
              groupLimit_ != null &&
              groupLimit_ != com.yorha.proto.Struct.DropGroupLimit.getDefaultInstance()) {
            groupLimit_ =
              com.yorha.proto.Struct.DropGroupLimit.newBuilder(groupLimit_).mergeFrom(value).buildPartial();
          } else {
            groupLimit_ = value;
          }
          onChanged();
        } else {
          groupLimitBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       */
      public Builder clearGroupLimit() {
        if (groupLimitBuilder_ == null) {
          groupLimit_ = null;
          onChanged();
        } else {
          groupLimitBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       */
      public com.yorha.proto.Struct.DropGroupLimit.Builder getGroupLimitBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getGroupLimitFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       */
      public com.yorha.proto.Struct.DropGroupLimitOrBuilder getGroupLimitOrBuilder() {
        if (groupLimitBuilder_ != null) {
          return groupLimitBuilder_.getMessageOrBuilder();
        } else {
          return groupLimit_ == null ?
              com.yorha.proto.Struct.DropGroupLimit.getDefaultInstance() : groupLimit_;
        }
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimit groupLimit = 9;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.DropGroupLimit, com.yorha.proto.Struct.DropGroupLimit.Builder, com.yorha.proto.Struct.DropGroupLimitOrBuilder> 
          getGroupLimitFieldBuilder() {
        if (groupLimitBuilder_ == null) {
          groupLimitBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.DropGroupLimit, com.yorha.proto.Struct.DropGroupLimit.Builder, com.yorha.proto.Struct.DropGroupLimitOrBuilder>(
                  getGroupLimit(),
                  getParentForChildren(),
                  isClean());
          groupLimit_ = null;
        }
        return groupLimitBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DropObjectEntity)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DropObjectEntity)
    private static final com.yorha.proto.DropObject.DropObjectEntity DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.DropObject.DropObjectEntity();
    }

    public static com.yorha.proto.DropObject.DropObjectEntity getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DropObjectEntity>
        PARSER = new com.google.protobuf.AbstractParser<DropObjectEntity>() {
      @java.lang.Override
      public DropObjectEntity parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DropObjectEntity(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DropObjectEntity> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DropObjectEntity> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.DropObject.DropObjectEntity getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DropObjectEntity_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DropObjectEntity_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)ss_proto/gen/dropObject/drop_object.pr" +
      "oto\022\017com.yorha.proto\032\034ss_proto/gen/cnc/b" +
      "asic.proto\032 ss_proto/gen/common/struct.p" +
      "roto\"\314\002\n\020DropObjectEntity\022\022\n\ntemplateId\030" +
      "\001 \001(\005\022%\n\005point\030\002 \001(\0132\026.com.yorha.proto.P" +
      "oint\0221\n\rpickUpPlayers\030\003 \001(\0132\032.com.yorha." +
      "proto.Int64List\022\023\n\013pickUpTimes\030\004 \001(\005\022\020\n\010" +
      "bornTime\030\005 \001(\003\022\017\n\007ownerId\030\006 \001(\003\022.\n\npickU" +
      "pArmy\030\007 \001(\0132\032.com.yorha.proto.Int64List\022" +
      "-\n\nitemReward\030\010 \001(\0132\031.com.yorha.proto.It" +
      "emList\0223\n\ngroupLimit\030\t \001(\0132\037.com.yorha.p" +
      "roto.DropGroupLimitB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Basic.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_DropObjectEntity_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_DropObjectEntity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DropObjectEntity_descriptor,
        new java.lang.String[] { "TemplateId", "Point", "PickUpPlayers", "PickUpTimes", "BornTime", "OwnerId", "PickUpArmy", "ItemReward", "GroupLimit", });
    com.yorha.proto.Basic.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

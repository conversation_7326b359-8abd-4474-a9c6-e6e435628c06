// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_map.proto

package com.yorha.proto;

public final class SsSceneMap {
  private SsSceneMap() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ExecuteSceneGmAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ExecuteSceneGmAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional string command = 2;</code>
     * @return Whether the command field is set.
     */
    boolean hasCommand();
    /**
     * <code>optional string command = 2;</code>
     * @return The command.
     */
    java.lang.String getCommand();
    /**
     * <code>optional string command = 2;</code>
     * @return The bytes for command.
     */
    com.google.protobuf.ByteString
        getCommandBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ExecuteSceneGmAsk}
   */
  public static final class ExecuteSceneGmAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ExecuteSceneGmAsk)
      ExecuteSceneGmAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ExecuteSceneGmAsk.newBuilder() to construct.
    private ExecuteSceneGmAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ExecuteSceneGmAsk() {
      command_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ExecuteSceneGmAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ExecuteSceneGmAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              command_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk.class, com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int COMMAND_FIELD_NUMBER = 2;
    private volatile java.lang.Object command_;
    /**
     * <code>optional string command = 2;</code>
     * @return Whether the command field is set.
     */
    @java.lang.Override
    public boolean hasCommand() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string command = 2;</code>
     * @return The command.
     */
    @java.lang.Override
    public java.lang.String getCommand() {
      java.lang.Object ref = command_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          command_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string command = 2;</code>
     * @return The bytes for command.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCommandBytes() {
      java.lang.Object ref = command_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        command_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, command_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, command_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk other = (com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasCommand() != other.hasCommand()) return false;
      if (hasCommand()) {
        if (!getCommand()
            .equals(other.getCommand())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasCommand()) {
        hash = (37 * hash) + COMMAND_FIELD_NUMBER;
        hash = (53 * hash) + getCommand().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ExecuteSceneGmAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ExecuteSceneGmAsk)
        com.yorha.proto.SsSceneMap.ExecuteSceneGmAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk.class, com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        command_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk build() {
        com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk buildPartial() {
        com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk result = new com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.command_ = command_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk) {
          return mergeFrom((com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk other) {
        if (other == com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasCommand()) {
          bitField0_ |= 0x00000002;
          command_ = other.command_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object command_ = "";
      /**
       * <code>optional string command = 2;</code>
       * @return Whether the command field is set.
       */
      public boolean hasCommand() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string command = 2;</code>
       * @return The command.
       */
      public java.lang.String getCommand() {
        java.lang.Object ref = command_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            command_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string command = 2;</code>
       * @return The bytes for command.
       */
      public com.google.protobuf.ByteString
          getCommandBytes() {
        java.lang.Object ref = command_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          command_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string command = 2;</code>
       * @param value The command to set.
       * @return This builder for chaining.
       */
      public Builder setCommand(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        command_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string command = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommand() {
        bitField0_ = (bitField0_ & ~0x00000002);
        command_ = getDefaultInstance().getCommand();
        onChanged();
        return this;
      }
      /**
       * <code>optional string command = 2;</code>
       * @param value The bytes for command to set.
       * @return This builder for chaining.
       */
      public Builder setCommandBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        command_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ExecuteSceneGmAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ExecuteSceneGmAsk)
    private static final com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk();
    }

    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ExecuteSceneGmAsk>
        PARSER = new com.google.protobuf.AbstractParser<ExecuteSceneGmAsk>() {
      @java.lang.Override
      public ExecuteSceneGmAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ExecuteSceneGmAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ExecuteSceneGmAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ExecuteSceneGmAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.ExecuteSceneGmAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ExecuteSceneGmAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ExecuteSceneGmAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string result = 1;</code>
     * @return Whether the result field is set.
     */
    boolean hasResult();
    /**
     * <code>optional string result = 1;</code>
     * @return The result.
     */
    java.lang.String getResult();
    /**
     * <code>optional string result = 1;</code>
     * @return The bytes for result.
     */
    com.google.protobuf.ByteString
        getResultBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ExecuteSceneGmAns}
   */
  public static final class ExecuteSceneGmAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ExecuteSceneGmAns)
      ExecuteSceneGmAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ExecuteSceneGmAns.newBuilder() to construct.
    private ExecuteSceneGmAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ExecuteSceneGmAns() {
      result_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ExecuteSceneGmAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ExecuteSceneGmAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              result_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.ExecuteSceneGmAns.class, com.yorha.proto.SsSceneMap.ExecuteSceneGmAns.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private volatile java.lang.Object result_;
    /**
     * <code>optional string result = 1;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public java.lang.String getResult() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          result_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string result = 1;</code>
     * @return The bytes for result.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getResultBytes() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        result_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, result_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, result_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.ExecuteSceneGmAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.ExecuteSceneGmAns other = (com.yorha.proto.SsSceneMap.ExecuteSceneGmAns) obj;

      if (hasResult() != other.hasResult()) return false;
      if (hasResult()) {
        if (!getResult()
            .equals(other.getResult())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.ExecuteSceneGmAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ExecuteSceneGmAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ExecuteSceneGmAns)
        com.yorha.proto.SsSceneMap.ExecuteSceneGmAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.ExecuteSceneGmAns.class, com.yorha.proto.SsSceneMap.ExecuteSceneGmAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.ExecuteSceneGmAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        result_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_ExecuteSceneGmAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.ExecuteSceneGmAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.ExecuteSceneGmAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.ExecuteSceneGmAns build() {
        com.yorha.proto.SsSceneMap.ExecuteSceneGmAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.ExecuteSceneGmAns buildPartial() {
        com.yorha.proto.SsSceneMap.ExecuteSceneGmAns result = new com.yorha.proto.SsSceneMap.ExecuteSceneGmAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.result_ = result_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.ExecuteSceneGmAns) {
          return mergeFrom((com.yorha.proto.SsSceneMap.ExecuteSceneGmAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.ExecuteSceneGmAns other) {
        if (other == com.yorha.proto.SsSceneMap.ExecuteSceneGmAns.getDefaultInstance()) return this;
        if (other.hasResult()) {
          bitField0_ |= 0x00000001;
          result_ = other.result_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.ExecuteSceneGmAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.ExecuteSceneGmAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object result_ = "";
      /**
       * <code>optional string result = 1;</code>
       * @return Whether the result field is set.
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string result = 1;</code>
       * @return The result.
       */
      public java.lang.String getResult() {
        java.lang.Object ref = result_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            result_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string result = 1;</code>
       * @return The bytes for result.
       */
      public com.google.protobuf.ByteString
          getResultBytes() {
        java.lang.Object ref = result_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          result_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = getDefaultInstance().getResult();
        onChanged();
        return this;
      }
      /**
       * <code>optional string result = 1;</code>
       * @param value The bytes for result to set.
       * @return This builder for chaining.
       */
      public Builder setResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        result_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ExecuteSceneGmAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ExecuteSceneGmAns)
    private static final com.yorha.proto.SsSceneMap.ExecuteSceneGmAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.ExecuteSceneGmAns();
    }

    public static com.yorha.proto.SsSceneMap.ExecuteSceneGmAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ExecuteSceneGmAns>
        PARSER = new com.google.protobuf.AbstractParser<ExecuteSceneGmAns>() {
      @java.lang.Override
      public ExecuteSceneGmAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ExecuteSceneGmAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ExecuteSceneGmAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ExecuteSceneGmAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.ExecuteSceneGmAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchPathAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchPathAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Point src = 1;</code>
     * @return Whether the src field is set.
     */
    boolean hasSrc();
    /**
     * <code>optional .com.yorha.proto.Point src = 1;</code>
     * @return The src.
     */
    com.yorha.proto.Struct.Point getSrc();
    /**
     * <code>optional .com.yorha.proto.Point src = 1;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getSrcOrBuilder();

    /**
     * <code>optional .com.yorha.proto.Point end = 2;</code>
     * @return Whether the end field is set.
     */
    boolean hasEnd();
    /**
     * <code>optional .com.yorha.proto.Point end = 2;</code>
     * @return The end.
     */
    com.yorha.proto.Struct.Point getEnd();
    /**
     * <code>optional .com.yorha.proto.Point end = 2;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getEndOrBuilder();

    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 用来参与集结拿不到目标时用
     * </pre>
     *
     * <code>optional int64 rallyId = 4;</code>
     * @return Whether the rallyId field is set.
     */
    boolean hasRallyId();
    /**
     * <pre>
     * 用来参与集结拿不到目标时用
     * </pre>
     *
     * <code>optional int64 rallyId = 4;</code>
     * @return The rallyId.
     */
    long getRallyId();

    /**
     * <code>optional int64 targetId = 5;</code>
     * @return Whether the targetId field is set.
     */
    boolean hasTargetId();
    /**
     * <code>optional int64 targetId = 5;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchPathAsk}
   */
  public static final class SearchPathAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchPathAsk)
      SearchPathAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchPathAsk.newBuilder() to construct.
    private SearchPathAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchPathAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchPathAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchPathAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = src_.toBuilder();
              }
              src_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(src_);
                src_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = end_.toBuilder();
              }
              end_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(end_);
                end_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              rallyId_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              targetId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.SearchPathAsk.class, com.yorha.proto.SsSceneMap.SearchPathAsk.Builder.class);
    }

    private int bitField0_;
    public static final int SRC_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.Point src_;
    /**
     * <code>optional .com.yorha.proto.Point src = 1;</code>
     * @return Whether the src field is set.
     */
    @java.lang.Override
    public boolean hasSrc() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Point src = 1;</code>
     * @return The src.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getSrc() {
      return src_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : src_;
    }
    /**
     * <code>optional .com.yorha.proto.Point src = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getSrcOrBuilder() {
      return src_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : src_;
    }

    public static final int END_FIELD_NUMBER = 2;
    private com.yorha.proto.Struct.Point end_;
    /**
     * <code>optional .com.yorha.proto.Point end = 2;</code>
     * @return Whether the end field is set.
     */
    @java.lang.Override
    public boolean hasEnd() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Point end = 2;</code>
     * @return The end.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getEnd() {
      return end_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : end_;
    }
    /**
     * <code>optional .com.yorha.proto.Point end = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getEndOrBuilder() {
      return end_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : end_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int RALLYID_FIELD_NUMBER = 4;
    private long rallyId_;
    /**
     * <pre>
     * 用来参与集结拿不到目标时用
     * </pre>
     *
     * <code>optional int64 rallyId = 4;</code>
     * @return Whether the rallyId field is set.
     */
    @java.lang.Override
    public boolean hasRallyId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 用来参与集结拿不到目标时用
     * </pre>
     *
     * <code>optional int64 rallyId = 4;</code>
     * @return The rallyId.
     */
    @java.lang.Override
    public long getRallyId() {
      return rallyId_;
    }

    public static final int TARGETID_FIELD_NUMBER = 5;
    private long targetId_;
    /**
     * <code>optional int64 targetId = 5;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int64 targetId = 5;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getSrc());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getEnd());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, rallyId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, targetId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getSrc());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getEnd());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, rallyId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, targetId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.SearchPathAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.SearchPathAsk other = (com.yorha.proto.SsSceneMap.SearchPathAsk) obj;

      if (hasSrc() != other.hasSrc()) return false;
      if (hasSrc()) {
        if (!getSrc()
            .equals(other.getSrc())) return false;
      }
      if (hasEnd() != other.hasEnd()) return false;
      if (hasEnd()) {
        if (!getEnd()
            .equals(other.getEnd())) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasRallyId() != other.hasRallyId()) return false;
      if (hasRallyId()) {
        if (getRallyId()
            != other.getRallyId()) return false;
      }
      if (hasTargetId() != other.hasTargetId()) return false;
      if (hasTargetId()) {
        if (getTargetId()
            != other.getTargetId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSrc()) {
        hash = (37 * hash) + SRC_FIELD_NUMBER;
        hash = (53 * hash) + getSrc().hashCode();
      }
      if (hasEnd()) {
        hash = (37 * hash) + END_FIELD_NUMBER;
        hash = (53 * hash) + getEnd().hashCode();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasRallyId()) {
        hash = (37 * hash) + RALLYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRallyId());
      }
      if (hasTargetId()) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.SearchPathAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchPathAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchPathAsk)
        com.yorha.proto.SsSceneMap.SearchPathAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.SearchPathAsk.class, com.yorha.proto.SsSceneMap.SearchPathAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.SearchPathAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSrcFieldBuilder();
          getEndFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (srcBuilder_ == null) {
          src_ = null;
        } else {
          srcBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (endBuilder_ == null) {
          end_ = null;
        } else {
          endBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        rallyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        targetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.SearchPathAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.SearchPathAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.SearchPathAsk build() {
        com.yorha.proto.SsSceneMap.SearchPathAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.SearchPathAsk buildPartial() {
        com.yorha.proto.SsSceneMap.SearchPathAsk result = new com.yorha.proto.SsSceneMap.SearchPathAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (srcBuilder_ == null) {
            result.src_ = src_;
          } else {
            result.src_ = srcBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (endBuilder_ == null) {
            result.end_ = end_;
          } else {
            result.end_ = endBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.rallyId_ = rallyId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.targetId_ = targetId_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.SearchPathAsk) {
          return mergeFrom((com.yorha.proto.SsSceneMap.SearchPathAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.SearchPathAsk other) {
        if (other == com.yorha.proto.SsSceneMap.SearchPathAsk.getDefaultInstance()) return this;
        if (other.hasSrc()) {
          mergeSrc(other.getSrc());
        }
        if (other.hasEnd()) {
          mergeEnd(other.getEnd());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasRallyId()) {
          setRallyId(other.getRallyId());
        }
        if (other.hasTargetId()) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.SearchPathAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.SearchPathAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.Point src_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> srcBuilder_;
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       * @return Whether the src field is set.
       */
      public boolean hasSrc() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       * @return The src.
       */
      public com.yorha.proto.Struct.Point getSrc() {
        if (srcBuilder_ == null) {
          return src_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : src_;
        } else {
          return srcBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       */
      public Builder setSrc(com.yorha.proto.Struct.Point value) {
        if (srcBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          src_ = value;
          onChanged();
        } else {
          srcBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       */
      public Builder setSrc(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (srcBuilder_ == null) {
          src_ = builderForValue.build();
          onChanged();
        } else {
          srcBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       */
      public Builder mergeSrc(com.yorha.proto.Struct.Point value) {
        if (srcBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              src_ != null &&
              src_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            src_ =
              com.yorha.proto.Struct.Point.newBuilder(src_).mergeFrom(value).buildPartial();
          } else {
            src_ = value;
          }
          onChanged();
        } else {
          srcBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       */
      public Builder clearSrc() {
        if (srcBuilder_ == null) {
          src_ = null;
          onChanged();
        } else {
          srcBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getSrcBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSrcFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getSrcOrBuilder() {
        if (srcBuilder_ != null) {
          return srcBuilder_.getMessageOrBuilder();
        } else {
          return src_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : src_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getSrcFieldBuilder() {
        if (srcBuilder_ == null) {
          srcBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getSrc(),
                  getParentForChildren(),
                  isClean());
          src_ = null;
        }
        return srcBuilder_;
      }

      private com.yorha.proto.Struct.Point end_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> endBuilder_;
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       * @return Whether the end field is set.
       */
      public boolean hasEnd() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       * @return The end.
       */
      public com.yorha.proto.Struct.Point getEnd() {
        if (endBuilder_ == null) {
          return end_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : end_;
        } else {
          return endBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       */
      public Builder setEnd(com.yorha.proto.Struct.Point value) {
        if (endBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          end_ = value;
          onChanged();
        } else {
          endBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       */
      public Builder setEnd(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (endBuilder_ == null) {
          end_ = builderForValue.build();
          onChanged();
        } else {
          endBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       */
      public Builder mergeEnd(com.yorha.proto.Struct.Point value) {
        if (endBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              end_ != null &&
              end_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            end_ =
              com.yorha.proto.Struct.Point.newBuilder(end_).mergeFrom(value).buildPartial();
          } else {
            end_ = value;
          }
          onChanged();
        } else {
          endBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       */
      public Builder clearEnd() {
        if (endBuilder_ == null) {
          end_ = null;
          onChanged();
        } else {
          endBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getEndBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getEndFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getEndOrBuilder() {
        if (endBuilder_ != null) {
          return endBuilder_.getMessageOrBuilder();
        } else {
          return end_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : end_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getEndFieldBuilder() {
        if (endBuilder_ == null) {
          endBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getEnd(),
                  getParentForChildren(),
                  isClean());
          end_ = null;
        }
        return endBuilder_;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long rallyId_ ;
      /**
       * <pre>
       * 用来参与集结拿不到目标时用
       * </pre>
       *
       * <code>optional int64 rallyId = 4;</code>
       * @return Whether the rallyId field is set.
       */
      @java.lang.Override
      public boolean hasRallyId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 用来参与集结拿不到目标时用
       * </pre>
       *
       * <code>optional int64 rallyId = 4;</code>
       * @return The rallyId.
       */
      @java.lang.Override
      public long getRallyId() {
        return rallyId_;
      }
      /**
       * <pre>
       * 用来参与集结拿不到目标时用
       * </pre>
       *
       * <code>optional int64 rallyId = 4;</code>
       * @param value The rallyId to set.
       * @return This builder for chaining.
       */
      public Builder setRallyId(long value) {
        bitField0_ |= 0x00000008;
        rallyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 用来参与集结拿不到目标时用
       * </pre>
       *
       * <code>optional int64 rallyId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearRallyId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        rallyId_ = 0L;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <code>optional int64 targetId = 5;</code>
       * @return Whether the targetId field is set.
       */
      @java.lang.Override
      public boolean hasTargetId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int64 targetId = 5;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>optional int64 targetId = 5;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        bitField0_ |= 0x00000010;
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 targetId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchPathAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchPathAsk)
    private static final com.yorha.proto.SsSceneMap.SearchPathAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.SearchPathAsk();
    }

    public static com.yorha.proto.SsSceneMap.SearchPathAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchPathAsk>
        PARSER = new com.google.protobuf.AbstractParser<SearchPathAsk>() {
      @java.lang.Override
      public SearchPathAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchPathAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchPathAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchPathAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.SearchPathAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchPathAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchPathAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    java.util.List<com.yorha.proto.Struct.Point> 
        getPathList();
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    com.yorha.proto.Struct.Point getPath(int index);
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    int getPathCount();
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
        getPathOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPathOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchPathAns}
   */
  public static final class SearchPathAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchPathAns)
      SearchPathAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchPathAns.newBuilder() to construct.
    private SearchPathAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchPathAns() {
      path_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchPathAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchPathAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                path_ = new java.util.ArrayList<com.yorha.proto.Struct.Point>();
                mutable_bitField0_ |= 0x00000001;
              }
              path_.add(
                  input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          path_ = java.util.Collections.unmodifiableList(path_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.SearchPathAns.class, com.yorha.proto.SsSceneMap.SearchPathAns.Builder.class);
    }

    public static final int PATH_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.Struct.Point> path_;
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.Struct.Point> getPathList() {
      return path_;
    }
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
        getPathOrBuilderList() {
      return path_;
    }
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    @java.lang.Override
    public int getPathCount() {
      return path_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPath(int index) {
      return path_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.Point path = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPathOrBuilder(
        int index) {
      return path_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < path_.size(); i++) {
        output.writeMessage(1, path_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < path_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, path_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.SearchPathAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.SearchPathAns other = (com.yorha.proto.SsSceneMap.SearchPathAns) obj;

      if (!getPathList()
          .equals(other.getPathList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPathCount() > 0) {
        hash = (37 * hash) + PATH_FIELD_NUMBER;
        hash = (53 * hash) + getPathList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.SearchPathAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.SearchPathAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchPathAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchPathAns)
        com.yorha.proto.SsSceneMap.SearchPathAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.SearchPathAns.class, com.yorha.proto.SsSceneMap.SearchPathAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.SearchPathAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPathFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (pathBuilder_ == null) {
          path_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          pathBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_SearchPathAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.SearchPathAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.SearchPathAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.SearchPathAns build() {
        com.yorha.proto.SsSceneMap.SearchPathAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.SearchPathAns buildPartial() {
        com.yorha.proto.SsSceneMap.SearchPathAns result = new com.yorha.proto.SsSceneMap.SearchPathAns(this);
        int from_bitField0_ = bitField0_;
        if (pathBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            path_ = java.util.Collections.unmodifiableList(path_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.path_ = path_;
        } else {
          result.path_ = pathBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.SearchPathAns) {
          return mergeFrom((com.yorha.proto.SsSceneMap.SearchPathAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.SearchPathAns other) {
        if (other == com.yorha.proto.SsSceneMap.SearchPathAns.getDefaultInstance()) return this;
        if (pathBuilder_ == null) {
          if (!other.path_.isEmpty()) {
            if (path_.isEmpty()) {
              path_ = other.path_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePathIsMutable();
              path_.addAll(other.path_);
            }
            onChanged();
          }
        } else {
          if (!other.path_.isEmpty()) {
            if (pathBuilder_.isEmpty()) {
              pathBuilder_.dispose();
              pathBuilder_ = null;
              path_ = other.path_;
              bitField0_ = (bitField0_ & ~0x00000001);
              pathBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPathFieldBuilder() : null;
            } else {
              pathBuilder_.addAllMessages(other.path_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.SearchPathAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.SearchPathAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.Struct.Point> path_ =
        java.util.Collections.emptyList();
      private void ensurePathIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          path_ = new java.util.ArrayList<com.yorha.proto.Struct.Point>(path_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pathBuilder_;

      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Point> getPathList() {
        if (pathBuilder_ == null) {
          return java.util.Collections.unmodifiableList(path_);
        } else {
          return pathBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public int getPathCount() {
        if (pathBuilder_ == null) {
          return path_.size();
        } else {
          return pathBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public com.yorha.proto.Struct.Point getPath(int index) {
        if (pathBuilder_ == null) {
          return path_.get(index);
        } else {
          return pathBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder setPath(
          int index, com.yorha.proto.Struct.Point value) {
        if (pathBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePathIsMutable();
          path_.set(index, value);
          onChanged();
        } else {
          pathBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder setPath(
          int index, com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pathBuilder_ == null) {
          ensurePathIsMutable();
          path_.set(index, builderForValue.build());
          onChanged();
        } else {
          pathBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder addPath(com.yorha.proto.Struct.Point value) {
        if (pathBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePathIsMutable();
          path_.add(value);
          onChanged();
        } else {
          pathBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder addPath(
          int index, com.yorha.proto.Struct.Point value) {
        if (pathBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePathIsMutable();
          path_.add(index, value);
          onChanged();
        } else {
          pathBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder addPath(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pathBuilder_ == null) {
          ensurePathIsMutable();
          path_.add(builderForValue.build());
          onChanged();
        } else {
          pathBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder addPath(
          int index, com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pathBuilder_ == null) {
          ensurePathIsMutable();
          path_.add(index, builderForValue.build());
          onChanged();
        } else {
          pathBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder addAllPath(
          java.lang.Iterable<? extends com.yorha.proto.Struct.Point> values) {
        if (pathBuilder_ == null) {
          ensurePathIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, path_);
          onChanged();
        } else {
          pathBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder clearPath() {
        if (pathBuilder_ == null) {
          path_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          pathBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public Builder removePath(int index) {
        if (pathBuilder_ == null) {
          ensurePathIsMutable();
          path_.remove(index);
          onChanged();
        } else {
          pathBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPathBuilder(
          int index) {
        return getPathFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPathOrBuilder(
          int index) {
        if (pathBuilder_ == null) {
          return path_.get(index);  } else {
          return pathBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
           getPathOrBuilderList() {
        if (pathBuilder_ != null) {
          return pathBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(path_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder addPathBuilder() {
        return getPathFieldBuilder().addBuilder(
            com.yorha.proto.Struct.Point.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder addPathBuilder(
          int index) {
        return getPathFieldBuilder().addBuilder(
            index, com.yorha.proto.Struct.Point.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.Point path = 1;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Point.Builder> 
           getPathBuilderList() {
        return getPathFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPathFieldBuilder() {
        if (pathBuilder_ == null) {
          pathBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  path_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          path_ = null;
        }
        return pathBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchPathAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchPathAns)
    private static final com.yorha.proto.SsSceneMap.SearchPathAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.SearchPathAns();
    }

    public static com.yorha.proto.SsSceneMap.SearchPathAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchPathAns>
        PARSER = new com.google.protobuf.AbstractParser<SearchPathAns>() {
      @java.lang.Override
      public SearchPathAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchPathAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchPathAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchPathAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.SearchPathAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanCityPointListAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanCityPointListAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int32 version = 2;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <code>optional int32 version = 2;</code>
     * @return The version.
     */
    int getVersion();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanCityPointListAsk}
   */
  public static final class FetchClanCityPointListAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanCityPointListAsk)
      FetchClanCityPointListAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanCityPointListAsk.newBuilder() to construct.
    private FetchClanCityPointListAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanCityPointListAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanCityPointListAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanCityPointListAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              version_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk.class, com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int VERSION_FIELD_NUMBER = 2;
    private int version_;
    /**
     * <code>optional int32 version = 2;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 version = 2;</code>
     * @return The version.
     */
    @java.lang.Override
    public int getVersion() {
      return version_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, version_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, version_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk other = (com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getVersion();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanCityPointListAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanCityPointListAsk)
        com.yorha.proto.SsSceneMap.FetchClanCityPointListAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk.class, com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        version_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk build() {
        com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk buildPartial() {
        com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk result = new com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk) {
          return mergeFrom((com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk other) {
        if (other == com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int version_ ;
      /**
       * <code>optional int32 version = 2;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 version = 2;</code>
       * @return The version.
       */
      @java.lang.Override
      public int getVersion() {
        return version_;
      }
      /**
       * <code>optional int32 version = 2;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(int value) {
        bitField0_ |= 0x00000002;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 version = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000002);
        version_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanCityPointListAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanCityPointListAsk)
    private static final com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk();
    }

    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanCityPointListAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanCityPointListAsk>() {
      @java.lang.Override
      public FetchClanCityPointListAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanCityPointListAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanCityPointListAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanCityPointListAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.FetchClanCityPointListAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanCityPointListAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanCityPointListAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 version = 1;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <code>optional int32 version = 1;</code>
     * @return The version.
     */
    int getVersion();

    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    java.util.List<com.yorha.proto.Struct.Point> 
        getPointListList();
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    com.yorha.proto.Struct.Point getPointList(int index);
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    int getPointListCount();
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
        getPointListOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointListOrBuilder(
        int index);

    /**
     * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
     * @return Whether the ownerPos field is set.
     */
    boolean hasOwnerPos();
    /**
     * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
     * @return The ownerPos.
     */
    com.yorha.proto.Struct.Point getOwnerPos();
    /**
     * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getOwnerPosOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanCityPointListAns}
   */
  public static final class FetchClanCityPointListAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanCityPointListAns)
      FetchClanCityPointListAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanCityPointListAns.newBuilder() to construct.
    private FetchClanCityPointListAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanCityPointListAns() {
      pointList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanCityPointListAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanCityPointListAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              version_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                pointList_ = new java.util.ArrayList<com.yorha.proto.Struct.Point>();
                mutable_bitField0_ |= 0x00000002;
              }
              pointList_.add(
                  input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry));
              break;
            }
            case 26: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = ownerPos_.toBuilder();
              }
              ownerPos_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(ownerPos_);
                ownerPos_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          pointList_ = java.util.Collections.unmodifiableList(pointList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.FetchClanCityPointListAns.class, com.yorha.proto.SsSceneMap.FetchClanCityPointListAns.Builder.class);
    }

    private int bitField0_;
    public static final int VERSION_FIELD_NUMBER = 1;
    private int version_;
    /**
     * <code>optional int32 version = 1;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 version = 1;</code>
     * @return The version.
     */
    @java.lang.Override
    public int getVersion() {
      return version_;
    }

    public static final int POINTLIST_FIELD_NUMBER = 2;
    private java.util.List<com.yorha.proto.Struct.Point> pointList_;
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.Struct.Point> getPointListList() {
      return pointList_;
    }
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
        getPointListOrBuilderList() {
      return pointList_;
    }
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    @java.lang.Override
    public int getPointListCount() {
      return pointList_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPointList(int index) {
      return pointList_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointListOrBuilder(
        int index) {
      return pointList_.get(index);
    }

    public static final int OWNERPOS_FIELD_NUMBER = 3;
    private com.yorha.proto.Struct.Point ownerPos_;
    /**
     * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
     * @return Whether the ownerPos field is set.
     */
    @java.lang.Override
    public boolean hasOwnerPos() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
     * @return The ownerPos.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getOwnerPos() {
      return ownerPos_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : ownerPos_;
    }
    /**
     * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getOwnerPosOrBuilder() {
      return ownerPos_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : ownerPos_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, version_);
      }
      for (int i = 0; i < pointList_.size(); i++) {
        output.writeMessage(2, pointList_.get(i));
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(3, getOwnerPos());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, version_);
      }
      for (int i = 0; i < pointList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, pointList_.get(i));
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getOwnerPos());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.FetchClanCityPointListAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.FetchClanCityPointListAns other = (com.yorha.proto.SsSceneMap.FetchClanCityPointListAns) obj;

      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (!getPointListList()
          .equals(other.getPointListList())) return false;
      if (hasOwnerPos() != other.hasOwnerPos()) return false;
      if (hasOwnerPos()) {
        if (!getOwnerPos()
            .equals(other.getOwnerPos())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getVersion();
      }
      if (getPointListCount() > 0) {
        hash = (37 * hash) + POINTLIST_FIELD_NUMBER;
        hash = (53 * hash) + getPointListList().hashCode();
      }
      if (hasOwnerPos()) {
        hash = (37 * hash) + OWNERPOS_FIELD_NUMBER;
        hash = (53 * hash) + getOwnerPos().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.FetchClanCityPointListAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanCityPointListAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanCityPointListAns)
        com.yorha.proto.SsSceneMap.FetchClanCityPointListAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.FetchClanCityPointListAns.class, com.yorha.proto.SsSceneMap.FetchClanCityPointListAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.FetchClanCityPointListAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointListFieldBuilder();
          getOwnerPosFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        version_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pointListBuilder_ == null) {
          pointList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          pointListBuilder_.clear();
        }
        if (ownerPosBuilder_ == null) {
          ownerPos_ = null;
        } else {
          ownerPosBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchClanCityPointListAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchClanCityPointListAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.FetchClanCityPointListAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchClanCityPointListAns build() {
        com.yorha.proto.SsSceneMap.FetchClanCityPointListAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchClanCityPointListAns buildPartial() {
        com.yorha.proto.SsSceneMap.FetchClanCityPointListAns result = new com.yorha.proto.SsSceneMap.FetchClanCityPointListAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000001;
        }
        if (pointListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            pointList_ = java.util.Collections.unmodifiableList(pointList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.pointList_ = pointList_;
        } else {
          result.pointList_ = pointListBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (ownerPosBuilder_ == null) {
            result.ownerPos_ = ownerPos_;
          } else {
            result.ownerPos_ = ownerPosBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.FetchClanCityPointListAns) {
          return mergeFrom((com.yorha.proto.SsSceneMap.FetchClanCityPointListAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.FetchClanCityPointListAns other) {
        if (other == com.yorha.proto.SsSceneMap.FetchClanCityPointListAns.getDefaultInstance()) return this;
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        if (pointListBuilder_ == null) {
          if (!other.pointList_.isEmpty()) {
            if (pointList_.isEmpty()) {
              pointList_ = other.pointList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePointListIsMutable();
              pointList_.addAll(other.pointList_);
            }
            onChanged();
          }
        } else {
          if (!other.pointList_.isEmpty()) {
            if (pointListBuilder_.isEmpty()) {
              pointListBuilder_.dispose();
              pointListBuilder_ = null;
              pointList_ = other.pointList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              pointListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPointListFieldBuilder() : null;
            } else {
              pointListBuilder_.addAllMessages(other.pointList_);
            }
          }
        }
        if (other.hasOwnerPos()) {
          mergeOwnerPos(other.getOwnerPos());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.FetchClanCityPointListAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.FetchClanCityPointListAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int version_ ;
      /**
       * <code>optional int32 version = 1;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @return The version.
       */
      @java.lang.Override
      public int getVersion() {
        return version_;
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(int value) {
        bitField0_ |= 0x00000001;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000001);
        version_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.Struct.Point> pointList_ =
        java.util.Collections.emptyList();
      private void ensurePointListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          pointList_ = new java.util.ArrayList<com.yorha.proto.Struct.Point>(pointList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointListBuilder_;

      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Point> getPointListList() {
        if (pointListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(pointList_);
        } else {
          return pointListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public int getPointListCount() {
        if (pointListBuilder_ == null) {
          return pointList_.size();
        } else {
          return pointListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public com.yorha.proto.Struct.Point getPointList(int index) {
        if (pointListBuilder_ == null) {
          return pointList_.get(index);
        } else {
          return pointListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder setPointList(
          int index, com.yorha.proto.Struct.Point value) {
        if (pointListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePointListIsMutable();
          pointList_.set(index, value);
          onChanged();
        } else {
          pointListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder setPointList(
          int index, com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointListBuilder_ == null) {
          ensurePointListIsMutable();
          pointList_.set(index, builderForValue.build());
          onChanged();
        } else {
          pointListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder addPointList(com.yorha.proto.Struct.Point value) {
        if (pointListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePointListIsMutable();
          pointList_.add(value);
          onChanged();
        } else {
          pointListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder addPointList(
          int index, com.yorha.proto.Struct.Point value) {
        if (pointListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePointListIsMutable();
          pointList_.add(index, value);
          onChanged();
        } else {
          pointListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder addPointList(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointListBuilder_ == null) {
          ensurePointListIsMutable();
          pointList_.add(builderForValue.build());
          onChanged();
        } else {
          pointListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder addPointList(
          int index, com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointListBuilder_ == null) {
          ensurePointListIsMutable();
          pointList_.add(index, builderForValue.build());
          onChanged();
        } else {
          pointListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder addAllPointList(
          java.lang.Iterable<? extends com.yorha.proto.Struct.Point> values) {
        if (pointListBuilder_ == null) {
          ensurePointListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, pointList_);
          onChanged();
        } else {
          pointListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder clearPointList() {
        if (pointListBuilder_ == null) {
          pointList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          pointListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public Builder removePointList(int index) {
        if (pointListBuilder_ == null) {
          ensurePointListIsMutable();
          pointList_.remove(index);
          onChanged();
        } else {
          pointListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointListBuilder(
          int index) {
        return getPointListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointListOrBuilder(
          int index) {
        if (pointListBuilder_ == null) {
          return pointList_.get(index);  } else {
          return pointListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
           getPointListOrBuilderList() {
        if (pointListBuilder_ != null) {
          return pointListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(pointList_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public com.yorha.proto.Struct.Point.Builder addPointListBuilder() {
        return getPointListFieldBuilder().addBuilder(
            com.yorha.proto.Struct.Point.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public com.yorha.proto.Struct.Point.Builder addPointListBuilder(
          int index) {
        return getPointListFieldBuilder().addBuilder(
            index, com.yorha.proto.Struct.Point.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.Point pointList = 2;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Point.Builder> 
           getPointListBuilderList() {
        return getPointListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointListFieldBuilder() {
        if (pointListBuilder_ == null) {
          pointListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  pointList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          pointList_ = null;
        }
        return pointListBuilder_;
      }

      private com.yorha.proto.Struct.Point ownerPos_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> ownerPosBuilder_;
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       * @return Whether the ownerPos field is set.
       */
      public boolean hasOwnerPos() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       * @return The ownerPos.
       */
      public com.yorha.proto.Struct.Point getOwnerPos() {
        if (ownerPosBuilder_ == null) {
          return ownerPos_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : ownerPos_;
        } else {
          return ownerPosBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       */
      public Builder setOwnerPos(com.yorha.proto.Struct.Point value) {
        if (ownerPosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ownerPos_ = value;
          onChanged();
        } else {
          ownerPosBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       */
      public Builder setOwnerPos(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (ownerPosBuilder_ == null) {
          ownerPos_ = builderForValue.build();
          onChanged();
        } else {
          ownerPosBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       */
      public Builder mergeOwnerPos(com.yorha.proto.Struct.Point value) {
        if (ownerPosBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              ownerPos_ != null &&
              ownerPos_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            ownerPos_ =
              com.yorha.proto.Struct.Point.newBuilder(ownerPos_).mergeFrom(value).buildPartial();
          } else {
            ownerPos_ = value;
          }
          onChanged();
        } else {
          ownerPosBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       */
      public Builder clearOwnerPos() {
        if (ownerPosBuilder_ == null) {
          ownerPos_ = null;
          onChanged();
        } else {
          ownerPosBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getOwnerPosBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getOwnerPosFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getOwnerPosOrBuilder() {
        if (ownerPosBuilder_ != null) {
          return ownerPosBuilder_.getMessageOrBuilder();
        } else {
          return ownerPos_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : ownerPos_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point ownerPos = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getOwnerPosFieldBuilder() {
        if (ownerPosBuilder_ == null) {
          ownerPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getOwnerPos(),
                  getParentForChildren(),
                  isClean());
          ownerPos_ = null;
        }
        return ownerPosBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanCityPointListAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanCityPointListAns)
    private static final com.yorha.proto.SsSceneMap.FetchClanCityPointListAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.FetchClanCityPointListAns();
    }

    public static com.yorha.proto.SsSceneMap.FetchClanCityPointListAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanCityPointListAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanCityPointListAns>() {
      @java.lang.Override
      public FetchClanCityPointListAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanCityPointListAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanCityPointListAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanCityPointListAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.FetchClanCityPointListAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchSingleClanMemberCityPointAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchSingleClanMemberCityPointAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 fetchPlayerId = 2;</code>
     * @return Whether the fetchPlayerId field is set.
     */
    boolean hasFetchPlayerId();
    /**
     * <code>optional int64 fetchPlayerId = 2;</code>
     * @return The fetchPlayerId.
     */
    long getFetchPlayerId();

    /**
     * <pre>
     * 是true的话，会忽略fetchPlayerId，直接拉取军团长id
     * </pre>
     *
     * <code>optional bool onlyFetchOwner = 3;</code>
     * @return Whether the onlyFetchOwner field is set.
     */
    boolean hasOnlyFetchOwner();
    /**
     * <pre>
     * 是true的话，会忽略fetchPlayerId，直接拉取军团长id
     * </pre>
     *
     * <code>optional bool onlyFetchOwner = 3;</code>
     * @return The onlyFetchOwner.
     */
    boolean getOnlyFetchOwner();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchSingleClanMemberCityPointAsk}
   */
  public static final class FetchSingleClanMemberCityPointAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchSingleClanMemberCityPointAsk)
      FetchSingleClanMemberCityPointAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchSingleClanMemberCityPointAsk.newBuilder() to construct.
    private FetchSingleClanMemberCityPointAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchSingleClanMemberCityPointAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchSingleClanMemberCityPointAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchSingleClanMemberCityPointAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              fetchPlayerId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              onlyFetchOwner_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk.class, com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int FETCHPLAYERID_FIELD_NUMBER = 2;
    private long fetchPlayerId_;
    /**
     * <code>optional int64 fetchPlayerId = 2;</code>
     * @return Whether the fetchPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasFetchPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 fetchPlayerId = 2;</code>
     * @return The fetchPlayerId.
     */
    @java.lang.Override
    public long getFetchPlayerId() {
      return fetchPlayerId_;
    }

    public static final int ONLYFETCHOWNER_FIELD_NUMBER = 3;
    private boolean onlyFetchOwner_;
    /**
     * <pre>
     * 是true的话，会忽略fetchPlayerId，直接拉取军团长id
     * </pre>
     *
     * <code>optional bool onlyFetchOwner = 3;</code>
     * @return Whether the onlyFetchOwner field is set.
     */
    @java.lang.Override
    public boolean hasOnlyFetchOwner() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 是true的话，会忽略fetchPlayerId，直接拉取军团长id
     * </pre>
     *
     * <code>optional bool onlyFetchOwner = 3;</code>
     * @return The onlyFetchOwner.
     */
    @java.lang.Override
    public boolean getOnlyFetchOwner() {
      return onlyFetchOwner_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, fetchPlayerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(3, onlyFetchOwner_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, fetchPlayerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, onlyFetchOwner_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk other = (com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasFetchPlayerId() != other.hasFetchPlayerId()) return false;
      if (hasFetchPlayerId()) {
        if (getFetchPlayerId()
            != other.getFetchPlayerId()) return false;
      }
      if (hasOnlyFetchOwner() != other.hasOnlyFetchOwner()) return false;
      if (hasOnlyFetchOwner()) {
        if (getOnlyFetchOwner()
            != other.getOnlyFetchOwner()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasFetchPlayerId()) {
        hash = (37 * hash) + FETCHPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getFetchPlayerId());
      }
      if (hasOnlyFetchOwner()) {
        hash = (37 * hash) + ONLYFETCHOWNER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getOnlyFetchOwner());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchSingleClanMemberCityPointAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchSingleClanMemberCityPointAsk)
        com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk.class, com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        fetchPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        onlyFetchOwner_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk build() {
        com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk buildPartial() {
        com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk result = new com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.fetchPlayerId_ = fetchPlayerId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.onlyFetchOwner_ = onlyFetchOwner_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk) {
          return mergeFrom((com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk other) {
        if (other == com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasFetchPlayerId()) {
          setFetchPlayerId(other.getFetchPlayerId());
        }
        if (other.hasOnlyFetchOwner()) {
          setOnlyFetchOwner(other.getOnlyFetchOwner());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long fetchPlayerId_ ;
      /**
       * <code>optional int64 fetchPlayerId = 2;</code>
       * @return Whether the fetchPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasFetchPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 fetchPlayerId = 2;</code>
       * @return The fetchPlayerId.
       */
      @java.lang.Override
      public long getFetchPlayerId() {
        return fetchPlayerId_;
      }
      /**
       * <code>optional int64 fetchPlayerId = 2;</code>
       * @param value The fetchPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setFetchPlayerId(long value) {
        bitField0_ |= 0x00000002;
        fetchPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 fetchPlayerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFetchPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        fetchPlayerId_ = 0L;
        onChanged();
        return this;
      }

      private boolean onlyFetchOwner_ ;
      /**
       * <pre>
       * 是true的话，会忽略fetchPlayerId，直接拉取军团长id
       * </pre>
       *
       * <code>optional bool onlyFetchOwner = 3;</code>
       * @return Whether the onlyFetchOwner field is set.
       */
      @java.lang.Override
      public boolean hasOnlyFetchOwner() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 是true的话，会忽略fetchPlayerId，直接拉取军团长id
       * </pre>
       *
       * <code>optional bool onlyFetchOwner = 3;</code>
       * @return The onlyFetchOwner.
       */
      @java.lang.Override
      public boolean getOnlyFetchOwner() {
        return onlyFetchOwner_;
      }
      /**
       * <pre>
       * 是true的话，会忽略fetchPlayerId，直接拉取军团长id
       * </pre>
       *
       * <code>optional bool onlyFetchOwner = 3;</code>
       * @param value The onlyFetchOwner to set.
       * @return This builder for chaining.
       */
      public Builder setOnlyFetchOwner(boolean value) {
        bitField0_ |= 0x00000004;
        onlyFetchOwner_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是true的话，会忽略fetchPlayerId，直接拉取军团长id
       * </pre>
       *
       * <code>optional bool onlyFetchOwner = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOnlyFetchOwner() {
        bitField0_ = (bitField0_ & ~0x00000004);
        onlyFetchOwner_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchSingleClanMemberCityPointAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchSingleClanMemberCityPointAsk)
    private static final com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk();
    }

    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchSingleClanMemberCityPointAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchSingleClanMemberCityPointAsk>() {
      @java.lang.Override
      public FetchSingleClanMemberCityPointAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchSingleClanMemberCityPointAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchSingleClanMemberCityPointAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchSingleClanMemberCityPointAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchSingleClanMemberCityPointAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchSingleClanMemberCityPointAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
     * @return Whether the memberPos field is set.
     */
    boolean hasMemberPos();
    /**
     * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
     * @return The memberPos.
     */
    com.yorha.proto.Struct.Point getMemberPos();
    /**
     * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getMemberPosOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchSingleClanMemberCityPointAns}
   */
  public static final class FetchSingleClanMemberCityPointAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchSingleClanMemberCityPointAns)
      FetchSingleClanMemberCityPointAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchSingleClanMemberCityPointAns.newBuilder() to construct.
    private FetchSingleClanMemberCityPointAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchSingleClanMemberCityPointAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchSingleClanMemberCityPointAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchSingleClanMemberCityPointAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = memberPos_.toBuilder();
              }
              memberPos_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(memberPos_);
                memberPos_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns.class, com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns.Builder.class);
    }

    private int bitField0_;
    public static final int MEMBERPOS_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.Point memberPos_;
    /**
     * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
     * @return Whether the memberPos field is set.
     */
    @java.lang.Override
    public boolean hasMemberPos() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
     * @return The memberPos.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getMemberPos() {
      return memberPos_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : memberPos_;
    }
    /**
     * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getMemberPosOrBuilder() {
      return memberPos_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : memberPos_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getMemberPos());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getMemberPos());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns other = (com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns) obj;

      if (hasMemberPos() != other.hasMemberPos()) return false;
      if (hasMemberPos()) {
        if (!getMemberPos()
            .equals(other.getMemberPos())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMemberPos()) {
        hash = (37 * hash) + MEMBERPOS_FIELD_NUMBER;
        hash = (53 * hash) + getMemberPos().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchSingleClanMemberCityPointAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchSingleClanMemberCityPointAns)
        com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns.class, com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMemberPosFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (memberPosBuilder_ == null) {
          memberPos_ = null;
        } else {
          memberPosBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns build() {
        com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns buildPartial() {
        com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns result = new com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (memberPosBuilder_ == null) {
            result.memberPos_ = memberPos_;
          } else {
            result.memberPos_ = memberPosBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns) {
          return mergeFrom((com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns other) {
        if (other == com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns.getDefaultInstance()) return this;
        if (other.hasMemberPos()) {
          mergeMemberPos(other.getMemberPos());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.Point memberPos_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> memberPosBuilder_;
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       * @return Whether the memberPos field is set.
       */
      public boolean hasMemberPos() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       * @return The memberPos.
       */
      public com.yorha.proto.Struct.Point getMemberPos() {
        if (memberPosBuilder_ == null) {
          return memberPos_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : memberPos_;
        } else {
          return memberPosBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       */
      public Builder setMemberPos(com.yorha.proto.Struct.Point value) {
        if (memberPosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          memberPos_ = value;
          onChanged();
        } else {
          memberPosBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       */
      public Builder setMemberPos(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (memberPosBuilder_ == null) {
          memberPos_ = builderForValue.build();
          onChanged();
        } else {
          memberPosBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       */
      public Builder mergeMemberPos(com.yorha.proto.Struct.Point value) {
        if (memberPosBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              memberPos_ != null &&
              memberPos_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            memberPos_ =
              com.yorha.proto.Struct.Point.newBuilder(memberPos_).mergeFrom(value).buildPartial();
          } else {
            memberPos_ = value;
          }
          onChanged();
        } else {
          memberPosBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       */
      public Builder clearMemberPos() {
        if (memberPosBuilder_ == null) {
          memberPos_ = null;
          onChanged();
        } else {
          memberPosBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getMemberPosBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getMemberPosFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getMemberPosOrBuilder() {
        if (memberPosBuilder_ != null) {
          return memberPosBuilder_.getMessageOrBuilder();
        } else {
          return memberPos_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : memberPos_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point memberPos = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getMemberPosFieldBuilder() {
        if (memberPosBuilder_ == null) {
          memberPosBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getMemberPos(),
                  getParentForChildren(),
                  isClean());
          memberPos_ = null;
        }
        return memberPosBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchSingleClanMemberCityPointAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchSingleClanMemberCityPointAns)
    private static final com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns();
    }

    public static com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchSingleClanMemberCityPointAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchSingleClanMemberCityPointAns>() {
      @java.lang.Override
      public FetchSingleClanMemberCityPointAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchSingleClanMemberCityPointAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchSingleClanMemberCityPointAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchSingleClanMemberCityPointAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.FetchSingleClanMemberCityPointAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchTerritoryMapAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchTerritoryMapAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 version = 1;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <code>optional int32 version = 1;</code>
     * @return The version.
     */
    int getVersion();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchTerritoryMapAsk}
   */
  public static final class FetchTerritoryMapAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchTerritoryMapAsk)
      FetchTerritoryMapAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchTerritoryMapAsk.newBuilder() to construct.
    private FetchTerritoryMapAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchTerritoryMapAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchTerritoryMapAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchTerritoryMapAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              version_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk.class, com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk.Builder.class);
    }

    private int bitField0_;
    public static final int VERSION_FIELD_NUMBER = 1;
    private int version_;
    /**
     * <code>optional int32 version = 1;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 version = 1;</code>
     * @return The version.
     */
    @java.lang.Override
    public int getVersion() {
      return version_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, version_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, version_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk other = (com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk) obj;

      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getVersion();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchTerritoryMapAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchTerritoryMapAsk)
        com.yorha.proto.SsSceneMap.FetchTerritoryMapAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk.class, com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        version_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk build() {
        com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk buildPartial() {
        com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk result = new com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk) {
          return mergeFrom((com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk other) {
        if (other == com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk.getDefaultInstance()) return this;
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int version_ ;
      /**
       * <code>optional int32 version = 1;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @return The version.
       */
      @java.lang.Override
      public int getVersion() {
        return version_;
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(int value) {
        bitField0_ |= 0x00000001;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000001);
        version_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchTerritoryMapAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchTerritoryMapAsk)
    private static final com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk();
    }

    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchTerritoryMapAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchTerritoryMapAsk>() {
      @java.lang.Override
      public FetchTerritoryMapAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchTerritoryMapAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchTerritoryMapAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchTerritoryMapAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.FetchTerritoryMapAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchTerritoryMapAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchTerritoryMapAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 version = 1;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <code>optional int32 version = 1;</code>
     * @return The version.
     */
    int getVersion();

    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    java.util.List<com.yorha.proto.StructPB.TerritoryMapItemPB> 
        getMapItemList();
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    com.yorha.proto.StructPB.TerritoryMapItemPB getMapItem(int index);
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    int getMapItemCount();
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    java.util.List<? extends com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder> 
        getMapItemOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder getMapItemOrBuilder(
        int index);

    /**
     * <code>repeated int64 delClanId = 3;</code>
     * @return A list containing the delClanId.
     */
    java.util.List<java.lang.Long> getDelClanIdList();
    /**
     * <code>repeated int64 delClanId = 3;</code>
     * @return The count of delClanId.
     */
    int getDelClanIdCount();
    /**
     * <code>repeated int64 delClanId = 3;</code>
     * @param index The index of the element to return.
     * @return The delClanId at the given index.
     */
    long getDelClanId(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchTerritoryMapAns}
   */
  public static final class FetchTerritoryMapAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchTerritoryMapAns)
      FetchTerritoryMapAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchTerritoryMapAns.newBuilder() to construct.
    private FetchTerritoryMapAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchTerritoryMapAns() {
      mapItem_ = java.util.Collections.emptyList();
      delClanId_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchTerritoryMapAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchTerritoryMapAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              version_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                mapItem_ = new java.util.ArrayList<com.yorha.proto.StructPB.TerritoryMapItemPB>();
                mutable_bitField0_ |= 0x00000002;
              }
              mapItem_.add(
                  input.readMessage(com.yorha.proto.StructPB.TerritoryMapItemPB.PARSER, extensionRegistry));
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                delClanId_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              delClanId_.addLong(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                delClanId_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                delClanId_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          mapItem_ = java.util.Collections.unmodifiableList(mapItem_);
        }
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          delClanId_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMap.FetchTerritoryMapAns.class, com.yorha.proto.SsSceneMap.FetchTerritoryMapAns.Builder.class);
    }

    private int bitField0_;
    public static final int VERSION_FIELD_NUMBER = 1;
    private int version_;
    /**
     * <code>optional int32 version = 1;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 version = 1;</code>
     * @return The version.
     */
    @java.lang.Override
    public int getVersion() {
      return version_;
    }

    public static final int MAPITEM_FIELD_NUMBER = 2;
    private java.util.List<com.yorha.proto.StructPB.TerritoryMapItemPB> mapItem_;
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructPB.TerritoryMapItemPB> getMapItemList() {
      return mapItem_;
    }
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder> 
        getMapItemOrBuilderList() {
      return mapItem_;
    }
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    @java.lang.Override
    public int getMapItemCount() {
      return mapItem_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.TerritoryMapItemPB getMapItem(int index) {
      return mapItem_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder getMapItemOrBuilder(
        int index) {
      return mapItem_.get(index);
    }

    public static final int DELCLANID_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.LongList delClanId_;
    /**
     * <code>repeated int64 delClanId = 3;</code>
     * @return A list containing the delClanId.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDelClanIdList() {
      return delClanId_;
    }
    /**
     * <code>repeated int64 delClanId = 3;</code>
     * @return The count of delClanId.
     */
    public int getDelClanIdCount() {
      return delClanId_.size();
    }
    /**
     * <code>repeated int64 delClanId = 3;</code>
     * @param index The index of the element to return.
     * @return The delClanId at the given index.
     */
    public long getDelClanId(int index) {
      return delClanId_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, version_);
      }
      for (int i = 0; i < mapItem_.size(); i++) {
        output.writeMessage(2, mapItem_.get(i));
      }
      for (int i = 0; i < delClanId_.size(); i++) {
        output.writeInt64(3, delClanId_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, version_);
      }
      for (int i = 0; i < mapItem_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, mapItem_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < delClanId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(delClanId_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDelClanIdList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMap.FetchTerritoryMapAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMap.FetchTerritoryMapAns other = (com.yorha.proto.SsSceneMap.FetchTerritoryMapAns) obj;

      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (!getMapItemList()
          .equals(other.getMapItemList())) return false;
      if (!getDelClanIdList()
          .equals(other.getDelClanIdList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getVersion();
      }
      if (getMapItemCount() > 0) {
        hash = (37 * hash) + MAPITEM_FIELD_NUMBER;
        hash = (53 * hash) + getMapItemList().hashCode();
      }
      if (getDelClanIdCount() > 0) {
        hash = (37 * hash) + DELCLANID_FIELD_NUMBER;
        hash = (53 * hash) + getDelClanIdList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMap.FetchTerritoryMapAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchTerritoryMapAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchTerritoryMapAns)
        com.yorha.proto.SsSceneMap.FetchTerritoryMapAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMap.FetchTerritoryMapAns.class, com.yorha.proto.SsSceneMap.FetchTerritoryMapAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMap.FetchTerritoryMapAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMapItemFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        version_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (mapItemBuilder_ == null) {
          mapItem_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          mapItemBuilder_.clear();
        }
        delClanId_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMap.internal_static_com_yorha_proto_FetchTerritoryMapAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchTerritoryMapAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMap.FetchTerritoryMapAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchTerritoryMapAns build() {
        com.yorha.proto.SsSceneMap.FetchTerritoryMapAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMap.FetchTerritoryMapAns buildPartial() {
        com.yorha.proto.SsSceneMap.FetchTerritoryMapAns result = new com.yorha.proto.SsSceneMap.FetchTerritoryMapAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000001;
        }
        if (mapItemBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            mapItem_ = java.util.Collections.unmodifiableList(mapItem_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.mapItem_ = mapItem_;
        } else {
          result.mapItem_ = mapItemBuilder_.build();
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          delClanId_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.delClanId_ = delClanId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMap.FetchTerritoryMapAns) {
          return mergeFrom((com.yorha.proto.SsSceneMap.FetchTerritoryMapAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMap.FetchTerritoryMapAns other) {
        if (other == com.yorha.proto.SsSceneMap.FetchTerritoryMapAns.getDefaultInstance()) return this;
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        if (mapItemBuilder_ == null) {
          if (!other.mapItem_.isEmpty()) {
            if (mapItem_.isEmpty()) {
              mapItem_ = other.mapItem_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureMapItemIsMutable();
              mapItem_.addAll(other.mapItem_);
            }
            onChanged();
          }
        } else {
          if (!other.mapItem_.isEmpty()) {
            if (mapItemBuilder_.isEmpty()) {
              mapItemBuilder_.dispose();
              mapItemBuilder_ = null;
              mapItem_ = other.mapItem_;
              bitField0_ = (bitField0_ & ~0x00000002);
              mapItemBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getMapItemFieldBuilder() : null;
            } else {
              mapItemBuilder_.addAllMessages(other.mapItem_);
            }
          }
        }
        if (!other.delClanId_.isEmpty()) {
          if (delClanId_.isEmpty()) {
            delClanId_ = other.delClanId_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureDelClanIdIsMutable();
            delClanId_.addAll(other.delClanId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMap.FetchTerritoryMapAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMap.FetchTerritoryMapAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int version_ ;
      /**
       * <code>optional int32 version = 1;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @return The version.
       */
      @java.lang.Override
      public int getVersion() {
        return version_;
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(int value) {
        bitField0_ |= 0x00000001;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000001);
        version_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.StructPB.TerritoryMapItemPB> mapItem_ =
        java.util.Collections.emptyList();
      private void ensureMapItemIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          mapItem_ = new java.util.ArrayList<com.yorha.proto.StructPB.TerritoryMapItemPB>(mapItem_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructPB.TerritoryMapItemPB, com.yorha.proto.StructPB.TerritoryMapItemPB.Builder, com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder> mapItemBuilder_;

      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public java.util.List<com.yorha.proto.StructPB.TerritoryMapItemPB> getMapItemList() {
        if (mapItemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(mapItem_);
        } else {
          return mapItemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public int getMapItemCount() {
        if (mapItemBuilder_ == null) {
          return mapItem_.size();
        } else {
          return mapItemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public com.yorha.proto.StructPB.TerritoryMapItemPB getMapItem(int index) {
        if (mapItemBuilder_ == null) {
          return mapItem_.get(index);
        } else {
          return mapItemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder setMapItem(
          int index, com.yorha.proto.StructPB.TerritoryMapItemPB value) {
        if (mapItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMapItemIsMutable();
          mapItem_.set(index, value);
          onChanged();
        } else {
          mapItemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder setMapItem(
          int index, com.yorha.proto.StructPB.TerritoryMapItemPB.Builder builderForValue) {
        if (mapItemBuilder_ == null) {
          ensureMapItemIsMutable();
          mapItem_.set(index, builderForValue.build());
          onChanged();
        } else {
          mapItemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder addMapItem(com.yorha.proto.StructPB.TerritoryMapItemPB value) {
        if (mapItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMapItemIsMutable();
          mapItem_.add(value);
          onChanged();
        } else {
          mapItemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder addMapItem(
          int index, com.yorha.proto.StructPB.TerritoryMapItemPB value) {
        if (mapItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMapItemIsMutable();
          mapItem_.add(index, value);
          onChanged();
        } else {
          mapItemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder addMapItem(
          com.yorha.proto.StructPB.TerritoryMapItemPB.Builder builderForValue) {
        if (mapItemBuilder_ == null) {
          ensureMapItemIsMutable();
          mapItem_.add(builderForValue.build());
          onChanged();
        } else {
          mapItemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder addMapItem(
          int index, com.yorha.proto.StructPB.TerritoryMapItemPB.Builder builderForValue) {
        if (mapItemBuilder_ == null) {
          ensureMapItemIsMutable();
          mapItem_.add(index, builderForValue.build());
          onChanged();
        } else {
          mapItemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder addAllMapItem(
          java.lang.Iterable<? extends com.yorha.proto.StructPB.TerritoryMapItemPB> values) {
        if (mapItemBuilder_ == null) {
          ensureMapItemIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, mapItem_);
          onChanged();
        } else {
          mapItemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder clearMapItem() {
        if (mapItemBuilder_ == null) {
          mapItem_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          mapItemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public Builder removeMapItem(int index) {
        if (mapItemBuilder_ == null) {
          ensureMapItemIsMutable();
          mapItem_.remove(index);
          onChanged();
        } else {
          mapItemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public com.yorha.proto.StructPB.TerritoryMapItemPB.Builder getMapItemBuilder(
          int index) {
        return getMapItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder getMapItemOrBuilder(
          int index) {
        if (mapItemBuilder_ == null) {
          return mapItem_.get(index);  } else {
          return mapItemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder> 
           getMapItemOrBuilderList() {
        if (mapItemBuilder_ != null) {
          return mapItemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(mapItem_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public com.yorha.proto.StructPB.TerritoryMapItemPB.Builder addMapItemBuilder() {
        return getMapItemFieldBuilder().addBuilder(
            com.yorha.proto.StructPB.TerritoryMapItemPB.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public com.yorha.proto.StructPB.TerritoryMapItemPB.Builder addMapItemBuilder(
          int index) {
        return getMapItemFieldBuilder().addBuilder(
            index, com.yorha.proto.StructPB.TerritoryMapItemPB.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.TerritoryMapItemPB mapItem = 2;</code>
       */
      public java.util.List<com.yorha.proto.StructPB.TerritoryMapItemPB.Builder> 
           getMapItemBuilderList() {
        return getMapItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructPB.TerritoryMapItemPB, com.yorha.proto.StructPB.TerritoryMapItemPB.Builder, com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder> 
          getMapItemFieldBuilder() {
        if (mapItemBuilder_ == null) {
          mapItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructPB.TerritoryMapItemPB, com.yorha.proto.StructPB.TerritoryMapItemPB.Builder, com.yorha.proto.StructPB.TerritoryMapItemPBOrBuilder>(
                  mapItem_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          mapItem_ = null;
        }
        return mapItemBuilder_;
      }

      private com.google.protobuf.Internal.LongList delClanId_ = emptyLongList();
      private void ensureDelClanIdIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          delClanId_ = mutableCopy(delClanId_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int64 delClanId = 3;</code>
       * @return A list containing the delClanId.
       */
      public java.util.List<java.lang.Long>
          getDelClanIdList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(delClanId_) : delClanId_;
      }
      /**
       * <code>repeated int64 delClanId = 3;</code>
       * @return The count of delClanId.
       */
      public int getDelClanIdCount() {
        return delClanId_.size();
      }
      /**
       * <code>repeated int64 delClanId = 3;</code>
       * @param index The index of the element to return.
       * @return The delClanId at the given index.
       */
      public long getDelClanId(int index) {
        return delClanId_.getLong(index);
      }
      /**
       * <code>repeated int64 delClanId = 3;</code>
       * @param index The index to set the value at.
       * @param value The delClanId to set.
       * @return This builder for chaining.
       */
      public Builder setDelClanId(
          int index, long value) {
        ensureDelClanIdIsMutable();
        delClanId_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delClanId = 3;</code>
       * @param value The delClanId to add.
       * @return This builder for chaining.
       */
      public Builder addDelClanId(long value) {
        ensureDelClanIdIsMutable();
        delClanId_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delClanId = 3;</code>
       * @param values The delClanId to add.
       * @return This builder for chaining.
       */
      public Builder addAllDelClanId(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDelClanIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, delClanId_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 delClanId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDelClanId() {
        delClanId_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchTerritoryMapAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchTerritoryMapAns)
    private static final com.yorha.proto.SsSceneMap.FetchTerritoryMapAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMap.FetchTerritoryMapAns();
    }

    public static com.yorha.proto.SsSceneMap.FetchTerritoryMapAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchTerritoryMapAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchTerritoryMapAns>() {
      @java.lang.Override
      public FetchTerritoryMapAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchTerritoryMapAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchTerritoryMapAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchTerritoryMapAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMap.FetchTerritoryMapAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ExecuteSceneGmAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ExecuteSceneGmAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ExecuteSceneGmAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ExecuteSceneGmAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchPathAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchPathAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchPathAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchPathAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanCityPointListAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanCityPointListAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanCityPointListAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanCityPointListAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchTerritoryMapAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchTerritoryMapAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchTerritoryMapAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchTerritoryMapAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n%ss_proto/gen/scene/ss_scene_map.proto\022" +
      "\017com.yorha.proto\032\"cs_proto/gen/common/st" +
      "ructPB.proto\032 ss_proto/gen/common/struct" +
      ".proto\"6\n\021ExecuteSceneGmAsk\022\020\n\010playerId\030" +
      "\001 \001(\003\022\017\n\007command\030\002 \001(\t\"#\n\021ExecuteSceneGm" +
      "Ans\022\016\n\006result\030\001 \001(\t\"\216\001\n\rSearchPathAsk\022#\n" +
      "\003src\030\001 \001(\0132\026.com.yorha.proto.Point\022#\n\003en" +
      "d\030\002 \001(\0132\026.com.yorha.proto.Point\022\020\n\010playe" +
      "rId\030\003 \001(\003\022\017\n\007rallyId\030\004 \001(\003\022\020\n\010targetId\030\005" +
      " \001(\003\"5\n\rSearchPathAns\022$\n\004path\030\001 \003(\0132\026.co" +
      "m.yorha.proto.Point\">\n\031FetchClanCityPoin" +
      "tListAsk\022\020\n\010playerId\030\001 \001(\003\022\017\n\007version\030\002 " +
      "\001(\005\"\201\001\n\031FetchClanCityPointListAns\022\017\n\007ver" +
      "sion\030\001 \001(\005\022)\n\tpointList\030\002 \003(\0132\026.com.yorh" +
      "a.proto.Point\022(\n\010ownerPos\030\003 \001(\0132\026.com.yo" +
      "rha.proto.Point\"d\n!FetchSingleClanMember" +
      "CityPointAsk\022\020\n\010playerId\030\001 \001(\003\022\025\n\rfetchP" +
      "layerId\030\002 \001(\003\022\026\n\016onlyFetchOwner\030\003 \001(\010\"N\n" +
      "!FetchSingleClanMemberCityPointAns\022)\n\tme" +
      "mberPos\030\001 \001(\0132\026.com.yorha.proto.Point\"\'\n" +
      "\024FetchTerritoryMapAsk\022\017\n\007version\030\001 \001(\005\"p" +
      "\n\024FetchTerritoryMapAns\022\017\n\007version\030\001 \001(\005\022" +
      "4\n\007mapItem\030\002 \003(\0132#.com.yorha.proto.Terri" +
      "toryMapItemPB\022\021\n\tdelClanId\030\003 \003(\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_ExecuteSceneGmAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ExecuteSceneGmAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ExecuteSceneGmAsk_descriptor,
        new java.lang.String[] { "PlayerId", "Command", });
    internal_static_com_yorha_proto_ExecuteSceneGmAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_ExecuteSceneGmAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ExecuteSceneGmAns_descriptor,
        new java.lang.String[] { "Result", });
    internal_static_com_yorha_proto_SearchPathAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SearchPathAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchPathAsk_descriptor,
        new java.lang.String[] { "Src", "End", "PlayerId", "RallyId", "TargetId", });
    internal_static_com_yorha_proto_SearchPathAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_SearchPathAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchPathAns_descriptor,
        new java.lang.String[] { "Path", });
    internal_static_com_yorha_proto_FetchClanCityPointListAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_FetchClanCityPointListAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanCityPointListAsk_descriptor,
        new java.lang.String[] { "PlayerId", "Version", });
    internal_static_com_yorha_proto_FetchClanCityPointListAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_FetchClanCityPointListAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanCityPointListAns_descriptor,
        new java.lang.String[] { "Version", "PointList", "OwnerPos", });
    internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAsk_descriptor,
        new java.lang.String[] { "PlayerId", "FetchPlayerId", "OnlyFetchOwner", });
    internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchSingleClanMemberCityPointAns_descriptor,
        new java.lang.String[] { "MemberPos", });
    internal_static_com_yorha_proto_FetchTerritoryMapAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_FetchTerritoryMapAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchTerritoryMapAsk_descriptor,
        new java.lang.String[] { "Version", });
    internal_static_com_yorha_proto_FetchTerritoryMapAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_FetchTerritoryMapAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchTerritoryMapAns_descriptor,
        new java.lang.String[] { "Version", "MapItem", "DelClanId", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/dungeonBuilding/dungeonBuildingPB.proto

package com.yorha.proto;

public final class DungeonBuildingPB {
  private DungeonBuildingPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DungeonBuildingEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DungeonBuildingEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     * @return The point.
     */
    com.yorha.proto.StructPB.PointPB getPoint();
    /**
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder();

    /**
     * <code>optional .com.yorha.proto.Camp camp = 3;</code>
     * @return Whether the camp field is set.
     */
    boolean hasCamp();
    /**
     * <code>optional .com.yorha.proto.Camp camp = 3;</code>
     * @return The camp.
     */
    com.yorha.proto.CommonEnum.Camp getCamp();

    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
     * @return Whether the troop field is set.
     */
    boolean hasTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
     * @return The troop.
     */
    com.yorha.proto.StructPlayerPB.TroopPB getTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
     */
    com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder();

    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
     * @return Whether the battle field is set.
     */
    boolean hasBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
     * @return The battle.
     */
    com.yorha.proto.StructBattlePB.BattlePB getBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
     */
    com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder();

    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
     * @return Whether the buffSys field is set.
     */
    boolean hasBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
     * @return The buffSys.
     */
    com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
     */
    com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DungeonBuildingEntityPB}
   */
  public static final class DungeonBuildingEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DungeonBuildingEntityPB)
      DungeonBuildingEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DungeonBuildingEntityPB.newBuilder() to construct.
    private DungeonBuildingEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DungeonBuildingEntityPB() {
      camp_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DungeonBuildingEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DungeonBuildingEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              templateId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Camp value = com.yorha.proto.CommonEnum.Camp.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(3, rawValue);
              } else {
                bitField0_ |= 0x00000004;
                camp_ = rawValue;
              }
              break;
            }
            case 34: {
              com.yorha.proto.StructPlayerPB.TroopPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = troop_.toBuilder();
              }
              troop_ = input.readMessage(com.yorha.proto.StructPlayerPB.TroopPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(troop_);
                troop_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 42: {
              com.yorha.proto.StructBattlePB.BattlePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = battle_.toBuilder();
              }
              battle_ = input.readMessage(com.yorha.proto.StructBattlePB.BattlePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battle_);
                battle_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 58: {
              com.yorha.proto.StructBattlePB.BuffSysPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000020) != 0)) {
                subBuilder = buffSys_.toBuilder();
              }
              buffSys_ = input.readMessage(com.yorha.proto.StructBattlePB.BuffSysPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buffSys_);
                buffSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000020;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.DungeonBuildingPB.internal_static_com_yorha_proto_DungeonBuildingEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.DungeonBuildingPB.internal_static_com_yorha_proto_DungeonBuildingEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.class, com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int TEMPLATEID_FIELD_NUMBER = 1;
    private int templateId_;
    /**
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int POINT_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPB.PointPB point_;
    /**
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getPoint() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }
    /**
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }

    public static final int CAMP_FIELD_NUMBER = 3;
    private int camp_;
    /**
     * <code>optional .com.yorha.proto.Camp camp = 3;</code>
     * @return Whether the camp field is set.
     */
    @java.lang.Override public boolean hasCamp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Camp camp = 3;</code>
     * @return The camp.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Camp getCamp() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
      return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
    }

    public static final int TROOP_FIELD_NUMBER = 4;
    private com.yorha.proto.StructPlayerPB.TroopPB troop_;
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
     * @return Whether the troop field is set.
     */
    @java.lang.Override
    public boolean hasTroop() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
     * @return The troop.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }

    public static final int BATTLE_FIELD_NUMBER = 5;
    private com.yorha.proto.StructBattlePB.BattlePB battle_;
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
     * @return Whether the battle field is set.
     */
    @java.lang.Override
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
     * @return The battle.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }

    public static final int BUFFSYS_FIELD_NUMBER = 7;
    private com.yorha.proto.StructBattlePB.BuffSysPB buffSys_;
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
     * @return Whether the buffSys field is set.
     */
    @java.lang.Override
    public boolean hasBuffSys() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
     * @return The buffSys.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys() {
      return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder() {
      return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeEnum(3, camp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getTroop());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getBattle());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(7, getBuffSys());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, camp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getTroop());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getBattle());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getBuffSys());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB other = (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) obj;

      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasCamp() != other.hasCamp()) return false;
      if (hasCamp()) {
        if (camp_ != other.camp_) return false;
      }
      if (hasTroop() != other.hasTroop()) return false;
      if (hasTroop()) {
        if (!getTroop()
            .equals(other.getTroop())) return false;
      }
      if (hasBattle() != other.hasBattle()) return false;
      if (hasBattle()) {
        if (!getBattle()
            .equals(other.getBattle())) return false;
      }
      if (hasBuffSys() != other.hasBuffSys()) return false;
      if (hasBuffSys()) {
        if (!getBuffSys()
            .equals(other.getBuffSys())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasCamp()) {
        hash = (37 * hash) + CAMP_FIELD_NUMBER;
        hash = (53 * hash) + camp_;
      }
      if (hasTroop()) {
        hash = (37 * hash) + TROOP_FIELD_NUMBER;
        hash = (53 * hash) + getTroop().hashCode();
      }
      if (hasBattle()) {
        hash = (37 * hash) + BATTLE_FIELD_NUMBER;
        hash = (53 * hash) + getBattle().hashCode();
      }
      if (hasBuffSys()) {
        hash = (37 * hash) + BUFFSYS_FIELD_NUMBER;
        hash = (53 * hash) + getBuffSys().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DungeonBuildingEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DungeonBuildingEntityPB)
        com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.DungeonBuildingPB.internal_static_com_yorha_proto_DungeonBuildingEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.DungeonBuildingPB.internal_static_com_yorha_proto_DungeonBuildingEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.class, com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getTroopFieldBuilder();
          getBattleFieldBuilder();
          getBuffSysFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        camp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (troopBuilder_ == null) {
          troop_ = null;
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (battleBuilder_ == null) {
          battle_ = null;
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.DungeonBuildingPB.internal_static_com_yorha_proto_DungeonBuildingEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB build() {
        com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB buildPartial() {
        com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB result = new com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.camp_ = camp_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (troopBuilder_ == null) {
            result.troop_ = troop_;
          } else {
            result.troop_ = troopBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (battleBuilder_ == null) {
            result.battle_ = battle_;
          } else {
            result.battle_ = battleBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          if (buffSysBuilder_ == null) {
            result.buffSys_ = buffSys_;
          } else {
            result.buffSys_ = buffSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000020;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) {
          return mergeFrom((com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB other) {
        if (other == com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB.getDefaultInstance()) return this;
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasCamp()) {
          setCamp(other.getCamp());
        }
        if (other.hasTroop()) {
          mergeTroop(other.getTroop());
        }
        if (other.hasBattle()) {
          mergeBattle(other.getBattle());
        }
        if (other.hasBuffSys()) {
          mergeBuffSys(other.getBuffSys());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int templateId_ ;
      /**
       * <code>optional int32 templateId = 1;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 templateId = 1;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <code>optional int32 templateId = 1;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000001;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 templateId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pointBuilder_;
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       * @return The point.
       */
      public com.yorha.proto.StructPB.PointPB getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public Builder setPoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public Builder setPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public Builder mergePoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            point_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPointBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private int camp_ = 0;
      /**
       * <code>optional .com.yorha.proto.Camp camp = 3;</code>
       * @return Whether the camp field is set.
       */
      @java.lang.Override public boolean hasCamp() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Camp camp = 3;</code>
       * @return The camp.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Camp getCamp() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
        return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.Camp camp = 3;</code>
       * @param value The camp to set.
       * @return This builder for chaining.
       */
      public Builder setCamp(com.yorha.proto.CommonEnum.Camp value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        camp_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Camp camp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCamp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        camp_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPlayerPB.TroopPB troop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> troopBuilder_;
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       * @return Whether the troop field is set.
       */
      public boolean hasTroop() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       * @return The troop.
       */
      public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
        if (troopBuilder_ == null) {
          return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        } else {
          return troopBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       */
      public Builder setTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          troop_ = value;
          onChanged();
        } else {
          troopBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       */
      public Builder setTroop(
          com.yorha.proto.StructPlayerPB.TroopPB.Builder builderForValue) {
        if (troopBuilder_ == null) {
          troop_ = builderForValue.build();
          onChanged();
        } else {
          troopBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       */
      public Builder mergeTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              troop_ != null &&
              troop_ != com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance()) {
            troop_ =
              com.yorha.proto.StructPlayerPB.TroopPB.newBuilder(troop_).mergeFrom(value).buildPartial();
          } else {
            troop_ = value;
          }
          onChanged();
        } else {
          troopBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       */
      public Builder clearTroop() {
        if (troopBuilder_ == null) {
          troop_ = null;
          onChanged();
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPB.Builder getTroopBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getTroopFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
        if (troopBuilder_ != null) {
          return troopBuilder_.getMessageOrBuilder();
        } else {
          return troop_ == null ?
              com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> 
          getTroopFieldBuilder() {
        if (troopBuilder_ == null) {
          troopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder>(
                  getTroop(),
                  getParentForChildren(),
                  isClean());
          troop_ = null;
        }
        return troopBuilder_;
      }

      private com.yorha.proto.StructBattlePB.BattlePB battle_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> battleBuilder_;
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       * @return Whether the battle field is set.
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       * @return The battle.
       */
      public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
        if (battleBuilder_ == null) {
          return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        } else {
          return battleBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       */
      public Builder setBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battle_ = value;
          onChanged();
        } else {
          battleBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       */
      public Builder setBattle(
          com.yorha.proto.StructBattlePB.BattlePB.Builder builderForValue) {
        if (battleBuilder_ == null) {
          battle_ = builderForValue.build();
          onChanged();
        } else {
          battleBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       */
      public Builder mergeBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              battle_ != null &&
              battle_ != com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance()) {
            battle_ =
              com.yorha.proto.StructBattlePB.BattlePB.newBuilder(battle_).mergeFrom(value).buildPartial();
          } else {
            battle_ = value;
          }
          onChanged();
        } else {
          battleBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       */
      public Builder clearBattle() {
        if (battleBuilder_ == null) {
          battle_ = null;
          onChanged();
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePB.Builder getBattleBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getBattleFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
        if (battleBuilder_ != null) {
          return battleBuilder_.getMessageOrBuilder();
        } else {
          return battle_ == null ?
              com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> 
          getBattleFieldBuilder() {
        if (battleBuilder_ == null) {
          battleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder>(
                  getBattle(),
                  getParentForChildren(),
                  isClean());
          battle_ = null;
        }
        return battleBuilder_;
      }

      private com.yorha.proto.StructBattlePB.BuffSysPB buffSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder> buffSysBuilder_;
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       * @return Whether the buffSys field is set.
       */
      public boolean hasBuffSys() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       * @return The buffSys.
       */
      public com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys() {
        if (buffSysBuilder_ == null) {
          return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
        } else {
          return buffSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       */
      public Builder setBuffSys(com.yorha.proto.StructBattlePB.BuffSysPB value) {
        if (buffSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buffSys_ = value;
          onChanged();
        } else {
          buffSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       */
      public Builder setBuffSys(
          com.yorha.proto.StructBattlePB.BuffSysPB.Builder builderForValue) {
        if (buffSysBuilder_ == null) {
          buffSys_ = builderForValue.build();
          onChanged();
        } else {
          buffSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       */
      public Builder mergeBuffSys(com.yorha.proto.StructBattlePB.BuffSysPB value) {
        if (buffSysBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
              buffSys_ != null &&
              buffSys_ != com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance()) {
            buffSys_ =
              com.yorha.proto.StructBattlePB.BuffSysPB.newBuilder(buffSys_).mergeFrom(value).buildPartial();
          } else {
            buffSys_ = value;
          }
          onChanged();
        } else {
          buffSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       */
      public Builder clearBuffSys() {
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
          onChanged();
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       */
      public com.yorha.proto.StructBattlePB.BuffSysPB.Builder getBuffSysBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getBuffSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       */
      public com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder() {
        if (buffSysBuilder_ != null) {
          return buffSysBuilder_.getMessageOrBuilder();
        } else {
          return buffSys_ == null ?
              com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder> 
          getBuffSysFieldBuilder() {
        if (buffSysBuilder_ == null) {
          buffSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder>(
                  getBuffSys(),
                  getParentForChildren(),
                  isClean());
          buffSys_ = null;
        }
        return buffSysBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DungeonBuildingEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DungeonBuildingEntityPB)
    private static final com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB();
    }

    public static com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DungeonBuildingEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<DungeonBuildingEntityPB>() {
      @java.lang.Override
      public DungeonBuildingEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DungeonBuildingEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DungeonBuildingEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DungeonBuildingEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.DungeonBuildingPB.DungeonBuildingEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DungeonBuildingEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DungeonBuildingEntityPB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n4cs_proto/gen/dungeonBuilding/dungeonBu" +
      "ildingPB.proto\022\017com.yorha.proto\032\"cs_prot" +
      "o/gen/common/structPB.proto\032)cs_proto/ge" +
      "n/common/struct_battlePB.proto\032)cs_proto" +
      "/gen/common/struct_playerPB.proto\032%ss_pr" +
      "oto/gen/common/common_enum.proto\"\374\001\n\027Dun" +
      "geonBuildingEntityPB\022\022\n\ntemplateId\030\001 \001(\005" +
      "\022\'\n\005point\030\002 \001(\0132\030.com.yorha.proto.PointP" +
      "B\022#\n\004camp\030\003 \001(\0162\025.com.yorha.proto.Camp\022\'" +
      "\n\005troop\030\004 \001(\0132\030.com.yorha.proto.TroopPB\022" +
      ")\n\006battle\030\005 \001(\0132\031.com.yorha.proto.Battle" +
      "PB\022+\n\007buffSys\030\007 \001(\0132\032.com.yorha.proto.Bu" +
      "ffSysPBB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.StructBattlePB.getDescriptor(),
          com.yorha.proto.StructPlayerPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_DungeonBuildingEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_DungeonBuildingEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DungeonBuildingEntityPB_descriptor,
        new java.lang.String[] { "TemplateId", "Point", "Camp", "Troop", "Battle", "BuffSys", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.StructBattlePB.getDescriptor();
    com.yorha.proto.StructPlayerPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

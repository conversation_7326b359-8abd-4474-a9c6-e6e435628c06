// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/pushNotification/ss_push_notification.proto

package com.yorha.proto;

public final class SsPushNotification {
  private SsPushNotification() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PushSingleNotificationCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PushSingleNotificationCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return Whether the title field is set.
     */
    boolean hasTitle();
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return The title.
     */
    int getTitle();

    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return Whether the body field is set.
     */
    boolean hasBody();
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return The body.
     */
    int getBody();

    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 推送类型
     * </pre>
     *
     * <code>optional int32 modelId = 4;</code>
     * @return Whether the modelId field is set.
     */
    boolean hasModelId();
    /**
     * <pre>
     * 推送类型
     * </pre>
     *
     * <code>optional int32 modelId = 4;</code>
     * @return The modelId.
     */
    int getModelId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PushSingleNotificationCmd}
   */
  public static final class PushSingleNotificationCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PushSingleNotificationCmd)
      PushSingleNotificationCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PushSingleNotificationCmd.newBuilder() to construct.
    private PushSingleNotificationCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PushSingleNotificationCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PushSingleNotificationCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PushSingleNotificationCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              title_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              body_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              modelId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushSingleNotificationCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushSingleNotificationCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPushNotification.PushSingleNotificationCmd.class, com.yorha.proto.SsPushNotification.PushSingleNotificationCmd.Builder.class);
    }

    private int bitField0_;
    public static final int TITLE_FIELD_NUMBER = 1;
    private int title_;
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return Whether the title field is set.
     */
    @java.lang.Override
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return The title.
     */
    @java.lang.Override
    public int getTitle() {
      return title_;
    }

    public static final int BODY_FIELD_NUMBER = 2;
    private int body_;
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return Whether the body field is set.
     */
    @java.lang.Override
    public boolean hasBody() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return The body.
     */
    @java.lang.Override
    public int getBody() {
      return body_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 目标玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int MODELID_FIELD_NUMBER = 4;
    private int modelId_;
    /**
     * <pre>
     * 推送类型
     * </pre>
     *
     * <code>optional int32 modelId = 4;</code>
     * @return Whether the modelId field is set.
     */
    @java.lang.Override
    public boolean hasModelId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 推送类型
     * </pre>
     *
     * <code>optional int32 modelId = 4;</code>
     * @return The modelId.
     */
    @java.lang.Override
    public int getModelId() {
      return modelId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, title_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, body_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, modelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, title_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, body_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, modelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPushNotification.PushSingleNotificationCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPushNotification.PushSingleNotificationCmd other = (com.yorha.proto.SsPushNotification.PushSingleNotificationCmd) obj;

      if (hasTitle() != other.hasTitle()) return false;
      if (hasTitle()) {
        if (getTitle()
            != other.getTitle()) return false;
      }
      if (hasBody() != other.hasBody()) return false;
      if (hasBody()) {
        if (getBody()
            != other.getBody()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasModelId() != other.hasModelId()) return false;
      if (hasModelId()) {
        if (getModelId()
            != other.getModelId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTitle()) {
        hash = (37 * hash) + TITLE_FIELD_NUMBER;
        hash = (53 * hash) + getTitle();
      }
      if (hasBody()) {
        hash = (37 * hash) + BODY_FIELD_NUMBER;
        hash = (53 * hash) + getBody();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasModelId()) {
        hash = (37 * hash) + MODELID_FIELD_NUMBER;
        hash = (53 * hash) + getModelId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPushNotification.PushSingleNotificationCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PushSingleNotificationCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PushSingleNotificationCmd)
        com.yorha.proto.SsPushNotification.PushSingleNotificationCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushSingleNotificationCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushSingleNotificationCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPushNotification.PushSingleNotificationCmd.class, com.yorha.proto.SsPushNotification.PushSingleNotificationCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPushNotification.PushSingleNotificationCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        title_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        body_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        modelId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushSingleNotificationCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushSingleNotificationCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPushNotification.PushSingleNotificationCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushSingleNotificationCmd build() {
        com.yorha.proto.SsPushNotification.PushSingleNotificationCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushSingleNotificationCmd buildPartial() {
        com.yorha.proto.SsPushNotification.PushSingleNotificationCmd result = new com.yorha.proto.SsPushNotification.PushSingleNotificationCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.title_ = title_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.body_ = body_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.modelId_ = modelId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPushNotification.PushSingleNotificationCmd) {
          return mergeFrom((com.yorha.proto.SsPushNotification.PushSingleNotificationCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPushNotification.PushSingleNotificationCmd other) {
        if (other == com.yorha.proto.SsPushNotification.PushSingleNotificationCmd.getDefaultInstance()) return this;
        if (other.hasTitle()) {
          setTitle(other.getTitle());
        }
        if (other.hasBody()) {
          setBody(other.getBody());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasModelId()) {
          setModelId(other.getModelId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPushNotification.PushSingleNotificationCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPushNotification.PushSingleNotificationCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int title_ ;
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return Whether the title field is set.
       */
      @java.lang.Override
      public boolean hasTitle() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return The title.
       */
      @java.lang.Override
      public int getTitle() {
        return title_;
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(int value) {
        bitField0_ |= 0x00000001;
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        bitField0_ = (bitField0_ & ~0x00000001);
        title_ = 0;
        onChanged();
        return this;
      }

      private int body_ ;
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return Whether the body field is set.
       */
      @java.lang.Override
      public boolean hasBody() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return The body.
       */
      @java.lang.Override
      public int getBody() {
        return body_;
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @param value The body to set.
       * @return This builder for chaining.
       */
      public Builder setBody(int value) {
        bitField0_ |= 0x00000002;
        body_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBody() {
        bitField0_ = (bitField0_ & ~0x00000002);
        body_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int modelId_ ;
      /**
       * <pre>
       * 推送类型
       * </pre>
       *
       * <code>optional int32 modelId = 4;</code>
       * @return Whether the modelId field is set.
       */
      @java.lang.Override
      public boolean hasModelId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 推送类型
       * </pre>
       *
       * <code>optional int32 modelId = 4;</code>
       * @return The modelId.
       */
      @java.lang.Override
      public int getModelId() {
        return modelId_;
      }
      /**
       * <pre>
       * 推送类型
       * </pre>
       *
       * <code>optional int32 modelId = 4;</code>
       * @param value The modelId to set.
       * @return This builder for chaining.
       */
      public Builder setModelId(int value) {
        bitField0_ |= 0x00000008;
        modelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送类型
       * </pre>
       *
       * <code>optional int32 modelId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearModelId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        modelId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PushSingleNotificationCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PushSingleNotificationCmd)
    private static final com.yorha.proto.SsPushNotification.PushSingleNotificationCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPushNotification.PushSingleNotificationCmd();
    }

    public static com.yorha.proto.SsPushNotification.PushSingleNotificationCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PushSingleNotificationCmd>
        PARSER = new com.google.protobuf.AbstractParser<PushSingleNotificationCmd>() {
      @java.lang.Override
      public PushSingleNotificationCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PushSingleNotificationCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PushSingleNotificationCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PushSingleNotificationCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPushNotification.PushSingleNotificationCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PushMultipleNotificationCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PushMultipleNotificationCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return Whether the title field is set.
     */
    boolean hasTitle();
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return The title.
     */
    int getTitle();

    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return Whether the body field is set.
     */
    boolean hasBody();
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return The body.
     */
    int getBody();

    /**
     * <pre>
     * 目标玩家id list
     * </pre>
     *
     * <code>repeated int64 playerIdList = 3;</code>
     * @return A list containing the playerIdList.
     */
    java.util.List<java.lang.Long> getPlayerIdListList();
    /**
     * <pre>
     * 目标玩家id list
     * </pre>
     *
     * <code>repeated int64 playerIdList = 3;</code>
     * @return The count of playerIdList.
     */
    int getPlayerIdListCount();
    /**
     * <pre>
     * 目标玩家id list
     * </pre>
     *
     * <code>repeated int64 playerIdList = 3;</code>
     * @param index The index of the element to return.
     * @return The playerIdList at the given index.
     */
    long getPlayerIdList(int index);

    /**
     * <pre>
     * 推送类型
     * </pre>
     *
     * <code>optional int32 modelId = 4;</code>
     * @return Whether the modelId field is set.
     */
    boolean hasModelId();
    /**
     * <pre>
     * 推送类型
     * </pre>
     *
     * <code>optional int32 modelId = 4;</code>
     * @return The modelId.
     */
    int getModelId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PushMultipleNotificationCmd}
   */
  public static final class PushMultipleNotificationCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PushMultipleNotificationCmd)
      PushMultipleNotificationCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PushMultipleNotificationCmd.newBuilder() to construct.
    private PushMultipleNotificationCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PushMultipleNotificationCmd() {
      playerIdList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PushMultipleNotificationCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PushMultipleNotificationCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              title_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              body_ = input.readInt32();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                playerIdList_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              playerIdList_.addLong(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                playerIdList_ = newLongList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                playerIdList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              modelId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          playerIdList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushMultipleNotificationCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushMultipleNotificationCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd.class, com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd.Builder.class);
    }

    private int bitField0_;
    public static final int TITLE_FIELD_NUMBER = 1;
    private int title_;
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return Whether the title field is set.
     */
    @java.lang.Override
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return The title.
     */
    @java.lang.Override
    public int getTitle() {
      return title_;
    }

    public static final int BODY_FIELD_NUMBER = 2;
    private int body_;
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return Whether the body field is set.
     */
    @java.lang.Override
    public boolean hasBody() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return The body.
     */
    @java.lang.Override
    public int getBody() {
      return body_;
    }

    public static final int PLAYERIDLIST_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.LongList playerIdList_;
    /**
     * <pre>
     * 目标玩家id list
     * </pre>
     *
     * <code>repeated int64 playerIdList = 3;</code>
     * @return A list containing the playerIdList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getPlayerIdListList() {
      return playerIdList_;
    }
    /**
     * <pre>
     * 目标玩家id list
     * </pre>
     *
     * <code>repeated int64 playerIdList = 3;</code>
     * @return The count of playerIdList.
     */
    public int getPlayerIdListCount() {
      return playerIdList_.size();
    }
    /**
     * <pre>
     * 目标玩家id list
     * </pre>
     *
     * <code>repeated int64 playerIdList = 3;</code>
     * @param index The index of the element to return.
     * @return The playerIdList at the given index.
     */
    public long getPlayerIdList(int index) {
      return playerIdList_.getLong(index);
    }

    public static final int MODELID_FIELD_NUMBER = 4;
    private int modelId_;
    /**
     * <pre>
     * 推送类型
     * </pre>
     *
     * <code>optional int32 modelId = 4;</code>
     * @return Whether the modelId field is set.
     */
    @java.lang.Override
    public boolean hasModelId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 推送类型
     * </pre>
     *
     * <code>optional int32 modelId = 4;</code>
     * @return The modelId.
     */
    @java.lang.Override
    public int getModelId() {
      return modelId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, title_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, body_);
      }
      for (int i = 0; i < playerIdList_.size(); i++) {
        output.writeInt64(3, playerIdList_.getLong(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(4, modelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, title_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, body_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < playerIdList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(playerIdList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getPlayerIdListList().size();
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, modelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd other = (com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd) obj;

      if (hasTitle() != other.hasTitle()) return false;
      if (hasTitle()) {
        if (getTitle()
            != other.getTitle()) return false;
      }
      if (hasBody() != other.hasBody()) return false;
      if (hasBody()) {
        if (getBody()
            != other.getBody()) return false;
      }
      if (!getPlayerIdListList()
          .equals(other.getPlayerIdListList())) return false;
      if (hasModelId() != other.hasModelId()) return false;
      if (hasModelId()) {
        if (getModelId()
            != other.getModelId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTitle()) {
        hash = (37 * hash) + TITLE_FIELD_NUMBER;
        hash = (53 * hash) + getTitle();
      }
      if (hasBody()) {
        hash = (37 * hash) + BODY_FIELD_NUMBER;
        hash = (53 * hash) + getBody();
      }
      if (getPlayerIdListCount() > 0) {
        hash = (37 * hash) + PLAYERIDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerIdListList().hashCode();
      }
      if (hasModelId()) {
        hash = (37 * hash) + MODELID_FIELD_NUMBER;
        hash = (53 * hash) + getModelId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PushMultipleNotificationCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PushMultipleNotificationCmd)
        com.yorha.proto.SsPushNotification.PushMultipleNotificationCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushMultipleNotificationCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushMultipleNotificationCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd.class, com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        title_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        body_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerIdList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        modelId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushMultipleNotificationCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd build() {
        com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd buildPartial() {
        com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd result = new com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.title_ = title_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.body_ = body_;
          to_bitField0_ |= 0x00000002;
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          playerIdList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.playerIdList_ = playerIdList_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.modelId_ = modelId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd) {
          return mergeFrom((com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd other) {
        if (other == com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd.getDefaultInstance()) return this;
        if (other.hasTitle()) {
          setTitle(other.getTitle());
        }
        if (other.hasBody()) {
          setBody(other.getBody());
        }
        if (!other.playerIdList_.isEmpty()) {
          if (playerIdList_.isEmpty()) {
            playerIdList_ = other.playerIdList_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensurePlayerIdListIsMutable();
            playerIdList_.addAll(other.playerIdList_);
          }
          onChanged();
        }
        if (other.hasModelId()) {
          setModelId(other.getModelId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int title_ ;
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return Whether the title field is set.
       */
      @java.lang.Override
      public boolean hasTitle() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return The title.
       */
      @java.lang.Override
      public int getTitle() {
        return title_;
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(int value) {
        bitField0_ |= 0x00000001;
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        bitField0_ = (bitField0_ & ~0x00000001);
        title_ = 0;
        onChanged();
        return this;
      }

      private int body_ ;
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return Whether the body field is set.
       */
      @java.lang.Override
      public boolean hasBody() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return The body.
       */
      @java.lang.Override
      public int getBody() {
        return body_;
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @param value The body to set.
       * @return This builder for chaining.
       */
      public Builder setBody(int value) {
        bitField0_ |= 0x00000002;
        body_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBody() {
        bitField0_ = (bitField0_ & ~0x00000002);
        body_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList playerIdList_ = emptyLongList();
      private void ensurePlayerIdListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          playerIdList_ = mutableCopy(playerIdList_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <pre>
       * 目标玩家id list
       * </pre>
       *
       * <code>repeated int64 playerIdList = 3;</code>
       * @return A list containing the playerIdList.
       */
      public java.util.List<java.lang.Long>
          getPlayerIdListList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(playerIdList_) : playerIdList_;
      }
      /**
       * <pre>
       * 目标玩家id list
       * </pre>
       *
       * <code>repeated int64 playerIdList = 3;</code>
       * @return The count of playerIdList.
       */
      public int getPlayerIdListCount() {
        return playerIdList_.size();
      }
      /**
       * <pre>
       * 目标玩家id list
       * </pre>
       *
       * <code>repeated int64 playerIdList = 3;</code>
       * @param index The index of the element to return.
       * @return The playerIdList at the given index.
       */
      public long getPlayerIdList(int index) {
        return playerIdList_.getLong(index);
      }
      /**
       * <pre>
       * 目标玩家id list
       * </pre>
       *
       * <code>repeated int64 playerIdList = 3;</code>
       * @param index The index to set the value at.
       * @param value The playerIdList to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerIdList(
          int index, long value) {
        ensurePlayerIdListIsMutable();
        playerIdList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id list
       * </pre>
       *
       * <code>repeated int64 playerIdList = 3;</code>
       * @param value The playerIdList to add.
       * @return This builder for chaining.
       */
      public Builder addPlayerIdList(long value) {
        ensurePlayerIdListIsMutable();
        playerIdList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id list
       * </pre>
       *
       * <code>repeated int64 playerIdList = 3;</code>
       * @param values The playerIdList to add.
       * @return This builder for chaining.
       */
      public Builder addAllPlayerIdList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensurePlayerIdListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerIdList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标玩家id list
       * </pre>
       *
       * <code>repeated int64 playerIdList = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerIdList() {
        playerIdList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private int modelId_ ;
      /**
       * <pre>
       * 推送类型
       * </pre>
       *
       * <code>optional int32 modelId = 4;</code>
       * @return Whether the modelId field is set.
       */
      @java.lang.Override
      public boolean hasModelId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 推送类型
       * </pre>
       *
       * <code>optional int32 modelId = 4;</code>
       * @return The modelId.
       */
      @java.lang.Override
      public int getModelId() {
        return modelId_;
      }
      /**
       * <pre>
       * 推送类型
       * </pre>
       *
       * <code>optional int32 modelId = 4;</code>
       * @param value The modelId to set.
       * @return This builder for chaining.
       */
      public Builder setModelId(int value) {
        bitField0_ |= 0x00000008;
        modelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送类型
       * </pre>
       *
       * <code>optional int32 modelId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearModelId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        modelId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PushMultipleNotificationCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PushMultipleNotificationCmd)
    private static final com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd();
    }

    public static com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PushMultipleNotificationCmd>
        PARSER = new com.google.protobuf.AbstractParser<PushMultipleNotificationCmd>() {
      @java.lang.Override
      public PushMultipleNotificationCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PushMultipleNotificationCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PushMultipleNotificationCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PushMultipleNotificationCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPushNotification.PushMultipleNotificationCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PushTopicNotificationCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PushTopicNotificationCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return Whether the title field is set.
     */
    boolean hasTitle();
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return The title.
     */
    int getTitle();

    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return Whether the body field is set.
     */
    boolean hasBody();
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return The body.
     */
    int getBody();

    /**
     * <pre>
     * 推送主题
     * </pre>
     *
     * <code>optional string topic = 3;</code>
     * @return Whether the topic field is set.
     */
    boolean hasTopic();
    /**
     * <pre>
     * 推送主题
     * </pre>
     *
     * <code>optional string topic = 3;</code>
     * @return The topic.
     */
    java.lang.String getTopic();
    /**
     * <pre>
     * 推送主题
     * </pre>
     *
     * <code>optional string topic = 3;</code>
     * @return The bytes for topic.
     */
    com.google.protobuf.ByteString
        getTopicBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PushTopicNotificationCmd}
   */
  public static final class PushTopicNotificationCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PushTopicNotificationCmd)
      PushTopicNotificationCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PushTopicNotificationCmd.newBuilder() to construct.
    private PushTopicNotificationCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PushTopicNotificationCmd() {
      topic_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PushTopicNotificationCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PushTopicNotificationCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              title_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              body_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              topic_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushTopicNotificationCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushTopicNotificationCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPushNotification.PushTopicNotificationCmd.class, com.yorha.proto.SsPushNotification.PushTopicNotificationCmd.Builder.class);
    }

    private int bitField0_;
    public static final int TITLE_FIELD_NUMBER = 1;
    private int title_;
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return Whether the title field is set.
     */
    @java.lang.Override
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 推送标题
     * </pre>
     *
     * <code>optional int32 title = 1;</code>
     * @return The title.
     */
    @java.lang.Override
    public int getTitle() {
      return title_;
    }

    public static final int BODY_FIELD_NUMBER = 2;
    private int body_;
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return Whether the body field is set.
     */
    @java.lang.Override
    public boolean hasBody() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 推送信息体
     * </pre>
     *
     * <code>optional int32 body = 2;</code>
     * @return The body.
     */
    @java.lang.Override
    public int getBody() {
      return body_;
    }

    public static final int TOPIC_FIELD_NUMBER = 3;
    private volatile java.lang.Object topic_;
    /**
     * <pre>
     * 推送主题
     * </pre>
     *
     * <code>optional string topic = 3;</code>
     * @return Whether the topic field is set.
     */
    @java.lang.Override
    public boolean hasTopic() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 推送主题
     * </pre>
     *
     * <code>optional string topic = 3;</code>
     * @return The topic.
     */
    @java.lang.Override
    public java.lang.String getTopic() {
      java.lang.Object ref = topic_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          topic_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 推送主题
     * </pre>
     *
     * <code>optional string topic = 3;</code>
     * @return The bytes for topic.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTopicBytes() {
      java.lang.Object ref = topic_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        topic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, title_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, body_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, topic_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, title_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, body_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, topic_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPushNotification.PushTopicNotificationCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPushNotification.PushTopicNotificationCmd other = (com.yorha.proto.SsPushNotification.PushTopicNotificationCmd) obj;

      if (hasTitle() != other.hasTitle()) return false;
      if (hasTitle()) {
        if (getTitle()
            != other.getTitle()) return false;
      }
      if (hasBody() != other.hasBody()) return false;
      if (hasBody()) {
        if (getBody()
            != other.getBody()) return false;
      }
      if (hasTopic() != other.hasTopic()) return false;
      if (hasTopic()) {
        if (!getTopic()
            .equals(other.getTopic())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTitle()) {
        hash = (37 * hash) + TITLE_FIELD_NUMBER;
        hash = (53 * hash) + getTitle();
      }
      if (hasBody()) {
        hash = (37 * hash) + BODY_FIELD_NUMBER;
        hash = (53 * hash) + getBody();
      }
      if (hasTopic()) {
        hash = (37 * hash) + TOPIC_FIELD_NUMBER;
        hash = (53 * hash) + getTopic().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPushNotification.PushTopicNotificationCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PushTopicNotificationCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PushTopicNotificationCmd)
        com.yorha.proto.SsPushNotification.PushTopicNotificationCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushTopicNotificationCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushTopicNotificationCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPushNotification.PushTopicNotificationCmd.class, com.yorha.proto.SsPushNotification.PushTopicNotificationCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPushNotification.PushTopicNotificationCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        title_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        body_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        topic_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPushNotification.internal_static_com_yorha_proto_PushTopicNotificationCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushTopicNotificationCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPushNotification.PushTopicNotificationCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushTopicNotificationCmd build() {
        com.yorha.proto.SsPushNotification.PushTopicNotificationCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPushNotification.PushTopicNotificationCmd buildPartial() {
        com.yorha.proto.SsPushNotification.PushTopicNotificationCmd result = new com.yorha.proto.SsPushNotification.PushTopicNotificationCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.title_ = title_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.body_ = body_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.topic_ = topic_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPushNotification.PushTopicNotificationCmd) {
          return mergeFrom((com.yorha.proto.SsPushNotification.PushTopicNotificationCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPushNotification.PushTopicNotificationCmd other) {
        if (other == com.yorha.proto.SsPushNotification.PushTopicNotificationCmd.getDefaultInstance()) return this;
        if (other.hasTitle()) {
          setTitle(other.getTitle());
        }
        if (other.hasBody()) {
          setBody(other.getBody());
        }
        if (other.hasTopic()) {
          bitField0_ |= 0x00000004;
          topic_ = other.topic_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPushNotification.PushTopicNotificationCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPushNotification.PushTopicNotificationCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int title_ ;
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return Whether the title field is set.
       */
      @java.lang.Override
      public boolean hasTitle() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return The title.
       */
      @java.lang.Override
      public int getTitle() {
        return title_;
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(int value) {
        bitField0_ |= 0x00000001;
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送标题
       * </pre>
       *
       * <code>optional int32 title = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        bitField0_ = (bitField0_ & ~0x00000001);
        title_ = 0;
        onChanged();
        return this;
      }

      private int body_ ;
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return Whether the body field is set.
       */
      @java.lang.Override
      public boolean hasBody() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return The body.
       */
      @java.lang.Override
      public int getBody() {
        return body_;
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @param value The body to set.
       * @return This builder for chaining.
       */
      public Builder setBody(int value) {
        bitField0_ |= 0x00000002;
        body_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送信息体
       * </pre>
       *
       * <code>optional int32 body = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBody() {
        bitField0_ = (bitField0_ & ~0x00000002);
        body_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object topic_ = "";
      /**
       * <pre>
       * 推送主题
       * </pre>
       *
       * <code>optional string topic = 3;</code>
       * @return Whether the topic field is set.
       */
      public boolean hasTopic() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 推送主题
       * </pre>
       *
       * <code>optional string topic = 3;</code>
       * @return The topic.
       */
      public java.lang.String getTopic() {
        java.lang.Object ref = topic_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            topic_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 推送主题
       * </pre>
       *
       * <code>optional string topic = 3;</code>
       * @return The bytes for topic.
       */
      public com.google.protobuf.ByteString
          getTopicBytes() {
        java.lang.Object ref = topic_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          topic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 推送主题
       * </pre>
       *
       * <code>optional string topic = 3;</code>
       * @param value The topic to set.
       * @return This builder for chaining.
       */
      public Builder setTopic(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        topic_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送主题
       * </pre>
       *
       * <code>optional string topic = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTopic() {
        bitField0_ = (bitField0_ & ~0x00000004);
        topic_ = getDefaultInstance().getTopic();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 推送主题
       * </pre>
       *
       * <code>optional string topic = 3;</code>
       * @param value The bytes for topic to set.
       * @return This builder for chaining.
       */
      public Builder setTopicBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        topic_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PushTopicNotificationCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PushTopicNotificationCmd)
    private static final com.yorha.proto.SsPushNotification.PushTopicNotificationCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPushNotification.PushTopicNotificationCmd();
    }

    public static com.yorha.proto.SsPushNotification.PushTopicNotificationCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PushTopicNotificationCmd>
        PARSER = new com.google.protobuf.AbstractParser<PushTopicNotificationCmd>() {
      @java.lang.Override
      public PushTopicNotificationCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PushTopicNotificationCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PushTopicNotificationCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PushTopicNotificationCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPushNotification.PushTopicNotificationCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PushSingleNotificationCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PushSingleNotificationCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PushMultipleNotificationCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PushMultipleNotificationCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PushTopicNotificationCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PushTopicNotificationCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n8ss_proto/gen/pushNotification/ss_push_" +
      "notification.proto\022\017com.yorha.proto\"[\n\031P" +
      "ushSingleNotificationCmd\022\r\n\005title\030\001 \001(\005\022" +
      "\014\n\004body\030\002 \001(\005\022\020\n\010playerId\030\003 \001(\003\022\017\n\007model" +
      "Id\030\004 \001(\005\"a\n\033PushMultipleNotificationCmd\022" +
      "\r\n\005title\030\001 \001(\005\022\014\n\004body\030\002 \001(\005\022\024\n\014playerId" +
      "List\030\003 \003(\003\022\017\n\007modelId\030\004 \001(\005\"F\n\030PushTopic" +
      "NotificationCmd\022\r\n\005title\030\001 \001(\005\022\014\n\004body\030\002" +
      " \001(\005\022\r\n\005topic\030\003 \001(\tB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_PushSingleNotificationCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_PushSingleNotificationCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PushSingleNotificationCmd_descriptor,
        new java.lang.String[] { "Title", "Body", "PlayerId", "ModelId", });
    internal_static_com_yorha_proto_PushMultipleNotificationCmd_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_PushMultipleNotificationCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PushMultipleNotificationCmd_descriptor,
        new java.lang.String[] { "Title", "Body", "PlayerIdList", "ModelId", });
    internal_static_com_yorha_proto_PushTopicNotificationCmd_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_PushTopicNotificationCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PushTopicNotificationCmd_descriptor,
        new java.lang.String[] { "Title", "Body", "Topic", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

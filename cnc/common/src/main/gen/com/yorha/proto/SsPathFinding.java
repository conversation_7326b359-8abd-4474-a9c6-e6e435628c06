// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/pathFinding/ss_path_finding.proto

package com.yorha.proto;

public final class SsPathFinding {
  private SsPathFinding() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SearchPathAsyncAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchPathAsyncAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <code>optional .com.yorha.proto.Point src = 2;</code>
     * @return Whether the src field is set.
     */
    boolean hasSrc();
    /**
     * <code>optional .com.yorha.proto.Point src = 2;</code>
     * @return The src.
     */
    com.yorha.proto.Struct.Point getSrc();
    /**
     * <code>optional .com.yorha.proto.Point src = 2;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getSrcOrBuilder();

    /**
     * <code>optional .com.yorha.proto.Point end = 3;</code>
     * @return Whether the end field is set.
     */
    boolean hasEnd();
    /**
     * <code>optional .com.yorha.proto.Point end = 3;</code>
     * @return The end.
     */
    com.yorha.proto.Struct.Point getEnd();
    /**
     * <code>optional .com.yorha.proto.Point end = 3;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getEndOrBuilder();

    /**
     * <code>optional int32 searchTag = 4;</code>
     * @return Whether the searchTag field is set.
     */
    boolean hasSearchTag();
    /**
     * <code>optional int32 searchTag = 4;</code>
     * @return The searchTag.
     */
    int getSearchTag();

    /**
     * <code>optional int32 srcRegion = 5;</code>
     * @return Whether the srcRegion field is set.
     */
    boolean hasSrcRegion();
    /**
     * <code>optional int32 srcRegion = 5;</code>
     * @return The srcRegion.
     */
    int getSrcRegion();

    /**
     * <code>optional int32 endRegion = 6;</code>
     * @return Whether the endRegion field is set.
     */
    boolean hasEndRegion();
    /**
     * <code>optional int32 endRegion = 6;</code>
     * @return The endRegion.
     */
    int getEndRegion();

    /**
     * <code>repeated int32 cross = 7;</code>
     * @return A list containing the cross.
     */
    java.util.List<java.lang.Integer> getCrossList();
    /**
     * <code>repeated int32 cross = 7;</code>
     * @return The count of cross.
     */
    int getCrossCount();
    /**
     * <code>repeated int32 cross = 7;</code>
     * @param index The index of the element to return.
     * @return The cross at the given index.
     */
    int getCross(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchPathAsyncAsk}
   */
  public static final class SearchPathAsyncAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchPathAsyncAsk)
      SearchPathAsyncAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchPathAsyncAsk.newBuilder() to construct.
    private SearchPathAsyncAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchPathAsyncAsk() {
      cross_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchPathAsyncAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchPathAsyncAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              entityId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = src_.toBuilder();
              }
              src_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(src_);
                src_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = end_.toBuilder();
              }
              end_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(end_);
                end_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              searchTag_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              srcRegion_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              endRegion_ = input.readInt32();
              break;
            }
            case 56: {
              if (!((mutable_bitField0_ & 0x00000040) != 0)) {
                cross_ = newIntList();
                mutable_bitField0_ |= 0x00000040;
              }
              cross_.addInt(input.readInt32());
              break;
            }
            case 58: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000040) != 0) && input.getBytesUntilLimit() > 0) {
                cross_ = newIntList();
                mutable_bitField0_ |= 0x00000040;
              }
              while (input.getBytesUntilLimit() > 0) {
                cross_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000040) != 0)) {
          cross_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPathFinding.SearchPathAsyncAsk.class, com.yorha.proto.SsPathFinding.SearchPathAsyncAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int SRC_FIELD_NUMBER = 2;
    private com.yorha.proto.Struct.Point src_;
    /**
     * <code>optional .com.yorha.proto.Point src = 2;</code>
     * @return Whether the src field is set.
     */
    @java.lang.Override
    public boolean hasSrc() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Point src = 2;</code>
     * @return The src.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getSrc() {
      return src_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : src_;
    }
    /**
     * <code>optional .com.yorha.proto.Point src = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getSrcOrBuilder() {
      return src_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : src_;
    }

    public static final int END_FIELD_NUMBER = 3;
    private com.yorha.proto.Struct.Point end_;
    /**
     * <code>optional .com.yorha.proto.Point end = 3;</code>
     * @return Whether the end field is set.
     */
    @java.lang.Override
    public boolean hasEnd() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Point end = 3;</code>
     * @return The end.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getEnd() {
      return end_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : end_;
    }
    /**
     * <code>optional .com.yorha.proto.Point end = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getEndOrBuilder() {
      return end_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : end_;
    }

    public static final int SEARCHTAG_FIELD_NUMBER = 4;
    private int searchTag_;
    /**
     * <code>optional int32 searchTag = 4;</code>
     * @return Whether the searchTag field is set.
     */
    @java.lang.Override
    public boolean hasSearchTag() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 searchTag = 4;</code>
     * @return The searchTag.
     */
    @java.lang.Override
    public int getSearchTag() {
      return searchTag_;
    }

    public static final int SRCREGION_FIELD_NUMBER = 5;
    private int srcRegion_;
    /**
     * <code>optional int32 srcRegion = 5;</code>
     * @return Whether the srcRegion field is set.
     */
    @java.lang.Override
    public boolean hasSrcRegion() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 srcRegion = 5;</code>
     * @return The srcRegion.
     */
    @java.lang.Override
    public int getSrcRegion() {
      return srcRegion_;
    }

    public static final int ENDREGION_FIELD_NUMBER = 6;
    private int endRegion_;
    /**
     * <code>optional int32 endRegion = 6;</code>
     * @return Whether the endRegion field is set.
     */
    @java.lang.Override
    public boolean hasEndRegion() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 endRegion = 6;</code>
     * @return The endRegion.
     */
    @java.lang.Override
    public int getEndRegion() {
      return endRegion_;
    }

    public static final int CROSS_FIELD_NUMBER = 7;
    private com.google.protobuf.Internal.IntList cross_;
    /**
     * <code>repeated int32 cross = 7;</code>
     * @return A list containing the cross.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getCrossList() {
      return cross_;
    }
    /**
     * <code>repeated int32 cross = 7;</code>
     * @return The count of cross.
     */
    public int getCrossCount() {
      return cross_.size();
    }
    /**
     * <code>repeated int32 cross = 7;</code>
     * @param index The index of the element to return.
     * @return The cross at the given index.
     */
    public int getCross(int index) {
      return cross_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getSrc());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getEnd());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, searchTag_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, srcRegion_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, endRegion_);
      }
      for (int i = 0; i < cross_.size(); i++) {
        output.writeInt32(7, cross_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSrc());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getEnd());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, searchTag_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, srcRegion_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, endRegion_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < cross_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(cross_.getInt(i));
        }
        size += dataSize;
        size += 1 * getCrossList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPathFinding.SearchPathAsyncAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPathFinding.SearchPathAsyncAsk other = (com.yorha.proto.SsPathFinding.SearchPathAsyncAsk) obj;

      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (hasSrc() != other.hasSrc()) return false;
      if (hasSrc()) {
        if (!getSrc()
            .equals(other.getSrc())) return false;
      }
      if (hasEnd() != other.hasEnd()) return false;
      if (hasEnd()) {
        if (!getEnd()
            .equals(other.getEnd())) return false;
      }
      if (hasSearchTag() != other.hasSearchTag()) return false;
      if (hasSearchTag()) {
        if (getSearchTag()
            != other.getSearchTag()) return false;
      }
      if (hasSrcRegion() != other.hasSrcRegion()) return false;
      if (hasSrcRegion()) {
        if (getSrcRegion()
            != other.getSrcRegion()) return false;
      }
      if (hasEndRegion() != other.hasEndRegion()) return false;
      if (hasEndRegion()) {
        if (getEndRegion()
            != other.getEndRegion()) return false;
      }
      if (!getCrossList()
          .equals(other.getCrossList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      if (hasSrc()) {
        hash = (37 * hash) + SRC_FIELD_NUMBER;
        hash = (53 * hash) + getSrc().hashCode();
      }
      if (hasEnd()) {
        hash = (37 * hash) + END_FIELD_NUMBER;
        hash = (53 * hash) + getEnd().hashCode();
      }
      if (hasSearchTag()) {
        hash = (37 * hash) + SEARCHTAG_FIELD_NUMBER;
        hash = (53 * hash) + getSearchTag();
      }
      if (hasSrcRegion()) {
        hash = (37 * hash) + SRCREGION_FIELD_NUMBER;
        hash = (53 * hash) + getSrcRegion();
      }
      if (hasEndRegion()) {
        hash = (37 * hash) + ENDREGION_FIELD_NUMBER;
        hash = (53 * hash) + getEndRegion();
      }
      if (getCrossCount() > 0) {
        hash = (37 * hash) + CROSS_FIELD_NUMBER;
        hash = (53 * hash) + getCrossList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPathFinding.SearchPathAsyncAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchPathAsyncAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchPathAsyncAsk)
        com.yorha.proto.SsPathFinding.SearchPathAsyncAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPathFinding.SearchPathAsyncAsk.class, com.yorha.proto.SsPathFinding.SearchPathAsyncAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPathFinding.SearchPathAsyncAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSrcFieldBuilder();
          getEndFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (srcBuilder_ == null) {
          src_ = null;
        } else {
          srcBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (endBuilder_ == null) {
          end_ = null;
        } else {
          endBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        searchTag_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        srcRegion_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        endRegion_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        cross_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.SearchPathAsyncAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPathFinding.SearchPathAsyncAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.SearchPathAsyncAsk build() {
        com.yorha.proto.SsPathFinding.SearchPathAsyncAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.SearchPathAsyncAsk buildPartial() {
        com.yorha.proto.SsPathFinding.SearchPathAsyncAsk result = new com.yorha.proto.SsPathFinding.SearchPathAsyncAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (srcBuilder_ == null) {
            result.src_ = src_;
          } else {
            result.src_ = srcBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (endBuilder_ == null) {
            result.end_ = end_;
          } else {
            result.end_ = endBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.searchTag_ = searchTag_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.srcRegion_ = srcRegion_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.endRegion_ = endRegion_;
          to_bitField0_ |= 0x00000020;
        }
        if (((bitField0_ & 0x00000040) != 0)) {
          cross_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.cross_ = cross_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPathFinding.SearchPathAsyncAsk) {
          return mergeFrom((com.yorha.proto.SsPathFinding.SearchPathAsyncAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPathFinding.SearchPathAsyncAsk other) {
        if (other == com.yorha.proto.SsPathFinding.SearchPathAsyncAsk.getDefaultInstance()) return this;
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        if (other.hasSrc()) {
          mergeSrc(other.getSrc());
        }
        if (other.hasEnd()) {
          mergeEnd(other.getEnd());
        }
        if (other.hasSearchTag()) {
          setSearchTag(other.getSearchTag());
        }
        if (other.hasSrcRegion()) {
          setSrcRegion(other.getSrcRegion());
        }
        if (other.hasEndRegion()) {
          setEndRegion(other.getEndRegion());
        }
        if (!other.cross_.isEmpty()) {
          if (cross_.isEmpty()) {
            cross_ = other.cross_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureCrossIsMutable();
            cross_.addAll(other.cross_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPathFinding.SearchPathAsyncAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPathFinding.SearchPathAsyncAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long entityId_ ;
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000001;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Point src_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> srcBuilder_;
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       * @return Whether the src field is set.
       */
      public boolean hasSrc() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       * @return The src.
       */
      public com.yorha.proto.Struct.Point getSrc() {
        if (srcBuilder_ == null) {
          return src_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : src_;
        } else {
          return srcBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       */
      public Builder setSrc(com.yorha.proto.Struct.Point value) {
        if (srcBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          src_ = value;
          onChanged();
        } else {
          srcBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       */
      public Builder setSrc(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (srcBuilder_ == null) {
          src_ = builderForValue.build();
          onChanged();
        } else {
          srcBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       */
      public Builder mergeSrc(com.yorha.proto.Struct.Point value) {
        if (srcBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              src_ != null &&
              src_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            src_ =
              com.yorha.proto.Struct.Point.newBuilder(src_).mergeFrom(value).buildPartial();
          } else {
            src_ = value;
          }
          onChanged();
        } else {
          srcBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       */
      public Builder clearSrc() {
        if (srcBuilder_ == null) {
          src_ = null;
          onChanged();
        } else {
          srcBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getSrcBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSrcFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getSrcOrBuilder() {
        if (srcBuilder_ != null) {
          return srcBuilder_.getMessageOrBuilder();
        } else {
          return src_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : src_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point src = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getSrcFieldBuilder() {
        if (srcBuilder_ == null) {
          srcBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getSrc(),
                  getParentForChildren(),
                  isClean());
          src_ = null;
        }
        return srcBuilder_;
      }

      private com.yorha.proto.Struct.Point end_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> endBuilder_;
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       * @return Whether the end field is set.
       */
      public boolean hasEnd() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       * @return The end.
       */
      public com.yorha.proto.Struct.Point getEnd() {
        if (endBuilder_ == null) {
          return end_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : end_;
        } else {
          return endBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       */
      public Builder setEnd(com.yorha.proto.Struct.Point value) {
        if (endBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          end_ = value;
          onChanged();
        } else {
          endBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       */
      public Builder setEnd(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (endBuilder_ == null) {
          end_ = builderForValue.build();
          onChanged();
        } else {
          endBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       */
      public Builder mergeEnd(com.yorha.proto.Struct.Point value) {
        if (endBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              end_ != null &&
              end_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            end_ =
              com.yorha.proto.Struct.Point.newBuilder(end_).mergeFrom(value).buildPartial();
          } else {
            end_ = value;
          }
          onChanged();
        } else {
          endBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       */
      public Builder clearEnd() {
        if (endBuilder_ == null) {
          end_ = null;
          onChanged();
        } else {
          endBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getEndBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getEndFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getEndOrBuilder() {
        if (endBuilder_ != null) {
          return endBuilder_.getMessageOrBuilder();
        } else {
          return end_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : end_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Point end = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getEndFieldBuilder() {
        if (endBuilder_ == null) {
          endBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getEnd(),
                  getParentForChildren(),
                  isClean());
          end_ = null;
        }
        return endBuilder_;
      }

      private int searchTag_ ;
      /**
       * <code>optional int32 searchTag = 4;</code>
       * @return Whether the searchTag field is set.
       */
      @java.lang.Override
      public boolean hasSearchTag() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 searchTag = 4;</code>
       * @return The searchTag.
       */
      @java.lang.Override
      public int getSearchTag() {
        return searchTag_;
      }
      /**
       * <code>optional int32 searchTag = 4;</code>
       * @param value The searchTag to set.
       * @return This builder for chaining.
       */
      public Builder setSearchTag(int value) {
        bitField0_ |= 0x00000008;
        searchTag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 searchTag = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSearchTag() {
        bitField0_ = (bitField0_ & ~0x00000008);
        searchTag_ = 0;
        onChanged();
        return this;
      }

      private int srcRegion_ ;
      /**
       * <code>optional int32 srcRegion = 5;</code>
       * @return Whether the srcRegion field is set.
       */
      @java.lang.Override
      public boolean hasSrcRegion() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 srcRegion = 5;</code>
       * @return The srcRegion.
       */
      @java.lang.Override
      public int getSrcRegion() {
        return srcRegion_;
      }
      /**
       * <code>optional int32 srcRegion = 5;</code>
       * @param value The srcRegion to set.
       * @return This builder for chaining.
       */
      public Builder setSrcRegion(int value) {
        bitField0_ |= 0x00000010;
        srcRegion_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 srcRegion = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrcRegion() {
        bitField0_ = (bitField0_ & ~0x00000010);
        srcRegion_ = 0;
        onChanged();
        return this;
      }

      private int endRegion_ ;
      /**
       * <code>optional int32 endRegion = 6;</code>
       * @return Whether the endRegion field is set.
       */
      @java.lang.Override
      public boolean hasEndRegion() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional int32 endRegion = 6;</code>
       * @return The endRegion.
       */
      @java.lang.Override
      public int getEndRegion() {
        return endRegion_;
      }
      /**
       * <code>optional int32 endRegion = 6;</code>
       * @param value The endRegion to set.
       * @return This builder for chaining.
       */
      public Builder setEndRegion(int value) {
        bitField0_ |= 0x00000020;
        endRegion_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 endRegion = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndRegion() {
        bitField0_ = (bitField0_ & ~0x00000020);
        endRegion_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList cross_ = emptyIntList();
      private void ensureCrossIsMutable() {
        if (!((bitField0_ & 0x00000040) != 0)) {
          cross_ = mutableCopy(cross_);
          bitField0_ |= 0x00000040;
         }
      }
      /**
       * <code>repeated int32 cross = 7;</code>
       * @return A list containing the cross.
       */
      public java.util.List<java.lang.Integer>
          getCrossList() {
        return ((bitField0_ & 0x00000040) != 0) ?
                 java.util.Collections.unmodifiableList(cross_) : cross_;
      }
      /**
       * <code>repeated int32 cross = 7;</code>
       * @return The count of cross.
       */
      public int getCrossCount() {
        return cross_.size();
      }
      /**
       * <code>repeated int32 cross = 7;</code>
       * @param index The index of the element to return.
       * @return The cross at the given index.
       */
      public int getCross(int index) {
        return cross_.getInt(index);
      }
      /**
       * <code>repeated int32 cross = 7;</code>
       * @param index The index to set the value at.
       * @param value The cross to set.
       * @return This builder for chaining.
       */
      public Builder setCross(
          int index, int value) {
        ensureCrossIsMutable();
        cross_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 cross = 7;</code>
       * @param value The cross to add.
       * @return This builder for chaining.
       */
      public Builder addCross(int value) {
        ensureCrossIsMutable();
        cross_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 cross = 7;</code>
       * @param values The cross to add.
       * @return This builder for chaining.
       */
      public Builder addAllCross(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureCrossIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cross_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 cross = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearCross() {
        cross_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchPathAsyncAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchPathAsyncAsk)
    private static final com.yorha.proto.SsPathFinding.SearchPathAsyncAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPathFinding.SearchPathAsyncAsk();
    }

    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchPathAsyncAsk>
        PARSER = new com.google.protobuf.AbstractParser<SearchPathAsyncAsk>() {
      @java.lang.Override
      public SearchPathAsyncAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchPathAsyncAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchPathAsyncAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchPathAsyncAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPathFinding.SearchPathAsyncAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PathListOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PathList)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    java.util.List<com.yorha.proto.Struct.Point> 
        getPointList();
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    com.yorha.proto.Struct.Point getPoint(int index);
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    int getPointCount();
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
        getPointOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.PathList}
   */
  public static final class PathList extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PathList)
      PathListOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PathList.newBuilder() to construct.
    private PathList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PathList() {
      point_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PathList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PathList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                point_ = new java.util.ArrayList<com.yorha.proto.Struct.Point>();
                mutable_bitField0_ |= 0x00000001;
              }
              point_.add(
                  input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          point_ = java.util.Collections.unmodifiableList(point_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_PathList_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_PathList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPathFinding.PathList.class, com.yorha.proto.SsPathFinding.PathList.Builder.class);
    }

    public static final int POINT_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.Struct.Point> point_;
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.Struct.Point> getPointList() {
      return point_;
    }
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
        getPointOrBuilderList() {
      return point_;
    }
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    @java.lang.Override
    public int getPointCount() {
      return point_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint(int index) {
      return point_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.Point point = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder(
        int index) {
      return point_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < point_.size(); i++) {
        output.writeMessage(1, point_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < point_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, point_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPathFinding.PathList)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPathFinding.PathList other = (com.yorha.proto.SsPathFinding.PathList) obj;

      if (!getPointList()
          .equals(other.getPointList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPointCount() > 0) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPointList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPathFinding.PathList parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.PathList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPathFinding.PathList prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PathList}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PathList)
        com.yorha.proto.SsPathFinding.PathListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_PathList_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_PathList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPathFinding.PathList.class, com.yorha.proto.SsPathFinding.PathList.Builder.class);
      }

      // Construct using com.yorha.proto.SsPathFinding.PathList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (pointBuilder_ == null) {
          point_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          pointBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_PathList_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.PathList getDefaultInstanceForType() {
        return com.yorha.proto.SsPathFinding.PathList.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.PathList build() {
        com.yorha.proto.SsPathFinding.PathList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.PathList buildPartial() {
        com.yorha.proto.SsPathFinding.PathList result = new com.yorha.proto.SsPathFinding.PathList(this);
        int from_bitField0_ = bitField0_;
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            point_ = java.util.Collections.unmodifiableList(point_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.point_ = point_;
        } else {
          result.point_ = pointBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPathFinding.PathList) {
          return mergeFrom((com.yorha.proto.SsPathFinding.PathList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPathFinding.PathList other) {
        if (other == com.yorha.proto.SsPathFinding.PathList.getDefaultInstance()) return this;
        if (pointBuilder_ == null) {
          if (!other.point_.isEmpty()) {
            if (point_.isEmpty()) {
              point_ = other.point_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePointIsMutable();
              point_.addAll(other.point_);
            }
            onChanged();
          }
        } else {
          if (!other.point_.isEmpty()) {
            if (pointBuilder_.isEmpty()) {
              pointBuilder_.dispose();
              pointBuilder_ = null;
              point_ = other.point_;
              bitField0_ = (bitField0_ & ~0x00000001);
              pointBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPointFieldBuilder() : null;
            } else {
              pointBuilder_.addAllMessages(other.point_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPathFinding.PathList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPathFinding.PathList) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.Struct.Point> point_ =
        java.util.Collections.emptyList();
      private void ensurePointIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          point_ = new java.util.ArrayList<com.yorha.proto.Struct.Point>(point_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;

      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Point> getPointList() {
        if (pointBuilder_ == null) {
          return java.util.Collections.unmodifiableList(point_);
        } else {
          return pointBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public int getPointCount() {
        if (pointBuilder_ == null) {
          return point_.size();
        } else {
          return pointBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.Point getPoint(int index) {
        if (pointBuilder_ == null) {
          return point_.get(index);
        } else {
          return pointBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder setPoint(
          int index, com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePointIsMutable();
          point_.set(index, value);
          onChanged();
        } else {
          pointBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder setPoint(
          int index, com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          ensurePointIsMutable();
          point_.set(index, builderForValue.build());
          onChanged();
        } else {
          pointBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder addPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePointIsMutable();
          point_.add(value);
          onChanged();
        } else {
          pointBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder addPoint(
          int index, com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePointIsMutable();
          point_.add(index, value);
          onChanged();
        } else {
          pointBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder addPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          ensurePointIsMutable();
          point_.add(builderForValue.build());
          onChanged();
        } else {
          pointBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder addPoint(
          int index, com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          ensurePointIsMutable();
          point_.add(index, builderForValue.build());
          onChanged();
        } else {
          pointBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder addAllPoint(
          java.lang.Iterable<? extends com.yorha.proto.Struct.Point> values) {
        if (pointBuilder_ == null) {
          ensurePointIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, point_);
          onChanged();
        } else {
          pointBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public Builder removePoint(int index) {
        if (pointBuilder_ == null) {
          ensurePointIsMutable();
          point_.remove(index);
          onChanged();
        } else {
          pointBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder(
          int index) {
        return getPointFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder(
          int index) {
        if (pointBuilder_ == null) {
          return point_.get(index);  } else {
          return pointBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.Struct.PointOrBuilder> 
           getPointOrBuilderList() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(point_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder addPointBuilder() {
        return getPointFieldBuilder().addBuilder(
            com.yorha.proto.Struct.Point.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder addPointBuilder(
          int index) {
        return getPointFieldBuilder().addBuilder(
            index, com.yorha.proto.Struct.Point.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.Point point = 1;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Point.Builder> 
           getPointBuilderList() {
        return getPointFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  point_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PathList)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PathList)
    private static final com.yorha.proto.SsPathFinding.PathList DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPathFinding.PathList();
    }

    public static com.yorha.proto.SsPathFinding.PathList getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PathList>
        PARSER = new com.google.protobuf.AbstractParser<PathList>() {
      @java.lang.Override
      public PathList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PathList(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PathList> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PathList> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPathFinding.PathList getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchPathAsyncAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchPathAsyncAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 code = 1;</code>
     * @return Whether the code field is set.
     */
    boolean hasCode();
    /**
     * <code>optional int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    java.util.List<com.yorha.proto.SsPathFinding.PathList> 
        getPathListList();
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    com.yorha.proto.SsPathFinding.PathList getPathList(int index);
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    int getPathListCount();
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    java.util.List<? extends com.yorha.proto.SsPathFinding.PathListOrBuilder> 
        getPathListOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    com.yorha.proto.SsPathFinding.PathListOrBuilder getPathListOrBuilder(
        int index);

    /**
     * <code>repeated int32 part = 3;</code>
     * @return A list containing the part.
     */
    java.util.List<java.lang.Integer> getPartList();
    /**
     * <code>repeated int32 part = 3;</code>
     * @return The count of part.
     */
    int getPartCount();
    /**
     * <code>repeated int32 part = 3;</code>
     * @param index The index of the element to return.
     * @return The part at the given index.
     */
    int getPart(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchPathAsyncAns}
   */
  public static final class SearchPathAsyncAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchPathAsyncAns)
      SearchPathAsyncAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchPathAsyncAns.newBuilder() to construct.
    private SearchPathAsyncAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchPathAsyncAns() {
      pathList_ = java.util.Collections.emptyList();
      part_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchPathAsyncAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchPathAsyncAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              code_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                pathList_ = new java.util.ArrayList<com.yorha.proto.SsPathFinding.PathList>();
                mutable_bitField0_ |= 0x00000002;
              }
              pathList_.add(
                  input.readMessage(com.yorha.proto.SsPathFinding.PathList.PARSER, extensionRegistry));
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                part_ = newIntList();
                mutable_bitField0_ |= 0x00000004;
              }
              part_.addInt(input.readInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                part_ = newIntList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                part_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          pathList_ = java.util.Collections.unmodifiableList(pathList_);
        }
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          part_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPathFinding.SearchPathAsyncAns.class, com.yorha.proto.SsPathFinding.SearchPathAsyncAns.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <code>optional int32 code = 1;</code>
     * @return Whether the code field is set.
     */
    @java.lang.Override
    public boolean hasCode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int PATHLIST_FIELD_NUMBER = 2;
    private java.util.List<com.yorha.proto.SsPathFinding.PathList> pathList_;
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.SsPathFinding.PathList> getPathListList() {
      return pathList_;
    }
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.SsPathFinding.PathListOrBuilder> 
        getPathListOrBuilderList() {
      return pathList_;
    }
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    @java.lang.Override
    public int getPathListCount() {
      return pathList_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsPathFinding.PathList getPathList(int index) {
      return pathList_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsPathFinding.PathListOrBuilder getPathListOrBuilder(
        int index) {
      return pathList_.get(index);
    }

    public static final int PART_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.IntList part_;
    /**
     * <code>repeated int32 part = 3;</code>
     * @return A list containing the part.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getPartList() {
      return part_;
    }
    /**
     * <code>repeated int32 part = 3;</code>
     * @return The count of part.
     */
    public int getPartCount() {
      return part_.size();
    }
    /**
     * <code>repeated int32 part = 3;</code>
     * @param index The index of the element to return.
     * @return The part at the given index.
     */
    public int getPart(int index) {
      return part_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, code_);
      }
      for (int i = 0; i < pathList_.size(); i++) {
        output.writeMessage(2, pathList_.get(i));
      }
      for (int i = 0; i < part_.size(); i++) {
        output.writeInt32(3, part_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      for (int i = 0; i < pathList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, pathList_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < part_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(part_.getInt(i));
        }
        size += dataSize;
        size += 1 * getPartList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPathFinding.SearchPathAsyncAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPathFinding.SearchPathAsyncAns other = (com.yorha.proto.SsPathFinding.SearchPathAsyncAns) obj;

      if (hasCode() != other.hasCode()) return false;
      if (hasCode()) {
        if (getCode()
            != other.getCode()) return false;
      }
      if (!getPathListList()
          .equals(other.getPathListList())) return false;
      if (!getPartList()
          .equals(other.getPartList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCode()) {
        hash = (37 * hash) + CODE_FIELD_NUMBER;
        hash = (53 * hash) + getCode();
      }
      if (getPathListCount() > 0) {
        hash = (37 * hash) + PATHLIST_FIELD_NUMBER;
        hash = (53 * hash) + getPathListList().hashCode();
      }
      if (getPartCount() > 0) {
        hash = (37 * hash) + PART_FIELD_NUMBER;
        hash = (53 * hash) + getPartList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPathFinding.SearchPathAsyncAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchPathAsyncAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchPathAsyncAns)
        com.yorha.proto.SsPathFinding.SearchPathAsyncAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPathFinding.SearchPathAsyncAns.class, com.yorha.proto.SsPathFinding.SearchPathAsyncAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPathFinding.SearchPathAsyncAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPathListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pathListBuilder_ == null) {
          pathList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          pathListBuilder_.clear();
        }
        part_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPathFinding.internal_static_com_yorha_proto_SearchPathAsyncAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.SearchPathAsyncAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPathFinding.SearchPathAsyncAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.SearchPathAsyncAns build() {
        com.yorha.proto.SsPathFinding.SearchPathAsyncAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPathFinding.SearchPathAsyncAns buildPartial() {
        com.yorha.proto.SsPathFinding.SearchPathAsyncAns result = new com.yorha.proto.SsPathFinding.SearchPathAsyncAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
          to_bitField0_ |= 0x00000001;
        }
        if (pathListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            pathList_ = java.util.Collections.unmodifiableList(pathList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.pathList_ = pathList_;
        } else {
          result.pathList_ = pathListBuilder_.build();
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          part_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.part_ = part_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPathFinding.SearchPathAsyncAns) {
          return mergeFrom((com.yorha.proto.SsPathFinding.SearchPathAsyncAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPathFinding.SearchPathAsyncAns other) {
        if (other == com.yorha.proto.SsPathFinding.SearchPathAsyncAns.getDefaultInstance()) return this;
        if (other.hasCode()) {
          setCode(other.getCode());
        }
        if (pathListBuilder_ == null) {
          if (!other.pathList_.isEmpty()) {
            if (pathList_.isEmpty()) {
              pathList_ = other.pathList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePathListIsMutable();
              pathList_.addAll(other.pathList_);
            }
            onChanged();
          }
        } else {
          if (!other.pathList_.isEmpty()) {
            if (pathListBuilder_.isEmpty()) {
              pathListBuilder_.dispose();
              pathListBuilder_ = null;
              pathList_ = other.pathList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              pathListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPathListFieldBuilder() : null;
            } else {
              pathListBuilder_.addAllMessages(other.pathList_);
            }
          }
        }
        if (!other.part_.isEmpty()) {
          if (part_.isEmpty()) {
            part_ = other.part_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensurePartIsMutable();
            part_.addAll(other.part_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPathFinding.SearchPathAsyncAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPathFinding.SearchPathAsyncAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>optional int32 code = 1;</code>
       * @return Whether the code field is set.
       */
      @java.lang.Override
      public boolean hasCode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>optional int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {
        bitField0_ |= 0x00000001;
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.SsPathFinding.PathList> pathList_ =
        java.util.Collections.emptyList();
      private void ensurePathListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          pathList_ = new java.util.ArrayList<com.yorha.proto.SsPathFinding.PathList>(pathList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsPathFinding.PathList, com.yorha.proto.SsPathFinding.PathList.Builder, com.yorha.proto.SsPathFinding.PathListOrBuilder> pathListBuilder_;

      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public java.util.List<com.yorha.proto.SsPathFinding.PathList> getPathListList() {
        if (pathListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(pathList_);
        } else {
          return pathListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public int getPathListCount() {
        if (pathListBuilder_ == null) {
          return pathList_.size();
        } else {
          return pathListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public com.yorha.proto.SsPathFinding.PathList getPathList(int index) {
        if (pathListBuilder_ == null) {
          return pathList_.get(index);
        } else {
          return pathListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder setPathList(
          int index, com.yorha.proto.SsPathFinding.PathList value) {
        if (pathListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePathListIsMutable();
          pathList_.set(index, value);
          onChanged();
        } else {
          pathListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder setPathList(
          int index, com.yorha.proto.SsPathFinding.PathList.Builder builderForValue) {
        if (pathListBuilder_ == null) {
          ensurePathListIsMutable();
          pathList_.set(index, builderForValue.build());
          onChanged();
        } else {
          pathListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder addPathList(com.yorha.proto.SsPathFinding.PathList value) {
        if (pathListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePathListIsMutable();
          pathList_.add(value);
          onChanged();
        } else {
          pathListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder addPathList(
          int index, com.yorha.proto.SsPathFinding.PathList value) {
        if (pathListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePathListIsMutable();
          pathList_.add(index, value);
          onChanged();
        } else {
          pathListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder addPathList(
          com.yorha.proto.SsPathFinding.PathList.Builder builderForValue) {
        if (pathListBuilder_ == null) {
          ensurePathListIsMutable();
          pathList_.add(builderForValue.build());
          onChanged();
        } else {
          pathListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder addPathList(
          int index, com.yorha.proto.SsPathFinding.PathList.Builder builderForValue) {
        if (pathListBuilder_ == null) {
          ensurePathListIsMutable();
          pathList_.add(index, builderForValue.build());
          onChanged();
        } else {
          pathListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder addAllPathList(
          java.lang.Iterable<? extends com.yorha.proto.SsPathFinding.PathList> values) {
        if (pathListBuilder_ == null) {
          ensurePathListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, pathList_);
          onChanged();
        } else {
          pathListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder clearPathList() {
        if (pathListBuilder_ == null) {
          pathList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          pathListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public Builder removePathList(int index) {
        if (pathListBuilder_ == null) {
          ensurePathListIsMutable();
          pathList_.remove(index);
          onChanged();
        } else {
          pathListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public com.yorha.proto.SsPathFinding.PathList.Builder getPathListBuilder(
          int index) {
        return getPathListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public com.yorha.proto.SsPathFinding.PathListOrBuilder getPathListOrBuilder(
          int index) {
        if (pathListBuilder_ == null) {
          return pathList_.get(index);  } else {
          return pathListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public java.util.List<? extends com.yorha.proto.SsPathFinding.PathListOrBuilder> 
           getPathListOrBuilderList() {
        if (pathListBuilder_ != null) {
          return pathListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(pathList_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public com.yorha.proto.SsPathFinding.PathList.Builder addPathListBuilder() {
        return getPathListFieldBuilder().addBuilder(
            com.yorha.proto.SsPathFinding.PathList.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public com.yorha.proto.SsPathFinding.PathList.Builder addPathListBuilder(
          int index) {
        return getPathListFieldBuilder().addBuilder(
            index, com.yorha.proto.SsPathFinding.PathList.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.PathList pathList = 2;</code>
       */
      public java.util.List<com.yorha.proto.SsPathFinding.PathList.Builder> 
           getPathListBuilderList() {
        return getPathListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsPathFinding.PathList, com.yorha.proto.SsPathFinding.PathList.Builder, com.yorha.proto.SsPathFinding.PathListOrBuilder> 
          getPathListFieldBuilder() {
        if (pathListBuilder_ == null) {
          pathListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.SsPathFinding.PathList, com.yorha.proto.SsPathFinding.PathList.Builder, com.yorha.proto.SsPathFinding.PathListOrBuilder>(
                  pathList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          pathList_ = null;
        }
        return pathListBuilder_;
      }

      private com.google.protobuf.Internal.IntList part_ = emptyIntList();
      private void ensurePartIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          part_ = mutableCopy(part_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int32 part = 3;</code>
       * @return A list containing the part.
       */
      public java.util.List<java.lang.Integer>
          getPartList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(part_) : part_;
      }
      /**
       * <code>repeated int32 part = 3;</code>
       * @return The count of part.
       */
      public int getPartCount() {
        return part_.size();
      }
      /**
       * <code>repeated int32 part = 3;</code>
       * @param index The index of the element to return.
       * @return The part at the given index.
       */
      public int getPart(int index) {
        return part_.getInt(index);
      }
      /**
       * <code>repeated int32 part = 3;</code>
       * @param index The index to set the value at.
       * @param value The part to set.
       * @return This builder for chaining.
       */
      public Builder setPart(
          int index, int value) {
        ensurePartIsMutable();
        part_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 part = 3;</code>
       * @param value The part to add.
       * @return This builder for chaining.
       */
      public Builder addPart(int value) {
        ensurePartIsMutable();
        part_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 part = 3;</code>
       * @param values The part to add.
       * @return This builder for chaining.
       */
      public Builder addAllPart(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensurePartIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, part_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 part = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPart() {
        part_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchPathAsyncAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchPathAsyncAns)
    private static final com.yorha.proto.SsPathFinding.SearchPathAsyncAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPathFinding.SearchPathAsyncAns();
    }

    public static com.yorha.proto.SsPathFinding.SearchPathAsyncAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchPathAsyncAns>
        PARSER = new com.google.protobuf.AbstractParser<SearchPathAsyncAns>() {
      @java.lang.Override
      public SearchPathAsyncAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchPathAsyncAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchPathAsyncAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchPathAsyncAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPathFinding.SearchPathAsyncAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchPathAsyncAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchPathAsyncAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PathList_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PathList_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchPathAsyncAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchPathAsyncAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/pathFinding/ss_path_findi" +
      "ng.proto\022\017com.yorha.proto\032 ss_proto/gen/" +
      "common/struct.proto\"\270\001\n\022SearchPathAsyncA" +
      "sk\022\020\n\010entityId\030\001 \001(\003\022#\n\003src\030\002 \001(\0132\026.com." +
      "yorha.proto.Point\022#\n\003end\030\003 \001(\0132\026.com.yor" +
      "ha.proto.Point\022\021\n\tsearchTag\030\004 \001(\005\022\021\n\tsrc" +
      "Region\030\005 \001(\005\022\021\n\tendRegion\030\006 \001(\005\022\r\n\005cross" +
      "\030\007 \003(\005\"1\n\010PathList\022%\n\005point\030\001 \003(\0132\026.com." +
      "yorha.proto.Point\"]\n\022SearchPathAsyncAns\022" +
      "\014\n\004code\030\001 \001(\005\022+\n\010pathList\030\002 \003(\0132\031.com.yo" +
      "rha.proto.PathList\022\014\n\004part\030\003 \003(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_SearchPathAsyncAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SearchPathAsyncAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchPathAsyncAsk_descriptor,
        new java.lang.String[] { "EntityId", "Src", "End", "SearchTag", "SrcRegion", "EndRegion", "Cross", });
    internal_static_com_yorha_proto_PathList_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_PathList_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PathList_descriptor,
        new java.lang.String[] { "Point", });
    internal_static_com_yorha_proto_SearchPathAsyncAns_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SearchPathAsyncAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchPathAsyncAns_descriptor,
        new java.lang.String[] { "Code", "PathList", "Part", });
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

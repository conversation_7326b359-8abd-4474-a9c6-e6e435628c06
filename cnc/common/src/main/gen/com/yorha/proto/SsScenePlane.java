// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_plane.proto

package com.yorha.proto;

public final class SsScenePlane {
  private SsScenePlane() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CreateSpyPlaneAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateSpyPlaneAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return Whether the spyInfo field is set.
     */
    boolean hasSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return The spyInfo.
     */
    com.yorha.proto.StructMsg.SpyInfo getSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     */
    com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateSpyPlaneAsk}
   */
  public static final class CreateSpyPlaneAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateSpyPlaneAsk)
      CreateSpyPlaneAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateSpyPlaneAsk.newBuilder() to construct.
    private CreateSpyPlaneAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateSpyPlaneAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateSpyPlaneAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateSpyPlaneAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructMsg.SpyInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = spyInfo_.toBuilder();
              }
              spyInfo_ = input.readMessage(com.yorha.proto.StructMsg.SpyInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(spyInfo_);
                spyInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk.class, com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int SPYINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return Whether the spyInfo field is set.
     */
    @java.lang.Override
    public boolean hasSpyInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return The spyInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getSpyInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSpyInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk other = (com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasSpyInfo() != other.hasSpyInfo()) return false;
      if (hasSpyInfo()) {
        if (!getSpyInfo()
            .equals(other.getSpyInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasSpyInfo()) {
        hash = (37 * hash) + SPYINFO_FIELD_NUMBER;
        hash = (53 * hash) + getSpyInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateSpyPlaneAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateSpyPlaneAsk)
        com.yorha.proto.SsScenePlane.CreateSpyPlaneAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk.class, com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSpyInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk build() {
        com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk buildPartial() {
        com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk result = new com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (spyInfoBuilder_ == null) {
            result.spyInfo_ = spyInfo_;
          } else {
            result.spyInfo_ = spyInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk) {
          return mergeFrom((com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk other) {
        if (other == com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasSpyInfo()) {
          mergeSpyInfo(other.getSpyInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> spyInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       * @return Whether the spyInfo field is set.
       */
      public boolean hasSpyInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       * @return The spyInfo.
       */
      public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
        if (spyInfoBuilder_ == null) {
          return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        } else {
          return spyInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder setSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          spyInfo_ = value;
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder setSpyInfo(
          com.yorha.proto.StructMsg.SpyInfo.Builder builderForValue) {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = builderForValue.build();
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder mergeSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              spyInfo_ != null &&
              spyInfo_ != com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance()) {
            spyInfo_ =
              com.yorha.proto.StructMsg.SpyInfo.newBuilder(spyInfo_).mergeFrom(value).buildPartial();
          } else {
            spyInfo_ = value;
          }
          onChanged();
        } else {
          spyInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder clearSpyInfo() {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
          onChanged();
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfo.Builder getSpyInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSpyInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
        if (spyInfoBuilder_ != null) {
          return spyInfoBuilder_.getMessageOrBuilder();
        } else {
          return spyInfo_ == null ?
              com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> 
          getSpyInfoFieldBuilder() {
        if (spyInfoBuilder_ == null) {
          spyInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder>(
                  getSpyInfo(),
                  getParentForChildren(),
                  isClean());
          spyInfo_ = null;
        }
        return spyInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateSpyPlaneAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateSpyPlaneAsk)
    private static final com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk();
    }

    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateSpyPlaneAsk>
        PARSER = new com.google.protobuf.AbstractParser<CreateSpyPlaneAsk>() {
      @java.lang.Override
      public CreateSpyPlaneAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateSpyPlaneAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateSpyPlaneAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateSpyPlaneAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.CreateSpyPlaneAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreateSpyPlaneAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateSpyPlaneAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 planeEntityId = 1;</code>
     * @return Whether the planeEntityId field is set.
     */
    boolean hasPlaneEntityId();
    /**
     * <code>optional int64 planeEntityId = 1;</code>
     * @return The planeEntityId.
     */
    long getPlaneEntityId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateSpyPlaneAns}
   */
  public static final class CreateSpyPlaneAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateSpyPlaneAns)
      CreateSpyPlaneAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateSpyPlaneAns.newBuilder() to construct.
    private CreateSpyPlaneAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateSpyPlaneAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateSpyPlaneAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateSpyPlaneAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              planeEntityId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.CreateSpyPlaneAns.class, com.yorha.proto.SsScenePlane.CreateSpyPlaneAns.Builder.class);
    }

    private int bitField0_;
    public static final int PLANEENTITYID_FIELD_NUMBER = 1;
    private long planeEntityId_;
    /**
     * <code>optional int64 planeEntityId = 1;</code>
     * @return Whether the planeEntityId field is set.
     */
    @java.lang.Override
    public boolean hasPlaneEntityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 planeEntityId = 1;</code>
     * @return The planeEntityId.
     */
    @java.lang.Override
    public long getPlaneEntityId() {
      return planeEntityId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, planeEntityId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, planeEntityId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.CreateSpyPlaneAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.CreateSpyPlaneAns other = (com.yorha.proto.SsScenePlane.CreateSpyPlaneAns) obj;

      if (hasPlaneEntityId() != other.hasPlaneEntityId()) return false;
      if (hasPlaneEntityId()) {
        if (getPlaneEntityId()
            != other.getPlaneEntityId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlaneEntityId()) {
        hash = (37 * hash) + PLANEENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlaneEntityId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.CreateSpyPlaneAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateSpyPlaneAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateSpyPlaneAns)
        com.yorha.proto.SsScenePlane.CreateSpyPlaneAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.CreateSpyPlaneAns.class, com.yorha.proto.SsScenePlane.CreateSpyPlaneAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.CreateSpyPlaneAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        planeEntityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateSpyPlaneAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateSpyPlaneAns getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.CreateSpyPlaneAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateSpyPlaneAns build() {
        com.yorha.proto.SsScenePlane.CreateSpyPlaneAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateSpyPlaneAns buildPartial() {
        com.yorha.proto.SsScenePlane.CreateSpyPlaneAns result = new com.yorha.proto.SsScenePlane.CreateSpyPlaneAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.planeEntityId_ = planeEntityId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.CreateSpyPlaneAns) {
          return mergeFrom((com.yorha.proto.SsScenePlane.CreateSpyPlaneAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.CreateSpyPlaneAns other) {
        if (other == com.yorha.proto.SsScenePlane.CreateSpyPlaneAns.getDefaultInstance()) return this;
        if (other.hasPlaneEntityId()) {
          setPlaneEntityId(other.getPlaneEntityId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.CreateSpyPlaneAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.CreateSpyPlaneAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long planeEntityId_ ;
      /**
       * <code>optional int64 planeEntityId = 1;</code>
       * @return Whether the planeEntityId field is set.
       */
      @java.lang.Override
      public boolean hasPlaneEntityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 planeEntityId = 1;</code>
       * @return The planeEntityId.
       */
      @java.lang.Override
      public long getPlaneEntityId() {
        return planeEntityId_;
      }
      /**
       * <code>optional int64 planeEntityId = 1;</code>
       * @param value The planeEntityId to set.
       * @return This builder for chaining.
       */
      public Builder setPlaneEntityId(long value) {
        bitField0_ |= 0x00000001;
        planeEntityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 planeEntityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlaneEntityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        planeEntityId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateSpyPlaneAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateSpyPlaneAns)
    private static final com.yorha.proto.SsScenePlane.CreateSpyPlaneAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.CreateSpyPlaneAns();
    }

    public static com.yorha.proto.SsScenePlane.CreateSpyPlaneAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateSpyPlaneAns>
        PARSER = new com.google.protobuf.AbstractParser<CreateSpyPlaneAns>() {
      @java.lang.Override
      public CreateSpyPlaneAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateSpyPlaneAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateSpyPlaneAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateSpyPlaneAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.CreateSpyPlaneAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeActionSpyPlaneAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeActionSpyPlaneAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return Whether the spyInfo field is set.
     */
    boolean hasSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return The spyInfo.
     */
    com.yorha.proto.StructMsg.SpyInfo getSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     */
    com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeActionSpyPlaneAsk}
   */
  public static final class ChangeActionSpyPlaneAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeActionSpyPlaneAsk)
      ChangeActionSpyPlaneAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeActionSpyPlaneAsk.newBuilder() to construct.
    private ChangeActionSpyPlaneAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeActionSpyPlaneAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeActionSpyPlaneAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeActionSpyPlaneAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructMsg.SpyInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = spyInfo_.toBuilder();
              }
              spyInfo_ = input.readMessage(com.yorha.proto.StructMsg.SpyInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(spyInfo_);
                spyInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk.class, com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int SPYINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return Whether the spyInfo field is set.
     */
    @java.lang.Override
    public boolean hasSpyInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return The spyInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getSpyInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSpyInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk other = (com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasSpyInfo() != other.hasSpyInfo()) return false;
      if (hasSpyInfo()) {
        if (!getSpyInfo()
            .equals(other.getSpyInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasSpyInfo()) {
        hash = (37 * hash) + SPYINFO_FIELD_NUMBER;
        hash = (53 * hash) + getSpyInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeActionSpyPlaneAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeActionSpyPlaneAsk)
        com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk.class, com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSpyInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk build() {
        com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk buildPartial() {
        com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk result = new com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (spyInfoBuilder_ == null) {
            result.spyInfo_ = spyInfo_;
          } else {
            result.spyInfo_ = spyInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk) {
          return mergeFrom((com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk other) {
        if (other == com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasSpyInfo()) {
          mergeSpyInfo(other.getSpyInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> spyInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       * @return Whether the spyInfo field is set.
       */
      public boolean hasSpyInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       * @return The spyInfo.
       */
      public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
        if (spyInfoBuilder_ == null) {
          return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        } else {
          return spyInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder setSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          spyInfo_ = value;
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder setSpyInfo(
          com.yorha.proto.StructMsg.SpyInfo.Builder builderForValue) {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = builderForValue.build();
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder mergeSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              spyInfo_ != null &&
              spyInfo_ != com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance()) {
            spyInfo_ =
              com.yorha.proto.StructMsg.SpyInfo.newBuilder(spyInfo_).mergeFrom(value).buildPartial();
          } else {
            spyInfo_ = value;
          }
          onChanged();
        } else {
          spyInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder clearSpyInfo() {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
          onChanged();
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfo.Builder getSpyInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSpyInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
        if (spyInfoBuilder_ != null) {
          return spyInfoBuilder_.getMessageOrBuilder();
        } else {
          return spyInfo_ == null ?
              com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> 
          getSpyInfoFieldBuilder() {
        if (spyInfoBuilder_ == null) {
          spyInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder>(
                  getSpyInfo(),
                  getParentForChildren(),
                  isClean());
          spyInfo_ = null;
        }
        return spyInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeActionSpyPlaneAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeActionSpyPlaneAsk)
    private static final com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk();
    }

    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeActionSpyPlaneAsk>
        PARSER = new com.google.protobuf.AbstractParser<ChangeActionSpyPlaneAsk>() {
      @java.lang.Override
      public ChangeActionSpyPlaneAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeActionSpyPlaneAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeActionSpyPlaneAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeActionSpyPlaneAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeActionSpyPlaneAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeActionSpyPlaneAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 planeEntityId = 1;</code>
     * @return Whether the planeEntityId field is set.
     */
    boolean hasPlaneEntityId();
    /**
     * <code>optional int64 planeEntityId = 1;</code>
     * @return The planeEntityId.
     */
    long getPlaneEntityId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeActionSpyPlaneAns}
   */
  public static final class ChangeActionSpyPlaneAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeActionSpyPlaneAns)
      ChangeActionSpyPlaneAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeActionSpyPlaneAns.newBuilder() to construct.
    private ChangeActionSpyPlaneAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeActionSpyPlaneAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeActionSpyPlaneAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeActionSpyPlaneAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              planeEntityId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns.class, com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns.Builder.class);
    }

    private int bitField0_;
    public static final int PLANEENTITYID_FIELD_NUMBER = 1;
    private long planeEntityId_;
    /**
     * <code>optional int64 planeEntityId = 1;</code>
     * @return Whether the planeEntityId field is set.
     */
    @java.lang.Override
    public boolean hasPlaneEntityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 planeEntityId = 1;</code>
     * @return The planeEntityId.
     */
    @java.lang.Override
    public long getPlaneEntityId() {
      return planeEntityId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, planeEntityId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, planeEntityId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns other = (com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns) obj;

      if (hasPlaneEntityId() != other.hasPlaneEntityId()) return false;
      if (hasPlaneEntityId()) {
        if (getPlaneEntityId()
            != other.getPlaneEntityId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlaneEntityId()) {
        hash = (37 * hash) + PLANEENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlaneEntityId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeActionSpyPlaneAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeActionSpyPlaneAns)
        com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns.class, com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        planeEntityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns build() {
        com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns buildPartial() {
        com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns result = new com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.planeEntityId_ = planeEntityId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns) {
          return mergeFrom((com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns other) {
        if (other == com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns.getDefaultInstance()) return this;
        if (other.hasPlaneEntityId()) {
          setPlaneEntityId(other.getPlaneEntityId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long planeEntityId_ ;
      /**
       * <code>optional int64 planeEntityId = 1;</code>
       * @return Whether the planeEntityId field is set.
       */
      @java.lang.Override
      public boolean hasPlaneEntityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 planeEntityId = 1;</code>
       * @return The planeEntityId.
       */
      @java.lang.Override
      public long getPlaneEntityId() {
        return planeEntityId_;
      }
      /**
       * <code>optional int64 planeEntityId = 1;</code>
       * @param value The planeEntityId to set.
       * @return This builder for chaining.
       */
      public Builder setPlaneEntityId(long value) {
        bitField0_ |= 0x00000001;
        planeEntityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 planeEntityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlaneEntityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        planeEntityId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeActionSpyPlaneAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeActionSpyPlaneAns)
    private static final com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns();
    }

    public static com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeActionSpyPlaneAns>
        PARSER = new com.google.protobuf.AbstractParser<ChangeActionSpyPlaneAns>() {
      @java.lang.Override
      public ChangeActionSpyPlaneAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeActionSpyPlaneAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeActionSpyPlaneAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeActionSpyPlaneAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.ChangeActionSpyPlaneAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckMapCreateSpyPlaneAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckMapCreateSpyPlaneAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return Whether the spyInfo field is set.
     */
    boolean hasSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return The spyInfo.
     */
    com.yorha.proto.StructMsg.SpyInfo getSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     */
    com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckMapCreateSpyPlaneAsk}
   */
  public static final class CheckMapCreateSpyPlaneAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckMapCreateSpyPlaneAsk)
      CheckMapCreateSpyPlaneAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckMapCreateSpyPlaneAsk.newBuilder() to construct.
    private CheckMapCreateSpyPlaneAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckMapCreateSpyPlaneAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckMapCreateSpyPlaneAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckMapCreateSpyPlaneAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructMsg.SpyInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = spyInfo_.toBuilder();
              }
              spyInfo_ = input.readMessage(com.yorha.proto.StructMsg.SpyInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(spyInfo_);
                spyInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk.class, com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int SPYINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return Whether the spyInfo field is set.
     */
    @java.lang.Override
    public boolean hasSpyInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     * @return The spyInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getSpyInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSpyInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk other = (com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasSpyInfo() != other.hasSpyInfo()) return false;
      if (hasSpyInfo()) {
        if (!getSpyInfo()
            .equals(other.getSpyInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasSpyInfo()) {
        hash = (37 * hash) + SPYINFO_FIELD_NUMBER;
        hash = (53 * hash) + getSpyInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckMapCreateSpyPlaneAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckMapCreateSpyPlaneAsk)
        com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk.class, com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSpyInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk build() {
        com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk buildPartial() {
        com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk result = new com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (spyInfoBuilder_ == null) {
            result.spyInfo_ = spyInfo_;
          } else {
            result.spyInfo_ = spyInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk) {
          return mergeFrom((com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk other) {
        if (other == com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasSpyInfo()) {
          mergeSpyInfo(other.getSpyInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> spyInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       * @return Whether the spyInfo field is set.
       */
      public boolean hasSpyInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       * @return The spyInfo.
       */
      public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
        if (spyInfoBuilder_ == null) {
          return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        } else {
          return spyInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder setSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          spyInfo_ = value;
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder setSpyInfo(
          com.yorha.proto.StructMsg.SpyInfo.Builder builderForValue) {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = builderForValue.build();
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder mergeSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              spyInfo_ != null &&
              spyInfo_ != com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance()) {
            spyInfo_ =
              com.yorha.proto.StructMsg.SpyInfo.newBuilder(spyInfo_).mergeFrom(value).buildPartial();
          } else {
            spyInfo_ = value;
          }
          onChanged();
        } else {
          spyInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public Builder clearSpyInfo() {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
          onChanged();
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfo.Builder getSpyInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSpyInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
        if (spyInfoBuilder_ != null) {
          return spyInfoBuilder_.getMessageOrBuilder();
        } else {
          return spyInfo_ == null ?
              com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> 
          getSpyInfoFieldBuilder() {
        if (spyInfoBuilder_ == null) {
          spyInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder>(
                  getSpyInfo(),
                  getParentForChildren(),
                  isClean());
          spyInfo_ = null;
        }
        return spyInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckMapCreateSpyPlaneAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckMapCreateSpyPlaneAsk)
    private static final com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk();
    }

    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckMapCreateSpyPlaneAsk>
        PARSER = new com.google.protobuf.AbstractParser<CheckMapCreateSpyPlaneAsk>() {
      @java.lang.Override
      public CheckMapCreateSpyPlaneAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckMapCreateSpyPlaneAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckMapCreateSpyPlaneAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckMapCreateSpyPlaneAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckMapCreateSpyPlaneAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckMapCreateSpyPlaneAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return Whether the errorCode field is set.
     */
    boolean hasErrorCode();
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return The errorCode.
     */
    int getErrorCode();

    /**
     * <code>optional .com.yorha.proto.Currency currency = 2;</code>
     * @return Whether the currency field is set.
     */
    boolean hasCurrency();
    /**
     * <code>optional .com.yorha.proto.Currency currency = 2;</code>
     * @return The currency.
     */
    com.yorha.proto.Struct.Currency getCurrency();
    /**
     * <code>optional .com.yorha.proto.Currency currency = 2;</code>
     */
    com.yorha.proto.Struct.CurrencyOrBuilder getCurrencyOrBuilder();

    /**
     * <code>optional .com.yorha.proto.MapBuildingType buildType = 3;</code>
     * @return Whether the buildType field is set.
     */
    boolean hasBuildType();
    /**
     * <code>optional .com.yorha.proto.MapBuildingType buildType = 3;</code>
     * @return The buildType.
     */
    com.yorha.proto.CommonEnum.MapBuildingType getBuildType();

    /**
     * <code>optional int32 configId = 4;</code>
     * @return Whether the configId field is set.
     */
    boolean hasConfigId();
    /**
     * <code>optional int32 configId = 4;</code>
     * @return The configId.
     */
    int getConfigId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckMapCreateSpyPlaneAns}
   */
  public static final class CheckMapCreateSpyPlaneAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckMapCreateSpyPlaneAns)
      CheckMapCreateSpyPlaneAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckMapCreateSpyPlaneAns.newBuilder() to construct.
    private CheckMapCreateSpyPlaneAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckMapCreateSpyPlaneAns() {
      buildType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckMapCreateSpyPlaneAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckMapCreateSpyPlaneAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              errorCode_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.Struct.Currency.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = currency_.toBuilder();
              }
              currency_ = input.readMessage(com.yorha.proto.Struct.Currency.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(currency_);
                currency_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MapBuildingType value = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(3, rawValue);
              } else {
                bitField0_ |= 0x00000004;
                buildType_ = rawValue;
              }
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              configId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns.class, com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns.Builder.class);
    }

    private int bitField0_;
    public static final int ERRORCODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return Whether the errorCode field is set.
     */
    @java.lang.Override
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return The errorCode.
     */
    @java.lang.Override
    public int getErrorCode() {
      return errorCode_;
    }

    public static final int CURRENCY_FIELD_NUMBER = 2;
    private com.yorha.proto.Struct.Currency currency_;
    /**
     * <code>optional .com.yorha.proto.Currency currency = 2;</code>
     * @return Whether the currency field is set.
     */
    @java.lang.Override
    public boolean hasCurrency() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Currency currency = 2;</code>
     * @return The currency.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Currency getCurrency() {
      return currency_ == null ? com.yorha.proto.Struct.Currency.getDefaultInstance() : currency_;
    }
    /**
     * <code>optional .com.yorha.proto.Currency currency = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.CurrencyOrBuilder getCurrencyOrBuilder() {
      return currency_ == null ? com.yorha.proto.Struct.Currency.getDefaultInstance() : currency_;
    }

    public static final int BUILDTYPE_FIELD_NUMBER = 3;
    private int buildType_;
    /**
     * <code>optional .com.yorha.proto.MapBuildingType buildType = 3;</code>
     * @return Whether the buildType field is set.
     */
    @java.lang.Override public boolean hasBuildType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MapBuildingType buildType = 3;</code>
     * @return The buildType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MapBuildingType getBuildType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(buildType_);
      return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
    }

    public static final int CONFIGID_FIELD_NUMBER = 4;
    private int configId_;
    /**
     * <code>optional int32 configId = 4;</code>
     * @return Whether the configId field is set.
     */
    @java.lang.Override
    public boolean hasConfigId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 configId = 4;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, errorCode_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getCurrency());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeEnum(3, buildType_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, configId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorCode_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCurrency());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, buildType_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, configId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns other = (com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns) obj;

      if (hasErrorCode() != other.hasErrorCode()) return false;
      if (hasErrorCode()) {
        if (getErrorCode()
            != other.getErrorCode()) return false;
      }
      if (hasCurrency() != other.hasCurrency()) return false;
      if (hasCurrency()) {
        if (!getCurrency()
            .equals(other.getCurrency())) return false;
      }
      if (hasBuildType() != other.hasBuildType()) return false;
      if (hasBuildType()) {
        if (buildType_ != other.buildType_) return false;
      }
      if (hasConfigId() != other.hasConfigId()) return false;
      if (hasConfigId()) {
        if (getConfigId()
            != other.getConfigId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasErrorCode()) {
        hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
        hash = (53 * hash) + getErrorCode();
      }
      if (hasCurrency()) {
        hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
        hash = (53 * hash) + getCurrency().hashCode();
      }
      if (hasBuildType()) {
        hash = (37 * hash) + BUILDTYPE_FIELD_NUMBER;
        hash = (53 * hash) + buildType_;
      }
      if (hasConfigId()) {
        hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
        hash = (53 * hash) + getConfigId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckMapCreateSpyPlaneAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckMapCreateSpyPlaneAns)
        com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns.class, com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCurrencyFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (currencyBuilder_ == null) {
          currency_ = null;
        } else {
          currencyBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        buildType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        configId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns build() {
        com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns buildPartial() {
        com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns result = new com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.errorCode_ = errorCode_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (currencyBuilder_ == null) {
            result.currency_ = currency_;
          } else {
            result.currency_ = currencyBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.buildType_ = buildType_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.configId_ = configId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns) {
          return mergeFrom((com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns other) {
        if (other == com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns.getDefaultInstance()) return this;
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        if (other.hasCurrency()) {
          mergeCurrency(other.getCurrency());
        }
        if (other.hasBuildType()) {
          setBuildType(other.getBuildType());
        }
        if (other.hasConfigId()) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return Whether the errorCode field is set.
       */
      @java.lang.Override
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000001;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorCode_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Currency currency_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder> currencyBuilder_;
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       * @return Whether the currency field is set.
       */
      public boolean hasCurrency() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       * @return The currency.
       */
      public com.yorha.proto.Struct.Currency getCurrency() {
        if (currencyBuilder_ == null) {
          return currency_ == null ? com.yorha.proto.Struct.Currency.getDefaultInstance() : currency_;
        } else {
          return currencyBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       */
      public Builder setCurrency(com.yorha.proto.Struct.Currency value) {
        if (currencyBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          currency_ = value;
          onChanged();
        } else {
          currencyBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       */
      public Builder setCurrency(
          com.yorha.proto.Struct.Currency.Builder builderForValue) {
        if (currencyBuilder_ == null) {
          currency_ = builderForValue.build();
          onChanged();
        } else {
          currencyBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       */
      public Builder mergeCurrency(com.yorha.proto.Struct.Currency value) {
        if (currencyBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              currency_ != null &&
              currency_ != com.yorha.proto.Struct.Currency.getDefaultInstance()) {
            currency_ =
              com.yorha.proto.Struct.Currency.newBuilder(currency_).mergeFrom(value).buildPartial();
          } else {
            currency_ = value;
          }
          onChanged();
        } else {
          currencyBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       */
      public Builder clearCurrency() {
        if (currencyBuilder_ == null) {
          currency_ = null;
          onChanged();
        } else {
          currencyBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       */
      public com.yorha.proto.Struct.Currency.Builder getCurrencyBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCurrencyFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       */
      public com.yorha.proto.Struct.CurrencyOrBuilder getCurrencyOrBuilder() {
        if (currencyBuilder_ != null) {
          return currencyBuilder_.getMessageOrBuilder();
        } else {
          return currency_ == null ?
              com.yorha.proto.Struct.Currency.getDefaultInstance() : currency_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Currency currency = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder> 
          getCurrencyFieldBuilder() {
        if (currencyBuilder_ == null) {
          currencyBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder>(
                  getCurrency(),
                  getParentForChildren(),
                  isClean());
          currency_ = null;
        }
        return currencyBuilder_;
      }

      private int buildType_ = 0;
      /**
       * <code>optional .com.yorha.proto.MapBuildingType buildType = 3;</code>
       * @return Whether the buildType field is set.
       */
      @java.lang.Override public boolean hasBuildType() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType buildType = 3;</code>
       * @return The buildType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MapBuildingType getBuildType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(buildType_);
        return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType buildType = 3;</code>
       * @param value The buildType to set.
       * @return This builder for chaining.
       */
      public Builder setBuildType(com.yorha.proto.CommonEnum.MapBuildingType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        buildType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType buildType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuildType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        buildType_ = 0;
        onChanged();
        return this;
      }

      private int configId_ ;
      /**
       * <code>optional int32 configId = 4;</code>
       * @return Whether the configId field is set.
       */
      @java.lang.Override
      public boolean hasConfigId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 configId = 4;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>optional int32 configId = 4;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        bitField0_ |= 0x00000008;
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 configId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        configId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckMapCreateSpyPlaneAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckMapCreateSpyPlaneAns)
    private static final com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns();
    }

    public static com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckMapCreateSpyPlaneAns>
        PARSER = new com.google.protobuf.AbstractParser<CheckMapCreateSpyPlaneAns>() {
      @java.lang.Override
      public CheckMapCreateSpyPlaneAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckMapCreateSpyPlaneAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckMapCreateSpyPlaneAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckMapCreateSpyPlaneAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.CheckMapCreateSpyPlaneAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreateLogisticsPlaneAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateLogisticsPlaneAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return Whether the logisticsInfo field is set.
     */
    boolean hasLogisticsInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return The logisticsInfo.
     */
    com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     */
    com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder();

    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    java.util.List<com.yorha.proto.Struct.Currency> 
        getResourcesList();
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    com.yorha.proto.Struct.Currency getResources(int index);
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    int getResourcesCount();
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    java.util.List<? extends com.yorha.proto.Struct.CurrencyOrBuilder> 
        getResourcesOrBuilderList();
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    com.yorha.proto.Struct.CurrencyOrBuilder getResourcesOrBuilder(
        int index);

    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    java.util.List<com.yorha.proto.Struct.Currency> 
        getTaxList();
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    com.yorha.proto.Struct.Currency getTax(int index);
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    int getTaxCount();
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    java.util.List<? extends com.yorha.proto.Struct.CurrencyOrBuilder> 
        getTaxOrBuilderList();
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    com.yorha.proto.Struct.CurrencyOrBuilder getTaxOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateLogisticsPlaneAsk}
   */
  public static final class CreateLogisticsPlaneAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateLogisticsPlaneAsk)
      CreateLogisticsPlaneAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateLogisticsPlaneAsk.newBuilder() to construct.
    private CreateLogisticsPlaneAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateLogisticsPlaneAsk() {
      resources_ = java.util.Collections.emptyList();
      tax_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateLogisticsPlaneAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateLogisticsPlaneAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructMsg.LogisticsInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = logisticsInfo_.toBuilder();
              }
              logisticsInfo_ = input.readMessage(com.yorha.proto.StructMsg.LogisticsInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(logisticsInfo_);
                logisticsInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                resources_ = new java.util.ArrayList<com.yorha.proto.Struct.Currency>();
                mutable_bitField0_ |= 0x00000004;
              }
              resources_.add(
                  input.readMessage(com.yorha.proto.Struct.Currency.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) != 0)) {
                tax_ = new java.util.ArrayList<com.yorha.proto.Struct.Currency>();
                mutable_bitField0_ |= 0x00000008;
              }
              tax_.add(
                  input.readMessage(com.yorha.proto.Struct.Currency.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          resources_ = java.util.Collections.unmodifiableList(resources_);
        }
        if (((mutable_bitField0_ & 0x00000008) != 0)) {
          tax_ = java.util.Collections.unmodifiableList(tax_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk.class, com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int LOGISTICSINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.StructMsg.LogisticsInfo logisticsInfo_;
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return Whether the logisticsInfo field is set.
     */
    @java.lang.Override
    public boolean hasLogisticsInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return The logisticsInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo() {
      return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder() {
      return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
    }

    public static final int RESOURCES_FIELD_NUMBER = 3;
    private java.util.List<com.yorha.proto.Struct.Currency> resources_;
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.Struct.Currency> getResourcesList() {
      return resources_;
    }
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.Struct.CurrencyOrBuilder> 
        getResourcesOrBuilderList() {
      return resources_;
    }
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    @java.lang.Override
    public int getResourcesCount() {
      return resources_.size();
    }
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Currency getResources(int index) {
      return resources_.get(index);
    }
    /**
     * <pre>
     * 运输的资源
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.CurrencyOrBuilder getResourcesOrBuilder(
        int index) {
      return resources_.get(index);
    }

    public static final int TAX_FIELD_NUMBER = 4;
    private java.util.List<com.yorha.proto.Struct.Currency> tax_;
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.Struct.Currency> getTaxList() {
      return tax_;
    }
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.Struct.CurrencyOrBuilder> 
        getTaxOrBuilderList() {
      return tax_;
    }
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    @java.lang.Override
    public int getTaxCount() {
      return tax_.size();
    }
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Currency getTax(int index) {
      return tax_.get(index);
    }
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.CurrencyOrBuilder getTaxOrBuilder(
        int index) {
      return tax_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getLogisticsInfo());
      }
      for (int i = 0; i < resources_.size(); i++) {
        output.writeMessage(3, resources_.get(i));
      }
      for (int i = 0; i < tax_.size(); i++) {
        output.writeMessage(4, tax_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getLogisticsInfo());
      }
      for (int i = 0; i < resources_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, resources_.get(i));
      }
      for (int i = 0; i < tax_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, tax_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk other = (com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasLogisticsInfo() != other.hasLogisticsInfo()) return false;
      if (hasLogisticsInfo()) {
        if (!getLogisticsInfo()
            .equals(other.getLogisticsInfo())) return false;
      }
      if (!getResourcesList()
          .equals(other.getResourcesList())) return false;
      if (!getTaxList()
          .equals(other.getTaxList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasLogisticsInfo()) {
        hash = (37 * hash) + LOGISTICSINFO_FIELD_NUMBER;
        hash = (53 * hash) + getLogisticsInfo().hashCode();
      }
      if (getResourcesCount() > 0) {
        hash = (37 * hash) + RESOURCES_FIELD_NUMBER;
        hash = (53 * hash) + getResourcesList().hashCode();
      }
      if (getTaxCount() > 0) {
        hash = (37 * hash) + TAX_FIELD_NUMBER;
        hash = (53 * hash) + getTaxList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateLogisticsPlaneAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateLogisticsPlaneAsk)
        com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk.class, com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getLogisticsInfoFieldBuilder();
          getResourcesFieldBuilder();
          getTaxFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = null;
        } else {
          logisticsInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (resourcesBuilder_ == null) {
          resources_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          resourcesBuilder_.clear();
        }
        if (taxBuilder_ == null) {
          tax_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          taxBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk build() {
        com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk buildPartial() {
        com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk result = new com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (logisticsInfoBuilder_ == null) {
            result.logisticsInfo_ = logisticsInfo_;
          } else {
            result.logisticsInfo_ = logisticsInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (resourcesBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            resources_ = java.util.Collections.unmodifiableList(resources_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.resources_ = resources_;
        } else {
          result.resources_ = resourcesBuilder_.build();
        }
        if (taxBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            tax_ = java.util.Collections.unmodifiableList(tax_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.tax_ = tax_;
        } else {
          result.tax_ = taxBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk) {
          return mergeFrom((com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk other) {
        if (other == com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasLogisticsInfo()) {
          mergeLogisticsInfo(other.getLogisticsInfo());
        }
        if (resourcesBuilder_ == null) {
          if (!other.resources_.isEmpty()) {
            if (resources_.isEmpty()) {
              resources_ = other.resources_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureResourcesIsMutable();
              resources_.addAll(other.resources_);
            }
            onChanged();
          }
        } else {
          if (!other.resources_.isEmpty()) {
            if (resourcesBuilder_.isEmpty()) {
              resourcesBuilder_.dispose();
              resourcesBuilder_ = null;
              resources_ = other.resources_;
              bitField0_ = (bitField0_ & ~0x00000004);
              resourcesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getResourcesFieldBuilder() : null;
            } else {
              resourcesBuilder_.addAllMessages(other.resources_);
            }
          }
        }
        if (taxBuilder_ == null) {
          if (!other.tax_.isEmpty()) {
            if (tax_.isEmpty()) {
              tax_ = other.tax_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureTaxIsMutable();
              tax_.addAll(other.tax_);
            }
            onChanged();
          }
        } else {
          if (!other.tax_.isEmpty()) {
            if (taxBuilder_.isEmpty()) {
              taxBuilder_.dispose();
              taxBuilder_ = null;
              tax_ = other.tax_;
              bitField0_ = (bitField0_ & ~0x00000008);
              taxBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTaxFieldBuilder() : null;
            } else {
              taxBuilder_.addAllMessages(other.tax_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructMsg.LogisticsInfo logisticsInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> logisticsInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       * @return Whether the logisticsInfo field is set.
       */
      public boolean hasLogisticsInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       * @return The logisticsInfo.
       */
      public com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo() {
        if (logisticsInfoBuilder_ == null) {
          return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
        } else {
          return logisticsInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder setLogisticsInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (logisticsInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          logisticsInfo_ = value;
          onChanged();
        } else {
          logisticsInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder setLogisticsInfo(
          com.yorha.proto.StructMsg.LogisticsInfo.Builder builderForValue) {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = builderForValue.build();
          onChanged();
        } else {
          logisticsInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder mergeLogisticsInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (logisticsInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              logisticsInfo_ != null &&
              logisticsInfo_ != com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance()) {
            logisticsInfo_ =
              com.yorha.proto.StructMsg.LogisticsInfo.newBuilder(logisticsInfo_).mergeFrom(value).buildPartial();
          } else {
            logisticsInfo_ = value;
          }
          onChanged();
        } else {
          logisticsInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder clearLogisticsInfo() {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = null;
          onChanged();
        } else {
          logisticsInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfo.Builder getLogisticsInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getLogisticsInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder() {
        if (logisticsInfoBuilder_ != null) {
          return logisticsInfoBuilder_.getMessageOrBuilder();
        } else {
          return logisticsInfo_ == null ?
              com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> 
          getLogisticsInfoFieldBuilder() {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder>(
                  getLogisticsInfo(),
                  getParentForChildren(),
                  isClean());
          logisticsInfo_ = null;
        }
        return logisticsInfoBuilder_;
      }

      private java.util.List<com.yorha.proto.Struct.Currency> resources_ =
        java.util.Collections.emptyList();
      private void ensureResourcesIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          resources_ = new java.util.ArrayList<com.yorha.proto.Struct.Currency>(resources_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder> resourcesBuilder_;

      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Currency> getResourcesList() {
        if (resourcesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(resources_);
        } else {
          return resourcesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public int getResourcesCount() {
        if (resourcesBuilder_ == null) {
          return resources_.size();
        } else {
          return resourcesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public com.yorha.proto.Struct.Currency getResources(int index) {
        if (resourcesBuilder_ == null) {
          return resources_.get(index);
        } else {
          return resourcesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder setResources(
          int index, com.yorha.proto.Struct.Currency value) {
        if (resourcesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResourcesIsMutable();
          resources_.set(index, value);
          onChanged();
        } else {
          resourcesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder setResources(
          int index, com.yorha.proto.Struct.Currency.Builder builderForValue) {
        if (resourcesBuilder_ == null) {
          ensureResourcesIsMutable();
          resources_.set(index, builderForValue.build());
          onChanged();
        } else {
          resourcesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder addResources(com.yorha.proto.Struct.Currency value) {
        if (resourcesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResourcesIsMutable();
          resources_.add(value);
          onChanged();
        } else {
          resourcesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder addResources(
          int index, com.yorha.proto.Struct.Currency value) {
        if (resourcesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResourcesIsMutable();
          resources_.add(index, value);
          onChanged();
        } else {
          resourcesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder addResources(
          com.yorha.proto.Struct.Currency.Builder builderForValue) {
        if (resourcesBuilder_ == null) {
          ensureResourcesIsMutable();
          resources_.add(builderForValue.build());
          onChanged();
        } else {
          resourcesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder addResources(
          int index, com.yorha.proto.Struct.Currency.Builder builderForValue) {
        if (resourcesBuilder_ == null) {
          ensureResourcesIsMutable();
          resources_.add(index, builderForValue.build());
          onChanged();
        } else {
          resourcesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder addAllResources(
          java.lang.Iterable<? extends com.yorha.proto.Struct.Currency> values) {
        if (resourcesBuilder_ == null) {
          ensureResourcesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, resources_);
          onChanged();
        } else {
          resourcesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder clearResources() {
        if (resourcesBuilder_ == null) {
          resources_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          resourcesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public Builder removeResources(int index) {
        if (resourcesBuilder_ == null) {
          ensureResourcesIsMutable();
          resources_.remove(index);
          onChanged();
        } else {
          resourcesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public com.yorha.proto.Struct.Currency.Builder getResourcesBuilder(
          int index) {
        return getResourcesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public com.yorha.proto.Struct.CurrencyOrBuilder getResourcesOrBuilder(
          int index) {
        if (resourcesBuilder_ == null) {
          return resources_.get(index);  } else {
          return resourcesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public java.util.List<? extends com.yorha.proto.Struct.CurrencyOrBuilder> 
           getResourcesOrBuilderList() {
        if (resourcesBuilder_ != null) {
          return resourcesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(resources_);
        }
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public com.yorha.proto.Struct.Currency.Builder addResourcesBuilder() {
        return getResourcesFieldBuilder().addBuilder(
            com.yorha.proto.Struct.Currency.getDefaultInstance());
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public com.yorha.proto.Struct.Currency.Builder addResourcesBuilder(
          int index) {
        return getResourcesFieldBuilder().addBuilder(
            index, com.yorha.proto.Struct.Currency.getDefaultInstance());
      }
      /**
       * <pre>
       * 运输的资源
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency resources = 3;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Currency.Builder> 
           getResourcesBuilderList() {
        return getResourcesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder> 
          getResourcesFieldBuilder() {
        if (resourcesBuilder_ == null) {
          resourcesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder>(
                  resources_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          resources_ = null;
        }
        return resourcesBuilder_;
      }

      private java.util.List<com.yorha.proto.Struct.Currency> tax_ =
        java.util.Collections.emptyList();
      private void ensureTaxIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          tax_ = new java.util.ArrayList<com.yorha.proto.Struct.Currency>(tax_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder> taxBuilder_;

      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Currency> getTaxList() {
        if (taxBuilder_ == null) {
          return java.util.Collections.unmodifiableList(tax_);
        } else {
          return taxBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public int getTaxCount() {
        if (taxBuilder_ == null) {
          return tax_.size();
        } else {
          return taxBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public com.yorha.proto.Struct.Currency getTax(int index) {
        if (taxBuilder_ == null) {
          return tax_.get(index);
        } else {
          return taxBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder setTax(
          int index, com.yorha.proto.Struct.Currency value) {
        if (taxBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaxIsMutable();
          tax_.set(index, value);
          onChanged();
        } else {
          taxBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder setTax(
          int index, com.yorha.proto.Struct.Currency.Builder builderForValue) {
        if (taxBuilder_ == null) {
          ensureTaxIsMutable();
          tax_.set(index, builderForValue.build());
          onChanged();
        } else {
          taxBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder addTax(com.yorha.proto.Struct.Currency value) {
        if (taxBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaxIsMutable();
          tax_.add(value);
          onChanged();
        } else {
          taxBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder addTax(
          int index, com.yorha.proto.Struct.Currency value) {
        if (taxBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaxIsMutable();
          tax_.add(index, value);
          onChanged();
        } else {
          taxBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder addTax(
          com.yorha.proto.Struct.Currency.Builder builderForValue) {
        if (taxBuilder_ == null) {
          ensureTaxIsMutable();
          tax_.add(builderForValue.build());
          onChanged();
        } else {
          taxBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder addTax(
          int index, com.yorha.proto.Struct.Currency.Builder builderForValue) {
        if (taxBuilder_ == null) {
          ensureTaxIsMutable();
          tax_.add(index, builderForValue.build());
          onChanged();
        } else {
          taxBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder addAllTax(
          java.lang.Iterable<? extends com.yorha.proto.Struct.Currency> values) {
        if (taxBuilder_ == null) {
          ensureTaxIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, tax_);
          onChanged();
        } else {
          taxBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder clearTax() {
        if (taxBuilder_ == null) {
          tax_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          taxBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public Builder removeTax(int index) {
        if (taxBuilder_ == null) {
          ensureTaxIsMutable();
          tax_.remove(index);
          onChanged();
        } else {
          taxBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public com.yorha.proto.Struct.Currency.Builder getTaxBuilder(
          int index) {
        return getTaxFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public com.yorha.proto.Struct.CurrencyOrBuilder getTaxOrBuilder(
          int index) {
        if (taxBuilder_ == null) {
          return tax_.get(index);  } else {
          return taxBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public java.util.List<? extends com.yorha.proto.Struct.CurrencyOrBuilder> 
           getTaxOrBuilderList() {
        if (taxBuilder_ != null) {
          return taxBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(tax_);
        }
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public com.yorha.proto.Struct.Currency.Builder addTaxBuilder() {
        return getTaxFieldBuilder().addBuilder(
            com.yorha.proto.Struct.Currency.getDefaultInstance());
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public com.yorha.proto.Struct.Currency.Builder addTaxBuilder(
          int index) {
        return getTaxFieldBuilder().addBuilder(
            index, com.yorha.proto.Struct.Currency.getDefaultInstance());
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>repeated .com.yorha.proto.Currency tax = 4;</code>
       */
      public java.util.List<com.yorha.proto.Struct.Currency.Builder> 
           getTaxBuilderList() {
        return getTaxFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder> 
          getTaxFieldBuilder() {
        if (taxBuilder_ == null) {
          taxBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.Struct.Currency, com.yorha.proto.Struct.Currency.Builder, com.yorha.proto.Struct.CurrencyOrBuilder>(
                  tax_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          tax_ = null;
        }
        return taxBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateLogisticsPlaneAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateLogisticsPlaneAsk)
    private static final com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk();
    }

    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateLogisticsPlaneAsk>
        PARSER = new com.google.protobuf.AbstractParser<CreateLogisticsPlaneAsk>() {
      @java.lang.Override
      public CreateLogisticsPlaneAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateLogisticsPlaneAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateLogisticsPlaneAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateLogisticsPlaneAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CreateLogisticsPlaneAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CreateLogisticsPlaneAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.CreateLogisticsPlaneAns}
   */
  public static final class CreateLogisticsPlaneAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CreateLogisticsPlaneAns)
      CreateLogisticsPlaneAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CreateLogisticsPlaneAns.newBuilder() to construct.
    private CreateLogisticsPlaneAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CreateLogisticsPlaneAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CreateLogisticsPlaneAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CreateLogisticsPlaneAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns.class, com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns other = (com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CreateLogisticsPlaneAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CreateLogisticsPlaneAns)
        com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns.class, com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CreateLogisticsPlaneAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns build() {
        com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns buildPartial() {
        com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns result = new com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns) {
          return mergeFrom((com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns other) {
        if (other == com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CreateLogisticsPlaneAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CreateLogisticsPlaneAns)
    private static final com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns();
    }

    public static com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CreateLogisticsPlaneAns>
        PARSER = new com.google.protobuf.AbstractParser<CreateLogisticsPlaneAns>() {
      @java.lang.Override
      public CreateLogisticsPlaneAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CreateLogisticsPlaneAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CreateLogisticsPlaneAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CreateLogisticsPlaneAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.CreateLogisticsPlaneAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckLogisticsActionAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckLogisticsActionAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return Whether the logisticsInfo field is set.
     */
    boolean hasLogisticsInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return The logisticsInfo.
     */
    com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     */
    com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckLogisticsActionAsk}
   */
  public static final class CheckLogisticsActionAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckLogisticsActionAsk)
      CheckLogisticsActionAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckLogisticsActionAsk.newBuilder() to construct.
    private CheckLogisticsActionAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckLogisticsActionAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckLogisticsActionAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckLogisticsActionAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructMsg.LogisticsInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = logisticsInfo_.toBuilder();
              }
              logisticsInfo_ = input.readMessage(com.yorha.proto.StructMsg.LogisticsInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(logisticsInfo_);
                logisticsInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk.class, com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int LOGISTICSINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.StructMsg.LogisticsInfo logisticsInfo_;
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return Whether the logisticsInfo field is set.
     */
    @java.lang.Override
    public boolean hasLogisticsInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return The logisticsInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo() {
      return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder() {
      return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getLogisticsInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getLogisticsInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk other = (com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasLogisticsInfo() != other.hasLogisticsInfo()) return false;
      if (hasLogisticsInfo()) {
        if (!getLogisticsInfo()
            .equals(other.getLogisticsInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasLogisticsInfo()) {
        hash = (37 * hash) + LOGISTICSINFO_FIELD_NUMBER;
        hash = (53 * hash) + getLogisticsInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckLogisticsActionAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckLogisticsActionAsk)
        com.yorha.proto.SsScenePlane.CheckLogisticsActionAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk.class, com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getLogisticsInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = null;
        } else {
          logisticsInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk build() {
        com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk buildPartial() {
        com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk result = new com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (logisticsInfoBuilder_ == null) {
            result.logisticsInfo_ = logisticsInfo_;
          } else {
            result.logisticsInfo_ = logisticsInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk) {
          return mergeFrom((com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk other) {
        if (other == com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasLogisticsInfo()) {
          mergeLogisticsInfo(other.getLogisticsInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructMsg.LogisticsInfo logisticsInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> logisticsInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       * @return Whether the logisticsInfo field is set.
       */
      public boolean hasLogisticsInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       * @return The logisticsInfo.
       */
      public com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo() {
        if (logisticsInfoBuilder_ == null) {
          return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
        } else {
          return logisticsInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder setLogisticsInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (logisticsInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          logisticsInfo_ = value;
          onChanged();
        } else {
          logisticsInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder setLogisticsInfo(
          com.yorha.proto.StructMsg.LogisticsInfo.Builder builderForValue) {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = builderForValue.build();
          onChanged();
        } else {
          logisticsInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder mergeLogisticsInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (logisticsInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              logisticsInfo_ != null &&
              logisticsInfo_ != com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance()) {
            logisticsInfo_ =
              com.yorha.proto.StructMsg.LogisticsInfo.newBuilder(logisticsInfo_).mergeFrom(value).buildPartial();
          } else {
            logisticsInfo_ = value;
          }
          onChanged();
        } else {
          logisticsInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder clearLogisticsInfo() {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = null;
          onChanged();
        } else {
          logisticsInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfo.Builder getLogisticsInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getLogisticsInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder() {
        if (logisticsInfoBuilder_ != null) {
          return logisticsInfoBuilder_.getMessageOrBuilder();
        } else {
          return logisticsInfo_ == null ?
              com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> 
          getLogisticsInfoFieldBuilder() {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder>(
                  getLogisticsInfo(),
                  getParentForChildren(),
                  isClean());
          logisticsInfo_ = null;
        }
        return logisticsInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckLogisticsActionAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckLogisticsActionAsk)
    private static final com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk();
    }

    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckLogisticsActionAsk>
        PARSER = new com.google.protobuf.AbstractParser<CheckLogisticsActionAsk>() {
      @java.lang.Override
      public CheckLogisticsActionAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckLogisticsActionAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckLogisticsActionAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckLogisticsActionAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.CheckLogisticsActionAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckLogisticsActionAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckLogisticsActionAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return Whether the errorCode field is set.
     */
    boolean hasErrorCode();
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return The errorCode.
     */
    int getErrorCode();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckLogisticsActionAns}
   */
  public static final class CheckLogisticsActionAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckLogisticsActionAns)
      CheckLogisticsActionAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckLogisticsActionAns.newBuilder() to construct.
    private CheckLogisticsActionAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckLogisticsActionAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckLogisticsActionAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckLogisticsActionAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              errorCode_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.CheckLogisticsActionAns.class, com.yorha.proto.SsScenePlane.CheckLogisticsActionAns.Builder.class);
    }

    private int bitField0_;
    public static final int ERRORCODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return Whether the errorCode field is set.
     */
    @java.lang.Override
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return The errorCode.
     */
    @java.lang.Override
    public int getErrorCode() {
      return errorCode_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, errorCode_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorCode_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.CheckLogisticsActionAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.CheckLogisticsActionAns other = (com.yorha.proto.SsScenePlane.CheckLogisticsActionAns) obj;

      if (hasErrorCode() != other.hasErrorCode()) return false;
      if (hasErrorCode()) {
        if (getErrorCode()
            != other.getErrorCode()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasErrorCode()) {
        hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
        hash = (53 * hash) + getErrorCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.CheckLogisticsActionAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckLogisticsActionAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckLogisticsActionAns)
        com.yorha.proto.SsScenePlane.CheckLogisticsActionAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.CheckLogisticsActionAns.class, com.yorha.proto.SsScenePlane.CheckLogisticsActionAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.CheckLogisticsActionAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_CheckLogisticsActionAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckLogisticsActionAns getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.CheckLogisticsActionAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckLogisticsActionAns build() {
        com.yorha.proto.SsScenePlane.CheckLogisticsActionAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.CheckLogisticsActionAns buildPartial() {
        com.yorha.proto.SsScenePlane.CheckLogisticsActionAns result = new com.yorha.proto.SsScenePlane.CheckLogisticsActionAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.errorCode_ = errorCode_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.CheckLogisticsActionAns) {
          return mergeFrom((com.yorha.proto.SsScenePlane.CheckLogisticsActionAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.CheckLogisticsActionAns other) {
        if (other == com.yorha.proto.SsScenePlane.CheckLogisticsActionAns.getDefaultInstance()) return this;
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.CheckLogisticsActionAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.CheckLogisticsActionAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return Whether the errorCode field is set.
       */
      @java.lang.Override
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000001;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorCode_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckLogisticsActionAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckLogisticsActionAns)
    private static final com.yorha.proto.SsScenePlane.CheckLogisticsActionAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.CheckLogisticsActionAns();
    }

    public static com.yorha.proto.SsScenePlane.CheckLogisticsActionAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckLogisticsActionAns>
        PARSER = new com.google.protobuf.AbstractParser<CheckLogisticsActionAns>() {
      @java.lang.Override
      public CheckLogisticsActionAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckLogisticsActionAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckLogisticsActionAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckLogisticsActionAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.CheckLogisticsActionAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeLogisticActionAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeLogisticActionAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return Whether the logisticsInfo field is set.
     */
    boolean hasLogisticsInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return The logisticsInfo.
     */
    com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     */
    com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeLogisticActionAsk}
   */
  public static final class ChangeLogisticActionAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeLogisticActionAsk)
      ChangeLogisticActionAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeLogisticActionAsk.newBuilder() to construct.
    private ChangeLogisticActionAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeLogisticActionAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeLogisticActionAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeLogisticActionAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructMsg.LogisticsInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = logisticsInfo_.toBuilder();
              }
              logisticsInfo_ = input.readMessage(com.yorha.proto.StructMsg.LogisticsInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(logisticsInfo_);
                logisticsInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticActionAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticActionAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk.class, com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int LOGISTICSINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.StructMsg.LogisticsInfo logisticsInfo_;
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return Whether the logisticsInfo field is set.
     */
    @java.lang.Override
    public boolean hasLogisticsInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     * @return The logisticsInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo() {
      return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder() {
      return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getLogisticsInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getLogisticsInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk other = (com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasLogisticsInfo() != other.hasLogisticsInfo()) return false;
      if (hasLogisticsInfo()) {
        if (!getLogisticsInfo()
            .equals(other.getLogisticsInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasLogisticsInfo()) {
        hash = (37 * hash) + LOGISTICSINFO_FIELD_NUMBER;
        hash = (53 * hash) + getLogisticsInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeLogisticActionAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeLogisticActionAsk)
        com.yorha.proto.SsScenePlane.ChangeLogisticActionAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticActionAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticActionAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk.class, com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getLogisticsInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = null;
        } else {
          logisticsInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticActionAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk build() {
        com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk buildPartial() {
        com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk result = new com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (logisticsInfoBuilder_ == null) {
            result.logisticsInfo_ = logisticsInfo_;
          } else {
            result.logisticsInfo_ = logisticsInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk) {
          return mergeFrom((com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk other) {
        if (other == com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasLogisticsInfo()) {
          mergeLogisticsInfo(other.getLogisticsInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructMsg.LogisticsInfo logisticsInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> logisticsInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       * @return Whether the logisticsInfo field is set.
       */
      public boolean hasLogisticsInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       * @return The logisticsInfo.
       */
      public com.yorha.proto.StructMsg.LogisticsInfo getLogisticsInfo() {
        if (logisticsInfoBuilder_ == null) {
          return logisticsInfo_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
        } else {
          return logisticsInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder setLogisticsInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (logisticsInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          logisticsInfo_ = value;
          onChanged();
        } else {
          logisticsInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder setLogisticsInfo(
          com.yorha.proto.StructMsg.LogisticsInfo.Builder builderForValue) {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = builderForValue.build();
          onChanged();
        } else {
          logisticsInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder mergeLogisticsInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (logisticsInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              logisticsInfo_ != null &&
              logisticsInfo_ != com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance()) {
            logisticsInfo_ =
              com.yorha.proto.StructMsg.LogisticsInfo.newBuilder(logisticsInfo_).mergeFrom(value).buildPartial();
          } else {
            logisticsInfo_ = value;
          }
          onChanged();
        } else {
          logisticsInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public Builder clearLogisticsInfo() {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfo_ = null;
          onChanged();
        } else {
          logisticsInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfo.Builder getLogisticsInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getLogisticsInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getLogisticsInfoOrBuilder() {
        if (logisticsInfoBuilder_ != null) {
          return logisticsInfoBuilder_.getMessageOrBuilder();
        } else {
          return logisticsInfo_ == null ?
              com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : logisticsInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo logisticsInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> 
          getLogisticsInfoFieldBuilder() {
        if (logisticsInfoBuilder_ == null) {
          logisticsInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder>(
                  getLogisticsInfo(),
                  getParentForChildren(),
                  isClean());
          logisticsInfo_ = null;
        }
        return logisticsInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeLogisticActionAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeLogisticActionAsk)
    private static final com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk();
    }

    public static com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeLogisticActionAsk>
        PARSER = new com.google.protobuf.AbstractParser<ChangeLogisticActionAsk>() {
      @java.lang.Override
      public ChangeLogisticActionAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeLogisticActionAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeLogisticActionAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeLogisticActionAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.ChangeLogisticActionAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeLogisticsActionAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeLogisticsActionAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeLogisticsActionAns}
   */
  public static final class ChangeLogisticsActionAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeLogisticsActionAns)
      ChangeLogisticsActionAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeLogisticsActionAns.newBuilder() to construct.
    private ChangeLogisticsActionAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeLogisticsActionAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeLogisticsActionAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeLogisticsActionAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticsActionAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticsActionAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns.class, com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns other = (com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeLogisticsActionAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeLogisticsActionAns)
        com.yorha.proto.SsScenePlane.ChangeLogisticsActionAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticsActionAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticsActionAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns.class, com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsScenePlane.internal_static_com_yorha_proto_ChangeLogisticsActionAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns getDefaultInstanceForType() {
        return com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns build() {
        com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns buildPartial() {
        com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns result = new com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns) {
          return mergeFrom((com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns other) {
        if (other == com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeLogisticsActionAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeLogisticsActionAns)
    private static final com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns();
    }

    public static com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeLogisticsActionAns>
        PARSER = new com.google.protobuf.AbstractParser<ChangeLogisticsActionAns>() {
      @java.lang.Override
      public ChangeLogisticsActionAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeLogisticsActionAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeLogisticsActionAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeLogisticsActionAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsScenePlane.ChangeLogisticsActionAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateSpyPlaneAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateSpyPlaneAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateSpyPlaneAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateSpyPlaneAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CreateLogisticsPlaneAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CreateLogisticsPlaneAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckLogisticsActionAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckLogisticsActionAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckLogisticsActionAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckLogisticsActionAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeLogisticActionAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeLogisticActionAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeLogisticsActionAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeLogisticsActionAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'ss_proto/gen/scene/ss_scene_plane.prot" +
      "o\022\017com.yorha.proto\032%ss_proto/gen/common/" +
      "common_enum.proto\032 ss_proto/gen/common/s" +
      "truct.proto\032$ss_proto/gen/common/struct_" +
      "msg.proto\"P\n\021CreateSpyPlaneAsk\022\020\n\010player" +
      "Id\030\001 \001(\003\022)\n\007spyInfo\030\002 \001(\0132\030.com.yorha.pr" +
      "oto.SpyInfo\"*\n\021CreateSpyPlaneAns\022\025\n\rplan" +
      "eEntityId\030\001 \001(\003\"V\n\027ChangeActionSpyPlaneA" +
      "sk\022\020\n\010playerId\030\001 \001(\003\022)\n\007spyInfo\030\002 \001(\0132\030." +
      "com.yorha.proto.SpyInfo\"0\n\027ChangeActionS" +
      "pyPlaneAns\022\025\n\rplaneEntityId\030\001 \001(\003\"X\n\031Che" +
      "ckMapCreateSpyPlaneAsk\022\020\n\010playerId\030\001 \001(\003" +
      "\022)\n\007spyInfo\030\002 \001(\0132\030.com.yorha.proto.SpyI" +
      "nfo\"\242\001\n\031CheckMapCreateSpyPlaneAns\022\021\n\terr" +
      "orCode\030\001 \001(\005\022+\n\010currency\030\002 \001(\0132\031.com.yor" +
      "ha.proto.Currency\0223\n\tbuildType\030\003 \001(\0162 .c" +
      "om.yorha.proto.MapBuildingType\022\020\n\010config" +
      "Id\030\004 \001(\005\"\270\001\n\027CreateLogisticsPlaneAsk\022\020\n\010" +
      "playerId\030\001 \001(\003\0225\n\rlogisticsInfo\030\002 \001(\0132\036." +
      "com.yorha.proto.LogisticsInfo\022,\n\tresourc" +
      "es\030\003 \003(\0132\031.com.yorha.proto.Currency\022&\n\003t" +
      "ax\030\004 \003(\0132\031.com.yorha.proto.Currency\"\031\n\027C" +
      "reateLogisticsPlaneAns\"b\n\027CheckLogistics" +
      "ActionAsk\022\020\n\010playerId\030\001 \001(\003\0225\n\rlogistics" +
      "Info\030\002 \001(\0132\036.com.yorha.proto.LogisticsIn" +
      "fo\",\n\027CheckLogisticsActionAns\022\021\n\terrorCo" +
      "de\030\001 \001(\005\"b\n\027ChangeLogisticActionAsk\022\020\n\010p" +
      "layerId\030\001 \001(\003\0225\n\rlogisticsInfo\030\002 \001(\0132\036.c" +
      "om.yorha.proto.LogisticsInfo\"\032\n\030ChangeLo" +
      "gisticsActionAnsB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_CreateSpyPlaneAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_CreateSpyPlaneAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateSpyPlaneAsk_descriptor,
        new java.lang.String[] { "PlayerId", "SpyInfo", });
    internal_static_com_yorha_proto_CreateSpyPlaneAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_CreateSpyPlaneAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateSpyPlaneAns_descriptor,
        new java.lang.String[] { "PlaneEntityId", });
    internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeActionSpyPlaneAsk_descriptor,
        new java.lang.String[] { "PlayerId", "SpyInfo", });
    internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeActionSpyPlaneAns_descriptor,
        new java.lang.String[] { "PlaneEntityId", });
    internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAsk_descriptor,
        new java.lang.String[] { "PlayerId", "SpyInfo", });
    internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckMapCreateSpyPlaneAns_descriptor,
        new java.lang.String[] { "ErrorCode", "Currency", "BuildType", "ConfigId", });
    internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateLogisticsPlaneAsk_descriptor,
        new java.lang.String[] { "PlayerId", "LogisticsInfo", "Resources", "Tax", });
    internal_static_com_yorha_proto_CreateLogisticsPlaneAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_CreateLogisticsPlaneAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CreateLogisticsPlaneAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_CheckLogisticsActionAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_CheckLogisticsActionAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckLogisticsActionAsk_descriptor,
        new java.lang.String[] { "PlayerId", "LogisticsInfo", });
    internal_static_com_yorha_proto_CheckLogisticsActionAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_CheckLogisticsActionAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckLogisticsActionAns_descriptor,
        new java.lang.String[] { "ErrorCode", });
    internal_static_com_yorha_proto_ChangeLogisticActionAsk_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_ChangeLogisticActionAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeLogisticActionAsk_descriptor,
        new java.lang.String[] { "PlayerId", "LogisticsInfo", });
    internal_static_com_yorha_proto_ChangeLogisticsActionAns_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_ChangeLogisticsActionAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeLogisticsActionAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
